// backend/models/Groups/tempGroupModel.js
const db = require('../../utils/db');
const { v4: uuidv4 } = require('uuid');

class TempGroupModel {
  async createTempGroup(creatorId, groupName, category, description, expiresInHours = 24) {
    const linkToken = uuidv4();
    const expiresAt = new Date(Date.now() + expiresInHours * 60 * 60 * 1000);
    const query = `
      INSERT INTO temp_groups (creator_id, group_name, category, description, link_token, expires_at)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *;
    `;
    const result = await db.query(query, [creatorId, groupName, category, description, linkToken, expiresAt]);
    return result.rows[0];
  }

  async getTempGroupByLinkToken(linkToken) {
    const query = `
      SELECT * FROM temp_groups
      WHERE link_token = $1 AND status = 'active' AND expires_at > NOW();
    `;
    const result = await db.query(query, [linkToken]);
    return result.rows[0];
  }

  async addParticipant(groupId, tempUsername) {
    const checkQuery = `
      SELECT 1 FROM temp_group_participants
      WHERE group_id = $1 AND temp_username = $2;
    `;
    const checkResult = await db.query(checkQuery, [groupId, tempUsername]);
    if (checkResult.rows.length > 0) {
      throw new Error('Username already taken in this group');
    }
    const insertQuery = `
      INSERT INTO temp_group_participants (group_id, temp_username)
      VALUES ($1, $2)
      RETURNING *;
    `;
    const result = await db.query(insertQuery, [groupId, tempUsername]);
    return result.rows[0];
  }

  async getParticipantById(participantId) {
    const query = `
      SELECT * FROM temp_group_participants
      WHERE id = $1;
    `;
    const result = await db.query(query, [participantId]);
    return result.rows[0];
  }

  async sendMessage(groupId, senderId, message) {
    const query = `
      INSERT INTO temp_group_chats (group_id, sender_id, message)
      VALUES ($1, $2, $3)
      RETURNING *;
    `;
    const result = await db.query(query, [groupId, senderId, message]);
    return result.rows[0];
  }

  async getMessages(groupId, limit = 50, offset = 0) {
    const query = `
      SELECT c.*, p.temp_username AS sender_username
      FROM temp_group_chats c
      JOIN temp_group_participants p ON c.sender_id = p.id
      WHERE c.group_id = $1
      ORDER BY c.sent_at DESC
      LIMIT $2 OFFSET $3;
    `;
    const result = await db.query(query, [groupId, limit, offset]);
    return result.rows;
  }
  async getTempGroupsByCreator(creatorId) {
    const query = `
      SELECT * FROM temp_groups
      WHERE creator_id = $1 AND status = 'active' AND expires_at > NOW()
      ORDER BY created_at DESC;
    `;
    const result = await db.query(query, [creatorId]);
    return result.rows;
  }
}

module.exports = new TempGroupModel();
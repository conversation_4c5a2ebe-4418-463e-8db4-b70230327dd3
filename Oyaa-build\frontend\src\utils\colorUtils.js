// frontend/src/utils/colorUtils.js

// Simple hash function to get a number from a string
function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash |= 0; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }
  
  // Predefined vibrant color palette (example)
  const colorPalette = [
    '#ee8181', // Red
    '#f7aa6f', // Orange
    '#f8df72', // Yellow
    '#a6e384', // Green
    '#82dde0', // Teal
    '#87b8f8', // Blue
    '#b8a6f5', // Purple
    '#f5b5f5', // Pink
  ];
  
  /**
   * Generates a consistent color from a predefined palette based on an ID.
   * @param {string} id - The user ID or any string identifier.
   * @returns {string} A hex color code.
   */
  export function generateColorForId(id) {
    if (!id) {
      return '#8e9297'; // Default grey for null/undefined IDs
    }
    const hash = simpleHash(id);
    const index = hash % colorPalette.length;
    return colorPalette[index];
  }
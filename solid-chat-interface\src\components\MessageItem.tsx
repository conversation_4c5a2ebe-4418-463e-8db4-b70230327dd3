import { Component } from 'solid-js';
import { Message } from '../types/chat';
import MediaContent from './MediaContent';
import './MessageItem.css';

interface MessageItemProps {
  message: Message;
}

const MessageItem: Component<MessageItemProps> = (props) => {
  const formatTime = (timestamp: Date): string => {
    return timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const isCurrentUser = () => props.message.sender.id === 'current-user';
  const hasMedia = () => props.message.media && props.message.type !== 'text';

  return (
    <div class={`message-item ${isCurrentUser() ? 'current-user' : 'other-user'}`}>
      <div class="message-content">
        {!isCurrentUser() && (
          <div class="avatar">
            <img 
              src={props.message.sender.avatar || '/default-avatar.png'} 
              alt={props.message.sender.name}
              loading="lazy"
            />
          </div>
        )}

        <div class="message-bubble">
          {!isCurrentUser() && (
            <div class="sender-name">{props.message.sender.name}</div>
          )}

          {hasMedia() && props.message.media && (
            <MediaContent media={props.message.media} />
          )}

          {props.message.text.trim() && (
            <div class="message-text">{props.message.text}</div>
          )}

          <div class="message-meta">
            <span class="timestamp">{formatTime(props.message.timestamp)}</span>
            {props.message.status && (
              <span class={`status status-${props.message.status}`}>
                {props.message.status === 'sent' && '✓'}
                {props.message.status === 'delivered' && '✓✓'}
                {props.message.status === 'read' && '✓✓'}
                {props.message.status === 'failed' && '⚠'}
              </span>
            )}
          </div>
        </div>

        {isCurrentUser() && (
          <div class="avatar">
            <img 
              src={props.message.sender.avatar || '/default-avatar.png'} 
              alt={props.message.sender.name}
              loading="lazy"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageItem;

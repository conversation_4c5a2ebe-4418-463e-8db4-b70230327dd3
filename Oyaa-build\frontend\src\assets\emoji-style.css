/* Emoji rendering styles to ensure consistent appearance across different devices */

/* Define emoji font family */
@font-face {
  font-family: 'EmojiFont';
  src: local('Segoe UI Emoji'),
       local('Segoe UI Symbol'),
       local('Apple Color Emoji'),
       local('Noto Color Emoji'),
       local('EmojiOne Color'),
       local('Android Emoji');
  font-display: swap;
}

/* Global emoji style */
.emoji {
  font-family: 'EmojiFont', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Apple Color Emoji', 'Noto Color Emoji', sans-serif;
  display: inline-block;
  vertical-align: middle;
  font-size: 1.2em;
  line-height: 1;
  margin: 0 0.1em;
}

/* Size variations */
.emoji--small {
  font-size: 1em;
}

.emoji--medium {
  font-size: 1.4em;
}

.emoji--large {
  font-size: 2em;
}

/* For single emoji messages (make them bigger) */
.emoji-only-message {
  font-size: 3em;
  line-height: 1.2;
  margin: 0.3em 0;
  text-align: center;
}

/* Emoji with text styling */
.with-emoji {
  font-family: inherit;
}

.with-emoji .emoji {
  vertical-align: -0.1em;
}

/* Fix for Firefox emoji rendering */
@-moz-document url-prefix() {
  .emoji {
    font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', sans-serif;
  }
}

/* Fix for Safari emoji rendering */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  _::-webkit-full-page-media, _:future, :root .emoji {
    font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', sans-serif;
  }
} 
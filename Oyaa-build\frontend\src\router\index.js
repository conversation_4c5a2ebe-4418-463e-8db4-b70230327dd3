// frontend/src/router/index.js
import { createRouter, createWebHistory } from 'vue-router';
import store from '../store';

import LandingPage from '../components/LandingPage.vue';
import SignUp from '../components/SignUp.vue';
import SignIn from '../components/SignIn.vue';
import Dashboard from '../components/Dashboard/Dashboard.vue';
import Profile from '../components/Dashboard/Profile.vue';
import Settings from '../components/Settings.vue';
import FriendRequests from '../components/FriendRequests/FriendRequests.vue';
import AddFriends from '../components/AddFriends.vue';
import AddFriendsInterest from '../components/AddFriendsInterest/AddFriendsInterest.vue';
import GroupRequests from '../components/Groups/GroupRequests/GroupRequests.vue';
import NearbyUsers from '@/components/nearYou/NearbyUsers.vue';
import ChatWindow from '@/components/TempChatPage/ChatWindow.vue';
import Preferences from '../components/settings/ContentAndActivity/Preferences.vue';
import TestSound from '../components/TestSound.vue';

const routes = [
  { path: '/', component: LandingPage },
  { path: '/signup', component: SignUp, meta: { requiresGuest: true } },
  { path: '/signin', component: SignIn, meta: { requiresGuest: true } },
  { path: '/dashboard', component: Dashboard, meta: { requiresAuth: true } },
  { path: '/profile', component: Profile, meta: { requiresAuth: true } },
  { path: '/settings', component: Settings, meta: { requiresAuth: true } },
  { path: '/friend-requests', component: FriendRequests, meta: { requiresAuth: true } },
  { path: '/add-friends', component: AddFriends, meta: { requiresAuth: true } },
{ path: '/add-friends-interest', component: AddFriendsInterest, meta: { requiresAuth: true } },
  { path: '/group-requests', component: GroupRequests, meta: { requiresAuth: true } },
  {
    path: '/chat/:friendId',
    name: 'Chat', // Add this line
    component: () => import("../components/chatPage/ChatWindow.vue").then(m => m.default),
    meta: { requiresAuth: true }
  },
  {
    path: '/near-you',
    name: 'NearYou',
    component: () => import('../components/nearYou/NearbyUsers.vue'),
    meta: { requiresAuth: true },
  },
  {
    path: '/near-you/map',
    name: 'NearbyUsersMap',
    component: () => import('../components/nearYou/NearbyUsersMap.vue'),
    meta: { requiresAuth: true },
  },
 // frontend/src/router/index.js
{
  path: '/temp-chat/:friendId',
  name: 'TempChat',
  component: ChatWindow,
  meta: { requiresAuth: true }
},
  { path: '/preferences', name: 'Preferences', component: Preferences },
  { path: '/TestSound', component: TestSound },

  // New routes for Groups
  {
    path: '/create-group',
    name: 'CreateGroup',
    component: () => import('../components/Groups/CreateGroup.vue'),
    meta: { requiresAuth: true },
  },
  {
    path: '/find-group',
    name: 'JoinGroup',
    component: () => import('../components/Groups/JoinGroup.vue'),
    meta: { requiresAuth: true },
  },
  {
    path: '/groups-near-you',
    name: 'GroupsNearYou',
    component: () => import('../components/Groups/GroupsNearYou/NearbyGroups.vue'),
    meta: { requiresAuth: true },
  },
  {
    path: '/groups/:groupId',
    name: 'GroupChatPage',
    component: () => import('@/components/Groups/groupChatPage/GroupChatPage.vue'),
    meta: { requiresAuth: true },
  },
  {
    path: '/trending-rooms',
    name: 'TrendingRooms',
    component: () => import('../components/Groups/TrendingRooms.vue'),
    meta: { requiresAuth: true },
  },

  {
    path: '/profile/edit',
    name: 'EditProfile',
    component: () => import('../components/Dashboard/EditProfile.vue'),
    meta: { requiresAuth: true }
  },
  // Anonymous
  {
    path: '/anonymous',
    name: 'Anonymous',
    component: () => import('../components/AnonymousGroup/AnonymousPage.vue'),
    meta: { requiresAuth: true },
  },
  {
    path: '/anonymous/join/:linkToken',
    name: 'JoinAnonymousGroup',
    component: () => import('../components/AnonymousGroup/JoinAnonymousGroup.vue'),
  },
  {
    path: '/anonymous/chat/:groupId',
    name: 'AnonymousGroupChat',
    component: () => import('../components/AnonymousGroup/AnonymousGroupChat.vue'),
  },

  {
    path: '/find-groups-interest',
    name: 'FindGroupsInterest',
    component: () => import('../components/Groups/FindGroupsInterest/FindGroupsInterest.vue'),
    meta: { requiresAuth: true },
  },


// About section routes
  { path: '/community-guidelines', component: () => import('@/components/settings/about/CommunityGuidelines.vue') },
  { path: '/terms-of-service', component: () => import('@/components/settings/about/TermsOfService.vue') },
  { path: '/privacy-policy', component: () => import('@/components/settings/about/PrivacyPolicy.vue') },
  { path: '/intellectual-property-policy', component: () => import('@/components/settings/about/IntellectualPropertyPolicy.vue') },
  // Account section routes
  { path: '/manage-account', component: () => import('@/components/settings/account/ManageAccount.vue') },
  { path: '/user-privacy', component: () => import('@/components/settings/account/UserPrivacy.vue') },
  { path: '/customer-support', component: () => import('@/components/settings/account/CustomerSupport.vue') },




// Add this entry in your routes array
{
  path: '/test',
  name: 'TestPage',
  component: () => import('../test/test.vue')
},
{
  path: '/test2',
  name: 'TestPage2',
  component: () => import('../test/test2.vue')
},


];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Import Firebase auth
import { auth } from '../config/firebase.config';

// Navigation guard to check authentication status.
router.beforeEach(async (to, from, next) => {
  // Check Firebase auth state directly
  const currentUser = auth.currentUser;
  const isAuthenticated = currentUser !== null || store.getters['auth/isAuthenticated'];

  // Check if user needs to select a username
  const needsUsername = store.getters['auth/needsUsername'];

  // For debugging
  console.log('Route navigation:', {
    to: to.path,
    requiresAuth: to.meta.requiresAuth,
    requiresGuest: to.meta.requiresGuest,
    isAuthenticated,
    currentUser: currentUser ? currentUser.uid : null,
    storeAuth: store.getters['auth/isAuthenticated'],
    needsUsername
  });

  // If user needs to select a username and is trying to access a protected route
  if (needsUsername && to.path !== '/signin' && to.path !== '/signup') {
    console.log('Redirecting to signin: User needs to select a username');
    next('/signin');
    return;
  }

  // Handle routes that require authentication
  if (to.meta.requiresAuth && !isAuthenticated) {
    console.log('Redirecting to signin: Auth required but user not authenticated');
    next('/signin');
    return;
  }

  // Handle routes that require guest access
  if (to.meta.requiresGuest && isAuthenticated) {
    // If user needs to select a username, don't redirect to dashboard
    if (needsUsername) {
      console.log('User is authenticated but needs username, allowing access to guest route');
      next();
      return;
    }

    console.log('Redirecting to dashboard: Guest only but user is authenticated');
    next('/dashboard');
    return;
  }

  next();
});

export default router;

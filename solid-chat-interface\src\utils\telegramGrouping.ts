import { Message } from '../types/chat';

export interface MessageGroup {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  messages: Message[];
  timestamp: Date;
  groupPosition: 'first' | 'middle' | 'last' | 'single';
}

export interface GroupedMessage extends Message {
  groupPosition: 'first' | 'middle' | 'last' | 'single';
  showAvatar: boolean;
  showTime: boolean;
  showDateHeader: boolean;
  dateHeaderText?: string;
}

/**
 * Telegram's exact message grouping logic
 * Groups messages by sender and time proximity (5 minutes)
 */
export function groupTelegramMessages(messages: Message[]): GroupedMessage[] {
  if (!messages.length) return [];

  const grouped: GroupedMessage[] = [];
  const GROUP_TIME_THRESHOLD = 5 * 60 * 1000; // 5 minutes in milliseconds
  const DATE_THRESHOLD = 24 * 60 * 60 * 1000; // 24 hours for date headers

  for (let i = 0; i < messages.length; i++) {
    const currentMessage = messages[i];
    const previousMessage = messages[i - 1];
    const nextMessage = messages[i + 1];
    
    const currentTime = new Date(currentMessage.timestamp);
    const previousTime = previousMessage ? new Date(previousMessage.timestamp) : null;
    const nextTime = nextMessage ? new Date(nextMessage.timestamp) : null;

    // Determine if we should show a date header
    let showDateHeader = false;
    let dateHeaderText = '';
    
    if (!previousMessage || 
        (previousTime && currentTime.getTime() - previousTime.getTime() > DATE_THRESHOLD) ||
        (previousTime && !isSameDay(currentTime, previousTime))) {
      showDateHeader = true;
      dateHeaderText = formatDateHeader(currentTime);
    }

    // Determine grouping position
    let groupPosition: 'first' | 'middle' | 'last' | 'single' = 'single';
    let showAvatar = true;
    let showTime = true;

    // Check if this message can be grouped with previous message
    const canGroupWithPrevious = previousMessage &&
      previousMessage.senderId === currentMessage.senderId &&
      previousTime &&
      currentTime.getTime() - previousTime.getTime() < GROUP_TIME_THRESHOLD &&
      isSameDay(currentTime, previousTime);

    // Check if this message can be grouped with next message
    const canGroupWithNext = nextMessage &&
      nextMessage.senderId === currentMessage.senderId &&
      nextTime &&
      nextTime.getTime() - currentTime.getTime() < GROUP_TIME_THRESHOLD &&
      isSameDay(currentTime, nextTime);

    // Determine group position based on grouping possibilities
    if (canGroupWithPrevious && canGroupWithNext) {
      groupPosition = 'middle';
      showAvatar = false;
      showTime = false;
    } else if (canGroupWithPrevious && !canGroupWithNext) {
      groupPosition = 'last';
      showAvatar = false;
      showTime = true; // Show time on last message of group
    } else if (!canGroupWithPrevious && canGroupWithNext) {
      groupPosition = 'first';
      showAvatar = true; // Show avatar on first message of group
      showTime = false;
    } else {
      groupPosition = 'single';
      showAvatar = true;
      showTime = true;
    }

    // For incoming messages, only show avatar on the last message of a group
    if (currentMessage.senderId !== 'current-user' && groupPosition !== 'last' && groupPosition !== 'single') {
      showAvatar = false;
    }

    grouped.push({
      ...currentMessage,
      groupPosition,
      showAvatar,
      showTime,
      showDateHeader,
      dateHeaderText
    });
  }

  return grouped;
}

/**
 * Check if two dates are on the same day
 */
function isSameDay(date1: Date, date2: Date): boolean {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
}

/**
 * Format date header like Telegram
 */
function formatDateHeader(date: Date): string {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  // Check if it's today
  if (isSameDay(date, today)) {
    return 'Today';
  }
  
  // Check if it's yesterday
  if (isSameDay(date, yesterday)) {
    return 'Yesterday';
  }
  
  // Check if it's this year
  if (date.getFullYear() === today.getFullYear()) {
    return date.toLocaleDateString('en-US', { 
      month: 'long', 
      day: 'numeric' 
    });
  }
  
  // Different year
  return date.toLocaleDateString('en-US', { 
    year: 'numeric',
    month: 'long', 
    day: 'numeric' 
  });
}

/**
 * Telegram's logic for determining if messages should be grouped
 * Based on sender, time proximity, and message types
 */
export function shouldGroupMessages(
  message1: Message, 
  message2: Message, 
  timeThreshold: number = 5 * 60 * 1000
): boolean {
  // Must be from same sender
  if (message1.senderId !== message2.senderId) {
    return false;
  }

  // Must be within time threshold
  const time1 = new Date(message1.timestamp);
  const time2 = new Date(message2.timestamp);
  const timeDiff = Math.abs(time2.getTime() - time1.getTime());
  
  if (timeDiff > timeThreshold) {
    return false;
  }

  // Must be on same day
  if (!isSameDay(time1, time2)) {
    return false;
  }

  // Service messages should not be grouped
  if (message1.type === 'service' || message2.type === 'service') {
    return false;
  }

  return true;
}

/**
 * Calculate optimal message window size based on viewport
 * Telegram's strategy for performance
 */
export function calculateMessageWindow(
  totalMessages: number,
  viewportHeight: number = window.innerHeight
): number {
  // Telegram's heuristic: show enough messages to fill 2x viewport height
  const estimatedMessageHeight = 60; // Average message height
  const messagesPerViewport = Math.ceil(viewportHeight / estimatedMessageHeight);
  const optimalWindow = messagesPerViewport * 2;
  
  // Minimum window size
  const minWindow = 50;
  
  // Maximum window size for performance
  const maxWindow = 1000;
  
  return Math.min(maxWindow, Math.max(minWindow, optimalWindow));
}

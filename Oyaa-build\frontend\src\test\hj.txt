<template>
  <div class="emoji-container">
    <div class="textarea-container">
      <textarea 
        v-model="message" 
        placeholder="Type a message..." 
        class="message-textarea"
      ></textarea>
      <button 
        @click="toggleEmojiMenu" 
        class="emoji-button"
        aria-label="Open emoji menu"
      >
        <i class="emoji-icon">😊</i>
      </button>
    </div>

    <div v-if="showEmojiMenu" class="emoji-menu">
      <div class="emoji-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="['tab-button', { active: activeTab === tab.id }]"
        >
          {{ tab.icon }}
        </button>
      </div>
      
      <div class="emoji-search">
        <input 
          type="text" 
          v-model="searchQuery" 
          :placeholder="`Search ${tabs.find(tab => tab.id === activeTab)?.name}...`" 
          class="search-input"
        />
      </div>
      
      <div class="emoji-content">
        <!-- Regular Emojis -->
        <div v-if="activeTab === 'emoji'" class="emoji-grid">
          <button 
            v-for="emoji in filteredEmojis" 
            :key="emoji.symbol"
            @click="insertEmoji(emoji.symbol)"
            class="emoji-item"
            :title="emoji.name"
          >
            {{ emoji.symbol }}
          </button>
        </div>
        
        <!-- GIFs -->
        <div v-else-if="activeTab === 'gif'" class="gif-grid">
          <div v-if="loading" class="loading">Loading...</div>
          <div v-else-if="gifs.length === 0" class="no-results">No GIFs found. Try another search.</div>
          <div 
            v-else
            v-for="gif in gifs" 
            :key="gif.id"
            @click="insertGif(gif.url)"
            class="gif-item"
          >
            <img :src="gif.url" :alt="gif.title" loading="lazy" />
          </div>
        </div>
        
        <!-- Stickers -->
        <div v-else-if="activeTab === 'sticker'" class="sticker-grid">
          <div v-if="loading" class="loading">Loading...</div>
          <div v-else-if="stickers.length === 0" class="no-results">No stickers found. Try another search.</div>
          <div 
            v-else
            v-for="sticker in stickers" 
            :key="sticker.id"
            @click="insertSticker(sticker.url)"
            class="sticker-item"
          >
            <img :src="sticker.url" :alt="sticker.title" loading="lazy" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { debounce } from 'lodash-es'

export default {
  name: 'EmojiMenu',
  setup() {
    const message = ref('')
    const showEmojiMenu = ref(false)
    const activeTab = ref('emoji')
    const searchQuery = ref('')
    const loading = ref(false)
    
    // Tabs configuration
    const tabs = [
      { id: 'emoji', name: 'Emojis', icon: '😊' },
      { id: 'gif', name: 'GIFs', icon: '🎬' },
      { id: 'sticker', name: 'Stickers', icon: '🏷️' }
    ]
    
    // Emoji data
    const emojis = ref([
      { name: 'Smile', symbol: '😊', keywords: ['happy', 'smile', 'joy'] },
      { name: 'Laugh', symbol: '😂', keywords: ['laugh', 'joy', 'lol'] },
      { name: 'Heart', symbol: '❤️', keywords: ['love', 'heart', 'red'] },
      { name: 'Fire', symbol: '🔥', keywords: ['fire', 'hot', 'lit'] },
      { name: 'Thumbs Up', symbol: '👍', keywords: ['like', 'approve', 'thumbs up'] },
      { name: 'Party', symbol: '🎉', keywords: ['celebration', 'party', 'confetti'] },
      { name: 'Thinking', symbol: '🤔', keywords: ['think', 'wondering', 'hmm'] },
      { name: 'Clap', symbol: '👏', keywords: ['applause', 'clap', 'praise'] },
      { name: 'Cry', symbol: '😢', keywords: ['sad', 'cry', 'tear'] },
      { name: 'Angry', symbol: '😡', keywords: ['mad', 'angry', 'rage'] },
      { name: 'Cool', symbol: '😎', keywords: ['cool', 'sunglasses', 'awesome'] },
      { name: 'Shocked', symbol: '😱', keywords: ['shocked', 'surprised', 'wow'] },
      { name: 'Eyes', symbol: '👀', keywords: ['eyes', 'look', 'see'] },
      { name: 'OK', symbol: '👌', keywords: ['ok', 'okay', 'perfect'] },
      { name: 'Pray', symbol: '🙏', keywords: ['pray', 'please', 'hope'] },
      { name: 'Rocket', symbol: '🚀', keywords: ['rocket', 'launch', 'space'] },
      { name: 'Star', symbol: '⭐', keywords: ['star', 'favorite', 'rating'] },
      { name: 'Moon', symbol: '🌙', keywords: ['moon', 'night', 'sleep'] },
      { name: 'Sun', symbol: '☀️', keywords: ['sun', 'day', 'bright'] },
      { name: 'Rainbow', symbol: '🌈', keywords: ['rainbow', 'colorful', 'pride'] },
      { name: 'Pizza', symbol: '🍕', keywords: ['pizza', 'food', 'slice'] },
      { name: 'Beer', symbol: '🍺', keywords: ['beer', 'drink', 'alcohol'] },
      { name: 'Coffee', symbol: '☕', keywords: ['coffee', 'drink', 'caffeine'] },
      { name: 'Cake', symbol: '🎂', keywords: ['cake', 'birthday', 'celebration'] },
    ])
    
    // GIFs and Stickers data
    const gifs = ref([])
    const stickers = ref([])
    
    // Filter emojis based on search query
    const filteredEmojis = computed(() => {
      if (!searchQuery.value) return emojis.value
      
      const query = searchQuery.value.toLowerCase()
      return emojis.value.filter(emoji => 
        emoji.name.toLowerCase().includes(query) || 
        emoji.keywords.some(keyword => keyword.includes(query))
      )
    })
    
    // Toggle emoji menu
    const toggleEmojiMenu = () => {
      showEmojiMenu.value = !showEmojiMenu.value
      if (showEmojiMenu.value && activeTab.value !== 'emoji') {
        fetchContent()
      }
    }
    
    // Insert emoji into textarea
    const insertEmoji = (emoji) => {
      message.value += emoji
    }
    
    // Insert GIF into textarea (in real app, this would insert a GIF reference)
    const insertGif = (url) => {
      message.value += ` [GIF: ${url}] `
      showEmojiMenu.value = false
    }
    
    // Insert sticker into textarea (in real app, this would insert a sticker reference)
    const insertSticker = (url) => {
      message.value += ` [Sticker: ${url}] `
      showEmojiMenu.value = false
    }
    
    // Fetch GIFs or stickers from API
    const fetchContent = async () => {
      if (activeTab.value === 'emoji') return
      
      loading.value = true
      
      try {
        if (activeTab.value === 'gif') {
          // Using Tenor API (in a real app, you'd use your API key)
          const response = await fetch(`https://g.tenor.com/v1/search?q=${encodeURIComponent(searchQuery.value || 'trending')}&key=LIVDSRZULELA&limit=20`)
          const data = await response.json()
          
          gifs.value = data.results.map(item => ({
            id: item.id,
            url: item.media[0].gif.url,
            title: item.title
          }))
        } else if (activeTab.value === 'sticker') {
          // Using Tenor API for stickers
          const response = await fetch(`https://g.tenor.com/v1/search?q=${encodeURIComponent(searchQuery.value || 'trending')}&key=LIVDSRZULELA&limit=20&media_filter=minimal&contentfilter=high`)
          const data = await response.json()
          
          stickers.value = data.results.map(item => ({
            id: item.id,
            url: item.media[0].tinygif.url,
            title: item.title
          }))
        }
      } catch (error) {
        console.error('Error fetching content:', error)
        if (activeTab.value === 'gif') {
          gifs.value = []
        } else {
          stickers.value = []
        }
      } finally {
        loading.value = false
      }
    }
    
    // Debounced search function
    const debouncedSearch = debounce(fetchContent, 500)
    
    // Watch for changes in search query or active tab
    watch([searchQuery, activeTab], () => {
      if (activeTab.value !== 'emoji') {
        debouncedSearch()
      }
    })
    
    return {
      message,
      showEmojiMenu,
      activeTab,
      searchQuery,
      tabs,
      emojis,
      gifs,
      stickers,
      loading,
      filteredEmojis,
      toggleEmojiMenu,
      insertEmoji,
      insertGif,
      insertSticker
    }
  }
}
</script>

<style>
:root {
  --dark-bg: #1e1e2e;
  --darker-bg: #181825;
  --accent: #7289da;
  --text: #e0e0e0;
  --text-secondary: #a0a0a0;
  --border: #3f3f5a;
  --hover: #2d2d3f;
}

.emoji-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--text);
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.textarea-container {
  position: relative;
  width: 100%;
  background-color: var(--darker-bg);
  border-radius: 8px;
  border: 1px solid var(--border);
  overflow: hidden;
}

.message-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px 40px 12px 12px;
  background-color: transparent;
  border: none;
  color: var(--text);
  font-size: 16px;
  resize: vertical;
  outline: none;
}

.emoji-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 20px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.emoji-button:hover {
  background-color: var(--hover);
}

.emoji-menu {
  position: absolute;
  bottom: calc(100% + 10px);
  right: 0;
  width: 100%;
  max-height: 350px;
  background-color: var(--dark-bg);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  border: 1px solid var(--border);
  overflow: hidden;
}

.emoji-tabs {
  display: flex;
  border-bottom: 1px solid var(--border);
  background-color: var(--darker-bg);
}

.tab-button {
  padding: 10px 15px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 18px;
  flex: 1;
  transition: all 0.2s;
}

.tab-button.active {
  color: var(--accent);
  border-bottom: 2px solid var(--accent);
  background-color: var(--dark-bg);
}

.tab-button:hover:not(.active) {
  background-color: var(--hover);
}

.emoji-search {
  padding: 10px;
  border-bottom: 1px solid var(--border);
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid var(--border);
  background-color: var(--darker-bg);
  color: var(--text);
  font-size: 14px;
  outline: none;
}

.search-input:focus {
  border-color: var(--accent);
}

.emoji-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 5px;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  aspect-ratio: 1;
  font-size: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.emoji-item:hover {
  background-color: var(--hover);
}

.gif-grid, .sticker-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.gif-item, .sticker-item {
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.2s;
}

.gif-item:hover, .sticker-item:hover {
  transform: scale(0.98);
}

.gif-item img, .sticker-item img {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
}

.loading, .no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 20px;
  color: var(--text-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (max-width: 480px) {
  .emoji-grid {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .gif-grid, .sticker-grid {
    grid-template-columns: 1fr;
  }
  
  .emoji-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    max-height: 50vh;
    border-radius: 12px 12px 0 0;
  }
}
</style>
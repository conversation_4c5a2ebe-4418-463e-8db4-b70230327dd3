<template>
    <div style="padding: 20px;">
      <button
        @click="playTestSound"
        style="padding: 10px 20px; background-color: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;"
      >
        Test Sound
      </button>
    </div>
  </template>
  
  <script>
  import { Howl, Howler } from 'howler';
  
  export default {
    name: 'TestSound',
    data() {
      return {
        testSound: new Howl({
          src: ['/sounds/mixkit-software-interface-start-2574.mp3'],
          volume: 1,
          preload: true,
        }),
      };
    },
    methods: {
      playTestSound() {
        // Check if the AudioContext is suspended and resume it if needed.
        if (Howler.ctx && Howler.ctx.state === 'suspended') {
          Howler.ctx.resume()
            .then(() => {
              console.log('AudioContext resumed');
              this.testSound.play();
            })
            .catch((err) => {
              console.error('Error resuming AudioContext', err);
            });
        } else {
          this.testSound.play();
        }
      },
    },
  };
  </script>
  
  <style scoped>
  /* Additional styles if needed */
  </style>
  
<!-- frontend/src/components/Preferences.vue -->
<template>
  <div class="preferences">
    <div class="header">
     
      <h1>Preferences</h1>
      <button class="close-button" @click="closePreferences">
        <i class="fa fa-times"></i>
      </button>
    </div>
    <div class="content">
      <h2>Select your avatar</h2>
      <!-- Use the existing avatar picker -->
      <PickAvatar @avatar-selected="handleAvatarSelected" />
      <button class="save-button" @click="updateUserAvatar" :disabled="loading">
        {{ loading ? 'Saving...' : 'Save' }}
      </button>
      <p v-if="error" class="error">{{ error }}</p>
    </div>
  </div>
</template>

<script>
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import axios from 'axios';
import PickAvatar from './PickAvatar.vue';

export default {
  name: 'Preferences',
  components: {
    PickAvatar,
  },
  data() {
    return {
      selectedAvatar: '',
      loading: false,
      error: '',
    };
  },
  setup() {
    const store = useStore();
    const router = useRouter();
    return { store, router };
  },
  methods: {
    handleAvatarSelected(avatar) {
      this.selectedAvatar = avatar;
    },
    async updateUserAvatar() {
      if (!this.selectedAvatar) {
        this.error = "Please select an avatar";
        return;
      }
      try {
        this.loading = true;
        const userId = this.store.getters['auth/userId'];
        const token = this.store.getters['auth/token'];
        // Assumes your backend exposes an endpoint at /api/users/avatar with a PUT method
        const response = await axios.put(
          `${import.meta.env.VITE_API_BASE_URL}/api/users/avatar`,
          { userId, avatarUrl: this.selectedAvatar },
          { headers: { Authorization: `Bearer ${token}` } }
        );
        // Optionally update your store with the new user data:
        // this.store.commit('auth/UPDATE_USER', response.data.user);
        this.loading = false;
        // Navigate back to settings (or anywhere you prefer)
        this.router.push('/settings');
      } catch (err) {
        this.error = err.response?.data?.message || "Error updating avatar";
        this.loading = false;
      }
    },
    goBack() {
      this.router.back();
    },
    closePreferences() {
      // Close preferences by navigating to the settings route.
      this.router.push('/settings');
    },
  },
};
</script>

<style scoped>
.preferences {
  padding: 20px;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.back-button,
.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #ff0000;
}
.close-button i {
  font-size: 24px;
}
.content {
  text-align: center;
}
.save-button {
  background: #151717;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 20px;
}
.error {
  color: red;
  margin-top: 10px;
}
</style>

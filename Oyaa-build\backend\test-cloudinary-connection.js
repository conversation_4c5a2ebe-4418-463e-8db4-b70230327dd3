// test-cloudinary-connection.js
require('dotenv').config();
const cloudinary = require('cloudinary').v2;

// Configure Cloudinary with environment variables
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

console.log('Cloudinary configuration:');
console.log(`Cloud name: ${process.env.CLOUDINARY_CLOUD_NAME}`);
console.log(`API key: ${process.env.CLOUDINARY_API_KEY}`);
console.log('API secret: [HIDDEN]');

// Test the connection
cloudinary.api.ping((error, result) => {
  if (error) {
    console.error('❌ Cloudinary connection failed:', error);
    process.exit(1);
  } else {
    console.log('✅ Cloudinary connected successfully:', result);
    
    // Test the secondary account if configured
    if (process.env.CLOUDINARY_CLOUD_NAME_AVATAR) {
      console.log('\nTesting secondary Cloudinary account (Avatar)...');
      const secondaryCloudinary = require('cloudinary').v2;
      secondaryCloudinary.config({
        cloud_name: process.env.CLOUDINARY_CLOUD_NAME_AVATAR,
        api_key: process.env.CLOUDINARY_API_KEY_AVATAR,
        api_secret: process.env.CLOUDINARY_API_SECRET_AVATAR
      });
      
      secondaryCloudinary.api.ping((error2, result2) => {
        if (error2) {
          console.error('❌ Secondary Cloudinary connection failed:', error2);
        } else {
          console.log('✅ Secondary Cloudinary connected successfully:', result2);
        }
      });
    }
  }
});

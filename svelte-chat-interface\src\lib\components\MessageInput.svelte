<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher<{
		sendMessage: { text: string; media?: any };
	}>();

	let messageText = '';
	let textArea: HTMLTextAreaElement;
	let fileInput: HTMLInputElement;
	let selectedFile: File | null = null;

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			sendMessage();
		}
	}

	function sendMessage() {
		const text = messageText.trim();
		if (!text && !selectedFile) return;

		let media = null;
		if (selectedFile) {
			media = {
				url: URL.createObjectURL(selectedFile),
				type: getFileType(selectedFile),
				name: selectedFile.name,
				size: selectedFile.size
			};
		}

		dispatch('sendMessage', { text, media });
		
		// Reset form
		messageText = '';
		selectedFile = null;
		if (fileInput) fileInput.value = '';
		
		// Reset textarea height
		if (textArea) {
			textArea.style.height = 'auto';
		}
	}

	function getFileType(file: File): 'image' | 'audio' | 'video' | 'file' {
		if (file.type.startsWith('image/')) return 'image';
		if (file.type.startsWith('audio/')) return 'audio';
		if (file.type.startsWith('video/')) return 'video';
		return 'file';
	}

	function handleFileSelect(event: Event) {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files[0]) {
			selectedFile = target.files[0];
		}
	}

	function removeSelectedFile() {
		selectedFile = null;
		if (fileInput) fileInput.value = '';
	}

	function autoResize() {
		if (textArea) {
			textArea.style.height = 'auto';
			textArea.style.height = Math.min(textArea.scrollHeight, 120) + 'px';
		}
	}

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}
</script>

<div class="message-input-container">
	{#if selectedFile}
		<div class="file-preview">
			<div class="file-info">
				<span class="file-icon">
					{#if selectedFile.type.startsWith('image/')}🖼️{/if}
					{#if selectedFile.type.startsWith('audio/')}🎵{/if}
					{#if selectedFile.type.startsWith('video/')}🎬{/if}
					{#if !selectedFile.type.startsWith('image/') && !selectedFile.type.startsWith('audio/') && !selectedFile.type.startsWith('video/')}📄{/if}
				</span>
				<div class="file-details">
					<span class="file-name">{selectedFile.name}</span>
					<span class="file-size">{formatFileSize(selectedFile.size)}</span>
				</div>
			</div>
			<button 
				class="remove-file"
				on:click={removeSelectedFile}
				type="button"
				aria-label="Remove file"
			>
				×
			</button>
		</div>
	{/if}

	<div class="input-row">
		<button 
			class="attach-button"
			on:click={() => fileInput?.click()}
			type="button"
			aria-label="Attach file"
		>
			📎
		</button>

		<div class="text-input-container">
			<textarea
				bind:this={textArea}
				bind:value={messageText}
				on:keydown={handleKeyDown}
				on:input={autoResize}
				placeholder="Type a message..."
				rows="1"
				class="message-textarea"
			></textarea>
		</div>

		<button 
			class="send-button"
			class:disabled={!messageText.trim() && !selectedFile}
			on:click={sendMessage}
			disabled={!messageText.trim() && !selectedFile}
			type="button"
			aria-label="Send message"
		>
			<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
				<path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
			</svg>
		</button>
	</div>

	<input
		bind:this={fileInput}
		type="file"
		on:change={handleFileSelect}
		accept="image/*,audio/*,video/*,.pdf,.doc,.docx,.txt"
		style="display: none;"
	/>
</div>

<style>
	.message-input-container {
		padding: 16px;
		background: #fff;
		border-top: 1px solid #e9ecef;
	}

	.file-preview {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #f8f9fa;
		border: 1px solid #dee2e6;
		border-radius: 8px;
		padding: 8px 12px;
		margin-bottom: 8px;
	}

	.file-info {
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.file-icon {
		font-size: 20px;
	}

	.file-details {
		display: flex;
		flex-direction: column;
		gap: 2px;
	}

	.file-name {
		font-size: 0.9rem;
		font-weight: 500;
	}

	.file-size {
		font-size: 0.75rem;
		color: #666;
	}

	.remove-file {
		background: none;
		border: none;
		font-size: 20px;
		cursor: pointer;
		color: #666;
		padding: 4px;
		border-radius: 4px;
		transition: background-color 0.2s;
	}

	.remove-file:hover {
		background: #e9ecef;
	}

	.input-row {
		display: flex;
		align-items: flex-end;
		gap: 8px;
	}

	.attach-button {
		background: none;
		border: none;
		font-size: 20px;
		cursor: pointer;
		padding: 8px;
		border-radius: 8px;
		transition: background-color 0.2s;
		flex-shrink: 0;
	}

	.attach-button:hover {
		background: #f8f9fa;
	}

	.text-input-container {
		flex: 1;
		position: relative;
	}

	.message-textarea {
		width: 100%;
		border: 1px solid #dee2e6;
		border-radius: 20px;
		padding: 12px 16px;
		font-family: inherit;
		font-size: 14px;
		line-height: 1.4;
		resize: none;
		outline: none;
		transition: border-color 0.2s;
		min-height: 44px;
		max-height: 120px;
	}

	.message-textarea:focus {
		border-color: #007bff;
	}

	.message-textarea::placeholder {
		color: #999;
	}

	.send-button {
		background: #007bff;
		color: white;
		border: none;
		border-radius: 50%;
		width: 44px;
		height: 44px;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s;
		flex-shrink: 0;
	}

	.send-button:hover:not(.disabled) {
		background: #0056b3;
		transform: scale(1.05);
	}

	.send-button.disabled {
		background: #ccc;
		cursor: not-allowed;
		transform: none;
	}

	/* Mobile responsiveness */
	@media (max-width: 768px) {
		.message-input-container {
			padding: 12px;
		}

		.input-row {
			gap: 6px;
		}

		.attach-button {
			padding: 6px;
		}

		.send-button {
			width: 40px;
			height: 40px;
		}
	}
</style>

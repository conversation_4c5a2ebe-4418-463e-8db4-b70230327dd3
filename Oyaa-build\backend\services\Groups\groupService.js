const GroupModel = require('../../models/Groups/groupModel');
const groupMembersService = require('./groupMembersService');
const groupMembersModel = require('../../models/Groups/groupMembersModel');

class GroupService {
  async createGroup(name, creatorId, description, avatar, tags = [], isWorldChat = false, latitude = null, longitude = null) {
    if (!Array.isArray(tags)) {
      throw new Error('tags must be an array');
    }
    if (typeof isWorldChat !== 'boolean') {
      throw new Error('isWorldChat must be a boolean');
    }
    const existingGroup = await GroupModel.getGroupByName(name);
    if (existingGroup) {
      const error = new Error('This name has been taken');
      error.status = 400;
      throw error;
    }
    const group = await GroupModel.createGroup(name, creatorId, description, avatar, tags, isWorldChat, latitude, longitude);
    if (creatorId > 0) {
      await groupMembersService.addMember(group.id, creatorId, 'admin');
    }
    return group;
  }

  async getGroups(userId) {
    return await GroupModel.getGroups(userId);
  }

  async getGroupRequests(userId, lastUpdated) {
    try {
      const groupRequests = await GroupModel.getGroupRequests(userId, lastUpdated);
      return groupRequests;
    } catch (err) {
      console.error('Service: Error fetching group requests:', err);
      throw err;
    }
  }

  async searchGroups(name) {
    console.log('[GroupService] Searching groups with name:', name);
    return await GroupModel.searchGroups(name);
  }

  async getGroupById(groupId) {
    return await GroupModel.getGroupById(groupId);
  }

  async getTrendingGroups(page, limit) {
    return await GroupModel.getTrendingGroups(page, limit);
  }

  async searchGroupsByTags(tags, limit, offset) {
    return await GroupModel.searchGroupsByTags(tags, limit, offset);
  }

  async getTagSuggestions(query) {
    return await GroupModel.getTagSuggestions(query);
  }

  // Add this method inside the GroupService class
async updateGroupAvatar(groupId, userId, newAvatar) {
  // Check if the user is an admin of the group
  const isAdmin = await groupMembersModel.isAdmin(groupId, userId);
  if (!isAdmin) {
    const error = new Error('Only admins can update the group avatar');
    error.status = 403;
    throw error;
  }

  // Update the avatar
  const updatedGroup = await GroupModel.updateAvatar(groupId, newAvatar);
  if (!updatedGroup) {
    const error = new Error('Group not found');
    error.status = 404;
    throw error;
  }

  return updatedGroup;
}
async updateGroupName(groupId, userId, newName) {
  const isAdmin = await groupMembersModel.isAdmin(groupId, userId);
  if (!isAdmin) {
    const error = new Error('Only admins can update the group name');
    error.status = 403;
    throw error;
  }
  const updatedGroup = await GroupModel.updateName(groupId, newName);
  if (!updatedGroup) {
    const error = new Error('Group not found');
    error.status = 404;
    throw error;
  }
  return updatedGroup;
}

async updateGroupDescription(groupId, userId, newDescription) {
  const isAdmin = await groupMembersModel.isAdmin(groupId, userId);
  if (!isAdmin) {
    const error = new Error('Only admins can update the group description');
    error.status = 403;
    throw error;
  }
  const updatedGroup = await GroupModel.updateDescription(groupId, newDescription);
  if (!updatedGroup) {
    const error = new Error('Group not found');
    error.status = 404;
    throw error;
  }
  return updatedGroup;
}

async updateGroupTags(groupId, userId, newTags) {
  const isAdmin = await groupMembersModel.isAdmin(groupId, userId);
  if (!isAdmin) {
    const error = new Error('Only admins can update the group tags');
    error.status = 403;
    throw error;
  }
  const updatedGroup = await GroupModel.updateTags(groupId, newTags);
  if (!updatedGroup) {
    const error = new Error('Group not found');
    error.status = 404;
    throw error;
  }
  return updatedGroup;
}

/**
 * Check if a user is a member of a group
 * @param {number} groupId - The group ID
 * @param {number} userId - The user ID
 * @returns {Promise<boolean>} - True if the user is a member, false otherwise
 */
async isMember(groupId, userId) {
  return await GroupModel.isMember(groupId, userId);
}

/**
 * Check if a user is an admin of a group
 * @param {number} groupId - The group ID
 * @param {number} userId - The user ID
 * @returns {Promise<boolean>} - True if the user is an admin, false otherwise
 */
async isAdmin(groupId, userId) {
  return await groupMembersModel.isAdmin(groupId, userId);
}

}

module.exports = new GroupService();
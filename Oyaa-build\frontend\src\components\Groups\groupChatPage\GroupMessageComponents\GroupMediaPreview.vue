<template>
  <div class="media-preview-container">
    <div
      class="media-preview-wrapper"
      @click="openGallery"
      @mouseenter="isHovered = true"
      @mouseleave="isHovered = false"
    >
      <!-- Image preview -->
      <div v-if="isImage" class="image-preview" :class="{ 'loading': isLoading }">
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner"></div>
        </div>
        <img
          v-show="!hasError"
          ref="imageRef"
          :src="media.thumbnailUrl || getOptimizedThumbnail(media.mediaUrl)"
          :alt="`Media shared by ${senderName || 'user'}`"
          class="media-image"
          :class="{ 'blur-up': isLoading }"
          @load="handleImageLoaded"
          @error="handleMediaError"
          loading="lazy"
        />
        <div v-if="hasError" class="error-placeholder">
          <i class="fas fa-exclamation-circle"></i>
          <span>Failed to load image</span>
        </div>
      </div>

      <!-- Video preview -->
      <div v-else-if="isVideo" class="video-preview">
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner"></div>
        </div>
        <div class="video-thumbnail-container">
          <img
            v-if="!hasError && (media.thumbnailUrl || getVideoThumbnail(media.mediaUrl))"
            ref="videoThumbRef"
            :src="media.thumbnailUrl || getVideoThumbnail(media.mediaUrl)"
            :alt="`Video shared by ${senderName || 'user'}`"
            class="media-image"
            :class="{ 'blur-up': isLoading }"
            @load="handleImageLoaded"
            @error="handleMediaError"
            loading="lazy"
          />
          <div v-else-if="!isLoading && !hasError" class="video-fallback">
            <i class="fas fa-video"></i>
          </div>
        </div>

        <!-- Video duration badge -->
        <div v-if="media.duration && !hasError" class="duration-badge">
          {{ formatDuration(media.duration) }}
        </div>

        <!-- Play button overlay -->
        <div v-if="!hasError && !isLoading" class="play-button-overlay">
          <div class="play-button">
            <i class="fas fa-play"></i>
          </div>
        </div>
      </div>

      <!-- Audio preview -->
      <div v-else-if="isAudio" class="audio-preview">
        <group-howler-audio-player
          :media-url="media.mediaUrl"
          :is-sender="false"
          :duration="media.duration || 0"
          :lazy-load="true"
          class="embedded-audio-player"
        />
      </div>

      <!-- Unsupported media type -->
      <div v-else class="unsupported-media">
        <i class="fas fa-file-alt"></i>
        <span>Unsupported media</span>
      </div>

      <!-- Hover overlay with action hints (not for audio) -->
      <div v-if="!isAudio" class="preview-overlay" :class="{ 'visible': isHovered || hasError }">
        <div v-if="hasError" class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <span>Failed to load</span>
        </div>
        <div v-else-if="isFirstClick" class="action-hint">
          <i class="fas fa-download"></i>
          <span v-if="isVideo">Load video preview</span>
          <span v-else>Load full image</span>
        </div>
        <div v-else class="action-hint">
          <i class="fas fa-expand"></i>
          <span v-if="isVideo">Play video</span>
          <span v-else>View full size</span>
        </div>
      </div>

      <!-- Loading state indicator -->
      <div v-if="!isFirstClick && !fullSizeLoaded && !hasError" class="loading-state-badge">
        <i class="fas fa-sync-alt fa-spin"></i>
        <span>Loading...</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue';
import GroupHowlerAudioPlayer from './GroupHowlerAudioPlayer.vue';

export default {
  name: 'GroupMediaPreview',
  components: {
    GroupHowlerAudioPlayer
  },
  props: {
    media: {
      type: Object,
      required: true,
    },
    messageId: {
      type: [String, Number],
      required: true,
    },
    groupId: {
      type: [String, Number],
      default: null
    },
    senderName: {
      type: String,
      default: null
    },
    lazyLoad: {
      type: Boolean,
      default: true
    }
  },
  emits: ['open-gallery'],
  setup(props, { emit }) {
    const isLoading = ref(true);
    const hasError = ref(false);
    const isHovered = ref(false);
    const imageRef = ref(null);
    const videoThumbRef = ref(null);
    const naturalAspectRatio = ref(16/9); // Default aspect ratio

    const isImage = computed(() => {
      return props.media.mediaType && props.media.mediaType.startsWith('image');
    });

    const isVideo = computed(() => {
      return props.media.mediaType && props.media.mediaType.startsWith('video');
    });

    const isAudio = computed(() => {
      return props.media.mediaType && props.media.mediaType.startsWith('audio');
    });

    // Track if this is the first click (for two-step loading)
    const isFirstClick = ref(true);
    const fullSizeLoaded = ref(false);
    const fullSizeImageUrl = ref('');

    // Get the full-size image URL (not the thumbnail)
    const getFullSizeImageUrl = (mediaUrl) => {
      if (!mediaUrl) return '';

      // For Cloudinary URLs, generate a high-quality version
      if (mediaUrl.includes('cloudinary.com')) {
        const urlParts = mediaUrl.split('/upload/');
        if (urlParts.length === 2) {
          return `${urlParts[0]}/upload/q_auto:good/${urlParts[1]}`;
        }
      }

      return mediaUrl;
    };

    // Get the full-size video thumbnail
    const getFullSizeVideoThumbnail = (mediaUrl) => {
      if (!mediaUrl) return null;

      if (mediaUrl.includes('cloudinary.com')) {
        if (isAudio.value) return null;

        const urlParts = mediaUrl.split('/upload/');
        if (urlParts.length === 2) {
          const fullPath = urlParts[1];
          const versionMatch = fullPath.match(/v(\d+)\//);
          const version = versionMatch ? versionMatch[1] : '';

          const idWithExt = fullPath.split('/').pop();
          const videoId = idWithExt.split('.')[0];

          return `${urlParts[0]}/video/upload/q_auto:good/${version ? 'v' + version + '/' : ''}${videoId}.jpg`;
        }
      }

      return null;
    };

    const openGallery = () => {
      if (hasError.value) return;

      // Don't open gallery for audio files - they play inline
      if (isAudio.value) return;

      // First click: Load the full-size image/video but don't open gallery yet
      if (isFirstClick.value) {
        isFirstClick.value = false;
        isLoading.value = true;

        // Preload the full-size image or video thumbnail
        if (isImage.value) {
          fullSizeImageUrl.value = getFullSizeImageUrl(props.media.mediaUrl);
          const img = new Image();
          img.onload = () => {
            isLoading.value = false;
            fullSizeLoaded.value = true;

            // Update the displayed image to the full-size version
            if (imageRef.value) {
              imageRef.value.src = fullSizeImageUrl.value;
            }
          };
          img.onerror = () => {
            isLoading.value = false;
            hasError.value = true;
          };
          img.src = fullSizeImageUrl.value;
        }
        else if (isVideo.value) {
          const fullSizeThumb = getFullSizeVideoThumbnail(props.media.mediaUrl);
          if (fullSizeThumb) {
            const img = new Image();
            img.onload = () => {
              isLoading.value = false;
              fullSizeLoaded.value = true;

              // Update the displayed thumbnail to the full-size version
              if (videoThumbRef.value) {
                videoThumbRef.value.src = fullSizeThumb;
              }
            };
            img.onerror = () => {
              isLoading.value = false;
              hasError.value = true;
            };
            img.src = fullSizeThumb;
          } else {
            isLoading.value = false;
            fullSizeLoaded.value = true;
          }
        }

        return; // Don't open gallery on first click
      }

      // Second click: Open the gallery with the full-size media
      emit('open-gallery', {
        media: {
          ...props.media,
          // Use the full-size URL if we've loaded it
          mediaUrl: fullSizeLoaded.value && isImage.value ?
            fullSizeImageUrl.value : props.media.mediaUrl
        },
        messageId: props.messageId
      });
    };

    const handleImageLoaded = async () => {
      isLoading.value = false;

      // Calculate and store the natural aspect ratio for responsive sizing
      await nextTick();
      if (isImage.value && imageRef.value) {
        const img = imageRef.value;
        if (img.naturalWidth && img.naturalHeight) {
          naturalAspectRatio.value = img.naturalWidth / img.naturalHeight;
        }
      } else if (isVideo.value && videoThumbRef.value) {
        const img = videoThumbRef.value;
        if (img.naturalWidth && img.naturalHeight) {
          naturalAspectRatio.value = img.naturalWidth / img.naturalHeight;
        }
      }
    };

    const handleMediaError = () => {
      isLoading.value = false;
      hasError.value = true;
    };

    // Format duration from seconds to MM:SS
    const formatDuration = (seconds) => {
      if (!seconds) return '00:00';
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    // Generate very lightweight thumbnail for images
    const getOptimizedThumbnail = (mediaUrl) => {
      if (!mediaUrl) return '';

      // Check if it's a Cloudinary URL and optimize if possible
      if (mediaUrl.includes('cloudinary.com')) {
        const urlParts = mediaUrl.split('/upload/');
        if (urlParts.length === 2) {
          // Create a tiny, low-quality placeholder for initial load
          // w_60 = width 60px, q_10 = quality 10%, e_blur:800 = heavy blur effect
          return `${urlParts[0]}/upload/w_60,h_60,c_fill,q_10,e_blur:800/${urlParts[1]}`;
        }
      }

      return mediaUrl;
    };

    // Generate very lightweight video thumbnail
    const getVideoThumbnail = (mediaUrl) => {
      if (!mediaUrl) return null;

      // Check if it's a Cloudinary URL
      if (mediaUrl.includes('cloudinary.com')) {
        // Don't try to generate thumbnails for audio files
        if (isAudio.value) {
          return null;
        }

        const urlParts = mediaUrl.split('/upload/');
        if (urlParts.length === 2) {
          // Extract the public ID and version from the URL
          const fullPath = urlParts[1];
          const versionMatch = fullPath.match(/v(\d+)\//);
          const version = versionMatch ? versionMatch[1] : '';

          // Extract the ID without the file extension
          const idWithExt = fullPath.split('/').pop();
          const videoId = idWithExt.split('.')[0];

          // For video files, create a tiny, low-quality thumbnail
          // w_60 = width 60px, q_10 = quality 10%, e_blur:800 = heavy blur effect
          return `${urlParts[0]}/video/upload/w_60,h_60,c_fill,q_10,e_blur:800/${version ? 'v' + version + '/' : ''}${videoId}.jpg`;
        }
      }

      return null;
    };

    // Check if image is already cached
    onMounted(() => {
      if (isImage.value) {
        const img = new Image();
        img.onload = () => {
          isLoading.value = false;
          // Store natural aspect ratio for responsive sizing
          if (img.naturalWidth && img.naturalHeight) {
            naturalAspectRatio.value = img.naturalWidth / img.naturalHeight;
          }
        };
        img.onerror = () => {
          handleMediaError();
        };

        // Use the thumbnail URL if available, otherwise generate an optimized one
        img.src = props.media.thumbnailUrl || getOptimizedThumbnail(props.media.mediaUrl);
      } else if (isVideo.value) {
        const img = new Image();
        img.onload = () => {
          isLoading.value = false;
          // Store natural aspect ratio for responsive sizing
          if (img.naturalWidth && img.naturalHeight) {
            naturalAspectRatio.value = img.naturalWidth / img.naturalHeight;
          }
        };
        img.onerror = () => {
          handleMediaError();
        };

        // Use the thumbnail URL if available, otherwise generate one
        const thumbnailUrl = props.media.thumbnailUrl || getVideoThumbnail(props.media.mediaUrl);
        if (thumbnailUrl) {
          img.src = thumbnailUrl;
        } else {
          // If no thumbnail is available, just mark as loaded
          isLoading.value = false;
        }
      } else if (isAudio.value) {
        // For audio, no need to load an image, just mark as loaded immediately
        isLoading.value = false;
      } else {
        // For other types, no need to load an image
        isLoading.value = false;
      }
    });

    return {
      isImage,
      isVideo,
      isAudio,
      openGallery,
      isLoading,
      hasError,
      isHovered,
      imageRef,
      videoThumbRef,
      naturalAspectRatio,
      handleImageLoaded,
      handleMediaError,
      getOptimizedThumbnail,
      getVideoThumbnail,
      formatDuration,
      isFirstClick,
      fullSizeLoaded,
      fullSizeImageUrl,
      getFullSizeImageUrl,
      getFullSizeVideoThumbnail
    };
  }
};
</script>

<style scoped>
.media-preview-container {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 8px;
  background-color: #1a1a1a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
}

.media-preview-wrapper {
  position: relative;
  width: 100%;
  cursor: pointer;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.media-preview-wrapper:hover {
  transform: translateY(-2px);
}

/* Image preview styles */
.image-preview {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #121212;
  /* Default max-height that can be overridden by natural aspect ratio */
  max-height: 400px;
  min-height: 100px;
}

/* Maintain aspect ratio but limit maximum height */
.media-image {
  width: 40vw;
  max-width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: contain;
  display: block;
  transition: transform 0.3s ease;
}

.media-preview-wrapper:hover .media-image {
  transform: scale(1.03);
}

/* Video preview styles */
.video-preview {
  width: 100%;
  position: relative;
  background-color: #121212;
  display: flex;
  align-items: center;
  justify-content: center;
  max-height: 400px;
  min-height: 100px;
}

.video-thumbnail-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-fallback {
  width: 100%;
  height: 100%;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a1a1a;
  color: #aaa;
  font-size: 2rem;
}

.duration-badge {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 2;
}

.play-button-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 1;
}

.media-preview-wrapper:hover .play-button-overlay {
  opacity: 1;
}

.play-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.media-preview-wrapper:hover .play-button {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.8);
}

/* Audio preview styles */
.audio-preview {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: transparent;
  border-radius: 12px;
  width: 100%;
  max-width: 300px;
}

.embedded-audio-player {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
}

/* Loading state */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top-color: #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.blur-up {
  filter: blur(5px);
  transition: filter 0.5s ease;
  transform: scale(1.05); /* Slightly larger to cover edges */
}

/* Error state */
.error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 180px;
  background-color: #1a1a1a;
  color: #e74c3c;
  gap: 8px;
}

.error-placeholder i {
  font-size: 2rem;
}

.error-placeholder span {
  font-size: 0.9rem;
}

/* Unsupported media */
.unsupported-media {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background-color: #1a1a1a;
  color: #aaa;
  gap: 8px;
  min-height: 180px;
}

.unsupported-media i {
  font-size: 2rem;
}

/* Hover overlay */
.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 3;
}

.preview-overlay.visible {
  opacity: 1;
}

.loading-state-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 3;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.action-hint, .error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  gap: 8px;
}

.action-hint i, .error-message i {
  font-size: 1.5rem;
}

.error-message {
  color: #e74c3c;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .media-image {
    max-height: 350px;
  }

  .play-button {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .audio-preview {
    height: 70px;
    padding: 10px 12px;
  }

  .audio-waveform {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .media-image {
    max-height: 300px;
  }

  .play-button {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .audio-preview {
    height: 60px;
  }

  .waveform-bars {
    gap: 2px;
  }

  .waveform-bar {
    width: 2px;
  }

  .error-placeholder,
  .video-fallback,
  .unsupported-media {
    min-height: 150px;
  }
}
</style>
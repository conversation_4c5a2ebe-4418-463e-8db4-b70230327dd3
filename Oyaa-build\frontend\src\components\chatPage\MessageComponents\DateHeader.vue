<!-- frontend/src/components/chatPage/DateHeader.vue -->
<template>
    <div class="date-header">
      <span>{{ formattedDate }}</span>
    </div>
  </template>
  
  <script setup>
  import { computed } from 'vue';
  import { format, isToday, isYesterday } from 'date-fns';
  
  const props = defineProps({
    date: { type: [String, Date], required: true },
  });
  
  const formattedDate = computed(() => {
    const dateObj = new Date(props.date);
    if (isToday(dateObj)) return 'Today';
    if (isYesterday(dateObj)) return 'Yesterday';
    return format(dateObj, 'MMM d, yyyy');
  });
  </script>
  
  <style scoped>
  .date-header {
    text-align: center;
    margin: 1rem 0;
    font-size: 0.85rem;
    color: #72767d;
  }
  </style>
  
const express = require('express');
const authController = require('../../controllers/User/authController');
const router = express.Router();

// Legacy authentication routes (kept for backward compatibility)
router.post('/register', authController.register);
router.post('/login', authController.login);

// Firebase authentication routes
router.post('/firebase-register', authController.firebaseRegister);
router.post('/firebase-login', authController.firebaseLogin);
router.get('/check-username/:username', authController.checkUsername);
router.post('/update-username', authController.protect, authController.updateUsername);

// User info route - protected by auth middleware
router.get('/user-info', authController.protect, authController.getUserInfo);

module.exports = router;
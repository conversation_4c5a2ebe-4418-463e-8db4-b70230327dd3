<template>
  <div class="reply-preview">
    <span class="reply-text">
      <strong>Replying to {{ replyingMessage.sender_name || 'Unknown' }}:</strong>
      "{{ replyingMessage.message }}"
    </span>
    <button class="cancel-reply" @click="cancelReply" aria-label="Cancel Reply">
      <i class="fas fa-times"></i>
    </button>
  </div>
</template>

<script setup>
const props = defineProps({
  replyingMessage: { type: Object, required: true },
});

const emit = defineEmits(['cancel']);

function cancelReply() {
  emit('cancel');
}
</script>

<style scoped>
.reply-preview {
  background-color: #2f3136;
  border-left: 4px solid #3498db;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.reply-text {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Limits the text to 2 lines */
  line-clamp: 2; /* Standard property for compatibility */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 85%;
  font-size: 0.9rem;
}

.cancel-reply {
  background: transparent;
  border: none;
  color: #dcddde;
  cursor: pointer;
  font-size: 1rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.cancel-reply:hover {
  color: #ffffff;
}

.cancel-reply i {
  pointer-events: none;
}
</style>
<template>
  <transition name="dialog">
    <div v-if="show" class="info-dialog-backdrop" @click="$emit('close')">
      <div class="info-dialog" @click.stop>
        <div class="info-dialog-header">
          <h3>How to Find Groups by Interest</h3>
          <p>Discover groups that share your interests</p>
          <button class="close-button" @click="$emit('close')" aria-label="Close dialog">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="info-dialog-content">
          <div class="info-step">
            <div class="step-number">1</div>
            <div>
              <h4>Add your interests</h4>
              <p>Type any interest and press Enter, or select from suggestions. Add as many as you want.</p>
            </div>
          </div>
          <div class="info-step">
            <div class="step-number">2</div>
            <div>
              <h4>Search for groups</h4>
              <p>Click the Search button to find groups that share your interests.</p>
            </div>
          </div>
          <div class="info-step">
            <div class="step-number">3</div>
            <div>
              <h4>Join groups</h4>
              <p>Send join requests to groups you'd like to be part of.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style scoped>
.info-dialog-backdrop {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 1rem;
}

.info-dialog {
  background-color: var(--bg-tertiary);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

.info-dialog-header {
  padding: 1.5rem 1.5rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.info-dialog-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.info-dialog-header p {
  margin: 0.5rem 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.close-button {
  position: absolute;
  top: 1.25rem;
  right: 1.25rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.close-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  transform: scale(1.05);
}

.info-dialog-content {
  padding: 1.5rem;
}

.info-step {
  margin-bottom: 1.75rem;
  display: flex;
  gap: 1rem;
}

.info-step:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--accent-primary);
  flex-shrink: 0;
}

.info-step h4 {
  margin: 0 0 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.info-step p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.dialog-enter-active, .dialog-leave-active {
  transition: all 0.3s ease;
}

.dialog-enter-from, .dialog-leave-to {
  opacity: 0;
}

.dialog-enter-from .info-dialog, .dialog-leave-to .info-dialog {
  transform: scale(0.9);
}
</style>
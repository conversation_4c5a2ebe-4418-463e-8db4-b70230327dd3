// frontend/src/utils/authHandler.js
import { auth } from '../config/firebase.config';
import { onAuthStateChanged } from 'firebase/auth';
import store from '../store';
import router from '../router';

/**
 * Initialize the authentication handler
 * This sets up a listener for authentication state changes
 */
export const initAuthHandler = () => {
  // Set up the auth state listener
  onAuthStateChanged(auth, async (user) => {
    try {
      if (user) {
        // User is signed in
        console.log('Auth state changed: User is signed in', user.uid);

        // Get the ID token
        const idToken = await user.getIdToken(true);

        // Store the token in localStorage
        localStorage.setItem('token', idToken);

        // Check if this is an email/password sign-up
        const isEmailPasswordSignUp = localStorage.getItem('emailPasswordSignUp') === 'true';

        if (isEmailPasswordSignUp) {
          console.log('This is an email/password sign-up, skipping username selection');

          // Remove the flag
          localStorage.removeItem('emailPasswordSignUp');

          // We'll handle the backend registration in the register action
          // Just update the store with the Firebase user for now
          store.commit('auth/LOGIN_SUCCESS', {
            user: {
              firebaseUid: user.uid,
              username: user.displayName || user.email.split('@')[0],
              email: user.email,
              avatar: user.photoURL
            },
            token: idToken,
            firebaseUser: user
          });

          // Force a refresh of the auth state
          store.commit('auth/REFRESH_AUTH_STATE');

          return; // Exit early for email/password sign-up
        }

        try {
          // Get user info from backend
          const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

          // First, try to login with the Firebase token
          try {
            console.log('Attempting to login with Firebase token');
            const loginResponse = await fetch(`${API_BASE_URL}/api/auth/firebase-login`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${idToken}`
              },
              body: JSON.stringify({})
            });

            if (loginResponse.ok) {
              const userData = await loginResponse.json();
              console.log('User data from backend:', userData);

              // Update the store with both Firebase UID and database user ID
              store.commit('auth/LOGIN_SUCCESS', {
                user: {
                  id: userData.user.id, // Numeric database ID
                  firebaseUid: user.uid, // Firebase UID
                  username: userData.user.username || user.displayName || user.email.split('@')[0],
                  email: userData.user.email || user.email,
                  avatar: userData.user.avatar || user.photoURL
                },
                token: idToken,
                firebaseUser: user
              });

              // Force a refresh of the auth state
              store.commit('auth/REFRESH_AUTH_STATE');

              // Check if we're on an auth page and redirect to dashboard if needed
              const currentRoute = router.currentRoute.value;
              if (currentRoute.path === '/signin' || currentRoute.path === '/signup') {
                console.log('Redirecting to dashboard from auth page');
                router.push('/dashboard');
              }

              return; // Exit early if login was successful
            } else if (loginResponse.status === 403) {
              // User needs to select a username
              console.log('User needs to select a username');
              const responseData = await loginResponse.json();

              if (responseData.needsRegistration) {
                // Set the needs username flag
                store.commit('auth/SET_FIREBASE_USER', user);
                store.commit('auth/SET_NEEDS_USERNAME', {
                  firebaseUser: user,
                  isNewUser: responseData.isNewUser || false
                });

                console.log('User needs username selection, isNewUser:', responseData.isNewUser || false);

                // Redirect to signin for username selection
                if (router.currentRoute.value.path !== '/signin') {
                  router.push('/signin');
                }

                return; // Exit early
              }
            }
          } catch (loginError) {
            console.error('Error during Firebase login:', loginError);
          }

          // If login failed, try to register the user
          try {
            console.log('Attempting to register user with Firebase token');
            const registerResponse = await fetch(`${API_BASE_URL}/api/auth/firebase-register`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${idToken}`
              },
              body: JSON.stringify({
                // Don't provide a username, force the backend to require one
                avatar: user.photoURL
              })
            });

            if (registerResponse.ok) {
              const registerData = await registerResponse.json();
              console.log('User registered successfully:', registerData);

              // Update the store with the registered user data
              store.commit('auth/LOGIN_SUCCESS', {
                user: {
                  id: registerData.user.id,
                  firebaseUid: user.uid,
                  username: registerData.user.username,
                  handle: registerData.user.handle || registerData.user.username,
                  email: registerData.user.email || user.email,
                  avatar: registerData.user.avatar || user.photoURL
                },
                token: idToken,
                firebaseUser: user
              });

              // Force a refresh of the auth state
              store.commit('auth/REFRESH_AUTH_STATE');

              // Check if we're on an auth page and redirect to dashboard if needed
              const currentRoute = router.currentRoute.value;
              if (currentRoute.path === '/signin' || currentRoute.path === '/signup') {
                console.log('Redirecting to dashboard from auth page');
                router.push('/dashboard');
              }

              return; // Exit early if registration was successful
            } else if (registerResponse.status === 400) {
              // Check if the error is due to the user needing to select a username
              const errorData = await registerResponse.json();

              if (errorData.message && errorData.message.includes('Email already exists')) {
                console.log('Email already exists, user needs to select a username');

                // Set the needs username flag
                store.commit('auth/SET_FIREBASE_USER', user);
                store.commit('auth/SET_NEEDS_USERNAME', {
                  firebaseUser: user,
                  isNewUser: errorData.isNewUser || false
                });

                console.log('Email exists but needs username, isNewUser:', errorData.isNewUser || false);

                // Redirect to signin for username selection
                if (router.currentRoute.value.path !== '/signin') {
                  router.push('/signin');
                }

                return; // Exit early
              }
            }
          } catch (registerError) {
            console.error('Error during user registration:', registerError);
          }

          // If both login and registration failed, try to get user info directly
          try {
            console.log('Attempting to get user info directly');
            const userInfoResponse = await fetch(`${API_BASE_URL}/api/auth/user-info`, {
              headers: {
                'Authorization': `Bearer ${idToken}`
              }
            });

            if (userInfoResponse.ok) {
              const userInfo = await userInfoResponse.json();
              console.log('User info retrieved successfully:', userInfo);

              // Update the store with the user info
              store.commit('auth/LOGIN_SUCCESS', {
                user: {
                  id: userInfo.user.id,
                  firebaseUid: user.uid,
                  username: userInfo.user.username,
                  handle: userInfo.user.handle || userInfo.user.username,
                  email: userInfo.user.email || user.email,
                  avatar: userInfo.user.avatar || user.photoURL
                },
                token: idToken,
                firebaseUser: user
              });

              // Force a refresh of the auth state
              store.commit('auth/REFRESH_AUTH_STATE');

              // Check if we're on an auth page and redirect to dashboard if needed
              const currentRoute = router.currentRoute.value;
              if (currentRoute.path === '/signin' || currentRoute.path === '/signup') {
                console.log('Redirecting to dashboard from auth page');
                router.push('/dashboard');
              }

              return; // Exit early if user info was retrieved successfully
            }
          } catch (userInfoError) {
            console.error('Error getting user info directly:', userInfoError);
          }
        } catch (error) {
          console.error('All backend authentication attempts failed:', error);
        }

        // If all backend authentication attempts failed, fallback to Firebase data only
        console.warn('All backend authentication attempts failed, using Firebase data only');
        store.commit('auth/LOGIN_SUCCESS', {
          user: {
            firebaseUid: user.uid,
            username: user.displayName || user.email.split('@')[0],
            handle: user.displayName || user.email.split('@')[0],
            email: user.email,
            avatar: user.photoURL
          },
          token: idToken,
          firebaseUser: user
        });

        // Force a refresh of the auth state
        store.commit('auth/REFRESH_AUTH_STATE');

        // Check if we need to redirect
        const currentRoute = router.currentRoute.value;
        if (currentRoute.path === '/signin' || currentRoute.path === '/signup') {
          console.log('Redirecting to dashboard from auth page (fallback)');
          router.push('/dashboard');
        }
      } else {
        // User is signed out
        console.log('Auth state changed: User is signed out');

        // Clear the token
        localStorage.removeItem('token');

        // Update the store
        store.commit('auth/LOGOUT');

        // Check if we're on a protected page and redirect to signin if needed
        const currentRoute = router.currentRoute.value;
        if (currentRoute.meta.requiresAuth) {
          console.log('Redirecting to signin from protected page');
          router.push('/signin');
        }
      }
    } catch (error) {
      console.error('Error in auth state change handler:', error);
    }
  });
};

/**
 * Force a redirect to the dashboard
 * This is used when we know the user is authenticated but the automatic redirect didn't work
 */
export const forceDashboardRedirect = () => {
  const currentUser = auth.currentUser;
  if (currentUser) {
    console.log('Force redirecting to dashboard');
    router.push('/dashboard');
    return true;
  }
  return false;
};

<template>
    <div class="textarea-wrapper" :class="{ 'is-expanded': isExpanded }">
      <textarea
        ref="textarea"
        class="message-input"
        :class="{ 'is-expanded': isExpanded }"
        rows="1"
        :placeholder="placeholder"
        :value="modelValue"
        @input="onInput"
      ></textarea>
      <div v-if="isExpanded" class="char-count" :class="{ 'near-limit': isNearCharLimit }">
        {{ modelValue.length }}/2000
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, onMounted, watch } from 'vue'
  
  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['update:modelValue', 'input'])
  
  const textarea = ref(null)
  const isExpanded = ref(false)
  
  const onInput = (event) => {
    const value = event.target.value
    emit('update:modelValue', value)
    emit('input', event)
    adjustHeight()
  }
  
  const adjustHeight = () => {
    if (!textarea.value) return
    const el = textarea.value
    el.style.height = 'auto'
    const maxHeight = 84 // Maximum height (approx. 3 lines)
    let newHeight = el.scrollHeight
    if (newHeight > maxHeight) {
      newHeight = maxHeight
      el.style.overflowY = 'auto'
    } else {
      el.style.overflowY = 'hidden'
    }
    el.style.height = `${newHeight}px`
    isExpanded.value = newHeight > 44
  }
  
  onMounted(() => {
    adjustHeight()
  })
  
  // Watch for changes to the modelValue – when cleared, reset height and expansion
  watch(
    () => props.modelValue,
    (newValue, oldValue) => {
      if (newValue === '') {
        if (textarea.value) {
          textarea.value.style.height = 'auto'
          textarea.value.style.overflowY = 'hidden'
        }
        isExpanded.value = false
      } else {
        adjustHeight()
      }
    }
  )
  
  const isNearCharLimit = computed(() => props.modelValue.length > 1900)
  
  // Add a focus method to be called from the parent
  const focus = () => {
    if (textarea.value) {
      textarea.value.focus();
    }
  };
  
  // Expose the expansion state and focus method to the parent component
  defineExpose({ isExpanded, focus })
  </script>
  
  <style scoped>
  .textarea-wrapper {
    flex: 1;
    position: relative;
  }
  .textarea-wrapper.is-expanded {
    margin-bottom: 16px;
  }
  .message-input {
    width: 98%;
    background: transparent;
    border: none;
    color: #ffffff;
    font-size: 1rem;
    outline: none;
    padding: 12px 0;
    resize: none;
    line-height: 1.25;
    max-height: 84px;
    overflow-y: auto;
    font-family: inherit;
    transition: all 0.3s ease;
  }
  .message-input.is-expanded {
    background-color: #383a40;
    border-radius: 8px;
    padding: 12px;
  }
  .message-input::placeholder {
    color: #949ba4;
    font-style: italic;
    font-weight: 300;
  }
  .message-input::-webkit-scrollbar {
    width: 8px;
  }
  .message-input::-webkit-scrollbar-track {
    background: transparent;
  }
  .message-input::-webkit-scrollbar-thumb {
    background-color: #202225;
    border-radius: 4px;
  }
  .char-count {
    position: absolute;
    right: 8px;
    bottom: -20px;
    font-size: 0.75rem;
    color: #949ba4;
  }
  .char-count.near-limit {
    color: #faa61a;
  }
  </style>
  
<template>
  <div ref="scrollContainer" class="scroll-container">
    <GlobalNotificationCenter v-if="$store.getters['auth/isAuthenticated']" />
    <router-view />
  </div>
</template>

<script setup>
import { ref, onMounted, provide, watch } from 'vue';
import { useStore } from 'vuex';
import GlobalNotificationCenter from '@/components/GlobalNotificationCenter.vue';
import { initSSE, closeSSE } from '@/services/sseService';

const scrollContainer = ref(null);
const store = useStore();

// Create a method to show toasts that uses the Vuex store
const showToast = (message, type = 'info', duration = 3000) => {
  store.dispatch('app/showToast', { message, type, duration });
};

// Provide the showToast method to all components
provide('showToast', showToast);

// Watch for authentication state changes
watch(
  () => store.getters['auth/isAuthenticated'],
  (isAuthenticated) => {
    if (isAuthenticated) {
      // Check if we have a numeric user ID (which means the user is fully registered in the backend)
      const hasNumericId = store.getters['auth/user']?.id;

      if (hasNumericId) {
        console.log('User has numeric ID, initializing friend requests and SSE');
        // Initialize the friend requests store
        store.dispatch('friendRequests/fetchReceivedRequests');
        store.dispatch('friendRequests/fetchSentRequests');

        // Initialize SSE connection
        console.log('Initializing SSE connection');
        initSSE().catch(error => {
          console.error('Failed to initialize SSE connection:', error);
        });
      } else {
        console.log('User authenticated but no numeric ID yet, delaying friend requests and SSE');
        // We'll initialize these in the userId watcher below
      }
    } else {
      // Close SSE connection
      console.log('Closing SSE connection');
      closeSSE();
    }
  },
  { immediate: true }
);

// Watch for user ID changes to handle the case where the user is authenticated
// but the numeric ID is not available yet (during registration)
watch(
  () => store.getters['auth/user']?.id,
  (numericId) => {
    if (numericId && store.getters['auth/isAuthenticated']) {
      console.log('Numeric user ID now available:', numericId);
      // Initialize the friend requests store
      store.dispatch('friendRequests/fetchReceivedRequests');
      store.dispatch('friendRequests/fetchSentRequests');

      // Initialize SSE connection if not already connected
      console.log('Initializing SSE connection after numeric ID available');
      initSSE().catch(error => {
        console.error('Failed to initialize SSE connection:', error);
      });
    }
  }
);

onMounted(async () => {
  if (scrollContainer.value && window.OverlayScrollbars) {
    window.OverlayScrollbars(scrollContainer.value, {
      scrollbars: { autoHide: 'move' }
    });
  }

  // Add global method to Vue instance
  window.$showToast = showToast;

  // Initialize auth state
  console.log('Initializing auth state on mount');
  await store.dispatch('auth/initializeAuth');

  // Force a refresh of the auth state
  store.commit('auth/REFRESH_AUTH_STATE');

  // Initialize connections if user is authenticated
  if (store.getters['auth/isAuthenticated']) {
    console.log('User is authenticated, initializing connections');

    // Get the current user
    const currentUser = store.getters['auth/user'];
    console.log('Current user:', currentUser);

    // Force a refresh of the auth state
    store.commit('auth/REFRESH_AUTH_STATE');

    // Check if we have a numeric user ID
    if (currentUser?.id) {
      console.log('User has numeric ID, initializing friend requests and SSE');

      // Initialize the friend requests store
      store.dispatch('friendRequests/fetchReceivedRequests');
      store.dispatch('friendRequests/fetchSentRequests');

      // Initialize SSE connection
      console.log('Initializing SSE connection on mount');
      initSSE().catch(error => {
        console.error('Failed to initialize SSE connection on mount:', error);
      });
    } else {
      console.log('User authenticated but no numeric ID yet, delaying friend requests and SSE');
      // The watchers will handle initialization when the numeric ID becomes available
    }
  } else {
    console.log('User is not authenticated on mount');

    // Check if there's a token in localStorage
    const token = localStorage.getItem('token');
    if (token) {
      console.log('Token found in localStorage, trying to initialize auth again');

      // Try to initialize auth with the token
      store.dispatch('auth/initializeAuth').then(() => {
        console.log('Auth initialized from token');

        // Force a refresh of the auth state
        store.commit('auth/REFRESH_AUTH_STATE');

        // Check if the user is authenticated now
        if (store.getters['auth/isAuthenticated']) {
          console.log('User is now authenticated, initializing connections');

          // Get the current user
          const currentUser = store.getters['auth/user'];

          // Check if we have a numeric user ID
          if (currentUser?.id) {
            console.log('User has numeric ID, initializing friend requests and SSE');

            // Initialize the friend requests store
            store.dispatch('friendRequests/fetchReceivedRequests');
            store.dispatch('friendRequests/fetchSentRequests');

            // Initialize SSE connection
            console.log('Initializing SSE connection after auth');
            initSSE().catch(error => {
              console.error('Failed to initialize SSE connection after auth:', error);
            });
          } else {
            console.log('User authenticated but no numeric ID yet, delaying friend requests and SSE');
            // The watchers will handle initialization when the numeric ID becomes available
          }
        }
      }).catch(error => {
        console.error('Failed to initialize auth from token:', error);
      });
    }
  }
});
</script>

<style scoped>
.scroll-container {
  height: 100vh;
  overflow: auto;
}
</style>
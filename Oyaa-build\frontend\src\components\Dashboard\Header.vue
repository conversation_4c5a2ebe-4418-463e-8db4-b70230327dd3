<template>
  <header class="header">
    <!-- User Info with Avatar and Username -->
    <div class="user-info">
      <img class="avatar" :src="avatarSrc" alt="User Avatar" />
      <span class="username">{{ user?.username || username }}</span>
    </div>
    <div class="header-menu">
      <FriendRequestButton :friendRequestsCount="friendRequestsCount" />
      <GroupRequestButton :groupRequestsCount="groupRequestsCount" />
      <div class="menu-dropdown-container">
        <button class="menu-dots" @click="toggleMenu" aria-label="Open menu">
          <MoreVertical class="menu-dots-icon" />
        </button>
        <div v-if="isOpen" class="menu-dropdown">
          <ul>
            <li @click="navigateTo('settings')">
              <span class="menu-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
              </span>
              Settings
            </li>
            <li @click="handleLogout">
              <span class="menu-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                  <polyline points="16 17 21 12 16 7"></polyline>
                  <line x1="21" y1="12" x2="9" y2="12"></line>
                </svg>
              </span>
              Logout
            </li>
          </ul>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { MoreVertical } from 'lucide-vue-next'
import FriendRequestButton from './FriendRequestButton.vue'
import GroupRequestButton from './GroupRequestButton.vue'
import { mapGetters, mapActions } from 'vuex'

export default {
  components: { 
    FriendRequestButton,
    GroupRequestButton,
    MoreVertical
  },
  props: {
    friendRequestsCount: {
      type: Number,
      default: 0,
    },
    groupRequestsCount: {
      type: Number,
      default: 0,
    },
    // Fallback username if none is available from the store
    username: {
      type: String,
      default: 'User'
    }
  },
  data() {
    return {
      isOpen: false,
    }
  },
  computed: {
    ...mapGetters('auth', ['user']),
    // Compute the avatar source URL: log the user and check for avatar property.
    avatarSrc() {
      
      const avatar = this.user && this.user.avatar ? this.user.avatar : '/Avatar/default.svg';
     
      return avatar;
    },
  },
  methods: {
    toggleMenu() {
      this.isOpen = !this.isOpen;
    },
    navigateTo(route) {
      this.$router.push(`/${route}`);
      this.isOpen = false;
    },
    async handleLogout() {
      try {
        await this.logout();
        this.$router.push('/signin');
      } catch (error) {
        console.error('Logout failed:', error);
      }
    },
    ...mapActions('auth', ['logout']),
    handleClickOutside(event) {
      if (this.isOpen && !this.$el.contains(event.target)) {
        this.isOpen = false;
      }
    },
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
   
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  watch: {
    user(newVal) {

    }
  }
}
</script>

<style scoped>
.header {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  backdrop-filter: blur(10px);
 
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 5;
  width: 100%;
  box-sizing: border-box;
  
}

/* User Info styling */
.user-info {
  display: flex;
  align-items: center;
  min-width: 0; /* Allow shrinking */
  overflow: hidden; /* Prevent overflow */
}

/* Updated Avatar styling using an <img> tag */
.avatar {
  width: 38px;
  height: 38px;
  border-radius: 10px;
  object-fit: cover;
  margin-right: 12px;
  flex-shrink: 0;
  border: 2px solid var(--accent-primary);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}

.username {
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.3px;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px; /* Limit width on all screens */
}

.header-menu {
  display: flex;
  align-items: center;
  gap: 16px;
}

.menu-dropdown-container {
  position: relative;
}

.menu-dots {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  border-radius: 8px;
  transition: all var(--transition-fast);
}

.menu-dots:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  transform: scale(1.05);
}

.menu-dots-icon {
  width: 20px;
  height: 20px;
}

.menu-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background-color: var(--bg-tertiary);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  min-width: 180px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  animation: fadeIn 0.2s ease;
  transform-origin: top right;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

.menu-dropdown ul {
  list-style: none;
  padding: 8px 0;
  margin: 0;
}

.menu-dropdown li {
  padding: 12px 16px;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  display: flex;
  align-items: center;
  font-weight: 500;
}

.menu-dropdown li:hover {
  background-color: var(--accent-tertiary);
}

.menu-icon {
  display: flex;
  align-items: center;
  margin-right: 12px;
  color: var(--text-secondary);
}

.menu-dropdown li:hover .menu-icon {
  color: var(--accent-primary);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .header {
    padding: 10px 12px;
  }
  
  .avatar {
    width: 32px;
    height: 32px;
    margin-right: 8px;
    border-radius: 8px;
  }
  
  .username {
    font-size: 14px;
    max-width: 100px;
  }
  
  .header-menu {
    gap: 8px;
  }
  
  .menu-dots {
    padding: 6px;
  }
  
  .menu-dots-icon {
    width: 18px;
    height: 18px;
  }
  
  .menu-dropdown {
    min-width: 160px;
  }
  
  .menu-dropdown li {
    padding: 10px 14px;
    font-size: 14px;
  }
}

/* Extra small screens */
@media (max-width: 320px) {
  .header {
    padding: 8px 10px;
  }
  
  .avatar {
    width: 28px;
    height: 28px;
    margin-right: 6px;
  }
  
  .username {
    font-size: 13px;
    max-width: 70px;
  }
  
  .header-menu {
    gap: 4px;
  }
  
  .menu-dots {
    padding: 4px;
  }
  
  .menu-dots-icon {
    width: 16px;
    height: 16px;
  }
}
</style>
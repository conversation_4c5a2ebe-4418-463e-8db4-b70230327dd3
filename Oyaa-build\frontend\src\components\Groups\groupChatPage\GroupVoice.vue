<template>
  <div class="voice-recorder" :class="{ 'is-recording': isRecording }">
    <!-- Visualization and timer area -->
    <div class="recorder-visualization">
      <div class="visualization-container">
        <div
          v-for="(bar, index) in visualizationBars"
          :key="index"
          class="visualization-bar"
          :style="{ height: `${bar}px` }"
        ></div>
      </div>
      <div class="timer" :class="{ 'recording': isRecording }">
        {{ formattedTime }}
      </div>
    </div>

    <!-- Controls area -->
    <div class="recorder-controls">
      <button
        class="control-btn cancel-btn"
        @click="cancelRecording"
        title="Cancel"
      >
        <XIcon size="18" />
      </button>

      <button
        class="control-btn record-btn"
        :class="{
          'recording': isRecording && !isPaused,
          'paused': isPaused
        }"
        @click="toggleRecording"
        :title="recordButtonTitle"
      >
        <MicIcon v-if="!isRecording" size="22" />
        <PlayIcon v-else-if="isPaused" size="22" />
        <PauseIcon v-else size="22" />
      </button>

      <button
        v-if="isRecording"
        class="control-btn stop-btn"
        @click="stopRecording"
        title="Stop"
      >
        <SquareIcon size="18" />
      </button>

      <button
        v-if="hasRecording"
        class="control-btn send-btn"
        @click="sendRecording"
        title="Send"
      >
        <SendIcon size="18" />
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import {
  Mic as MicIcon,
  Pause as PauseIcon,
  Play as PlayIcon,
  Send as SendIcon,
  X as XIcon,
  Square as SquareIcon,
} from "lucide-vue-next";

const emit = defineEmits(["sendVoiceNote", "cancel"]);

// Reactive variables
const isRecording = ref(false);
const isPaused = ref(false);
const recordingTime = ref(0);
const hasRecording = ref(false);
const mediaRecorder = ref(null);
const audioChunks = ref([]);
const visualizationBars = ref(Array(30).fill(3)); // Increased number of bars for better visualization
const animationId = ref(null);
const audioContext = ref(null);
const analyser = ref(null);
const dataArray = ref(null);

let timerInterval = null;
let startTime = 0;
let accumulatedTime = 0;

// Computed properties
const formattedTime = computed(() => {
  const minutes = Math.floor(recordingTime.value / 60);
  const seconds = recordingTime.value % 60;
  return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
});

const recordButtonTitle = computed(() => {
  if (isRecording.value) return isPaused.value ? "Resume" : "Pause";
  return "Record";
});

// Recording functions
const startRecording = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    mediaRecorder.value = new MediaRecorder(stream);
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.value.push(event.data);
      }
    };

    mediaRecorder.value.onstop = () => {
      if (audioContext.value) {
        audioContext.value.close().catch((err) =>
          console.error("Error closing audio context:", err)
        );
        audioContext.value = null;
        analyser.value = null;
        dataArray.value = null;
      }
    };

    // Set up audio analysis for visualization
    audioContext.value = new (window.AudioContext || window.webkitAudioContext)();
    analyser.value = audioContext.value.createAnalyser();
    const source = audioContext.value.createMediaStreamSource(stream);
    source.connect(analyser.value);
    analyser.value.fftSize = 256;
    dataArray.value = new Uint8Array(analyser.value.frequencyBinCount);

    // Start recording
    mediaRecorder.value.start(100); // Collect data every 100ms
    isRecording.value = true;
    isPaused.value = false;
    accumulatedTime = 0;
    recordingTime.value = 0;
    startTime = Date.now();
    timerInterval = setInterval(updateTimer, 1000);
  } catch (error) {
    console.error("Error accessing microphone:", error);
    alert("Please allow microphone access to record voice messages.");
  }
};

const pauseRecording = () => {
  if (mediaRecorder.value && mediaRecorder.value.state === "recording") {
    mediaRecorder.value.pause();
    isPaused.value = true;
    clearInterval(timerInterval);
    accumulatedTime += Math.floor((Date.now() - startTime) / 1000);
    recordingTime.value = accumulatedTime;
  }
};

const resumeRecording = () => {
  if (mediaRecorder.value && mediaRecorder.value.state === "paused") {
    mediaRecorder.value.resume();
    isPaused.value = false;
    startTime = Date.now();
    timerInterval = setInterval(updateTimer, 1000);
  }
};

const stopRecording = () => {
  if (mediaRecorder.value && mediaRecorder.value.state !== "inactive") {
    mediaRecorder.value.stop();
    mediaRecorder.value.stream.getTracks().forEach((track) => track.stop());
    isRecording.value = false;
    isPaused.value = false;
    hasRecording.value = true;
    clearInterval(timerInterval);
  }
};

const toggleRecording = () => {
  if (!isRecording.value) {
    hasRecording.value = false;
    audioChunks.value = [];
    startRecording();
  } else if (isPaused.value) {
    resumeRecording();
  } else {
    pauseRecording();
  }
};

const sendRecording = () => {
  if (hasRecording.value && audioChunks.value.length > 0) {
    const audioBlob = new Blob(audioChunks.value, { type: "audio/webm" });

    // Create a custom File object with duration metadata
    const audioFile = new File([audioBlob], "voice_note.webm", {
      type: "audio/webm",
      lastModified: Date.now()
    });

    // Store the duration as an integer (seconds) in a custom property
    // This will be accessible when the file is uploaded
    audioFile.duration = Math.round(recordingTime.value);

    emit("sendVoiceNote", audioFile);
    hasRecording.value = false;
    audioChunks.value = [];
    recordingTime.value = 0;
  }
};

const cancelRecording = () => {
  if (mediaRecorder.value && mediaRecorder.value.state !== "inactive") {
    mediaRecorder.value.stop();
    mediaRecorder.value.stream.getTracks().forEach((track) => track.stop());
  }
  isRecording.value = false;
  isPaused.value = false;
  hasRecording.value = false;
  audioChunks.value = [];
  recordingTime.value = 0;
  clearInterval(timerInterval);
  emit("cancel");
};

const updateTimer = () => {
  const currentTime = Math.floor((Date.now() - startTime) / 1000);
  recordingTime.value = accumulatedTime + currentTime;
};

// Visualization
const updateVisualization = () => {
  if (isRecording.value && !isPaused.value && analyser.value && dataArray.value) {
    analyser.value.getByteFrequencyData(dataArray.value);

    // Create a smoother visualization
    for (let i = 0; i < visualizationBars.value.length; i++) {
      const barIndex = Math.floor(
        i * (dataArray.value.length / visualizationBars.value.length)
      );
      const value = dataArray.value[barIndex];
      // Smooth transition with previous value
      const prevHeight = visualizationBars.value[i];
      const targetHeight = value ? Math.max(3, value / 4) : 3;
      visualizationBars.value[i] = prevHeight + (targetHeight - prevHeight) * 0.3;
    }
  } else if (isPaused.value) {
    // Subtle animation when paused
    visualizationBars.value = visualizationBars.value.map(val =>
      Math.max(2, Math.min(8, val * 0.98))
    );
  } else if (!isRecording.value) {
    // Idle state animation
    visualizationBars.value = visualizationBars.value.map((val, i) => {
      const phase = Date.now() / 1000 + i * 0.2;
      return 3 + Math.sin(phase) * 2;
    });
  }

  animationId.value = requestAnimationFrame(updateVisualization);
};

// Lifecycle hooks
onMounted(() => {
  updateVisualization();
});

onUnmounted(() => {
  if (mediaRecorder.value && mediaRecorder.value.state !== "inactive") {
    mediaRecorder.value.stop();
    mediaRecorder.value.stream.getTracks().forEach((track) => track.stop());
  }
  if (audioContext.value) {
    audioContext.value.close().catch((err) =>
      console.error("Error closing audio context:", err)
    );
  }
  if (animationId.value) {
    cancelAnimationFrame(animationId.value);
  }
  clearInterval(timerInterval);
});
</script>

<style scoped>
.voice-recorder {
  width: 100%;
  background: rgba(30, 33, 43, 0.7);
  border-radius: 16px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.voice-recorder.is-recording {
  background: rgba(40, 43, 53, 0.85);
  border-color: rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.recorder-visualization {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  height: 60px;
  padding: 0 8px;
}

.visualization-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2px;
  height: 40px;
  padding: 0 8px;
  background: rgba(20, 22, 30, 0.4);
  border-radius: 12px;
  overflow: hidden;
}

.visualization-bar {
  flex: 1;
  min-width: 2px;
  max-width: 4px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 2px;
  transition: height 0.1s ease-out;
}

.is-recording .visualization-bar {
  background: linear-gradient(to top, rgba(52, 152, 219, 0.6), rgba(52, 152, 219, 1));
}

.timer {
  font-family: 'SF Mono', SFMono-Regular, ui-monospace, monospace;
  font-size: 1.1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  min-width: 60px;
  text-align: center;
  padding: 6px 12px;
  background: rgba(20, 22, 30, 0.4);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.timer.recording {
  color: #3498db;
  background: rgba(52, 152, 219, 0.15);
  box-shadow: 0 0 0 1px rgba(52, 152, 219, 0.3);
}

.recorder-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 8px 0;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.08);
  border: none;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.control-btn:active {
  transform: translateY(0);
}

.cancel-btn, .stop-btn {
  width: 40px;
  height: 40px;
}

.record-btn {
  width: 56px;
  height: 56px;
  background: rgba(52, 152, 219, 0.2);
  border: 2px solid rgba(52, 152, 219, 0.5);
  color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.record-btn:hover {
  background: rgba(52, 152, 219, 0.3);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.record-btn.recording {
  background: rgba(231, 76, 60, 0.2);
  border-color: rgba(231, 76, 60, 0.5);
  color: #e74c3c;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);
  animation: pulse 2s infinite;
}

.record-btn.recording:hover {
  background: rgba(231, 76, 60, 0.3);
}

.record-btn.paused {
  background: rgba(243, 156, 18, 0.2);
  border-color: rgba(243, 156, 18, 0.5);
  color: #f39c12;
  box-shadow: 0 2px 8px rgba(243, 156, 18, 0.2);
  animation: none;
}

.record-btn.paused:hover {
  background: rgba(243, 156, 18, 0.3);
}

.cancel-btn {
  color: rgba(255, 255, 255, 0.6);
}

.cancel-btn:hover {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.15);
}

.stop-btn {
  color: rgba(255, 255, 255, 0.7);
}

.stop-btn:hover {
  color: #f39c12;
  background: rgba(243, 156, 18, 0.15);
}

.send-btn {
  width: 40px;
  height: 40px;
  background: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.send-btn:hover {
  background: rgba(46, 204, 113, 0.3);
  box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .voice-recorder {
    padding: 12px;
    gap: 12px;
  }

  .recorder-visualization {
    height: 50px;
  }

  .visualization-container {
    height: 36px;
  }

  .timer {
    font-size: 1rem;
    min-width: 54px;
    padding: 4px 8px;
  }

  .recorder-controls {
    gap: 12px;
  }

  .cancel-btn, .stop-btn, .send-btn {
    width: 36px;
    height: 36px;
  }

  .record-btn {
    width: 48px;
    height: 48px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .voice-recorder {
    background: rgba(25, 28, 38, 0.8);
  }

  .voice-recorder.is-recording {
    background: rgba(35, 38, 48, 0.9);
  }
}
</style>
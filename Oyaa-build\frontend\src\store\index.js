import { createStore } from 'vuex';
import authModule from './authSlice'; // Import your auth module
import appModule from './appSlice'; // Import the new app module
import chat from './chat';
import tempChat from './groupTempChat';
import groupChat from './GroupChat';
import LocalChat from './LocalChat';
import friendRequests from './modules/friendRequests'; // Import the friend requests module

const store = createStore({
  modules: {
    auth: authModule, // Register the auth module
    app: appModule,   // Register the app module
    chat,
    tempChat,
    groupChat,
    LocalChat,
    friendRequests, // Register the friend requests module
  },
});

export default store;

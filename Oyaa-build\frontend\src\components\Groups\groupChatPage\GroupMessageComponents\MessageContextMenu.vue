<template>
  <Transition name="fade">
    <div 
      v-if="visible" 
      class="context-menu" 
      :style="positionStyle" 
      @click.stop
      role="menu"
      aria-label="Message options"
      tabindex="0"
      @keydown.esc="$emit('close')"
    >
      <div class="menu-group">
        <button 
          class="menu-item" 
          @click="handleReply" 
          v-if="!isDeleted"
          role="menuitem"
        >
          <i class="icon" aria-hidden="true">
            <ReplyIcon class="icon-size" />
          </i>
          <span>Reply</span>
        </button>
        <button 
          class="menu-item" 
          @click="handleCopy"
          role="menuitem"
        >
          <i class="icon" aria-hidden="true">
            <CopyIcon class="icon-size" />
          </i>
          <span>Copy</span>
        </button>
        <button 
          v-if="hasMedia && !isDeleted" 
          class="menu-item" 
          @click="handleOpenGallery"
          role="menuitem"
        >
          <i class="icon" aria-hidden="true">
            <ImageIcon class="icon-size" />
          </i>
          <span>View Media</span>
        </button>
      </div>

      <!-- Owner actions group -->
      <div class="menu-group" v-if="isCurrentUser">
        <button 
          v-if="!isDeleted" 
          class="menu-item" 
          @click="handleEdit"
          role="menuitem"
        >
          <i class="icon" aria-hidden="true">
            <EditIcon class="icon-size" />
          </i>
          <span>Edit</span>
        </button>
        <button 
          class="menu-item danger" 
          @click="handleDelete"
          role="menuitem"
        >
          <i class="icon" aria-hidden="true">
            <TrashIcon class="icon-size" />
          </i>
          <span>Delete</span>
        </button>
      </div>

      <!-- Other user actions group -->
      <div class="menu-group" v-else>
        <button 
          v-if="!isDeleted" 
          class="menu-item" 
          @click="handleReport"
          role="menuitem"
        >
          <i class="icon" aria-hidden="true">
            <FlagIcon class="icon-size" />
          </i>
          <span>Report</span>
        </button>
        <button 
          class="menu-item" 
          @click="handleOpenProfile"
          role="menuitem"
        >
          <i class="icon" aria-hidden="true">
            <UserIcon class="icon-size" />
          </i>
          <span>View Profile</span>
        </button>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue';
import { 
  Reply as ReplyIcon, 
  Copy as CopyIcon, 
  Image as ImageIcon, 
  Edit as EditIcon, 
  Trash as TrashIcon, 
  Flag as FlagIcon, 
  User as UserIcon 
} from 'lucide-vue-next';

const props = defineProps({
  visible: Boolean,
  message: Object,
  position: Object,
  currentUser: Object
});

const emit = defineEmits(['reply', 'copy', 'edit', 'delete', 'report', 'open-gallery', 'open-user-profile', 'close']);

// Handle clicks outside the menu to close it
const handleClickOutside = (event) => {
  if (props.visible) {
    emit('close');
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  // Focus the menu when it appears for keyboard navigation
  if (props.visible) {
    document.querySelector('.context-menu')?.focus();
  }
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

const positionStyle = computed(() => {
  if (!props.position || typeof window === 'undefined') return {};

  const menuWidth = 180; // Reduced width for mobile
  const menuHeight = calculateMenuHeight();

  // Calculate available space in each direction
  const spaceRight = window.innerWidth - props.position.x;
  const spaceBottom = window.innerHeight - props.position.y;
  
  // Default position (right and down from click)
  let x = props.position.x;
  let y = props.position.y;
  
  // Adjust horizontal position if needed
  if (spaceRight < menuWidth) {
    x = props.position.x - menuWidth;
  }
  
  // Adjust vertical position if needed
  if (spaceBottom < menuHeight) {
    y = window.innerHeight - menuHeight - 10;
  }
  
  // Ensure menu stays within viewport bounds
  x = Math.max(5, Math.min(window.innerWidth - menuWidth - 5, x));
  y = Math.max(5, Math.min(window.innerHeight - menuHeight - 5, y));

  return {
    top: `${y}px`,
    left: `${x}px`
  };
});

const isCurrentUser = computed(() => {
  return props.message?.sender_id === props.currentUser?.id;
});

const hasMedia = computed(() => {
  return props.message?.media && Array.isArray(props.message.media) && props.message.media.length > 0;
});

const isDeleted = computed(() => {
  return props.message?.deleted === true;
});

const calculateMenuHeight = () => {
  // Count visible items
  let itemCount = 1; // Copy is always visible
  if (!isDeleted.value) itemCount++; // Reply
  if (hasMedia.value && !isDeleted.value) itemCount++; // View Media
  
  // Add group-specific items
  if (isCurrentUser.value) {
    if (!isDeleted.value) itemCount++; // Edit
    itemCount++; // Delete
  } else {
    if (!isDeleted.value) itemCount++; // Report
    itemCount++; // View Profile
  }
  
  // Calculate groups
  const groupCount = isCurrentUser.value ? 2 : 2; // Always at least 2 groups
  
  // Calculate height - smaller for mobile
  const itemHeight = 36; // Reduced height per item
  const groupMargin = 4; // Reduced margin between groups
  const menuPadding = 6; // Reduced padding
  
  return (itemCount * itemHeight) + ((groupCount - 1) * groupMargin) + (menuPadding * 2);
};

const handleReply = () => {
  if (isDeleted.value) return;
  emit('reply', props.message);
  emit('close');
};

const handleCopy = () => {
  const content = isDeleted.value ? "This message was deleted" : (props.message?.content || props.message?.message);
  if (content) {
    navigator.clipboard.writeText(content)
      .then(() => console.log('Message content copied'))
      .catch(err => console.error('Failed to copy message: ', err));
  }
  emit('close');
};

const handleEdit = () => {
  if (isDeleted.value || !isCurrentUser.value) return;
  emit('edit', props.message);
  emit('close');
};

const handleDelete = () => {
  if (!isCurrentUser.value) return;
  emit('delete', props.message);
  emit('close');
};

const handleReport = () => {
  if (isDeleted.value || isCurrentUser.value) return;
  emit('report', props.message);
  emit('close');
};

const handleOpenGallery = () => {
  if (isDeleted.value || !hasMedia.value) return;
  emit('open-gallery', {
    media: props.message.media[0],
    allMedia: props.message.media,
    messageId: props.message.id
  });
  emit('close');
};

const handleOpenProfile = () => {
  if (isCurrentUser.value || !props.message?.sender_id) return;
  emit('open-user-profile', props.message.sender_id);
  emit('close');
};
</script>

<style scoped>
.context-menu {
  position: fixed;
  background-color: #1F1F1F;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  padding: 6px;
  min-width: 180px;
  z-index: 1000;
  overflow: hidden;
  user-select: none;
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  outline: none;
}

.menu-group {
  display: flex;
  flex-direction: column;
}

.menu-group + .menu-group {
  margin-top: 4px;
  padding-top: 4px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 8px;
  background: transparent;
  border: none;
  color: #E0E0E0;
  font-size: 13px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.menu-item:focus-visible {
  outline: 2px solid #4FC3F7;
  background-color: rgba(255, 255, 255, 0.08);
}

.menu-item:active {
  background-color: rgba(255, 255, 255, 0.12);
  transform: scale(0.98);
}

.menu-item.danger {
  color: #FF5252;
}

.menu-item.danger:hover {
  background-color: rgba(255, 82, 82, 0.12);
}

.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  opacity: 0.9;
}

.icon-size {
  width: 16px;
  height: 16px;
}

/* Animation */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease, transform 0.15s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .context-menu {
    min-width: 160px;
    max-width: 85vw;
    border-radius: 10px;
  }
  
  .menu-item {
    padding: 7px 8px;
    font-size: 12px;
  }
  
  .icon {
    width: 18px;
    height: 18px;
    margin-right: 6px;
  }
  
  .icon-size {
    width: 14px;
    height: 14px;
  }
}
</style>
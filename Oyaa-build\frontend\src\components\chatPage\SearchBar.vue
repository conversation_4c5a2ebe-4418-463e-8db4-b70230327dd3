<template>
  <div class="search-bar">
    <span v-if="!isActive" class="search-icon" @click="activateSearch">
      <i class="fa fa-search"></i>
    </span>
    <input
      v-if="isActive"
      ref="searchInput"
      v-model="searchQuery"
      type="text"
      placeholder="Search chat..."
      @input="handleInput"
      @blur="deactivateSearch"
    />
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';

// Emit the updated search query to the parent component
const emit = defineEmits(['update-search']);

const searchQuery = ref('');
const isActive = ref(false);
let debounceTimeout = null;
const searchInput = ref(null);

const handleInput = () => {
  if (debounceTimeout) clearTimeout(debounceTimeout);
  // Debounce the search input to limit how frequently we update the parent
  debounceTimeout = setTimeout(() => {
    emit('update-search', searchQuery.value.trim());
  }, 300); // 300ms debounce
};

// Activate the search input and focus it after rendering
const activateSearch = () => {
  isActive.value = true;
  nextTick(() => {
    searchInput.value?.focus();
  });
};

// Optionally, collapse the input back to just the icon when the input loses focus
// if the search query is empty.
const deactivateSearch = () => {
  if (searchQuery.value.trim() === '') {
    isActive.value = false;
  }
};
</script>

<style scoped>
.search-bar {
  padding: 0 4px;
  display: flex;
  align-items: center;
}

/* Style the search icon */
.search-icon {
  cursor: pointer;
  font-size: 18px;
  /* Adjust the icon color as needed */
  color: #666;
}

/* Style the input field */
input {
  width: 150px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  transition: width 0.3s ease;
}

input:focus {
  border-color: #42b983;
}
</style>

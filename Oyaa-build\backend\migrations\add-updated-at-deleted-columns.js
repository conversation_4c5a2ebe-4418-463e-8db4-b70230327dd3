// backend/migrations/add-updated-at-deleted-columns.js
require('dotenv').config();
const { omniQuery } = require('../config/alloydb.config');
const logger = require('../utils/logger');

async function addMissingColumns() {
  try {
    logger.info('Starting migration to add missing columns to group_messages table');

    // Check if the updated_at column exists
    const checkUpdatedAtQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'group_messages' AND column_name = 'updated_at'
    `;
    const updatedAtExists = await omniQuery(checkUpdatedAtQuery);
    
    // Add updated_at column if it doesn't exist
    if (updatedAtExists.rows.length === 0) {
      logger.info('Adding updated_at column to group_messages table');
      const addUpdatedAtQuery = `
        ALTER TABLE group_messages 
        ADD COLUMN updated_at TIMESTAMP DEFAULT NOW()
      `;
      await omniQuery(addUpdatedAtQuery);
      logger.info('updated_at column added successfully');
    } else {
      logger.info('updated_at column already exists');
    }

    // Check if the deleted column exists
    const checkDeletedQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'group_messages' AND column_name = 'deleted'
    `;
    const deletedExists = await omniQuery(checkDeletedQuery);
    
    // Add deleted column if it doesn't exist
    if (deletedExists.rows.length === 0) {
      logger.info('Adding deleted column to group_messages table');
      const addDeletedQuery = `
        ALTER TABLE group_messages 
        ADD COLUMN deleted BOOLEAN DEFAULT FALSE
      `;
      await omniQuery(addDeletedQuery);
      logger.info('deleted column added successfully');
    } else {
      logger.info('deleted column already exists');
    }

    logger.info('Migration completed successfully');
    return { success: true, message: 'Migration completed successfully' };
  } catch (error) {
    logger.error(`Migration failed: ${error.message}`, { error });
    return { success: false, message: `Migration failed: ${error.message}` };
  }
}

// Run the migration
addMissingColumns()
  .then(result => {
    console.log(result.message);
    process.exit(result.success ? 0 : 1);
  })
  .catch(err => {
    console.error('Migration error:', err);
    process.exit(1);
  });

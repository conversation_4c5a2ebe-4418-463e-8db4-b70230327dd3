<template>
  <div class="html-chat-list" ref="container">
    <!-- Loading indicator at the top -->
    <div v-if="loadingOlder" class="loading-indicator top-loader">
      <div class="spinner"></div>
      <span>Loading older messages...</span>
    </div>

    <!-- No more messages indicator -->
    <div v-if="!loadingOlder && noMoreOlderMessages && messages.length > 0" class="no-more-messages">
      <span>No more older messages</span>
    </div>

    <!-- Loading failed indicator -->
    <div v-if="!loadingOlder && loadingFailed" class="loading-failed">
      <span>Failed to load older messages</span>
      <button @click="retryLoadMore" class="retry-button">Retry</button>
    </div>

    <!-- Empty state -->
    <div v-if="messages.length === 0 && !loading" class="empty-state">
      <p>No messages yet</p>
      <p>Start a conversation!</p>
    </div>

    <!-- Messages container -->
    <div class="messages-container" ref="messagesContainer">
      <!-- Invisible spacer for scroll position preservation -->
      <div ref="topSpacer" class="scroll-spacer"></div>

      <!-- Messages with Telegram-style grouping -->
      <message-group-container
        :messages="messages"
        :current-user="currentUser"
        :register-ref="registerMessageRef"
        @show-context-menu="showContextMenu"
        @open-media="openMedia"
      />

      <!-- Context Menu -->
      <message-context-menu
        :visible="contextMenuVisible"
        :message="selectedMessage"
        :position="contextMenuPosition"
        :current-user="currentUser"
        @reply="handleReply"
        @copy="handleCopy"
        @edit="handleEdit"
        @delete="handleDelete"
        @report="handleReport"
        @open-gallery="handleOpenGallery"
        @open-user-profile="handleOpenUserProfile"
        @close="closeContextMenu"
      />
    </div>

    <!-- Media Gallery -->
    <group-media-gallery
      :is-open="galleryOpen"
      :media-items="galleryMedia"
      :initial-index="galleryInitialIndex"
      @close="closeGallery"
    />
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, nextTick, reactive, computed } from "vue"

import MessageContextMenu from "./GroupMessageComponents/MessageContextMenu.vue"
import MessageGroupContainer from "./GroupMessageComponents/MessageGroupContainer.vue"
import GroupMediaGallery from "./GroupMessageComponents/GroupMediaGallery.vue"


export default {
  name: "HtmlChatList",

  components: {
    MessageContextMenu,
    MessageGroupContainer,
    GroupMediaGallery,
  },

  props: {
    messages: {
      type: Array,
      default: () => [],
    },
    currentUser: {
      type: Object,
      required: true,
    },
    group: {
      type: Object,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    hasMoreMessages: {
      type: Boolean,
      default: false,
    },
    loadError: {
      type: [String, Object, Error],
      default: null,
    },
  },

  emits: ["load-more", "scroll-state-change", "cached-messages-loaded", "debug-metrics", "no-more-messages", "reply", "copy", "edit", "delete", "report", "open-gallery", "open-user-profile"],

  setup(props, { emit }) {
    // References
    const container = ref(null)
    const messagesContainer = ref(null)
    const messageRefs = reactive({}) // Keep messageRefs here for scroll logic

    // Function to register message refs from child components
    const registerMessageRef = (id, element) => {
      if (element) {
        messageRefs[id] = element;
      } else {
        delete messageRefs[id];
      }
    }

    // State
    const isAtBottom = ref(true)
    const loadingOlder = ref(false)
    const loadingFailed = ref(false)
    const isPreservingScroll = ref(false)
    const scrollObserver = ref(null)
    const loadMoreTriggered = ref(false)
    const initialScrollDone = ref(false)
    const scrollPercentage = ref(0)
    const noMoreOlderMessages = ref(!props.hasMoreMessages)
    const lastMessageId = ref(null)

    // Context menu state
    const contextMenuVisible = ref(false)
    const contextMenuPosition = ref({ x: 0, y: 0 })
    const selectedMessage = ref(null)

    // Media gallery state
    const galleryOpen = ref(false)
    const galleryMedia = ref([])
    const galleryInitialIndex = ref(0)

    // Computed properties (No longer needed as messages aren't reversed in template directly)
    // const reversedMessages = computed(() => {
    //   return [...props.messages];
    // });

    // Format time for display (remains in parent)
    const formatTime = (timestamp) => {
      if (!timestamp) return ""
      return new Date(timestamp).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    }

    // Find the first visible message (uses messageRefs)
    const findFirstVisibleMessage = () => {
      if (!container.value || props.messages.length === 0) return null;

      const containerRect = container.value.getBoundingClientRect();

      for (const message of props.messages) {
        // Use the DOM element stored in messageRefs
        const element = messageRefs[message.id];
        if (element) {
            const rect = element.getBoundingClientRect();
            if (rect.bottom >= containerRect.top && rect.top <= containerRect.bottom) {
                return {
                    id: message.id,
                    element,
                    distanceFromTop: rect.top - containerRect.top,
                };
            }
        }
      }
      return null;
    }

    // Save scroll position before loading more messages
    const saveScrollPosition = () => {
      if (!container.value) return

      const scrollAnchor = findFirstVisibleMessage()

      if (scrollAnchor) {
        console.log(
          `HtmlChatList: Saved scroll anchor - message: ${scrollAnchor.id}, distance from top: ${scrollAnchor.distanceFromTop}px`,
        )
        return scrollAnchor
      }

      return null
    }

    // Restore scroll position after loading more messages
    const restoreScrollPosition = (scrollAnchor) => {
        if (!container.value || !scrollAnchor) return;

        nextTick(() => {
            setTimeout(() => {
                try {
                    // Use the DOM element from messageRefs
                    const messageElement = messageRefs[scrollAnchor.id];

                    if (messageElement) {
                        const containerRect = container.value.getBoundingClientRect();
                        const elementRect = messageElement.getBoundingClientRect();
                        const currentDistanceFromTop = elementRect.top - containerRect.top;
                        const adjustment = currentDistanceFromTop - scrollAnchor.distanceFromTop;

                        isPreservingScroll.value = true;
                        container.value.scrollTop += adjustment;

                        console.log(`HtmlChatList: Restored scroll position - adjustment: ${adjustment}px`);

                        setTimeout(() => {
                            isPreservingScroll.value = false;
                        }, 50);
                    } else {
                         console.warn(`HtmlChatList: Could not find message element ${scrollAnchor.id} to restore scroll.`);
                         isPreservingScroll.value = false; // Ensure this is reset
                    }
                } catch (error) {
                    console.error("HtmlChatList: Error restoring scroll position:", error);
                    isPreservingScroll.value = false;
                }
            }, 20); // Increased delay slightly
        });
    }


    // Load more messages
    const loadMore = () => {
      if (loadingOlder.value || loadMoreTriggered.value) {
        console.log("HtmlChatList: Already loading or recently triggered, ignoring request")
        return
      }

      loadingFailed.value = false

      if (noMoreOlderMessages.value || !props.hasMoreMessages) {
        console.log("HtmlChatList: No more older messages available according to state flags")
        if (scrollObserver.value) {
          scrollObserver.value.disconnect()
          scrollObserver.value = null
          console.log("HtmlChatList: Disconnected scroll observer because there are no more messages")
        }
        emitDebugMetrics()
        emit("no-more-messages")
        return
      }

      if (props.messages.length > 0) {
        lastMessageId.value = props.messages[0].id
        console.log(`HtmlChatList: Using oldest message ID ${lastMessageId.value} as reference for loading more`)
      } else {
        console.log("HtmlChatList: No messages to use as reference for loading more")
        return
      }

      console.log("HtmlChatList: Loading more messages")
      adjustScrollPositionIfAtZero()
      const scrollAnchor = saveScrollPosition()

      loadingOlder.value = true
      loadMoreTriggered.value = true
      emit("load-more", lastMessageId.value, scrollAnchor) // Pass scrollAnchor with event

      setTimeout(() => {
        loadMoreTriggered.value = false
      }, 1000)

      // Return scrollAnchor so the watch handler can potentially use it
      // Note: The watch handler needs to receive it. Modifying emit.
      // Correction: The watch handler logic already handles scrollAnchor correctly
      // if passed via the load-more event *callback* mechanism, but here
      // we just emit. The parent component needs to pass it back via props or event.
      // Let's assume the parent handles passing the scrollAnchor back if needed,
      // or rely on the watch logic below. For simplicity, we'll stick to the watch.
    }

    // Setup intersection observer for infinite scrolling
    const setupScrollObserver = () => {
        if (!container.value) return;

        if (scrollObserver.value) {
            scrollObserver.value.disconnect();
        }

        if (!props.hasMoreMessages || noMoreOlderMessages.value) {
            console.log("HtmlChatList: Not setting up scroll observer because there are no more messages");
            return;
        }

        scrollObserver.value = new IntersectionObserver(
            (entries) => {
                if (loadingOlder.value || !props.hasMoreMessages || noMoreOlderMessages.value) return;

                const entry = entries[0];
                if (entry.isIntersecting) {
                    console.log("HtmlChatList: Scroll sentinel intersected, loading more messages");
                    loadMore();
                }
            },
            {
                root: container.value,
                rootMargin: "800px 0px 0px 0px",
                threshold: 0.01,
            },
        );

        // Observe the oldest message's DOM element
        if (props.messages.length > 0) {
            const oldestMessageId = props.messages[0].id;
            // Ensure the element ref is available before observing
            nextTick(() => {
                 const oldestMessageElement = messageRefs[oldestMessageId];
                 if (oldestMessageElement) {
                     scrollObserver.value.observe(oldestMessageElement);
                     console.log(`HtmlChatList: Observing message element ${oldestMessageId} as scroll sentinel`);
                 } else {
                      console.warn(`HtmlChatList: Could not find DOM element for message ${oldestMessageId} to observe.`);
                      // Maybe try again slightly later if refs aren't populated yet?
                      setTimeout(() => {
                           const element = messageRefs[oldestMessageId];
                           if (element && scrollObserver.value) { // Check observer still exists
                               scrollObserver.value.observe(element);
                               console.log(`HtmlChatList: Observing message element ${oldestMessageId} on retry.`);
                           } else if (!element) {
                               console.warn(`HtmlChatList: Still could not find DOM element for message ${oldestMessageId} on retry.`);
                           }
                      }, 100); // Short delay
                 }
            });
        }
    }


    // --- Debug Metrics (Unchanged) ---
    const lastFrameTime = ref(performance.now())
    const fps = ref(0)
    const frameCount = ref(0)
    const scrollDirection = ref("none")
    const lastScrollTop = ref(0)
    const visibleMessages = ref([])
    const visibleRangeStart = ref(0)
    const visibleRangeEnd = ref(0)

    const calculateFps = () => {
      const now = performance.now()
      const elapsed = now - lastFrameTime.value
      frameCount.value++
      if (elapsed >= 500) {
        fps.value = Math.round((frameCount.value * 1000) / elapsed)
        frameCount.value = 0
        lastFrameTime.value = now
        emitDebugMetrics()
      }
      requestAnimationFrame(calculateFps)
    }

    const countVisibleMessages = () => {
        if (!container.value) return [];

        const containerRect = container.value.getBoundingClientRect();
        const visible = [];
        let start = Infinity;
        let end = -1;

        props.messages.forEach((message, index) => {
            // Use the DOM element from messageRefs
            const element = messageRefs[message.id];
            if (element) {
                const rect = element.getBoundingClientRect();
                if (rect.bottom >= containerRect.top && rect.top <= containerRect.bottom) {
                    visible.push(message.id);
                    start = Math.min(start, index);
                    end = Math.max(end, index);
                }
            }
        });

        visibleMessages.value = visible;
        visibleRangeStart.value = start === Infinity ? 0 : start;
        visibleRangeEnd.value = end === -1 ? 0 : end;

        return visible;
    }

    const emitDebugMetrics = () => {
      if (!container.value) return
      const visibleCount = countVisibleMessages().length
      const metrics = {
        fps: fps.value,
        messageCount: props.messages.length,
        visibleCount,
        scrollPercentage: scrollPercentage.value,
        scrollDirection: scrollDirection.value,
        isAtBottom: isAtBottom.value,
        visibleRangeStart: visibleRangeStart.value,
        visibleRangeEnd: visibleRangeEnd.value,
        topSpacerHeight: 0,
        bottomSpacerHeight: 0,
      }
      emit("debug-metrics", metrics)
    }

    // --- Scroll Handling (Unchanged, uses countVisibleMessages which now uses messageRefs) ---
     const handleScroll = () => {
        if (!container.value || isPreservingScroll.value) return;

        try {
            const scrollTop = container.value.scrollTop;
            const scrollHeight = container.value.scrollHeight;
            const clientHeight = container.value.clientHeight;
            const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
            const wasAtBottom = isAtBottom.value;
            isAtBottom.value = distanceFromBottom < 20;

            if (isAtBottom.value !== wasAtBottom) {
                console.log(`HtmlChatList: At bottom changed to ${isAtBottom.value}`);
            }

            if (scrollHeight > clientHeight) {
                scrollPercentage.value = Math.round((scrollTop / (scrollHeight - clientHeight)) * 100);
            } else {
                scrollPercentage.value = 100;
            }

            if (scrollTop > lastScrollTop.value) {
                scrollDirection.value = "down";
            } else if (scrollTop < lastScrollTop.value) {
                scrollDirection.value = "up";
            } else {
                scrollDirection.value = "none";
            }
            lastScrollTop.value = scrollTop;

            countVisibleMessages(); // Uses updated logic

            if ((scrollPercentage.value < 40 ||
                (scrollPercentage.value < 50 && visibleRangeStart.value < props.messages.length * 0.3)) &&
                scrollDirection.value === "up" &&
                !loadingOlder.value &&
                !loadMoreTriggered.value &&
                props.hasMoreMessages &&
                !noMoreOlderMessages.value) {
                console.log(`HtmlChatList: Loading more messages at scroll position ${scrollPercentage.value}% (triggers at <40%)`);
                loadMore();
            }

            emit("scroll-state-change", {
                scrollTop,
                scrollHeight,
                clientHeight,
                isAtBottom: isAtBottom.value,
                scrollPercentage: scrollPercentage.value,
                scrollDirection: scrollDirection.value,
                visibleCount: visibleMessages.value.length,
                visibleRangeStart: visibleRangeStart.value,
                visibleRangeEnd: visibleRangeEnd.value
            });
        } catch (error) {
            console.error("HtmlChatList: Error in handleScroll:", error);
        }
    }


    // Open media in gallery
    const openMedia = (payload) => {
      if (!payload) return;

      // Check if this is an audio file - if so, don't open gallery
      const isAudioFile = (url) => {
        if (!url) return false;

        // Check if it's a Cloudinary audio URL (they use video resource type for audio)
        if (url.includes('cloudinary.com') && url.includes('/video/upload/') &&
            (url.endsWith('.webm') || url.endsWith('.mp3') || url.endsWith('.ogg') || url.endsWith('.m4a'))) {
          return true;
        }

        // Check for standard audio extensions
        return /\.(mp3|wav|ogg|m4a|aac|flac|webm)$/i.test(url);
      };

      // Get high-quality version of media URL for gallery display
      const getHighQualityUrl = (url) => {
        if (!url) return url;

        // For Cloudinary URLs, ensure we're using high quality
        if (url.includes('cloudinary.com')) {
          // Remove any existing transformations that might be for thumbnails
          const urlParts = url.split('/upload/');
          if (urlParts.length === 2) {
            // Check if there are transformations like w_60,h_60,c_fill,q_10,e_blur:800
            const secondPart = urlParts[1];
            if (secondPart.includes('/')) {
              // This likely has transformations, so replace with high quality
              return `${urlParts[0]}/upload/q_auto:good/${secondPart.split('/').pop()}`;
            }
            // No transformations or already high quality
            return url;
          }
        }

        return url;
      };

      // Handle different payload formats
      let mediaItems = [];
      let initialIndex = 0;

      if (Array.isArray(payload)) {
        // Direct array of media
        mediaItems = payload;
      } else if (payload.media && Array.isArray(payload.media)) {
        // Object with media array and index
        mediaItems = payload.media;
        initialIndex = payload.index || 0;
      } else if (payload.mediaUrl || payload.url) {
        // Single media item
        const mediaUrl = payload.mediaUrl || payload.url;

        // If this is an audio file, don't open gallery (it's played inline)
        if (isAudioFile(mediaUrl)) {
          console.log('Audio file detected, not opening gallery');
          return;
        }

        mediaItems = [{
          mediaUrl: mediaUrl,
          mediaType: getMediaType(mediaUrl),
          senderName: payload.senderName || 'User',
          createdAt: payload.createdAt || new Date().toISOString()
        }];
      } else {
        console.error('Invalid media payload:', payload);
        return;
      }

      // Filter out audio files from the gallery and ensure high quality URLs
      const filteredMedia = mediaItems.filter(item => {
        const url = item.mediaUrl || item.url;
        return !isAudioFile(url);
      }).map(item => {
        // Ensure we're using high quality URLs in the gallery
        return {
          ...item,
          mediaUrl: getHighQualityUrl(item.mediaUrl || item.url)
        };
      });

      // If no non-audio media items, don't open gallery
      if (filteredMedia.length === 0) {
        console.log('No non-audio media items to display in gallery');
        return;
      }

      galleryMedia.value = filteredMedia;
      galleryInitialIndex.value = initialIndex;
      galleryOpen.value = true;
    }

    // Close media gallery
    const closeGallery = () => {
      galleryOpen.value = false;
    }

    // Helper function to determine media type from URL
    const getMediaType = (url) => {
      if (!url) return null;

      // Check for image extensions
      if (/\.(jpe?g|png|gif|webp|bmp|svg)$/i.test(url)) {
        return 'image/jpeg';
      }

      // Check for video extensions
      if (/\.(mp4|webm|mov|avi|wmv|flv|mkv)$/i.test(url)) {
        return 'video/mp4';
      }

      // Check for audio extensions
      if (/\.(mp3|wav|ogg|m4a|aac|flac)$/i.test(url)) {
        return 'audio/mp3';
      }

      return null;
    }

    // --- Context Menu Logic (Unchanged, uses selectedMessage) ---
    const showContextMenu = (event, message) => {
      if (message.deleted) {
        return;
      }
      contextMenuPosition.value = { x: event.clientX, y: event.clientY };
      selectedMessage.value = message; // This message comes from the child component's event
      contextMenuVisible.value = true;
      document.addEventListener('click', closeContextMenuOnClickOutside);
    }

    const closeContextMenu = () => {
      contextMenuVisible.value = false;
      selectedMessage.value = null; // Clear selected message
      document.removeEventListener('click', closeContextMenuOnClickOutside);
    }

    const closeContextMenuOnClickOutside = (event) => {
      // Simple implementation: close if click is anywhere
      // A more robust version would check if the click was outside the menu itself
      closeContextMenu();
    }

    const handleReply = (message) => { emit('reply', message); closeContextMenu(); }
    const handleCopy = (message) => {
      if (message?.message || message?.content) {
        navigator.clipboard.writeText(message.message || message.content).then(() => {
          console.log('Message copied to clipboard');
        }).catch(err => {
          console.error('Failed to copy message:', err);
        });
      }
      closeContextMenu();
    }
    const handleEdit = (message) => { emit('edit', message); closeContextMenu(); }
    const handleDelete = (message) => { emit('delete', message); closeContextMenu(); }
    const handleReport = (message) => { emit('report', message); closeContextMenu(); }
    const handleOpenGallery = (data) => { emit('open-gallery', data); closeContextMenu(); }
    const handleOpenUserProfile = (userId) => { emit('open-user-profile', userId); closeContextMenu(); }

    // --- Scroll To Bottom / Jump To Latest (Unchanged) ---
     const scrollToBottom = () => {
        if (!container.value) return;
        nextTick(() => {
            const scrollHeight = container.value.scrollHeight;
            const clientHeight = container.value.clientHeight;
            container.value.scrollTop = scrollHeight - clientHeight; // Scroll to max scroll position
            isAtBottom.value = true;
            console.log("HtmlChatList: Scrolled to bottom (max scroll position)");
        });
    }

    const jumpToLatestMessages = () => {
        if (!container.value) return;

        const originalScrollBehavior = container.value.style.scrollBehavior;
        container.value.style.scrollBehavior = 'auto';

        console.log("DEBUG - Before jump:", { /* ... */ });

        const scrollHeight = container.value.scrollHeight;
        const clientHeight = container.value.clientHeight;
        const maxScrollPosition = scrollHeight - clientHeight;

        // For column-reverse, max scroll position shows latest messages at the bottom visually
        container.value.scrollTop = maxScrollPosition < 0 ? 0 : maxScrollPosition;
        isAtBottom.value = true;

        setTimeout(() => {
            container.value.style.scrollBehavior = originalScrollBehavior;
        }, 0);

        setTimeout(() => {
             console.log("DEBUG - After jump:", {
                scrollTop: container.value.scrollTop,
                scrollHeight: container.value.scrollHeight,
                clientHeight: container.value.clientHeight,
                isAtBottom: isAtBottom.value,
                maxScrollPosition
             });
            // Recalculate scroll state after jump
            handleScroll();
        }, 100);
    }

    // --- Lifecycle Hooks (Adjusted setupScrollObserver call) ---
    onMounted(() => {
      console.log("HtmlChatList: Component mounted")
      nextTick(() => {
        if (container.value) {
          container.value.addEventListener("scroll", handleScroll)
          console.log("HtmlChatList: Scroll event listener added")
          // Setup observer *after* initial messages might be rendered
           if (props.messages.length > 0) {
               setupScrollObserver();
           }
        }
        requestAnimationFrame(calculateFps)
      })
    })

    onUnmounted(() => {
      if (container.value) {
        container.value.removeEventListener("scroll", handleScroll)
      }
      if (scrollObserver.value) {
        scrollObserver.value.disconnect()
      }
      // Cancel FPS calculation? Not strictly necessary, but good practice
    })

    // --- Scroll Adjustment (Unchanged) ---
    const adjustScrollPositionIfAtZero = () => {
      if (!container.value) return false
      if (scrollPercentage.value === 0 && container.value.scrollTop === 0) { // More precise check
        console.log("HtmlChatList: User at 0 scroll, adjusting to preserve position on load more")
        const scrollHeight = container.value.scrollHeight
        const clientHeight = container.value.clientHeight
        const scrollableDistance = scrollHeight - clientHeight
        if (scrollableDistance > 0) {
          // Scroll down just a tiny bit
          container.value.scrollTop = 1; // Set to 1px instead of percentage
          return true
        }
      }
      return false
    }

    // --- Watchers (Adjusted messages watcher, added scrollAnchor parameter handling) ---
    watch(() => props.hasMoreMessages, (hasMore) => {
        console.log(`HtmlChatList: hasMoreMessages changed to ${hasMore}`);
        const wasNoMore = noMoreOlderMessages.value;
        noMoreOlderMessages.value = !hasMore;

        if (!hasMore) {
            console.log("HtmlChatList: No more older messages available according to store state");
            if (scrollObserver.value) {
                scrollObserver.value.disconnect();
                scrollObserver.value = null;
                console.log("HtmlChatList: Disconnected scroll observer because there are no more messages");
            }
            emit("no-more-messages");
        } else if (wasNoMore && hasMore) {
            // If we previously thought there were no more, but now there are, set up observer again
            nextTick(() => {
                 setupScrollObserver();
            });
        }
    });


    // Watch for changes in messages
    watch(
        () => props.messages,
        (newMessages, oldMessages) => {
            console.log(`HtmlChatList: Messages changed from ${oldMessages?.length || 0} to ${newMessages?.length || 0}`);
            const addedMessagesCount = (newMessages?.length || 0) - (oldMessages?.length || 0);
            const isLoadingOlder = addedMessagesCount > 0 && !isAtBottom.value && initialScrollDone.value; // Heuristic: added messages AND not at bottom AND not initial load
            const isReceivingNew = addedMessagesCount > 0 && isAtBottom.value; // Heuristic: added messages AND at bottom
            const isInitialLoad = (oldMessages?.length || 0) === 0 && (newMessages?.length || 0) > 0;

            console.log(`HtmlChatList Watcher - isLoadingOlder: ${isLoadingOlder}, isReceivingNew: ${isReceivingNew}, isInitialLoad: ${isInitialLoad}`);

            let scrollAnchor = null;
            if (isLoadingOlder) {
                 // Try to find the anchor based on the *first* of the *newly added* messages
                 // This assumes older messages are prepended
                 if (oldMessages && oldMessages.length > 0 && newMessages && newMessages.length > oldMessages.length) {
                     const firstOldMessageId = oldMessages[0].id;
                     const anchorElement = messageRefs[firstOldMessageId];
                     if(anchorElement && container.value) {
                         const containerRect = container.value.getBoundingClientRect();
                         const elementRect = anchorElement.getBoundingClientRect();
                         scrollAnchor = {
                             id: firstOldMessageId,
                             element: anchorElement,
                             distanceFromTop: elementRect.top - containerRect.top
                         };
                         console.log(`HtmlChatList Watcher: Saved scroll anchor during message update: ${scrollAnchor.id}`);
                     } else {
                         console.log("HtmlChatList Watcher: Could not find anchor element when messages updated.");
                     }
                 }
                 // Adjust scroll if needed *before* DOM update potentially shifts things
                 // adjustScrollPositionIfAtZero(); // This might interfere with anchor restoration
            }


            nextTick(() => {
                // Update refs: Clear old refs that no longer exist
                const newMessageIds = new Set(newMessages.map(m => m.id));
                Object.keys(messageRefs).forEach(id => {
                    if (!newMessageIds.has(parseInt(id, 10)) && !newMessageIds.has(id)) { // Handle string/number keys
                        delete messageRefs[id];
                    }
                });

                // Setup/Update observer *after* DOM updates from nextTick
                setupScrollObserver();

                // Handle scroll restoration / jump to bottom logic
                if (loadingOlder.value) {
                    loadingOlder.value = false; // Reset loading flag
                    if (scrollAnchor) {
                        console.log(`HtmlChatList Watcher: Attempting to restore scroll to anchor ${scrollAnchor.id}`);
                        restoreScrollPosition(scrollAnchor);
                    } else if(isLoadingOlder) {
                         // If loading older but no anchor was saved (e.g. previous first msg scrolled out),
                         // we might just stay put, letting the user see the new older messages appear above.
                         console.log("HtmlChatList Watcher: Older messages loaded, but no scroll anchor found/saved. Scroll position maintained by browser.");
                    }
                } else if (isInitialLoad) {
                     console.log("HtmlChatList Watcher: Initial load detected, jumping to latest messages");
                     requestAnimationFrame(() => { // Ensure DOM is fully ready
                         jumpToLatestMessages();
                         initialScrollDone.value = true;
                     });
                } else if (isReceivingNew || isAtBottom.value) { // If receiving new messages or explicitly scrolled to bottom
                     console.log("HtmlChatList Watcher: New message received or at bottom, jumping to latest messages");
                     // Only jump if we were already near the bottom before the update, or if it's a new message
                     jumpToLatestMessages();
                     initialScrollDone.value = true; // Mark initial scroll as done if it wasn't already
                } else {
                     // If messages changed but none of the above conditions met (e.g., deletions, edits mid-list)
                     // Maintain current scroll position as best as possible (usually default browser behavior)
                     console.log("HtmlChatList Watcher: Messages updated, maintaining scroll position.");
                }

            });
        },
        { deep: true } // Keep deep watch
    );


    // Retry loading more messages
    const retryLoadMore = () => {
      if (loadingOlder.value) return

      loadingFailed.value = false
      loadMoreTriggered.value = false
      console.log("HtmlChatList: Retrying to load more messages")

      if (lastMessageId.value && props.messages.length > 0) {
          // Attempt load using the last known oldest ID
          emit("load-more", lastMessageId.value);
      } else if (props.messages.length > 0) {
          const oldestMessageId = props.messages[0].id
          console.log(`HtmlChatList: Using current oldest message ID ${oldestMessageId} for retry`);
          emit("load-more", oldestMessageId);
      } else {
           console.log("HtmlChatList: Cannot retry load, no messages exist.");
           // Optionally, trigger a full refresh or initial load?
           // emit("load-more", null); // Or handle appropriately
      }
      // Set loading state *after* emitting, so parent knows a load is in progress
      loadingOlder.value = true;
    }


    // Watch for errors in loading more messages
    watch(
      () => props.loadError,
      (error) => {
        if (error) {
          console.error("HtmlChatList: Error loading more messages:", error)
          loadingFailed.value = true
          loadingOlder.value = false // Ensure loading stops on error
        } else {
          // Clear error state if prop becomes null
          loadingFailed.value = false;
        }
      }
    )


    return {
      container,
      messagesContainer,
      messageRefs, // Keep exposing for potential parent access? Or remove if only internal
      registerMessageRef, // Add the register function for child components
      loadingOlder,
      loadingFailed,
      noMoreOlderMessages,
      // lastMessageId, // Primarily internal now
      // reversedMessages, // Removed
      scrollToBottom,
      jumpToLatestMessages,
      formatTime, // Still needed for passing prop
      loadMore,
      retryLoadMore,
      openMedia, // Still needed for handling emit
      galleryOpen,
      galleryMedia,
      galleryInitialIndex,
      closeGallery,
      // Context menu state/handlers (unchanged)
      contextMenuVisible,
      contextMenuPosition,
      selectedMessage,
      showContextMenu,
      closeContextMenu,
      handleReply,
      handleCopy,
      handleEdit,
      handleDelete,
      handleReport,
      handleOpenGallery,
      handleOpenUserProfile,
      // Debug metrics (unchanged)
      fps,
      visibleMessages,
      visibleRangeStart,
      visibleRangeEnd,
      scrollDirection,
      emitDebugMetrics
    }
  },
}

</script>

<style scoped>
/* Keep general list styles, remove bubble-specific styles */
.html-chat-list {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  padding: 10px 5px 10px 10px; /* Adjusted padding slightly */
  box-sizing: border-box;
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 100, 100, 0.2) transparent;
}

.html-chat-list::-webkit-scrollbar {
  width: 4px;
}

.html-chat-list::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
}

.html-chat-list::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.2);
  border-radius: 4px;
}

.html-chat-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(150, 150, 150, 0.3);
}

.loading-indicator {
  /* position: sticky; */ /* Sticky might interfere with scroll restoration */
  width: calc(100% - 10px); /* Account for padding */
  margin-left: 5px; /* Center it */
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
  border-radius: 8px;
  margin-bottom: 10px;
}

.top-loader {
  /* If not sticky, needs to be handled differently or just appear inline */
   margin-top: 5px; /* Add some space if not sticky */
}

.no-more-messages, .loading-failed {
  width: calc(100% - 10px);
  margin-left: 5px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  border-radius: 8px;
  margin-bottom: 10px;
  text-align: center;
}

.loading-failed {
  background-color: rgba(255, 0, 0, 0.2);
  flex-direction: column;
  gap: 8px;
}

.retry-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.scroll-spacer {
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #FFFFFF;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100% - 40px); /* Adjust height considering padding/loaders */
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  padding: 20px;
}

.empty-state p {
  margin: 5px 0;
  font-size: 16px;
}

.messages-container {
  display: flex;
  flex-direction: column; /* Keep column for bubble layout */
  gap: 0; /* Remove gap, handled by bubble margins */
  width: 100%;
  padding-bottom: 10px; /* Reduced bottom padding */
  /* Remove scrollbar styles from here if main container handles it */
}

/* REMOVED bubble-specific styles like .message-bubble, .own-message, etc. */
/* They are now in the specific bubble components */

</style>
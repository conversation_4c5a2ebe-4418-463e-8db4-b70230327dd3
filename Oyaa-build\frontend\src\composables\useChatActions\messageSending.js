// frontend/src/composables/useChatActions/messageSending.js
import { formatTimestamp } from '../messageFormatter';

export function useMessageSending(state, socket, store) {
  const handleSendMessage = (payload) => {
    if (!payload.text.trim() && !payload.media) return;
    const newMsg = {
      sender_id: state.currentUser.value.id,
      receiver_id: state.friendId.value,
      message: payload.text,
      media: payload.media,
      reply: payload.reply ? payload.reply.id : null,
    };

    console.log('Preparing to send message:', newMsg);

    // Send the message using socket
    socket.sendMessage(newMsg);
    
    console.log('Message sent via socket');

    // Update last chat in store
    store.dispatch('chat/updateLastChat', {
      friendId: state.friendId.value,
      message: newMsg.message || (newMsg.media ? '[Media]' : ''),
      timestamp: formatTimestamp(new Date().toISOString()),
    });
    
    state.replyingMessage.value = null;
  };

  const handleTyping = () => {
    socket.sendTyping({
      senderId: state.currentUser.value.id,
      receiverId: state.friendId.value,
    });
  };

  return { handleSendMessage, handleTyping };
}

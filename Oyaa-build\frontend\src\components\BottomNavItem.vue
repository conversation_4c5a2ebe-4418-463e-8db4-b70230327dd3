<template>
  <router-link to="#" class="flex flex-col items-center justify-center">
    <component :is="iconComponent" :class="[ 'w-6 h-6', active ? 'text-blue-500' : 'text-gray-500' ]" />
  </router-link>
</template>

<script setup>
import { computed } from 'vue'
import { HomeIcon, GridIcon, ClockIcon, UserIcon } from 'lucide-vue-next'

const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  active: {
    type: Boolean,
    default: false,
  },
})

const iconComponent = computed(() => {
  switch (props.icon) {
    case 'home':
      return HomeIcon
    case 'grid':
      return GridIcon
    case 'clock':
      return ClockIcon
    case 'user':
      return UserIcon
    default:
      return null
  }
})
</script>

<style scoped>
/* Add any BottomNavItem-specific styles here */
</style>
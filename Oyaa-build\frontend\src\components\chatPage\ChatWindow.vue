<template>
  <div class="chat-window">
    <!-- Header stays at the top -->
    <Header v-if="friend" :friend="friend" @update-search="handleUpdateSearch" />

    <!-- This container always exists and flexes to fill available space -->
    <div class="chat-content">
      <!-- If messages are loaded, display them; otherwise, show the skeleton loading view -->
      <MessageList 
        v-if="messagesLoaded" 
        :messages="filteredMessages" 
        :friend="friend" 
        @reply="handleReply" 
        @react="handleReact"
        @load-older="handleLoadOlderMessages" 
      />
      <div v-else class="skeleton-container">
        <ul>
          <li v-for="i in 5" :key="i" class="chat-item skeleton">
            <div class="avatar skeleton-avatar"></div>
            <div class="chat-info">
              <span class="sender skeleton-text"></span>
              <span class="text skeleton-text"></span>
            </div>
            <div class="meta">
              <div class="timestamp skeleton-text"></div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    
    <!-- Message input always stays at the bottom -->
    <MessageInput 
      :friend="friend" 
      :replyingMessage="replyingMessage" 
      @send="handleSendMessage" 
      @typing="handleTyping" 
      @clearReply="clearReply" 
    />

    <!-- Attachments component -->
    <Attachments @file-changed="onFileChanged" />

  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import useChat from '@/composables/useChat';

import Header from './Header.vue';
import MessageList from './MessageList.vue';
import MessageInput from './MessageInput.vue';
import TypingIndicator from './TypingIndicator.vue';
import Attachments from './Attachments.vue';

const route = useRoute();
const router = useRouter();
const store = useStore();

const {
  friend,
  filteredMessages,
  messagesLoaded,
  isFriendTyping,
  replyingMessage,
  initializeChat,
  loadOlderMessages,
  handleSendMessage,
  handleTyping,
  handleFileUpload,
  handleUpdateSearch,
  handleReply,
  clearReply,
  handleReact
} = useChat(route, router, store);

const handleLoadOlderMessages = async () => {
  console.log("ChatWindow: loadOlderMessages triggered");
  await loadOlderMessages();
};

onMounted(() => {
  initializeChat();
});

onBeforeUnmount(() => {
  // Clear any local state if needed.
});
const onFileChanged = (file) => {
  if (file) {
    // Call the upload function with the raw file
    handleFileUpload(file);
  }
};

</script>

<style scoped>
.chat-window {
  display: flex;
  flex-direction: column;
  height: 100vh;
  margin: 0 auto;
  background-color: #f5f5f5; /* Adjust as needed */
}

/* Chat content area styling */
.chat-content {
  flex: 1;
  overflow-y: auto;
  background-color: #36393f; /* Ensures the area shows the desired styles even when empty */
}

/* Skeleton loading view styles */
.skeleton-container ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}

.chat-info {
  flex: 1;
  min-width: 0;
  margin-right: 12px;
}

.sender {
  display: block;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 4px;
}

.text {
  display: block;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.timestamp {
  width: 40px;
  height: 0.75em;
}

/* Skeleton specific styles */
.skeleton {
  animation: pulse 1.5s infinite;
}

.skeleton-avatar {
  background-color: var(--skeleton-bg, #2f3136);
}

.skeleton-text {
  height: 1em;
  margin-bottom: 0.5em;
  background-color: var(--skeleton-bg, #2f3136);
  border-radius: 4px;
}

/* Pulse animation */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}


</style>

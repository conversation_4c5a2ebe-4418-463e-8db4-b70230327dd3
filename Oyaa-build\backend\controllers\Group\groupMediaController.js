// backend/controllers/Group/groupMediaController.js
const groupMediaService = require('../../services/Groups/groupMediaService');
const cloudinary = require('../../config/cloudinary');
const fs = require('fs');

// Helper function to get upload options based on MIME type
// backend/controllers/Group/groupMediaController.js

function getUploadOptions(mimetype) {
  if (mimetype.startsWith('image/')) {
    // Handle all images (including GIFs) as 'image' resource_type
    return {
      resource_type: 'image',
      transformation: { width: 800, crop: 'limit', quality: 'auto:good' }
    };
  } else if (mimetype.startsWith('video/')) {
    // Videos use MP4 with H.264 (default codec in Cloudinary's MP4)
    return {
      resource_type: 'video',
      transformation: { width: 640, crop: 'limit', bit_rate: '500k', quality: 'auto', format: 'mp4' }
    };
  } else if (mimetype.startsWith('audio/')) {
    // Audio remains unchanged
    return {
      resource_type: 'video',
      transformation: { bit_rate: '128k', format: 'mp3' }
    };
  } else {
    return { resource_type: 'auto' };
  }
}

class GroupMediaController {
  async uploadGroupMedia(req, res) {
    try {
      if (!req.file) return res.status(400).json({ error: 'No file uploaded' });
      if (!req.body.userId) return res.status(400).json({ error: 'userId is required' });

      const filePath = req.file.path;

      // Get upload options based on MIME type
      const uploadOptions = getUploadOptions(req.file.mimetype);
      console.log(`Uploading file with MIME type: ${req.file.mimetype}, options:`, uploadOptions);

      // Upload to Cloudinary with transformations
      const result = await cloudinary.uploader.upload(filePath, {
        resource_type: uploadOptions.resource_type,
        transformation: uploadOptions.transformation
      });
      console.log('Cloudinary result:', result);

      // Define audio formats
      const audioFormats = ['mp3', 'm4a', 'wav', 'ogg', 'aac', 'webm'];
      let mediaType;
      if (result.resource_type === 'image') {
        mediaType = 'image';
      } else if (result.resource_type === 'video') {
        mediaType = audioFormats.includes(result.format) ? 'audio' : 'video';
      } else {
        mediaType = result.resource_type;
      }

      // Prepare media details
      let mediaDetails = {
        mediaUrl: result.secure_url,
        publicId: result.public_id,
        mediaType: mediaType,
      };

      // Add duration for audio and video files
      if (mediaType === 'audio' || mediaType === 'video') {
        // If a duration was explicitly provided in the request (for audio recordings), use that
        if (req.body.duration && mediaType === 'audio') {
          // Parse as integer to ensure it's a valid number
          const parsedDuration = parseInt(req.body.duration, 10);
          if (!isNaN(parsedDuration)) {
            mediaDetails.duration = parsedDuration;
          } else {
            // Fallback to Cloudinary's duration if parsing fails
            mediaDetails.duration = Math.round(result.duration || 0);
          }
        } else {
          // Otherwise use Cloudinary's duration and ensure it's an integer
          mediaDetails.duration = Math.round(result.duration || 0);
        }
      }

      // Generate thumbnail only for videos
      if (mediaType === 'video') {
        const thumbnailUrl = cloudinary.url(`${result.public_id}.jpg`, {
          resource_type: 'video',
          transformation: [{ width: 300, height: 200, crop: 'fill' }],
        });
        mediaDetails.thumbnailUrl = thumbnailUrl;
      }

      // Clean up temporary file
      fs.unlink(filePath, (err) => {
        if (err) console.error('Failed to delete temporary file:', err);
      });

      res.status(201).json({ message: 'Media uploaded successfully', media: mediaDetails });
    } catch (err) {
      console.error('Error in group media upload endpoint:', err);
      res.status(500).json({ message: err.message });
    }
  }

  // Other methods remain unchanged
  async getMediaByGroupChat(req, res) {
    try {
      const { groupChatId } = req.params;
      const mediaItems = await groupMediaService.getMediaByGroupChatId(groupChatId);
      res.status(200).json(mediaItems);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async getMediaByUser(req, res) {
    try {
      const { userId } = req.params;
      const mediaItems = await groupMediaService.getMediaByUserId(userId);
      res.status(200).json(mediaItems);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async deleteGroupMedia(req, res) {
    try {
      const { id } = req.params;
      const deletedMedia = await groupMediaService.deleteGroupMedia(id);
      res.status(200).json({ message: 'Group media deleted successfully', media: deletedMedia });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async getAllMediaByGroup(req, res) {
    try {
      const { groupId } = req.params;
      if (!groupId) {
        return res.status(400).json({ message: 'Group ID is required' });
      }
      const mediaItems = await groupMediaService.getAllMediaByGroupId(groupId);
      res.status(200).json(mediaItems);
    } catch (err) {
      console.error('Error fetching group media:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async getMediaByMessageIds(req, res) {
    try {
      const { messageIds } = req.body;
      if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
        return res.status(400).json({ message: 'Valid messageIds array is required' });
      }
      const mediaItems = await groupMediaService.getMediaByMessageIds(messageIds);
      res.status(200).json(mediaItems);
    } catch (err) {
      console.error('Error fetching media by message IDs:', err);
      res.status(500).json({ message: err.message });
    }
  }
}

module.exports = new GroupMediaController();
export interface User {
	id: string;
	name: string;
	avatar?: string;
	status?: 'online' | 'offline' | 'away';
}

export interface MediaFile {
	url: string;
	type: 'image' | 'audio' | 'video' | 'file';
	name: string;
	size?: number;
	duration?: number; // for audio/video
	width?: number; // for images/videos
	height?: number; // for images/videos
	thumbnail?: string; // for videos
}

export interface Message {
	id: string;
	text: string;
	timestamp: Date;
	sender: User;
	type: 'text' | 'image' | 'audio' | 'video' | 'file';
	media?: MediaFile;
	replyTo?: string; // ID of message being replied to
	edited?: boolean;
	reactions?: Record<string, string[]>; // emoji -> user IDs
	status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
}

export interface ChatState {
	messages: Message[];
	isLoading: boolean;
	hasMore: boolean;
	scrollPosition: number;
	selectedMessages: Set<string>;
}

export interface VirtualScrollItem {
	id: string;
	height: number;
	offset: number;
	data: Message;
}

export interface ScrollMetrics {
	scrollTop: number;
	scrollHeight: number;
	clientHeight: number;
	itemHeight: number;
	visibleStart: number;
	visibleEnd: number;
	bufferSize: number;
}

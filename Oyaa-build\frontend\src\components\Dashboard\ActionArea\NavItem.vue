<template>
  <button
    class="mobile-nav-item"
    :class="{ active }"
    @click="handleClick"
  >
    <div class="mobile-nav-icon-container" :class="{ active }">
      <component :is="icon" class="mobile-nav-icon" ref="iconRef" />
      <span v-if="active" class="active-indicator"></span>
    </div>
    <span class="mobile-nav-label" :class="{ active }">{{ label }}</span>
  </button>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  active: { type: Boolean, default: false },
  label: { type: String, required: true },
  icon: { type: [Object, String, Function], required: true },
});

const emit = defineEmits(['click']);
const iconRef = ref(null);

const handleClick = () => {
  emit('click');
};

// Animate icon when active state changes to true
watch(
  () => props.active,
  (newVal) => {
    if (newVal && iconRef.value) {
      iconRef.value.animate(
        [
          { transform: 'translateY(0) scale(1)', opacity: 0.5 },
          { transform: 'translateY(-6px) scale(1.1)', opacity: 1 },
          { transform: 'translateY(-4px) scale(1)', opacity: 1 },
        ],
        {
          duration: 400,
          easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
          fill: 'forwards',
        }
      );
    }
  }
);
</script>

<style scoped>
.mobile-nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  flex: 1;
  background: none;
  border: none;
  color: #a0a0a0;
  transition: transform 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  cursor: pointer;
  touch-action: manipulation; /* Improve touch behavior */
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
}

.mobile-nav-item:active {
  transform: scale(0.95);
}

.mobile-nav-item.active {
  color: #6366f1;
}

.mobile-nav-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-radius: 12px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.mobile-nav-icon-container.active {
  background-color: rgba(99, 102, 241, 0.15);
}

.mobile-nav-icon-container.active .active-indicator {
  position: absolute;
  inset: 0;
  border-radius: 12px;
  border: 1px solid rgba(99, 102, 241, 0.3);
  animation: pulse 2s infinite;
}

.mobile-nav-icon {
  width: 22px;
  height: 22px;
  transition: all 0.3s ease;
}

.mobile-nav-label {
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .mobile-nav-item {
    padding: 8px 4px;
  }
  
  .mobile-nav-icon-container {
    width: 36px;
    height: 36px;
  }
  
  .mobile-nav-icon {
    width: 20px;
    height: 20px;
  }
  
  .mobile-nav-label {
    font-size: 11px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .mobile-nav-item {
    padding: 6px 2px;
  }
  
  .mobile-nav-icon-container {
    width: 32px;
    height: 32px;
    margin-bottom: 2px;
  }
  
  .mobile-nav-icon {
    width: 18px;
    height: 18px;
  }
  
  .mobile-nav-label {
    font-size: 10px;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .mobile-nav-item,
  .mobile-nav-icon-container,
  .mobile-nav-icon,
  .mobile-nav-label {
    transition: none;
  }
  
  .mobile-nav-icon-container.active .active-indicator {
    animation: none;
  }
}
</style>
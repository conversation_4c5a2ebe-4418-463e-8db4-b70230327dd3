// backend/services/Groups/enhancedGroupChatService.js
const { omniQuery } = require('../../config/alloydb.config');
// Valkey cache temporarily disabled
// const {
//   groupChatValkeyLPush,
//   groupChatValkeyLRange,
//   groupChatValkeyLTrim,
//   groupChatValkeyGet,
//   groupChatValkeySet,
//   groupChatValkeyDel
// } = require('../../config/groupChatValkey.config');
const logger = require('../../utils/logger');
const db = require('../../utils/db'); // Still need main DB for user info
const Filter = require('bad-words');
const filter = new Filter();

class EnhancedGroupChatService {
  /**
   * Sends a message to a group chat
   * Stores in AlloyDB Omni and caches in Valkey
   */
  async sendMessage(senderId, groupId, message, sent_at, replyId = null, media = null) {
    if (!groupId) {
      throw new Error('Group ID is required');
    }

    try {
      // Check if user is muted
      const userRes = await db.query(
        'SELECT muted_until FROM users WHERE id = $1',
        [senderId]
      );

      // Check if user is muted
      if (userRes.rows[0]?.muted_until && new Date() < userRes.rows[0].muted_until) {
        throw new Error('You are muted and cannot send messages');
      }

      // Check for profanity if message is provided
      if (message && filter.isProfane(message)) {
        throw new Error('Message contains inappropriate content');
      }

      // 1. Store message in AlloyDB Omni
      const query = `
        INSERT INTO group_messages
        (group_id, sender_id, message, sent_at, reply_id, system_message)
        VALUES ($1, $2, $3, $4, $5, false)
        RETURNING id
      `;
      const values = [groupId, senderId, message, sent_at || new Date(), replyId];
      const result = await omniQuery(query, values);
      const messageId = result.rows[0].id;

      // 2. If media exists, store it
      let mediaData = null;
      if (media) {
        const mediaQuery = `
          INSERT INTO group_message_media
          (message_id, media_url, media_type, public_id, thumbnail_url, duration)
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING *
        `;
        const mediaValues = [
          messageId,
          media.mediaUrl,
          media.mediaType,
          media.publicId,
          media.thumbnailUrl || null,
          media.duration || null
        ];
        const mediaResult = await omniQuery(mediaQuery, mediaValues);
        mediaData = mediaResult.rows[0];
      }

      // 3. Fetch user info from main database
      const userQuery = `
        SELECT username, avatar FROM users WHERE id = $1
      `;
      const userResult = await db.query(userQuery, [senderId]);
      const userData = userResult.rows[0];

      // 4. Fetch reply info if needed
      let replyData = null;
      let replySenderId = null;
      if (replyId) {
        const replyQuery = `
          SELECT
            gm.message AS reply_message,
            gm.sender_id AS reply_sender_id,
            gmm.media_url AS reply_media_url,
            gmm.media_type AS reply_media_type,
            gmm.public_id AS reply_public_id
          FROM group_messages gm
          LEFT JOIN group_message_media gmm ON gm.id = gmm.message_id
          WHERE gm.id = $1
        `;
        const replyResult = await omniQuery(replyQuery, [replyId]);
        if (replyResult.rows.length > 0) {
          replyData = replyResult.rows[0];
          replySenderId = replyData.reply_sender_id;

          // Fetch reply sender info from main database if available
          if (replySenderId) {
            const replySenderQuery = `
              SELECT username FROM users WHERE id = $1
            `;
            const replySenderResult = await db.query(replySenderQuery, [replySenderId]);
            if (replySenderResult.rows.length > 0) {
              replyData.reply_sender_name = replySenderResult.rows[0].username;
            } else {
              replyData.reply_sender_name = 'Unknown User';
            }
          }
        }
      }

      // 5. Construct complete message object
      const completeMessage = {
        id: messageId,
        group_id: groupId,
        sender_id: senderId,
        message: message,
        sent_at: sent_at || new Date(),
        reply_id: replyId,
        sender_name: userData.username,
        sender_avatar: userData.avatar,
        reply_message: replyData?.reply_message || null,
        reply_sender_name: replyData?.reply_sender_name || null,
        reply_media_url: replyData?.reply_media_url || null,
        reply_media_type: replyData?.reply_media_type || null,
        reply_public_id: replyData?.reply_public_id || null,
        media: mediaData ? {
          mediaUrl: mediaData.media_url,
          mediaType: mediaData.media_type,
          publicId: mediaData.public_id,
          thumbnailUrl: mediaData.thumbnail_url,
          duration: mediaData.duration
        } : null
      };

      // 6. Cache update temporarily disabled
      logger.info(`Skipping cache update for new message in group ${groupId} (cache temporarily disabled)`);

      // 7. Return the complete message
      return completeMessage;
    } catch (error) {
      logger.error(`Error sending group message: ${error.message}`, { error });
      throw error;
    }
  }

  /**
   * Sends a system message to a group chat
   */
  async sendSystemMessage(groupId, message) {
    try {
      // 1. Store system message in AlloyDB Omni
      const query = `
        INSERT INTO group_messages
        (group_id, sender_id, message, sent_at, system_message)
        VALUES ($1, NULL, $2, NOW(), true)
        RETURNING id
      `;
      const result = await omniQuery(query, [groupId, message]);
      const messageId = result.rows[0].id;

      // 2. Construct system message object
      const systemMessage = {
        id: messageId,
        group_id: groupId,
        sender_id: null,
        message: message,
        sent_at: new Date(),
        system_message: true
      };

      // 3. Cache update temporarily disabled
      logger.info(`Skipping cache update for system message in group ${groupId} (cache temporarily disabled)`);

      return systemMessage;
    } catch (error) {
      logger.error(`Error sending system message: ${error.message}`, { error });
      throw error;
    }
  }

  /**
   * Gets messages for a group chat with pagination
   * Directly queries AlloyDB Omni (Valkey cache temporarily disabled)
   * Returns the newest messages first (DESC order by sent_at)
   */
  async getMessages(groupId, page = 1, pageSize = 100) {
    try {
      // Valkey cache temporarily disabled - always query the database directly
      logger.info(`Bypassing cache and querying database directly for group ${groupId}, page ${page}, pageSize ${pageSize}`);

      // For the initial load, we want the newest messages
      // No need for offset calculation as we'll use LIMIT directly

      // First, check if the deleted column exists
      const columnCheckQuery = `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'group_messages' AND column_name = 'deleted'
      `;
      const columnCheckResult = await omniQuery(columnCheckQuery, []);

      // Construct the query based on whether the deleted column exists
      let query;
      if (columnCheckResult.rows.length > 0) {
        // If the deleted column exists, exclude deleted messages
        query = `
          SELECT
            gm.id, gm.group_id, gm.sender_id, gm.message, gm.sent_at, gm.reply_id, gm.system_message,
            gm.deleted, gm.updated_at,
            gmm.media_url, gmm.media_type, gmm.public_id, gmm.thumbnail_url, gmm.duration,
            r.message AS reply_message,
            r.sender_id AS reply_sender_id,
            rmm.media_url AS reply_media_url,
            rmm.media_type AS reply_media_type,
            rmm.public_id AS reply_public_id
          FROM group_messages gm
          LEFT JOIN group_message_media gmm ON gm.id = gmm.message_id
          LEFT JOIN group_messages r ON gm.reply_id = r.id
          LEFT JOIN group_message_media rmm ON r.id = rmm.message_id
          WHERE gm.group_id = $1 AND (gm.deleted IS NULL OR gm.deleted = FALSE)
          ORDER BY gm.sent_at DESC
          LIMIT $2
        `;
      } else {
        // If the deleted column doesn't exist, use the original query
        query = `
          SELECT
            gm.id, gm.group_id, gm.sender_id, gm.message, gm.sent_at, gm.reply_id, gm.system_message,
            gmm.media_url, gmm.media_type, gmm.public_id, gmm.thumbnail_url, gmm.duration,
            r.message AS reply_message,
            r.sender_id AS reply_sender_id,
            rmm.media_url AS reply_media_url,
            rmm.media_type AS reply_media_type,
            rmm.public_id AS reply_public_id
          FROM group_messages gm
          LEFT JOIN group_message_media gmm ON gm.id = gmm.message_id
          LEFT JOIN group_messages r ON gm.reply_id = r.id
          LEFT JOIN group_message_media rmm ON r.id = rmm.message_id
          WHERE gm.group_id = $1
          ORDER BY gm.sent_at DESC
          LIMIT $2
        `;
      }

      // Construct the count query based on whether the deleted column exists
      let countQuery;
      if (columnCheckResult.rows.length > 0) {
        countQuery = `
          SELECT COUNT(*) FROM group_messages
          WHERE group_id = $1 AND (deleted IS NULL OR deleted = FALSE)
        `;
      } else {
        countQuery = `
          SELECT COUNT(*) FROM group_messages WHERE group_id = $1
        `;
      }

      // Execute queries to AlloyDB Omni - no offset needed for initial load
      const [messagesResult, countResult] = await Promise.all([
        omniQuery(query, [groupId, pageSize]),
        omniQuery(countQuery, [groupId])
      ]);

      // Get user information from main PostgreSQL database
      const senderIds = messagesResult.rows
        .map(row => row.sender_id)
        .filter(id => id !== null);

      const replySenderIds = messagesResult.rows
        .map(row => row.reply_sender_id)
        .filter(id => id !== null);

      // Combine all unique user IDs
      const uniqueUserIds = [...new Set([...senderIds, ...replySenderIds])];

      // If there are no users to fetch, skip the database query
      let userMap = {};
      if (uniqueUserIds.length > 0) {
        // Fetch user information from main PostgreSQL database
        const userQuery = `
          SELECT id, username, avatar
          FROM users
          WHERE id = ANY($1)
        `;
        const userResult = await db.query(userQuery, [uniqueUserIds]);

        // Create a map of user ID to user information
        userMap = userResult.rows.reduce((map, user) => {
          map[user.id] = user;
          return map;
        }, {});
      }

      const messages = messagesResult.rows.map(row => {
        // Get user info from the map
        const sender = row.sender_id ? userMap[row.sender_id] || {} : {};
        const replySender = row.reply_sender_id ? userMap[row.reply_sender_id] || {} : {};

        const message = {
          id: row.id,
          group_id: row.group_id,
          sender_id: row.sender_id,
          message: row.message,
          sent_at: row.sent_at,
          reply_id: row.reply_id,
          system_message: row.system_message,
          deleted: row.deleted || false,
          updated_at: row.updated_at,
          sender_name: sender.username || 'Unknown User',
          sender_avatar: sender.avatar || null,
          reply_message: row.reply_message,
          reply_sender_name: replySender.username || 'Unknown User',
          reply_media_url: row.reply_media_url,
          reply_media_type: row.reply_media_type,
          reply_public_id: row.reply_public_id
        };

        if (row.media_url) {
          message.media = {
            mediaUrl: row.media_url,
            mediaType: row.media_type,
            publicId: row.public_id,
            thumbnailUrl: row.thumbnail_url,
            duration: row.duration
          };
        }

        return message;
      });

      const totalCount = parseInt(countResult.rows[0].count);
      const hasMoreMessages = messages.length < totalCount;
      const remainingCount = totalCount - messages.length;

      // Cache update temporarily disabled
      if (page === 1 && messages.length > 0) {
        logger.info(`Skipping cache update for ${messages.length} messages in group ${groupId} (cache temporarily disabled)`);
      }

      logger.info(`Retrieved ${messages.length} messages for group ${groupId}, hasMoreMessages: ${hasMoreMessages}, remainingCount: ${remainingCount}, totalCount: ${totalCount}`);

      // For column-reverse layout, we need to reverse the order of messages
      // so that the newest message appears at the bottom after the CSS reverses it
      const reversedMessages = [...messages].reverse(); // Reverse to get oldest first

      logger.info(`Reversed message order for column-reverse layout (oldest first in array)`);

      return {
        messages: reversedMessages, // Now in ASC order (oldest first) for column-reverse layout
        hasMoreMessages,
        totalCount,
        remainingCount
      };
    } catch (error) {
      logger.error(`Error getting group messages: ${error.message}`, { error });
      throw error;
    }
  }



  /**
   * Gets messages for a group chat before a specific message ID
   * This is used for infinite scrolling pagination
   * Directly queries AlloyDB Omni (Valkey cache temporarily disabled)
   * @param {number} groupId - The group ID
   * @param {string} beforeMessageId - The message ID to get messages before
   * @param {number} pageSize - Number of messages to retrieve
   * @returns {Promise<Object>} - Object containing messages and hasMoreMessages flag
   */
  async getMessagesBefore(groupId, beforeMessageId, pageSize = 50) {
    try {
      logger.info(`Getting messages for group ${groupId} before message ${beforeMessageId}, limit: ${pageSize}`);

      // First get the reference message to find its timestamp
      const query = `
        SELECT sent_at FROM group_messages WHERE id = $1 AND group_id = $2
      `;

      const result = await omniQuery(query, [beforeMessageId, groupId]);

      if (result.rows.length === 0) {
        logger.warn(`Reference message ${beforeMessageId} not found for group ${groupId}`);
        return { messages: [], hasMoreMessages: false, totalCount: 0 };
      }

      const refTimestamp = result.rows[0].sent_at;

      // First, check if the deleted column exists
      const columnCheckQuery = `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'group_messages' AND column_name = 'deleted'
      `;
      const columnCheckResult = await omniQuery(columnCheckQuery, []);

      // Construct the query based on whether the deleted column exists
      let messagesQuery;
      if (columnCheckResult.rows.length > 0) {
        // If the deleted column exists, include it in the query
        messagesQuery = `
          SELECT
            gm.id, gm.group_id, gm.sender_id, gm.message, gm.sent_at, gm.reply_id, gm.system_message,
            gm.deleted, gm.updated_at,
            gmm.media_url, gmm.media_type, gmm.public_id, gmm.thumbnail_url, gmm.duration,
            r.message AS reply_message,
            r.sender_id AS reply_sender_id,
            rmm.media_url AS reply_media_url,
            rmm.media_type AS reply_media_type,
            rmm.public_id AS reply_public_id
          FROM group_messages gm
          LEFT JOIN group_message_media gmm ON gm.id = gmm.message_id
          LEFT JOIN group_messages r ON gm.reply_id = r.id
          LEFT JOIN group_message_media rmm ON r.id = rmm.message_id
          WHERE gm.group_id = $1 AND gm.sent_at < $2 AND (gm.deleted IS NULL OR gm.deleted = FALSE)
          ORDER BY gm.sent_at DESC
          LIMIT $3
        `;
      } else {
        // If the deleted column doesn't exist, use the original query
        messagesQuery = `
          SELECT
            gm.id, gm.group_id, gm.sender_id, gm.message, gm.sent_at, gm.reply_id, gm.system_message,
            gmm.media_url, gmm.media_type, gmm.public_id, gmm.thumbnail_url, gmm.duration,
            r.message AS reply_message,
            r.sender_id AS reply_sender_id,
            rmm.media_url AS reply_media_url,
            rmm.media_type AS reply_media_type,
            rmm.public_id AS reply_public_id
          FROM group_messages gm
          LEFT JOIN group_message_media gmm ON gm.id = gmm.message_id
          LEFT JOIN group_messages r ON gm.reply_id = r.id
          LEFT JOIN group_message_media rmm ON r.id = rmm.message_id
          WHERE gm.group_id = $1 AND gm.sent_at < $2
          ORDER BY gm.sent_at DESC
          LIMIT $3
        `;
      }

      // Construct the count queries based on whether the deleted column exists
      let countQuery, totalCountQuery;
      if (columnCheckResult.rows.length > 0) {
        countQuery = `
          SELECT COUNT(*) FROM group_messages
          WHERE group_id = $1 AND sent_at < $2 AND (deleted IS NULL OR deleted = FALSE)
        `;

        totalCountQuery = `
          SELECT COUNT(*) FROM group_messages
          WHERE group_id = $1 AND (deleted IS NULL OR deleted = FALSE)
        `;
      } else {
        countQuery = `
          SELECT COUNT(*) FROM group_messages WHERE group_id = $1 AND sent_at < $2
        `;

        totalCountQuery = `
          SELECT COUNT(*) FROM group_messages WHERE group_id = $1
        `;
      }

      // Execute queries
      const [messagesResult, countResult, totalCountResult] = await Promise.all([
        omniQuery(messagesQuery, [groupId, refTimestamp, pageSize]),
        omniQuery(countQuery, [groupId, refTimestamp]),
        omniQuery(totalCountQuery, [groupId])
      ]);

      // Get user information from main PostgreSQL database
      const senderIds = messagesResult.rows
        .map(row => row.sender_id)
        .filter(id => id !== null);

      const replySenderIds = messagesResult.rows
        .map(row => row.reply_sender_id)
        .filter(id => id !== null);

      // Combine all unique user IDs
      const uniqueUserIds = [...new Set([...senderIds, ...replySenderIds])];

      // If there are no users to fetch, skip the database query
      let userMap = {};
      if (uniqueUserIds.length > 0) {
        // Fetch user information from main PostgreSQL database
        const userQuery = `
          SELECT id, username, avatar
          FROM users
          WHERE id = ANY($1)
        `;
        const userResult = await db.query(userQuery, [uniqueUserIds]);

        // Create a map of user ID to user information
        userMap = userResult.rows.reduce((map, user) => {
          map[user.id] = user;
          return map;
        }, {});
      }

      // Map messages with user information
      const messages = messagesResult.rows.map(row => {
        // Get user info from the map
        const sender = row.sender_id ? userMap[row.sender_id] || {} : {};
        const replySender = row.reply_sender_id ? userMap[row.reply_sender_id] || {} : {};

        const message = {
          id: row.id,
          group_id: row.group_id,
          sender_id: row.sender_id,
          message: row.message,
          sent_at: row.sent_at,
          reply_id: row.reply_id,
          system_message: row.system_message,
          deleted: row.deleted || false,
          updated_at: row.updated_at,
          sender_name: sender.username || 'Unknown User',
          sender_avatar: sender.avatar || null,
          reply_message: row.reply_message,
          reply_sender_name: replySender.username || 'Unknown User',
          reply_media_url: row.reply_media_url,
          reply_media_type: row.reply_media_type,
          reply_public_id: row.reply_public_id
        };

        if (row.media_url) {
          message.media = {
            mediaUrl: row.media_url,
            mediaType: row.media_type,
            publicId: row.public_id,
            thumbnailUrl: row.thumbnail_url,
            duration: row.duration
          };
        }

        return message;
      });

      const remainingCount = parseInt(countResult.rows[0].count) - messages.length;
      const totalCount = parseInt(totalCountResult.rows[0].count);
      const hasMoreMessages = remainingCount > 0;

      logger.info(`Retrieved ${messages.length} messages before ${beforeMessageId} for group ${groupId}, hasMoreMessages: ${hasMoreMessages}, remainingCount: ${remainingCount}, totalCount: ${totalCount}`);

      // For column-reverse layout, we need to reverse the order of messages
      // so that the newest message appears at the bottom after the CSS reverses it
      const reversedMessages = [...messages].reverse(); // Reverse to get oldest first

      logger.info(`Reversed message order for column-reverse layout (oldest first in array)`);

      return {
        messages: reversedMessages, // Now in ASC order (oldest first) for column-reverse layout
        hasMoreMessages,
        totalCount,
        remainingCount
      };
    } catch (error) {
      logger.error(`Error getting messages before ${beforeMessageId} for group ${groupId}: ${error.message}`, { error });
      throw error;
    }
  }

  /**
   * Gets the last chat message for each group a user is in
   */
  async getLastChats(userId) {
    try {
      // Get all groups the user is a member of
      const groupsQuery = `
        SELECT group_id FROM group_members WHERE user_id = $1
      `;
      const groupsResult = await db.query(groupsQuery, [userId]);
      const groupIds = groupsResult.rows.map(row => row.group_id);

      if (groupIds.length === 0) {
        return [];
      }

      // For each group, get the last message from cache or database
      const lastChats = [];

      for (const groupId of groupIds) {
        // Cache temporarily disabled - always query the database
        logger.info(`Bypassing cache for last chat in group ${groupId} (cache temporarily disabled)`);

        // First, check if the deleted column exists
        const columnCheckQuery = `
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'group_messages' AND column_name = 'deleted'
        `;
        const columnCheckResult = await omniQuery(columnCheckQuery, []);

        // Construct the query based on whether the deleted column exists
        let query;
        if (columnCheckResult.rows.length > 0) {
          // If the deleted column exists, exclude deleted messages
          query = `
            SELECT
              gm.id, gm.group_id, gm.sender_id, gm.message, gm.sent_at,
              gm.deleted, gm.updated_at,
              u.username AS sender_name, u.avatar AS sender_avatar
            FROM group_messages gm
            LEFT JOIN users u ON gm.sender_id = u.id
            WHERE gm.group_id = $1 AND (gm.deleted IS NULL OR gm.deleted = FALSE)
            ORDER BY gm.sent_at DESC
            LIMIT 1
          `;
        } else {
          // If the deleted column doesn't exist, use the original query
          query = `
            SELECT
              gm.id, gm.group_id, gm.sender_id, gm.message, gm.sent_at,
              u.username AS sender_name, u.avatar AS sender_avatar
            FROM group_messages gm
            LEFT JOIN users u ON gm.sender_id = u.id
            WHERE gm.group_id = $1
            ORDER BY gm.sent_at DESC
            LIMIT 1
          `;
        }

        const result = await omniQuery(query, [groupId]);

        if (result.rows.length > 0) {
          lastChats.push(result.rows[0]);
        }
      }

      return lastChats;
    } catch (error) {
      logger.error(`Error getting last chats: ${error.message}`, { error });
      throw error;
    }
  }

  /**
   * Edits a message in a group chat
   * @param {number} messageId - The ID of the message to edit
   * @param {number} userId - The ID of the user editing the message
   * @param {string} newContent - The new content of the message
   * @returns {Promise<Object>} - The updated message
   */
  async editMessage(messageId, userId, newContent) {
    try {
      logger.info(`Editing message ${messageId} with new content: ${newContent}`);

      // Check if the message exists and belongs to the user
      const checkQuery = `
        SELECT * FROM group_messages WHERE id = $1
      `;
      const checkResult = await omniQuery(checkQuery, [messageId]);

      if (checkResult.rows.length === 0) {
        throw new Error('Message not found');
      }

      const existingMessage = checkResult.rows[0];

      // Check if the user is the sender of the message
      if (existingMessage.sender_id !== userId) {
        throw new Error('You can only edit your own messages');
      }

      // Check for profanity
      if (newContent && filter.isProfane(newContent)) {
        throw new Error('Message contains inappropriate content');
      }

      // Update the message
      const updateQuery = `
        UPDATE group_messages
        SET message = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING *
      `;
      const updateResult = await omniQuery(updateQuery, [newContent, messageId]);
      const updatedMessage = updateResult.rows[0];

      // Get user information
      const userQuery = `
        SELECT username, avatar FROM users WHERE id = $1
      `;
      const userResult = await db.query(userQuery, [userId]);
      const userData = userResult.rows[0];

      // Get media information if any
      const mediaQuery = `
        SELECT * FROM group_message_media WHERE message_id = $1
      `;
      const mediaResult = await omniQuery(mediaQuery, [messageId]);
      const mediaData = mediaResult.rows[0];

      // Construct complete message object
      const completeMessage = {
        id: updatedMessage.id,
        group_id: updatedMessage.group_id,
        sender_id: updatedMessage.sender_id,
        message: updatedMessage.message,
        sent_at: updatedMessage.sent_at,
        updated_at: updatedMessage.updated_at,
        reply_id: updatedMessage.reply_id,
        sender_name: userData.username,
        sender_avatar: userData.avatar
      };

      if (mediaData) {
        completeMessage.media = {
          mediaUrl: mediaData.media_url,
          mediaType: mediaData.media_type,
          publicId: mediaData.public_id,
          thumbnailUrl: mediaData.thumbnail_url,
          duration: mediaData.duration
        };
      }

      // Cache update temporarily disabled
      logger.info(`Skipping cache update for edited message ${messageId} (cache temporarily disabled)`);

      return completeMessage;
    } catch (error) {
      logger.error(`Error editing message: ${error.message}`, { error });
      throw error;
    }
  }

  /**
   * Deletes a message from a group chat (soft delete)
   * @param {number} messageId - The ID of the message to delete
   * @param {number} userId - The ID of the user deleting the message
   * @returns {Promise<Object>} - The deleted message ID and group ID
   */
  async deleteMessage(messageId, userId) {
    try {
      logger.info(`Deleting message ${messageId}`);

      // Check if the message exists and belongs to the user
      const checkQuery = `
        SELECT * FROM group_messages WHERE id = $1
      `;
      const checkResult = await omniQuery(checkQuery, [messageId]);

      if (checkResult.rows.length === 0) {
        throw new Error('Message not found');
      }

      const existingMessage = checkResult.rows[0];

      // Check if the user is the sender of the message
      if (existingMessage.sender_id !== userId) {
        throw new Error('You can only delete your own messages');
      }

      // Soft delete the message by setting a deleted flag
      // First, check if the deleted column exists
      const columnCheckQuery = `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'group_messages' AND column_name = 'deleted'
      `;
      const columnCheckResult = await omniQuery(columnCheckQuery, []);

      // If the deleted column doesn't exist, add it
      if (columnCheckResult.rows.length === 0) {
        logger.info('Adding deleted column to group_messages table');
        const addColumnQuery = `
          ALTER TABLE group_messages ADD COLUMN deleted BOOLEAN DEFAULT FALSE
        `;
        await omniQuery(addColumnQuery, []);
      }

      // Now update the message to mark it as deleted
      const updateQuery = `
        UPDATE group_messages
        SET deleted = TRUE, updated_at = NOW()
        WHERE id = $1
      `;
      await omniQuery(updateQuery, [messageId]);

      // Cache update temporarily disabled
      logger.info(`Skipping cache update for deleted message ${messageId} (cache temporarily disabled)`);

      return {
        messageId,
        groupId: existingMessage.group_id
      };
    } catch (error) {
      logger.error(`Error deleting message: ${error.message}`, { error });
      throw error;
    }
  }
}

module.exports = new EnhancedGroupChatService();

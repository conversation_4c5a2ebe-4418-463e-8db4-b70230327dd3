// frontend/src/services/firebaseService.js
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  GoogleAuthProvider,
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
  sendPasswordResetEmail,
  sendEmailVerification,
  updateProfile,
  onAuthStateChanged
} from 'firebase/auth';
import { auth } from '../config/firebase.config';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

/**
 * Register a new user with email and password
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @param {string} username - User's chosen username
 * @param {string} avatar - User's avatar URL
 * @returns {Promise} - Firebase user and custom token
 */
export const registerWithEmailPassword = async (email, password, username, avatar) => {
  try {
    // Validate inputs
    if (!email || !password || !username) {
      throw new Error('Email, password, and username are required');
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      throw new Error('Username must be 3-20 characters and contain only letters, numbers, and underscores');
    }

    // Check if username is available before creating Firebase user
    console.log('Checking if username is available:', username);
    const isAvailable = await checkUsernameAvailability(username);
    if (!isAvailable) {
      throw new Error('Username already exists. Please choose a different username.');
    }

    console.log('Creating new user in Firebase with email:', email);

    // Create user in Firebase
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    console.log('User created in Firebase:', user.uid);

    // Update Firebase profile with username
    await updateProfile(user, {
      displayName: username,
      photoURL: avatar || null
    });

    console.log('Firebase profile updated with username:', username);

    // Get Firebase ID token
    const idToken = await user.getIdToken();

    // Register user in our backend
    console.log('Registering user in backend with username:', username);
    const response = await axios.post(`${API_BASE_URL}/api/auth/firebase-register`, {
      firebaseUid: user.uid,
      email: user.email,
      username,
      avatar
    }, {
      headers: {
        Authorization: `Bearer ${idToken}`
      }
    });

    console.log('User registered successfully in backend');

    return {
      user: response.data.user,
      token: idToken,
      ...response.data
    };
  } catch (error) {
    console.error('Registration error:', error);

    // Handle Firebase-specific errors with user-friendly messages
    if (error.code === 'auth/email-already-in-use') {
      throw new Error('This email is already registered. Please use a different email or sign in.');
    } else if (error.code === 'auth/weak-password') {
      throw new Error('Password is too weak. Please use a stronger password.');
    } else if (error.code === 'auth/invalid-email') {
      throw new Error('Invalid email address. Please check your email and try again.');
    }

    throw error;
  }
};

/**
 * Sign in with email and password
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @returns {Promise} - Firebase user and custom token
 */
export const loginWithEmailPassword = async (email, password) => {
  try {
    // Validate inputs
    if (!email || !password) {
      throw new Error('Email and password are required');
    }

    console.log('Attempting to sign in with email and password');

    // Sign in with Firebase
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    console.log('Firebase authentication successful for user:', user.uid);

    // Get Firebase ID token
    const idToken = await user.getIdToken();

    // Authenticate with our backend
    console.log('Authenticating with backend');
    const response = await axios.post(`${API_BASE_URL}/api/auth/firebase-login`, {
      firebaseUid: user.uid
    }, {
      headers: {
        Authorization: `Bearer ${idToken}`
      }
    });

    console.log('Backend authentication successful');

    return {
      user: response.data.user,
      token: idToken,
      ...response.data
    };
  } catch (error) {
    console.error('Login error:', error);

    // Handle Firebase-specific errors with user-friendly messages
    if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
      throw new Error('Invalid email or password. Please check your credentials and try again.');
    } else if (error.code === 'auth/too-many-requests') {
      throw new Error('Too many failed login attempts. Please try again later or reset your password.');
    } else if (error.code === 'auth/user-disabled') {
      throw new Error('This account has been disabled. Please contact support for assistance.');
    } else if (error.code === 'auth/invalid-email') {
      throw new Error('Invalid email address. Please check your email and try again.');
    }

    throw error;
  }
};

/**
 * Sign in with Google
 * @returns {Promise} - Firebase user and custom token
 */
export const signInWithGoogle = async () => {
  try {
    // Create Google auth provider with proper scopes
    const provider = new GoogleAuthProvider();
    provider.addScope('profile');
    provider.addScope('email');

    // Always show account selection dialog
    provider.setCustomParameters({
      prompt: 'select_account'
    });

    // Try popup sign-in with fallback to redirect if needed
    let userCredential;
    try {
      console.log('Attempting Google sign-in with popup...');
      userCredential = await signInWithPopup(auth, provider);
    } catch (popupError) {
      console.warn('Popup sign-in failed:', popupError.code);

      // Handle specific error cases with user-friendly messages
      if (popupError.code === 'auth/popup-blocked') {
        console.log('Popup blocked, falling back to redirect method');
        // Start redirect sign-in flow as fallback
        await signInWithRedirect(auth, provider);
        return; // Function will resume after redirect
      } else if (popupError.code === 'auth/network-request-failed') {
        throw new Error('Network error. Please check your internet connection and try again.');
      } else if (popupError.code === 'auth/popup-closed-by-user') {
        throw new Error('Sign-in was cancelled. Please try again.');
      } else {
        throw popupError;
      }
    }

    // Check for redirect result (in case we used redirect method)
    if (!userCredential) {
      try {
        userCredential = await getRedirectResult(auth);
        if (!userCredential) {
          // No redirect result, user may have just started the redirect flow
          return;
        }
      } catch (redirectError) {
        console.error('Error getting redirect result:', redirectError);
        throw redirectError;
      }
    }

    // At this point we have a successful sign-in
    const user = userCredential.user;
    console.log('Google sign-in successful, user:', user.uid);

    // Get Firebase ID token for backend authentication
    const idToken = await user.getIdToken();

    // Check if this is a new user
    const isNewUser = userCredential.additionalUserInfo?.isNewUser || false;
    console.log('Is new user:', isNewUser);

    // For Google sign-ins, we always want to prompt for username selection
    // This ensures users choose their own username rather than using Google's
    console.log('Requiring username selection for Google user');

    // Return user info with needsUsername flag
    return {
      user,
      isNewUser,
      needsUsername: true, // Always require username selection for Google users
      token: idToken
    };

    // The code below is intentionally unreachable - we're keeping it for reference
    try {
      console.log('Authenticating with backend...');
      // Try to authenticate with our backend
      const response = await axios.post(`${API_BASE_URL}/api/auth/firebase-login`, {
        firebaseUid: user.uid
      }, {
        headers: {
          Authorization: `Bearer ${idToken}`
        }
      });

      console.log('Backend authentication successful');

      // Check if the account was linked with an existing email
      if (response.data.wasLinked) {
        return {
          user,
          isNewUser: false,
          wasLinked: true,
          ...response.data
        };
      }

      // Always require username selection for Google sign-in
      // This ensures users choose a custom username for the application
      return {
        user,
        isNewUser: false,
        needsUsername: true,  // Always require username selection
        ...response.data
      };
    } catch (error) {
      console.warn('Backend authentication error:', error);

      // If user doesn't exist in our database, they need to create a username
      if (error.response && error.response.status === 404) {
        console.log('User needs to create a username');
        return {
          user,
          isNewUser: true,
          needsUsername: true
        };
      }

      // If there's an email conflict that couldn't be automatically resolved
      if (error.response && error.response.status === 400 &&
          error.response.data.error &&
          error.response.data.error.includes('Email already exists')) {
        console.log('Email conflict detected');
        return {
          user,
          isNewUser: false,
          emailConflict: true,
          error: 'This email is already registered. Please sign in with your existing account.'
        };
      }

      throw error;
    }
  } catch (error) {
    console.error('Google sign-in error:', error);
    throw error;
  }
};

/**
 * Complete Google sign-up by setting a username and handle
 * @param {Object} user - Firebase user
 * @param {string} username - User's chosen username
 * @param {string} handle - User's chosen handle (display name)
 * @param {string} avatar - User's avatar URL
 * @returns {Promise} - Backend response
 */
export const completeGoogleSignUp = async (user, username, handle, avatar) => {
  try {
    console.log('Completing Google sign-up with username:', username);

    // Validate inputs
    if (!user) {
      throw new Error('No Firebase user provided');
    }

    if (!username) {
      throw new Error('Username is required');
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      throw new Error('Username must be 3-20 characters and contain only letters, numbers, and underscores');
    }

    // Check if username is available before proceeding
    console.log('Checking if username is available:', username);

    try {
      const isAvailable = await checkUsernameAvailability(username);
      if (!isAvailable) {
        throw new Error('Username already exists. Please choose a different username.');
      }
    } catch (availabilityError) {
      console.warn('Error checking username availability, proceeding anyway:', availabilityError);
      // If we can't check availability due to server issues, we'll proceed anyway
      // The backend will do a final check during registration
    }

    // Get a fresh Firebase ID token
    const idToken = await user.getIdToken(true);

    console.log('Registering user with backend using custom username:', username);

    try {
      // Register or update user in our backend with the CUSTOM username
      const response = await axios.post(`${API_BASE_URL}/api/auth/firebase-register`, {
        username: username, // Use the custom username provided by the user
        avatar: avatar || user.photoURL
      }, {
        headers: {
          Authorization: `Bearer ${idToken}`
        }
      });

      console.log('Backend registration successful with username:', username);

      // Update Firebase profile with the custom username
      await updateProfile(user, {
        displayName: username // Use the custom username, not the Google name
      });

      console.log('Firebase profile updated with custom username');

      return {
        user: response.data.user,
        token: idToken,
        ...response.data
      };
    } catch (apiError) {
      console.error('API error during registration:', apiError);

      // If we get a 400 error with "Email already exists" message
      if (apiError.response &&
          apiError.response.status === 400 &&
          apiError.response.data &&
          apiError.response.data.message &&
          apiError.response.data.message.includes('Email already exists')) {

        console.log('Email already exists error, trying to login and update username');

        try {
          // Try to login with Firebase token
          const loginResponse = await axios.post(`${API_BASE_URL}/api/auth/firebase-login`, {}, {
            headers: {
              Authorization: `Bearer ${idToken}`
            }
          });

          console.log('Login successful:', loginResponse.data);

          // Now try to update the username
          let updateResponse;
          try {
            // First try the /api/auth/update-username endpoint
            updateResponse = await axios.post(`${API_BASE_URL}/api/auth/update-username`, {
              username: username
            }, {
              headers: {
                Authorization: `Bearer ${idToken}`
              }
            });
          } catch (updateError) {
            console.warn('Error with /api/auth/update-username, trying /api/users/update-username:', updateError);

            // If that fails, try the /api/users/update-username endpoint
            updateResponse = await axios.post(`${API_BASE_URL}/api/users/update-username`, {
              username: username
            }, {
              headers: {
                Authorization: `Bearer ${idToken}`
              }
            });
          }

          console.log('Username update successful:', updateResponse.data);

          // Update Firebase profile with the custom username
          await updateProfile(user, {
            displayName: username
          });

          return {
            user: updateResponse.data.user || loginResponse.data.user,
            token: idToken,
            message: 'Username updated successfully'
          };
        } catch (fallbackError) {
          console.error('Fallback login/update failed:', fallbackError);
          throw fallbackError;
        }
      }

      // Re-throw the original error
      throw apiError;
    }
  } catch (error) {
    console.error('Complete Google sign-up error:', error);

    // Handle specific error cases
    if (error.response) {
      if (error.response.status === 409) {
        throw new Error('Username already exists. Please choose a different username.');
      } else if (error.response.data && error.response.data.message) {
        throw new Error(error.response.data.message);
      }
    }

    // Handle Firebase-specific errors
    if (error.code === 'auth/requires-recent-login') {
      throw new Error('For security reasons, please sign in again before updating your profile.');
    }

    throw error;
  }
};

/**
 * Sign out the current user
 * @returns {Promise} - Void
 */
export const logout = async () => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Logout error:', error);
    throw error;
  }
};

/**
 * Send password reset email
 * @param {string} email - User's email
 * @returns {Promise} - Void
 */
export const resetPassword = async (email) => {
  try {
    // Validate email
    if (!email) {
      throw new Error('Email is required');
    }

    console.log('Sending password reset email to:', email);

    // Configure password reset settings
    const actionCodeSettings = {
      url: `${window.location.origin}/signin?email=${encodeURIComponent(email)}`,
      handleCodeInApp: false
    };

    // Send password reset email
    await sendPasswordResetEmail(auth, email, actionCodeSettings);

    console.log('Password reset email sent successfully');
  } catch (error) {
    console.error('Password reset error:', error);

    // Handle Firebase-specific errors
    if (error.code === 'auth/user-not-found') {
      // For security reasons, don't reveal if the email exists or not
      // Just pretend it worked to prevent email enumeration attacks
      console.log('User not found, but not revealing this to the user');
      return;
    } else if (error.code === 'auth/invalid-email') {
      throw new Error('Invalid email address. Please check your email and try again.');
    } else if (error.code === 'auth/too-many-requests') {
      throw new Error('Too many requests. Please try again later.');
    }

    throw error;
  }
};

// checkUsernameAvailability function is now defined below

/**
 * Get current authenticated user
 * @returns {Object|null} - Firebase user or null
 */
export const getCurrentUser = () => {
  return auth.currentUser;
};

/**
 * Check if a username is available
 * @param {string} username - Username to check
 * @returns {Promise<boolean>} - Whether the username is available
 */
export const checkUsernameAvailability = async (username) => {
  try {
    console.log('Checking username availability for:', username);

    // Basic validation first
    if (!username || username.length < 3) {
      console.log('Username too short, returning false');
      return false;
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      console.log('Username format invalid, returning false');
      return false;
    }

    // Get the API base URL from environment variables
    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

    // Get current Firebase user's ID token if available
    let token = '';
    if (auth.currentUser) {
      token = await auth.currentUser.getIdToken();
    }

    try {
      // Make request to backend to check username availability
      const response = await axios.post(
        `${API_BASE_URL}/api/auth/check-username`,
        { username },
        {
          headers: token ? { Authorization: `Bearer ${token}` } : {},
          timeout: 5000 // Add a timeout to prevent long waits
        }
      );

      console.log('Username availability response:', response.data);

      // Return whether the username is available
      return response.data.available;
    } catch (networkError) {
      console.error('Network error checking username availability:', networkError);

      // If we can't reach the server, fall back to client-side validation only
      console.log('Falling back to client-side validation only');

      // For network errors, we'll assume the username is valid if it passes regex
      // This allows users to continue even if the backend is temporarily unavailable
      return true;
    }
  } catch (error) {
    console.error('Error in checkUsernameAvailability:', error);

    // If the backend returns a 409 status, the username is taken
    if (error.response && error.response.status === 409) {
      return false;
    }

    // For other errors, assume the username is not available to be safe
    return false;
  }
};

/**
 * Get ID token for current user
 * @param {boolean} forceRefresh - Whether to force a token refresh
 * @returns {Promise<string>} - Firebase ID token
 */
export const getIdToken = async (forceRefresh = false) => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('No user is signed in');
  }
  return await user.getIdToken(forceRefresh);
};

/**
 * Set up an auth state change listener
 * @param {Function} callback - Callback function to be called when auth state changes
 * @returns {Function} - Unsubscribe function
 */
export const onAuthStateChange = (callback) => {
  return onAuthStateChanged(auth, (user) => {
    callback(user);
  });
};

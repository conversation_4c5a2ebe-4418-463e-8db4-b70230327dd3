// test-db-connection.js
require('dotenv').config();
const { Client } = require('pg');
const fs = require('fs');

// Read the SSL certificate from the ca.pem file
const caCert = fs.readFileSync('./ca.pem').toString();

// Database configuration
const client = new Client({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  ssl: {
    rejectUnauthorized: true,
    ca: caCert, // Use the certificate from the file
  },
});

// Connect to the database
console.log('Attempting to connect to database with the following settings:');
console.log(`Host: ${process.env.DB_HOST}`);
console.log(`Port: ${process.env.DB_PORT}`);
console.log(`Database: ${process.env.DB_NAME}`);
console.log(`User: ${process.env.DB_USER}`);
console.log('Password: [HIDDEN]');

client.connect()
  .then(() => {
    console.log('✅ Database connected successfully!');
    // Execute a simple query to verify the connection
    return client.query('SELECT NOW() as current_time');
  })
  .then(result => {
    console.log('Query result:', result.rows[0]);
    client.end();
  })
  .catch(err => {
    console.error('❌ Database connection error:', err);
    process.exit(1);
  });

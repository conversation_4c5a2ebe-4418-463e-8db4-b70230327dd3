<template>
  <Teleport to="body">
    <div v-if="isVisible" class="fps-counter" :class="{ 'low-fps': fps < 30 }">
      <div class="fps-header">
        <span class="fps-title">Debug Menu</span>
        <div class="header-controls">
          <button class="copy-button" @click="copyDebugInfo" title="Copy debug info">📋</button>
          <button class="toggle-button" @click="toggleExpanded">{{ expanded ? '▼' : '▲' }}</button>
        </div>
      </div>
      <div v-if="expanded" class="fps-details">
        <div class="section-title">Performance</div>
        <div class="fps-row">
          <span class="fps-label">FPS:</span>
          <span class="fps-value" :class="{ 'warning': fps < 30, 'good': fps >= 50 }">{{ fps.toFixed(1) }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Min FPS:</span>
          <span class="fps-value" :class="{ 'warning': minFps < 30, 'good': minFps >= 50 }">{{ minFps === Infinity ? 'N/A' : minFps.toFixed(1) }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Max FPS:</span>
          <span class="fps-value" :class="{ 'warning': maxFps < 30, 'good': maxFps >= 50 }">{{ maxFps.toFixed(1) }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Avg FPS:</span>
          <span class="fps-value" :class="{ 'warning': avgFps < 30, 'good': avgFps >= 50 }">{{ avgFps.toFixed(1) }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Frame Time:</span>
          <span class="fps-value">{{ frameTime.toFixed(2) }}ms</span>
        </div>

        <div class="section-title">HTML Chat</div>
        <div class="fps-row">
          <span class="fps-label">FPS:</span>
          <span class="fps-value" :class="{ 'warning': htmlChatFps < 30, 'good': htmlChatFps >= 50 }">{{ htmlChatFps }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Messages:</span>
          <span class="fps-value">{{ messageCount }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Visible:</span>
          <span class="fps-value">{{ visibleCount }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Scroll %:</span>
          <span class="fps-value">{{ scrollPercentage }}%</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Direction:</span>
          <span class="fps-value">{{ scrollDirection }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">At Bottom:</span>
          <span class="fps-value">{{ isAtBottom ? 'Yes' : 'No' }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Range:</span>
          <span class="fps-value">{{ visibleRangeStart }} - {{ visibleRangeEnd }}</span>
        </div>

        <div class="section-title">System</div>
        <div class="fps-row">
          <span class="fps-label">Memory:</span>
          <span class="fps-value">{{ memoryUsage }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Device:</span>
          <span class="fps-value">{{ deviceInfo }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">Screen:</span>
          <span class="fps-value">{{ screenInfo }}</span>
        </div>
        <div class="fps-row">
          <span class="fps-label">DPR:</span>
          <span class="fps-value">{{ devicePixelRatio }}</span>
        </div>

        <div class="fps-controls">
          <button class="reset-button" @click="resetStats">Reset Stats</button>
          <button class="close-button" @click="isVisible = false">Close</button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

const props = defineProps({
  messageCount: {
    type: Number,
    default: 0
  },
  visibleCount: {
    type: Number,
    default: 0
  },
  scrollPercentage: {
    type: Number,
    default: 0
  },
  scrollDirection: {
    type: String,
    default: 'none'
  },
  isAtBottom: {
    type: Boolean,
    default: true
  },
  visibleRangeStart: {
    type: Number,
    default: 0
  },
  visibleRangeEnd: {
    type: Number,
    default: 0
  },
  topSpacerHeight: {
    type: Number,
    default: 0
  },
  bottomSpacerHeight: {
    type: Number,
    default: 0
  },
  htmlChatFps: {
    type: Number,
    default: 0
  }
});

// FPS tracking variables
const fps = ref(0);
const frames = ref(0);
const lastTime = ref(performance.now());
const times = ref([]);
const isVisible = ref(true); // Start visible by default
const expanded = ref(true);
const frameTime = ref(0); // Average frame time in ms

// Stats
const minFps = ref(Infinity);
const maxFps = ref(0);
const totalFps = ref(0);
const fpsReadings = ref(0);

// System info
const memoryUsage = ref('N/A');
const deviceInfo = ref('Unknown');
const screenInfo = ref('Unknown');
const devicePixelRatio = ref(window.devicePixelRatio || 1);

// Computed average FPS
const avgFps = computed(() => {
  return fpsReadings.value > 0 ? totalFps.value / fpsReadings.value : 0;
});

// Toggle expanded state
const toggleExpanded = () => {
  expanded.value = !expanded.value;
};

// Reset statistics
const resetStats = () => {
  minFps.value = Infinity;
  maxFps.value = 0;
  totalFps.value = 0;
  fpsReadings.value = 0;
  times.value = [];
  frameTime.value = 0;
};

// Copy debug info to clipboard
const copyDebugInfo = () => {
  const debugInfo = {
    performance: {
      fps: fps.value.toFixed(1),
      minFps: minFps.value === Infinity ? 'N/A' : minFps.value.toFixed(1),
      maxFps: maxFps.value.toFixed(1),
      avgFps: avgFps.value.toFixed(1),
      frameTime: frameTime.value.toFixed(2) + 'ms'
    },
    htmlChat: {
      fps: props.htmlChatFps,
      messageCount: props.messageCount,
      visibleCount: props.visibleCount,
      scrollPercentage: props.scrollPercentage + '%',
      scrollDirection: props.scrollDirection,
      isAtBottom: props.isAtBottom ? 'Yes' : 'No',
      visibleRange: `${props.visibleRangeStart} - ${props.visibleRangeEnd}`,
      topSpacerHeight: props.topSpacerHeight + 'px',
      bottomSpacerHeight: props.bottomSpacerHeight + 'px'
    },
    system: {
      memory: memoryUsage.value,
      device: deviceInfo.value,
      screen: screenInfo.value,
      devicePixelRatio: devicePixelRatio.value,
      userAgent: navigator.userAgent
    },
    timestamp: new Date().toISOString()
  };

  const debugText = JSON.stringify(debugInfo, null, 2);
  navigator.clipboard.writeText(debugText)
    .then(() => {
      alert('Debug info copied to clipboard!');
    })
    .catch(err => {
      console.error('Failed to copy debug info:', err);
      alert('Failed to copy debug info. See console for details.');
    });
};

// Calculate FPS
const calculateFps = () => {
  frames.value++;

  const now = performance.now();
  const elapsed = now - lastTime.value;

  // Update FPS every 500ms
  if (elapsed >= 500) {
    fps.value = (frames.value * 1000) / elapsed;
    frameTime.value = elapsed / frames.value;

    // Update stats
    minFps.value = Math.min(minFps.value, fps.value);
    maxFps.value = Math.max(maxFps.value, fps.value);
    totalFps.value += fps.value;
    fpsReadings.value++;

    // Reset for next calculation
    frames.value = 0;
    lastTime.value = now;

    // Keep a history of recent FPS values for potential graphing
    times.value.push(fps.value);
    if (times.value.length > 60) { // Keep last 60 readings (30 seconds at 500ms intervals)
      times.value.shift();
    }

    // Update memory usage if available
    updateMemoryUsage();
  }

  // Request next frame
  requestAnimationFrame(calculateFps);
};

// Update memory usage if performance.memory is available (Chrome only)
const updateMemoryUsage = () => {
  if (window.performance && window.performance.memory) {
    const memory = window.performance.memory;
    const usedHeapSize = (memory.usedJSHeapSize / 1048576).toFixed(2); // Convert to MB
    const totalHeapSize = (memory.totalJSHeapSize / 1048576).toFixed(2); // Convert to MB
    memoryUsage.value = `${usedHeapSize}MB / ${totalHeapSize}MB`;
  } else {
    memoryUsage.value = 'Not available';
  }
};

// Get device and screen info
const getSystemInfo = () => {
  // Device info
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  deviceInfo.value = isMobile ? 'Mobile' : 'Desktop';

  // Screen info
  screenInfo.value = `${window.innerWidth}x${window.innerHeight}`;

  // Device pixel ratio
  devicePixelRatio.value = window.devicePixelRatio || 1;
};

// Lifecycle hooks
onMounted(() => {
  // Start FPS calculation
  requestAnimationFrame(calculateFps);

  // Get system info
  getSystemInfo();

  // Update system info on resize
  window.addEventListener('resize', getSystemInfo);
});

onUnmounted(() => {
  // Clean up
  isVisible.value = false;
  window.removeEventListener('resize', getSystemInfo);
});
</script>

<style scoped>
.fps-counter {
  position: fixed;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.85);
  color: white;
  border-radius: 6px;
  padding: 10px;
  font-family: monospace;
  font-size: 11px;
  z-index: 99999; /* Increased z-index to ensure it's on top */
  min-width: 180px;
  max-width: 300px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease;
  pointer-events: auto; /* Ensure it can receive mouse events */
  max-height: 90vh;
  overflow-y: auto;
}

.fps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-controls {
  display: flex;
  gap: 8px;
}

.fps-title {
  font-weight: bold;
  font-size: 12px;
  color: #4fc3f7;
}

.toggle-button, .copy-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0 4px;
  font-size: 12px;
  transition: transform 0.2s ease;
}

.toggle-button:hover, .copy-button:hover {
  transform: scale(1.1);
}

.fps-details {
  margin-top: 8px;
}

.section-title {
  font-weight: bold;
  color: #4fc3f7;
  margin: 12px 0 6px 0;
  padding-bottom: 3px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.fps-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  padding: 2px 0;
}

.fps-label {
  margin-right: 8px;
  color: rgba(255, 255, 255, 0.8);
}

.fps-value {
  font-weight: bold;
}

.fps-value.warning {
  color: #ffab40;
}

.fps-value.good {
  color: #69f0ae;
}

.low-fps {
  background-color: rgba(255, 50, 50, 0.7);
}

.fps-controls {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.reset-button, .close-button {
  background-color: rgba(50, 50, 50, 0.8);
  border: none;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  transition: background-color 0.2s ease;
}

.reset-button:hover {
  background-color: #2196f3;
}

.close-button:hover {
  background-color: #f44336;
}

/* Scrollbar styling */
.fps-counter::-webkit-scrollbar {
  width: 6px;
}

.fps-counter::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.fps-counter::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.fps-counter::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>

.media-content {
  margin: 8px 0;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.image-container {
  position: relative;
  max-width: 300px;
  max-height: 300px;
}

.media-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: 300px;
  object-fit: cover;
  border-radius: 8px;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.media-image.loaded {
  opacity: 1;
}

.video-container {
  position: relative;
  max-width: 400px;
}

.media-video {
  width: 100%;
  max-height: 300px;
  border-radius: 8px;
}

.audio-container {
  min-width: 250px;
}

.media-audio {
  width: 100%;
  height: 40px;
}

.file-container {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  padding: 12px;
}

.file-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.file-size {
  font-size: 0.75rem;
  opacity: 0.7;
}

.media-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 8px;
  padding: 20px;
  background: rgba(0, 123, 255, 0.05);
  border: 2px dashed rgba(0, 123, 255, 0.3);
  border-radius: 8px;
  min-height: 120px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  width: 100%;
  max-width: 300px;
}

.media-placeholder:hover {
  background: rgba(0, 123, 255, 0.1);
  border-color: rgba(0, 123, 255, 0.5);
  transform: translateY(-2px);
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  text-align: center;
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: 4px;
}

.placeholder-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: #007bff;
}

.placeholder-name {
  font-size: 0.8rem;
  color: #666;
  font-weight: 400;
}

.placeholder-size,
.placeholder-duration {
  font-size: 0.7rem;
  color: #999;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 8px;
  border-radius: 8px;
  z-index: 10;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 8px;
  padding: 20px;
  background: rgba(220, 53, 69, 0.1);
  border-radius: 8px;
  min-height: 100px;
  color: #dc3545;
}

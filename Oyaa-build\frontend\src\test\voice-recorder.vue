<template>
    <div class="voice-recorder" :class="{ 'recording': isRecording }">
      <div class="recorder-content">
        <!-- Recording visualization -->
        <div class="visualization">
          <div 
            v-for="(bar, index) in visualizationBars" 
            :key="index" 
            class="bar"
            :style="{ height: `${bar}px` }"
          ></div>
        </div>
        
        <!-- Timer -->
        <div class="timer">{{ formattedTime }}</div>
        
        <!-- Controls -->
        <div class="controls">
          <button 
            class="control-button cancel-button" 
            @click="cancelRecording" 
            title="Cancel"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          
          <button 
            class="control-button record-button" 
            @click="toggleRecording" 
            :title="recordButtonTitle"
          >
            <!-- Pause Icon (when recording and not paused) -->
            <svg v-if="isRecording && !isPaused" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="6" y="4" width="4" height="16"></rect>
              <rect x="14" y="4" width="4" height="16"></rect>
            </svg>
            <!-- Resume Icon (when recording and paused) -->
            <svg v-else-if="isRecording && isPaused" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
            <!-- Record Icon (when not recording) -->
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="3" fill="currentColor"></circle>
            </svg>
          </button>
          
          <button 
            v-if="isRecording" 
            class="control-button stop-button" 
            @click="stopRecording" 
            title="Stop"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="6" y="6" width="12" height="12" rx="2"></rect>
            </svg>
          </button>
          
          <button 
            v-if="hasRecording"
            class="control-button send-button" 
            @click="sendRecording" 
            title="Send"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m22 2-7 20-4-9-9-4Z"></path>
              <path d="M22 2 11 13"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
</template>
  
  <script setup>
  import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
  
  const props = defineProps({
    onClose: { type: Function, required: true },
    onSend: { type: Function, required: true }
  });
  
  // State
  const isRecording = ref(false);
  const isPaused = ref(false); // New state for paused
  const recordingTime = ref(0);
  const hasRecording = ref(false);
  const mediaRecorder = ref(null);
  const audioChunks = ref([]);
  const visualizationBars = ref(Array(20).fill(3));
  const animationId = ref(null);
  const audioContext = ref(null);
  const analyser = ref(null);
  const dataArray = ref(null);
  
  // Computed properties
  const formattedTime = computed(() => {
    const minutes = Math.floor(recordingTime.value / 60);
    const seconds = recordingTime.value % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  });
  
  const recordButtonTitle = computed(() => {
    if (isRecording.value) return isPaused.value ? 'Resume' : 'Pause';
    return 'Record';
  });
  
  // Timer functionality
  let timerInterval = null;
  
  const startTimer = () => {
    if (!timerInterval) {
      timerInterval = setInterval(() => {
        recordingTime.value++;
        if (recordingTime.value >= 300) stopRecording();
      }, 1000);
    }
  };
  
  const stopTimer = () => {
    if (timerInterval) {
      clearInterval(timerInterval);
      timerInterval = null;
    }
  };
  
  // Visualization functionality
  const updateVisualization = () => {
    if (!analyser.value || !dataArray.value) return;
    analyser.value.getByteFrequencyData(dataArray.value);
    for (let i = 0; i < visualizationBars.value.length; i++) {
      const barIndex = Math.floor(i * (dataArray.value.length / visualizationBars.value.length));
      const value = dataArray.value[barIndex];
      visualizationBars.value[i] = value ? Math.max(3, value / 5) : 3;
    }
    animationId.value = requestAnimationFrame(updateVisualization);
  };
  
  const stopVisualization = () => {
    if (animationId.value) {
      cancelAnimationFrame(animationId.value);
      animationId.value = null;
    }
    if (audioContext.value) {
      audioContext.value.close().catch(err => console.error('Error closing audio context:', err));
    }
  };
  
  // Recording functionality
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioContext.value = new (window.AudioContext || window.webkitAudioContext)();
      analyser.value = audioContext.value.createAnalyser();
      const source = audioContext.value.createMediaStreamSource(stream);
      source.connect(analyser.value);
      analyser.value.fftSize = 256;
      dataArray.value = new Uint8Array(analyser.value.frequencyBinCount);
  
      mediaRecorder.value = new MediaRecorder(stream);
      audioChunks.value = [];
      mediaRecorder.value.ondataavailable = event => {
        if (event.data.size > 0) audioChunks.value.push(event.data);
      };
      mediaRecorder.value.onstop = () => {
        hasRecording.value = true;
        stopVisualization();
      };
  
      mediaRecorder.value.start();
      isRecording.value = true;
      isPaused.value = false;
      recordingTime.value = 0;
      startTimer();
      updateVisualization();
    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('Could not access microphone. Please check permissions.');
    }
  };
  
  const pauseRecording = () => {
    if (mediaRecorder.value && isRecording.value && !isPaused.value) {
      mediaRecorder.value.pause();
      isPaused.value = true;
      stopTimer();
      if (animationId.value) {
        cancelAnimationFrame(animationId.value);
        animationId.value = null;
      }
    }
  };
  
  const resumeRecording = () => {
    if (mediaRecorder.value && isRecording.value && isPaused.value) {
      mediaRecorder.value.resume();
      isPaused.value = false;
      startTimer();
      updateVisualization();
    }
  };
  
  const stopRecording = () => {
    if (mediaRecorder.value && isRecording.value) {
      mediaRecorder.value.stop();
      isRecording.value = false;
      isPaused.value = false;
      stopTimer();
      mediaRecorder.value.stream.getTracks().forEach(track => track.stop());
    }
  };
  
  const toggleRecording = () => {
    if (!isRecording.value) {
      startRecording();
    } else {
      if (isPaused.value) resumeRecording();
      else pauseRecording();
    }
  };
  
  const cancelRecording = () => {
    stopRecording();
    hasRecording.value = false;
    audioChunks.value = [];
    props.onClose();
  };
  
  const sendRecording = () => {
    if (!hasRecording.value || audioChunks.value.length === 0) return;
    const audioBlob = new Blob(audioChunks.value, { type: 'audio/webm' });
    const audioUrl = URL.createObjectURL(audioBlob);
    props.onSend({ type: 'audio', url: audioUrl, blob: audioBlob, duration: recordingTime.value });
    hasRecording.value = false;
    audioChunks.value = [];
    recordingTime.value = 0;
  };
  
  // Cleanup
  onBeforeUnmount(() => {
    stopRecording();
    stopVisualization();
    stopTimer();
  });
  
  // Idle animation
  onMounted(() => {
    const idleAnimation = () => {
      if (!isRecording.value || isPaused.value) {
        visualizationBars.value = visualizationBars.value.map(() => 
          Math.max(2, Math.min(10, 3 + Math.random() * 2))
        );
        setTimeout(idleAnimation, 500);
      }
    };
    idleAnimation();
  });
  </script>
  
  <style>
  .voice-recorder {
    width: 100%;
    background-color: #383a40;
    border-radius: 0.5rem;
    padding: 1rem;
    color: white;
  }
  
  .recorder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  
  /* Visualization */
  .visualization {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    height: 40px;
    width: 100%;
    padding: 0 1rem;
  }
  
  .bar {
    width: 3px;
    background-color: #b5bac1;
    border-radius: 1px;
    transition: height 0.1s ease;
  }
  
  .recording .bar {
    background-color: #ed4245;
  }
  
  /* Timer */
  .timer {
    font-family: monospace;
    font-size: 1rem;
    color: #b5bac1;
  }
  
  .recording .timer {
    color: #ed4245;
  }
  
  /* Controls */
  .controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }
  
  .control-button {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 50%;
    background: transparent;
    border: none;
    color: #b5bac1;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }
  
  .control-button:hover {
    background-color: #4e5058;
  }
  
  .control-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .cancel-button:hover {
    color: #ed4245;
  }
  
  .record-button {
    height: 3rem;
    width: 3rem;
    background-color: #4e5058;
  }
  
  .recording .record-button {
    color: #ed4245;
  }
  
  .send-button:hover:not(:disabled) {
    color: #3ba55c;
  }
  .stop-button {
  height: 2.5rem;
  width: 2.5rem;
  border-radius: 50%;
  background: transparent;
  border: none;
  color: #b5bac1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.stop-button:hover {
  background-color: #4e5058;
  color: #ed4245;
}

  </style>
<template>
    <div class="video-container">
      <video
        :id="'video-' + videoId"
        class="video-js vjs-default-skin"
        controls
        preload="auto"
      >
        <source :src="hlsUrl" type="application/x-mpegURL" />
        <source :src="mediaUrl" :type="mediaType" />
        Your browser does not support the video tag.
      </video>
    </div>
  </template>
  
  <script>
  import videojs from 'video.js';
  import 'video.js/dist/video-js.css';
  
  export default {
    name: 'ChatVideoPlayer',
    props: {
      mediaUrl: { type: String, required: true },
      mediaType: { type: String, required: true },
      videoId: { type: String, required: true },
    },
    computed: {
      hlsUrl() {
        if (this.mediaType.startsWith('video')) {
          const urlParts = this.mediaUrl.split('/upload/');
          if (urlParts.length === 2) {
            return `${urlParts[0]}/upload/sp_auto/${urlParts[1].replace(/\.\w+$/, '.m3u8')}`;
          }
        }
        return this.mediaUrl;
      },
    },
    mounted() {
      this.player = videojs('video-' + this.videoId, {
        fluid: true,
        responsive: true,
      });
    },
    beforeUnmount() {
      if (this.player) {
        this.player.dispose();
      }
    },
  };
  </script>
  
  <style scoped>
  .video-container {
    width: 100%;
    height: 100%;
  }
  </style>
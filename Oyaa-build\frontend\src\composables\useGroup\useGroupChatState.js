// frontend/src/composables/useGroup/useGroupChatState.js
import { ref, computed, watch } from 'vue';

export default function useGroupChatState(group, currentUser, searchQuery) {
  // Log initial group data
  console.log('useGroupChatState - Initial group:', group);
  console.log('useGroupChatState - Initial currentUser:', currentUser);
  
  // Store a reactive reference to the group and currentUser
  const groupRef = ref(group || {}); // Ensure groupRef is never null
  const currentUserRef = ref(currentUser || {}); // Ensure currentUserRef is never null
  
  const loading = ref(false);
  const error = ref(null);
  const messages = ref([]);
  const typingUsers = ref([]);
  const replyMessage = ref(null);
  const currentAdminUsername = ref('');
  const isAdmin = ref(false);
  const isMuted = ref(false);
  const isUploading = ref(false); // State for media uploads
  
  // Watch for changes to the original group prop and update our reactive reference
  watch(() => group, (newGroup) => {
    console.log('useGroupChatState - Group updated:', newGroup);
    
    if (newGroup && Object.keys(newGroup).length > 0) {
      groupRef.value = newGroup;
      console.log('useGroupChatState - Group ref updated with new data');
      
      // If we've received a valid group with an ID, update the isAdmin state
      if (newGroup.id && currentUserRef.value && currentUserRef.value.id) {
        isAdmin.value = newGroup.creator_id === currentUserRef.value.id;
        console.log(`useGroupChatState - User is${isAdmin.value ? '' : ' not'} admin of this group`);
      }
    }
  }, { deep: true, immediate: true });
  
  // Watch for changes to the currentUser and update our reactive reference
  watch(() => currentUser, (newUser) => {
    console.log('useGroupChatState - User updated:', newUser);
    
    if (newUser && Object.keys(newUser).length > 0) {
      currentUserRef.value = newUser;
      console.log('useGroupChatState - User ref updated with new data');
      
      // If we have a valid user and group, update isAdmin status
      if (currentUserRef.value.id && groupRef.value && groupRef.value.id) {
        isAdmin.value = groupRef.value.creator_id === currentUserRef.value.id;
      }
    }
  }, { deep: true, immediate: true });
  
  const filteredMessages = computed(() => {
    if (!searchQuery.value) return messages.value;

    return messages.value.filter(message => {
      if (!message.message) return false;
      return message.message.toLowerCase().includes(searchQuery.value.toLowerCase());
    });
  });

  const isWorldChat = computed(() => {
    const result = groupRef.value?.isWorldChat || groupRef.value?.is_world_chat || false;
    console.log('useGroupChatState - isWorldChat computed:', result);
    return result;
  });

  return {
    group: groupRef,  // Return the reactive group reference
    currentUser: currentUserRef, // Return the reactive user reference
    loading,
    error,
    messages,
    typingUsers,
    replyMessage,
    filteredMessages,
    currentAdminUsername,
    isAdmin,
    isWorldChat,
    isMuted,
    isUploading
  };
}
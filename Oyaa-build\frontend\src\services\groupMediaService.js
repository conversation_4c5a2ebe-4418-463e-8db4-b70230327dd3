// frontend/src/services/groupMediaService.js
import axios from 'axios';

export const uploadGroupMedia = async (file, userId, groupChatId, mediaType = 'image') => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('userId', userId);
  formData.append('groupChatId', groupChatId);
  formData.append('mediaType', mediaType);

  // If this is an audio file with a duration property, add it to the form data
  // Make sure it's an integer value
  if (mediaType === 'audio' && file.duration) {
    const durationInSeconds = Math.round(file.duration);
    formData.append('duration', durationInSeconds.toString());
  }

  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

  const response = await axios.post(`${API_BASE_URL}/api/group-media/upload`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });

  return response.data; // Returns { message, media: { mediaUrl, publicId, mediaType, duration } }
};
// frontend/src/services/groupMediaService.js

export const getOptimizedMediaUrl = (mediaUrl, mediaType) => {
  if (mediaType === 'image') {
    // Insert 'f_auto' after '/upload/' for images, GIFs, and stickers
    return mediaUrl.replace('/upload/', '/upload/f_auto/');
  } else {
    // Return original URL for videos and audio
    return mediaUrl;
  }
};
// Function to fetch all media for a specific group chat
export const getGroupChatMedia = async (groupChatId) => {
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

  try {
    const response = await axios.get(`${API_BASE_URL}/api/group-media/group-chat/${groupChatId}`);

    return response.data.map(media => ({
      id: media.id,
      mediaUrl: getOptimizedMediaUrl(media.media_url, media.media_type),
      mediaType: media.media_type,
      publicId: media.public_id,
      thumbnailUrl: media.thumbnail_url || generateThumbnailUrl(media.media_url, media.media_type),
      duration: media.duration,
      createdAt: media.created_at,
      userId: media.user_id,
      groupChatId: media.group_chat_id,
      senderName: media.username || 'Unknown User'
    }));
  } catch (error) {
    console.error('Error fetching group media:', error);
    throw error;
  }
};

// Function to fetch all media for a group (by group ID, not message ID)
export const getAllGroupMedia = async (groupId) => {
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

  try {
    const response = await axios.get(`${API_BASE_URL}/api/group-media/group/${groupId}`);

    return response.data.map(media => ({
      id: media.id,
      mediaUrl: getOptimizedMediaUrl(media.media_url, media.media_type),
      mediaType: media.media_type,
      publicId: media.public_id,
      thumbnailUrl: media.thumbnail_url || generateThumbnailUrl(media.media_url, media.media_type),
      duration: media.duration,
      createdAt: media.created_at,
      userId: media.user_id,
      groupChatId: media.group_chat_id,
      message: media.message,
      senderName: media.username || 'Unknown User'
    }));
  } catch (error) {
    console.error('Error fetching all group media:', error);
    throw error;
  }
};

// Function to fetch media by message IDs
export const getMediaByMessageIds = async (messageIds) => {
  if (!messageIds || !messageIds.length) {
    return [];
  }

  try {
    // Instead of calling a non-existent endpoint, use the existing getAllGroupMedia function
    // We'll need to filter the results to match the message IDs

    // Extract the group ID from the first message (since all messages should be from the same group)
    const groupId = messageIds[0]?.groupChatId;

    if (!groupId) {
      console.warn('No groupId available to fetch media');
      return [];
    }

    // Get all media for the group
    const allMedia = await getAllGroupMedia(groupId);

    // Filter to only include media from the specified message IDs
    return allMedia.filter(media => {
      // Check if the media is associated with any of the requested message IDs
      return messageIds.includes(media.messageId);
    });
  } catch (error) {
    console.error('Error fetching media by message IDs:', error);
    throw error;
  }
};

// Helper function to generate thumbnail URLs for videos from Cloudinary if not provided
export const generateThumbnailUrl = (mediaUrl, mediaType) => {
  if (!mediaUrl) return null;

  if (mediaType === 'image') {
    if (mediaUrl.includes('cloudinary.com')) {
      const urlParts = mediaUrl.split('/upload/');
      if (urlParts.length === 2) {
        // Use 'c_thumb' for static thumbnails, even for GIFs
        return `${urlParts[0]}/upload/c_thumb,w_320,h_240,q_auto:eco/${urlParts[1]}`;
      }
    }
    return mediaUrl;
  }

  if (mediaType === 'video') {
    if (mediaUrl.includes('cloudinary.com')) {
      const urlParts = mediaUrl.split('/upload/');
      if (urlParts.length === 2) {
        return `${urlParts[0]}/upload/c_thumb,w_480,h_360,g_center/${urlParts[1].replace(/\.\w+$/, '.jpg')}`;
      }
    }
  }

  return null;
};

// Helper function to process and standardize media items
export const processMediaItem = (media) => {
  return {
    id: media.id,
    mediaUrl: getOptimizedMediaUrl(media.media_url || media.mediaUrl, media.media_type || media.mediaType),
    mediaType: media.media_type || media.mediaType,
    publicId: media.public_id || media.publicId,
    thumbnailUrl: media.thumbnail_url || media.thumbnailUrl || generateThumbnailUrl(media.media_url || media.mediaUrl, media.media_type || media.mediaType),
    duration: media.duration,
    createdAt: media.created_at || media.createdAt,
    userId: media.user_id || media.userId,
    groupChatId: media.group_chat_id || media.groupChatId,
    message: media.message,
    senderName: media.username || media.senderName || 'Unknown User'
  };


};
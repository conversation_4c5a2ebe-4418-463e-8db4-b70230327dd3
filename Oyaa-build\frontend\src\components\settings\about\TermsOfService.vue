<template>
    <div class="container">
      <!-- Hero Section -->
      <section class="hero">
        <div class="hero-content">
          <h1> Terms of Service</h1>
          <p>Please read these terms carefully before using our platform. By accessing or using Oyaa, you agree to be bound by these Terms of Service.</p>
        </div>
        <div class="hero-background"></div>
      </section>
  
      <!-- Main Content -->
      <main class="main-content">
        <!-- Terms of Service Content -->
        <div class="tos-content">
          <section id="introduction" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Info class="icon purple" />
                  <h2 class="card-title">Introduction</h2>
                </div>
                <p class="card-description">Welcome to Oyaa</p>
              </div>
              <div class="card-content">
                <p>
                  These Terms of Service ("Terms") govern your access to and use of Oyaa's services, including our website, mobile applications, and any other software or services offered by Oyaa in connection to any of the foregoing ("Services"). Please read these Terms carefully, and contact us if you have any questions.
                </p>
                <p>
                  By joining Oyaa, you agree to these Terms of Service. To enhance your experience, our platform may use select device settings to help you connect with communities both near and far—ensuring a more tailored and engaging interaction.
                </p>
              </div>
            </div>
          </section>
  
          <section id="acceptance" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <CheckCircle class="icon green" />
                  <h2 class="card-title">Acceptance of Terms</h2>
                </div>
                <p class="card-description">Your agreement with Oyaa</p>
              </div>
              <div class="card-content">
                <p>
                  By accessing or using the Services, you agree to be bound by these Terms and all applicable laws and regulations. If you do not agree with any part of these Terms, you may not use our Services.
                </p>
              </div>
            </div>
          </section>
  
          <section id="age-requirement" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <UserCheck class="icon green" />
                  <h2 class="card-title">Age Requirement</h2>
                </div>
                <p class="card-description">Minimum age to use Oyaa</p>
              </div>
              <div class="card-content">
                <p>
                  You must be at least 13 years old to use Oyaa. By using our Services, you confirm that you meet this age requirement.
                </p>
              </div>
            </div>
          </section>
  
          <section id="user-accounts" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <User class="icon blue" />
                  <h2 class="card-title">User Accounts</h2>
                </div>
                <p class="card-description">Creating and maintaining your account</p>
              </div>
              <div class="card-content">
                <div class="accordion">
                  <div class="accordion-item">
                    <div class="accordion-header" @click="toggleAccordion('account-creation')">
                      <span>Account Creation</span>
                      <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === 'account-creation' }]" />
                    </div>
                    <div class="accordion-content" v-show="openAccordion === 'account-creation'">
                      <p>To access certain features of the Services, you must create an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.</p>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <div class="accordion-header" @click="toggleAccordion('account-responsibility')">
                      <span>Account Responsibility</span>
                      <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === 'account-responsibility' }]" />
                    </div>
                    <div class="accordion-content" v-show="openAccordion === 'account-responsibility'">
                      <p>You are solely responsible for maintaining the confidentiality of your account and password. You agree to accept responsibility for all activities that occur under your account or password.</p>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <div class="accordion-header" @click="toggleAccordion('public-groups')">
                      <span>Public Groups</span>
                      <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === 'public-groups' }]" />
                    </div>
                    <div class="accordion-content" v-show="openAccordion === 'public-groups'">
                      <p>Public groups are open to all Oyaa users. However, you must follow our Community Guidelines when participating in these groups. Failure to do so may result in removal from the group or further account actions.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
  
          <section id="user-content" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <FileText class="icon yellow" />
                  <h2 class="card-title">User Content</h2>
                </div>
                <p class="card-description">Your rights and responsibilities regarding content</p>
              </div>
              <div class="card-content">
                <div class="two-column">
                  <div class="column">
                    <h3>Content Ownership</h3>
                    <p>
                      You retain ownership of any intellectual property rights that you hold in the content you create and share on Oyaa.
                    </p>
                  </div>
                  <div class="column">
                    <h3>Content Responsibility</h3>
                    <p>
                      You are solely responsible for the content you share in your chats and any other interactions on Oyaa. Ensure that your content complies with our Community Guidelines and these Terms of Service.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
  
          <section id="prohibited-activities" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <AlertTriangle class="icon red" />
                  <h2 class="card-title">Prohibited Activities</h2>
                </div>
                <p class="card-description">Activities not allowed on Oyaa</p>
              </div>
              <div class="card-content">
                <div class="alert alert-red">
                  <h3>The following activities are strictly prohibited:</h3>
                  <ul>
                    <li>
                      <Ban class="alert-icon" />
                      <span>Violating any laws or regulations</span>
                    </li>
                    <li>
                      <Ban class="alert-icon" />
                      <span>Impersonating others or providing false information</span>
                    </li>
                    <li>
                      <Ban class="alert-icon" />
                      <span>Interfering with or disrupting the Services</span>
                    </li>
                    <li>
                      <Ban class="alert-icon" />
                      <span>Engaging in any form of harassment or hate speech</span>
                    </li>
                    <li>
                      <Ban class="alert-icon" />
                      <span>Unauthorized access to other users' accounts</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </section>
  
          <section id="intellectual-property" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Copyright class="icon purple" />
                  <h2 class="card-title">Intellectual Property</h2>
                </div>
                <p class="card-description">Oyaa's rights and your use of our content</p>
              </div>
              <div class="card-content">
                <p>
                  The Services and their original content, features, and functionality are owned by Oyaa and are protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws.
                </p>
                <div class="alert alert-blue">
                  <h3>Use of Oyaa Content</h3>
                  <p>
                    You may not use, reproduce, distribute, or create derivative works based upon Oyaa's content without express written permission from Oyaa.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="limitation-of-liability" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <ShieldAlert class="icon yellow" />
                  <h2 class="card-title">Limitation of Liability</h2>
                </div>
                <p class="card-description">Our liability for any issues or damages</p>
              </div>
              <div class="card-content">
                <p>
                  To the maximum extent permitted by law, Oyaa and its affiliates, officers, employees, agents, partners and licensors will not be liable for any direct, indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from:
                </p>
                <ul class="danger-list">
                  <li>Your access to or use of or inability to access or use the Services</li>
                  <li>Any conduct or content of any third party on the Services</li>
                  <li>Any content obtained from the Services</li>
                  <li>Unauthorized access, use or alteration of your transmissions or content</li>
                </ul>
              </div>
            </div>
          </section>
  
          <section id="termination" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <XCircle class="icon red" />
                  <h2 class="card-title">Termination</h2>
                </div>
                <p class="card-description">Ending your agreement with Oyaa</p>
              </div>
              <div class="card-content">
                <p>
                  We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.
                </p>
                <p>
                  If you wish to terminate your account, you may simply discontinue using the Service.
                </p>
              </div>
            </div>
          </section>
  
          <section id="changes-to-terms" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <RefreshCw class="icon green" />
                  <h2 class="card-title">Changes to Terms</h2>
                </div>
                <p class="card-description">How we update our Terms of Service</p>
              </div>
              <div class="card-content">
                <p>
                  We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material we will provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion.
                </p>
                <div class="alert alert-green">
                  <h3>Staying Informed</h3>
                  <p>
                    By continuing to access or use our Service after any revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, you are no longer authorized to use the Service.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="governing-law" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Scale class="icon blue" />
                  <h2 class="card-title">Governing Law</h2>
                </div>
                <p class="card-description">Legal jurisdiction for these Terms</p>
              </div>
              <div class="card-content">
                <p>
                  These Terms shall be governed and construed in accordance with the laws of Nigeria, without regard to its conflict of law provisions.
                </p>
                <p>
                  Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights. If any provision of these Terms is held to be invalid or unenforceable by a court, the remaining provisions of these Terms will remain in effect.
                </p>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  import { 
    Info, 
    CheckCircle, 
    User, 
    UserCheck,
    FileText, 
    AlertTriangle, 
    Ban, 
    Copyright, 
    ShieldAlert, 
    XCircle, 
    RefreshCw, 
    Scale,
    ChevronDown
  } from 'lucide-vue-next';
  
  const openAccordion = ref(null);
  
  const toggleAccordion = (id) => {
    if (openAccordion.value === id) {
      openAccordion.value = null;
    } else {
      openAccordion.value = id;
    }
  };
  </script>
  
  <style>
  /* Container with Variables and Base Styles */
  .container {
    /* Colors */
    --color-black: #000000;
    --color-white: #ffffff;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    --color-gray-950: #030712;
    --color-purple-300: #c4b5fd;
    --color-purple-400: #a78bfa;
    --color-purple-500: #8b5cf6;
    --color-purple-600: #7c3aed;
    --color-purple-700: #6d28d9;
    --color-purple-800: #5b21b6;
    --color-purple-900: #4c1d95;
    --color-purple-950: #2e1065;
    --color-blue-500: #3b82f6;
    --color-blue-600: #2563eb;
    --color-blue-700: #1d4ed8;
    --color-blue-800: #1e40af;
    --color-blue-900: #1e3a8a;
    --color-blue-950: #172554;
    --color-green-500: #22c55e;
    --color-green-600: #16a34a;
    --color-green-700: #15803d;
    --color-green-800: #166534;
    --color-green-900: #14532d;
    --color-green-950: #052e16;
    --color-yellow-500: #eab308;
    --color-yellow-600: #ca8a04;
    --color-yellow-700: #a16207;
    --color-yellow-800: #854d0e;
    --color-yellow-900: #713f12;
    --color-yellow-950: #422006;
    --color-red-500: #ef4444;
    --color-red-600: #dc2626;
    --color-red-700: #b91c1c;
    --color-red-800: #991b1b;
    --color-red-900: #7f1d1d;
    --color-red-950: #450a0a;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Original .container styles */
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;

    /* Moved from body */
    font-family: 'Rubik', sans-serif;
    background-color: var(--color-black);
    color: var(--color-white);
    line-height: 1.5;

    /* Scoped reset */
    box-sizing: border-box;
  }

  /* Scoped Heading Styles */
  .container h1,
  .container h2,
  .container h3,
  .container h4,
  .container h5,
  .container h6 {
    font-weight: 600;
    line-height: 1.2;
  }

  /* Scoped Link Styles */
  .container a {
    color: var(--color-gray-400);
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .container a:hover {
    color: var(--color-white);
  }

  /* Scoped List Styles */
  .container ul,
  .container ol {
    padding-left: var(--space-5);
  }

  /* Hero Section */
  .hero {
    position: relative;
    padding: var(--space-20) var(--space-4);
    border-bottom: 1px solid var(--color-gray-800);
    background: linear-gradient(to bottom right, var(--color-black), var(--color-gray-900), var(--color-purple-950));
    overflow: hidden;
    box-sizing: border-box;
  }

  .hero-content {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
  }

  .hero h1 {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-4);
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .hero p {
    max-width: 36rem;
    margin: 0 auto;
    color: var(--color-gray-400);
    font-size: var(--text-lg);
  }

  .hero-background {
    position: absolute;
    inset: 0;
    z-index: 0;
    opacity: 0.2;
    background: radial-gradient(circle at center, var(--color-purple-500), transparent);
  }

  /* Main Content */
  .main-content {
    flex: 1;
    padding: var(--space-12) var(--space-4);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
  }

  .tos-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-10);
  }

  /* Section */
  .section {
    scroll-margin-top: var(--space-20);
  }

  /* Card */
  .card {
    background-color: var(--color-gray-900);
    border: 1px solid var(--color-gray-800);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-sizing: border-box;
  }

  .card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-gray-800);
    box-sizing: border-box;
  }

  .card-title-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-1);
  }

  .card-title {
    font-size: var(--text-xl);
    font-weight: 600;
  }

  .card-description {
    color: var(--color-gray-400);
    font-size: var(--text-sm);
  }

  .card-content {
    padding: var(--space-6);
    box-sizing: border-box;
  }

  .card-content > p {
    margin-bottom: var(--space-4);
  }

  /* Accordion */
  .accordion {
    border: 1px solid var(--color-gray-800);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-sizing: border-box;
  }

  .accordion-item {
    border-bottom: 1px solid var(--color-gray-800);
    box-sizing: border-box;
  }

  .accordion-item:last-child {
    border-bottom: none;
  }

  .accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    cursor: pointer;
    font-weight: 500;
    transition: color 0.2s ease;
    box-sizing: border-box;
  }

  .accordion-header:hover {
    color: var(--color-white);
  }

  .accordion-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.2s ease;
  }

  .accordion-icon.rotated {
    transform: rotate(180deg);
  }

  .accordion-content {
    padding: var(--space-4) var(--space-6);
    color: var(--color-gray-400);
    box-sizing: border-box;
  }

  .accordion-content p {
    margin-bottom: var(--space-2);
  }

  /* Alerts */
  .alert {
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
    box-sizing: border-box;
  }

  .alert h3 {
    margin-bottom: var(--space-2);
    font-weight: 500;
    font-size: var(--text-base);
  }

  .alert ul {
    list-style: none;
    padding-left: 0;
  }

  .alert li {
    display: flex;
    align-items: flex-start;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
  }

  .alert-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    margin-top: 4px;
  }

  .alert-red {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--color-red-900);
  }

  .alert-red h3 {
    color: var(--color-red-400);
  }

  .alert-blue {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid var(--color-blue-900);
    margin-top: var(--space-4);
  }

  .alert-blue h3 {
    color: var(--color-blue-400);
  }

  .alert-green {
    background-color: rgba(34, 197, 94, 0.1);
    border: 1px solid var(--color-green-900);
  }

  .alert-green h3 {
    color: var(--color-green-400);
  }

  /* Two Column Layout */
  .two-column {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  @media (min-width: 768px) {
    .two-column {
      grid-template-columns: 1fr 1fr;
    }
  }

  .column h3 {
    margin-bottom: var(--space-3);
    font-size: var(--text-lg);
  }

  .column p {
    color: var(--color-gray-400);
    margin-bottom: var(--space-3);
  }

  /* Lists */
  .danger-list li {
    position: relative;
    padding-left: var(--space-4);
    margin-bottom: var(--space-1);
    color: var(--color-gray-400);
    box-sizing: border-box;
  }

  .danger-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--color-red-500);
  }

  /* Icons */
  .icon {
    width: 20px;
    height: 20px;
  }

  .icon.purple {
    color: var(--color-purple-500);
  }

  .icon.blue {
    color: var(--color-blue-500);
  }

  .icon.green {
    color: var(--color-green-500);
  }

  .icon.yellow {
    color: var(--color-yellow-500);
  }

  .icon.red {
    color: var(--color-red-500);
  }

  /* Responsive Adjustments */
  @media (min-width: 768px) {
    .hero h1 {
      font-size: var(--text-5xl);
    }

    .main-content {
      padding: var(--space-12) var(--space-8);
    }
  }

  @media (min-width: 1024px) {
    .hero h1 {
      font-size: var(--text-6xl);
    }
  }
</style>
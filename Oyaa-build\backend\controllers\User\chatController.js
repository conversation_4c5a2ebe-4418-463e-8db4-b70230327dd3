// backend/controllers/chatController.js
const chatService = require('../../services/User/chatService');

class ChatController {
  async start(req, res) {
    const { userId, targetUserId } = req.body;
    try {
      const result = await chatService.startChat(userId, targetUserId);
      res.status(200).json(result);
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: err.message });
    }
  }

  async send(req, res) {
    const { senderId, receiverId, message, reply, media } = req.body;
    let parsedReply = reply;
    if (reply && typeof reply === 'string') {
      try {
        parsedReply = JSON.parse(reply);
      } catch (e) {
        console.error('Failed to parse reply:', e);
        parsedReply = null;
      }
    }
    const replyId = parsedReply && parsedReply.id ? parsedReply.id : null;

    try {
      const sentMessage = await chatService.sendMessage(senderId, receiverId, message, replyId, media);
      res.status(200).json({ message: 'Message sent successfully', data: sentMessage });
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: err.message });
    }
  }

  async retrieve(req, res) {
    const { userId, targetUserId, page = 1, pageSize = 50 } = req.query;
    console.log(
      `Retrieve endpoint called: userId=${userId}, targetUserId=${targetUserId}, page=${page}, pageSize=${pageSize}`
    );
    try {
      const messages = await chatService.getMessages(userId, targetUserId, page, pageSize);
      console.log(`Retrieved ${messages.length} messages for page ${page}`);
      res.status(200).json(messages);
    } catch (err) {
      console.error('Error in retrieve:', err);
      res.status(500).json({ error: err.message });
    }
  }

  async getLastChats(req, res) {
    const { userId } = req.query;
    if (!userId) {
      return res.status(400).json({ error: "Missing userId parameter" });
    }
    try {
      const lastChats = await chatService.getLastChats(userId);
      res.status(200).json({ chats: lastChats });
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: err.message });
    }
  }
}

module.exports = new ChatController();
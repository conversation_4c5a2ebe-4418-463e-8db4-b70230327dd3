# Debugging Guide for Oyaa Backend

This guide provides instructions for diagnosing and fixing common issues with the Oyaa backend server.

## Error Logging

The application uses <PERSON> for comprehensive error logging. Logs are stored in the `logs` directory:

- `combined.log` - Contains all log levels
- `error.log` - Contains only error-level logs
- `exceptions.log` - Contains uncaught exceptions
- `rejections.log` - Contains unhandled promise rejections

## Diagnostic Tools

### Health Check Endpoint

The server provides a health check endpoint at `/health` that returns detailed information about:

- Server status
- Memory usage
- Database connection status
- Redis connection status
- Server uptime

Example usage:
```
curl https://localhost:5000/health
```

### Diagnostic Script

Run the diagnostic script to check for common issues:

```
npm run diagnose
```

This script checks:
- Node.js and npm versions
- System information
- Required files
- Environment variables
- SSL certificates
- Log files
- Database connection

## Common Issues and Solutions

### Server Crashes on Startup

1. **Missing SSL Certificates**
   - Error: `ENOENT: no such file or directory, open 'server.key'` or `server.cert`
   - Solution: Ensure `server.key` and `server.cert` files exist in the root directory

2. **Database Connection Issues**
   - Error: `Error: connect ECONNREFUSED`
   - Solution: Check database credentials in `.env` file and ensure the database server is running

3. **Port Already in Use**
   - Error: `Error: listen EADDRINUSE: address already in use :::5000`
   - Solution: Kill the process using the port or change the port in `.env`

4. **Missing Environment Variables**
   - Error: `TypeError: Cannot read properties of undefined`
   - Solution: Ensure all required environment variables are set in `.env`

### Runtime Errors

1. **Database Query Errors**
   - Check `error.log` for detailed SQL error messages
   - Common issues include:
     - Table doesn't exist
     - Column doesn't exist
     - Constraint violations

2. **Memory Leaks**
   - Monitor memory usage with the `/health` endpoint
   - Look for increasing memory usage over time

3. **Slow Performance**
   - Check `combined.log` for slow query warnings
   - Optimize database queries or add indexes

## Debugging with Node Inspector

To debug the server with Chrome DevTools:

1. Start the server in debug mode:
   ```
   npm run debug
   ```

2. Open Chrome and navigate to `chrome://inspect`

3. Click on "Open dedicated DevTools for Node"

4. Set breakpoints and debug as needed

## Viewing Logs in Real-Time

To watch logs in real-time:

```
tail -f logs/combined.log
```

To filter for errors only:

```
tail -f logs/error.log
```

## Getting Help

If you're still experiencing issues after trying these debugging steps, please:

1. Collect the relevant logs
2. Run the diagnostic script and save the output
3. Describe the steps to reproduce the issue
4. Contact the development team with this information

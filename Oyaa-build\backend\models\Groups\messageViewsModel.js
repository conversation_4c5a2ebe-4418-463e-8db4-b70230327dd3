// backend/models/messageViewsModel.js
const db = require('../../utils/db');

class MessageViewsModel {
  /**
   * Adds a new view record for a message.
   * @param {number} messageId - The ID of the message being viewed.
   * @param {number} userId - The ID of the user who viewed the message.
   * @returns {Promise<Object>} The inserted view record.
   */
  async addView(messageId, userId) {
    const query = `
      INSERT INTO message_views (message_id, user_id)
      VALUES ($1, $2)
      ON CONFLICT (message_id, user_id) DO NOTHING
      RETURNING *;
    `;
    const values = [messageId, userId];
    const result = await db.query(query, values);
    // If the view already exists, no row will be returned, so we return null
    return result.rows[0] || null;
  }
  

  /**
   * Retrieves the number of unique views for a given message.
   * @param {number} messageId - The ID of the message.
   * @returns {Promise<number>} The count of unique views.
   */
  async getViewCount(messageId) {
    const query = `
      SELECT COUNT(DISTINCT user_id) AS view_count
      FROM message_views
      WHERE message_id = $1;
    `;
    const result = await db.query(query, [messageId]);
    return parseInt(result.rows[0].view_count, 10);
  }

  /**
   * Retrieves all view records for a specific message.
   * @param {number} messageId - The ID of the message.
   * @returns {Promise<Array>} An array of view records.
   */
  async getViewsForMessage(messageId) {
    const query = `
      SELECT *
      FROM message_views
      WHERE message_id = $1
      ORDER BY viewed_at DESC;
    `;
    const result = await db.query(query, [messageId]);
    return result.rows;
  }
}

module.exports = new MessageViewsModel();

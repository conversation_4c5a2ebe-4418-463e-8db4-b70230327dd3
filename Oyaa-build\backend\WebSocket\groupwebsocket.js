// backend/WebSocket/groupwebsocket.js
const GroupService = require('../services/Groups/groupService');
const EnhancedGroupChatService = require('../services/Groups/enhancedGroupChatService');
const WorldChatService = require('../services/Groups/worldChatService');
const { processGroupImageMessage } = require('../services/Groups/groupImageLinkHandler');
const db = require('../utils/db'); // Import db for querying blocked users

module.exports = (io, worldChatId) => {
  const groupNamespace = io.of('/groups');

  groupNamespace.on('connection', (socket) => {
    console.log(`User connected to group namespace: ${socket.id}`);

    // Event: Join a group room with user-specific room
    socket.on('joinGroup', (data) => {
      console.log('joinGroup data received:', data);
      const { groupId, userId } = typeof data === 'object' ? data : { groupId: data, userId: null };
      if (!groupId || !userId) {
        console.error('Invalid joinGroup data:', data);
        socket.emit('error', { message: 'Invalid joinGroup data, missing groupId or userId' });
        return;
      }
      socket.join(`group_${groupId}`);
      socket.join(`user_${userId}`);
      console.log(`Joined rooms: group_${groupId}, user_${userId}`);
    });

    // Event: Leave a group room
    socket.on('leaveGroup', (data) => {
      const { groupId, userId } = typeof data === 'object' ? data : { groupId: data, userId: null };
      if (!groupId || !userId) {
        console.error('Invalid leaveGroup data:', data);
        return;
      }
      socket.leave(`group_${groupId}`);
      socket.leave(`user_${userId}`);
      console.log(`User ${socket.id} (userId: ${userId}) left group room: group_${groupId}`);
    });

    // Event: Handle sending a group message (with optional media)
    socket.on('sendGroupMessage', async (data) => {
      console.log('Received sendGroupMessage event with data:', data);
      const { senderId, groupId, message, media, replyId } = data;

      // Validate that either message text or media is provided
      if ((!message || message.trim() === '') && !media) {
        console.error('sendGroupMessage event missing required fields:', data);
        socket.emit('error', { message: 'Either message text or media must be provided.' });
        return;
      }

      try {
        console.log(`Saving group message from ${senderId} to group ${groupId}`);
        let chatMessage;

        if (parseInt(groupId) === parseInt(worldChatId)) {
          console.log(`This is a world chat message (worldChatId: ${worldChatId})`);
          chatMessage = await WorldChatService.sendMessage(
            senderId,
            message,
            new Date(),
            replyId,
            media
          );
        } else {
          console.log(`This is a regular group chat message`);
          chatMessage = await EnhancedGroupChatService.sendMessage(
            senderId,
            groupId,
            message,
            new Date(),
            replyId,
            media
          );
        }

        console.log('Message saved to database:', chatMessage);

        // Process message for image links if needed
        const processedMessage = processGroupImageMessage(chatMessage);
        console.log('Processed message:', processedMessage);

        // Fetch group members
        console.log('Fetching group members');
        const membersQuery = `
          SELECT user_id
          FROM group_members
          WHERE group_id = $1
        `;
        const membersResult = await db.query(membersQuery, [groupId]);
        const memberIds = membersResult.rows.map(row => row.user_id);
        console.log(`Found ${memberIds.length} members in the group`);

        // Fetch users who have blocked the sender
        console.log('Checking for users who blocked the sender');
        const blockedQuery = `
          SELECT blocker_id
          FROM blocked_users
          WHERE blocked_id = $1 AND blocker_id = ANY($2)
        `;
        const blockedResult = await db.query(blockedQuery, [senderId, memberIds]);
        const blockedByIds = blockedResult.rows.map(row => row.blocker_id);
        console.log(`${blockedByIds.length} users have blocked the sender`);

        // Determine recipients (members who haven't blocked the sender)
        const recipients = memberIds.filter(id => !blockedByIds.includes(id));

        // Generate a unique ID for this specific message broadcast
        const broadcastId = Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        processedMessage.broadcastId = broadcastId;

        // Use ONLY the group room broadcast to avoid duplicates
        // Instead of broadcasting to individual user rooms
        console.log(`Broadcasting message to group_${groupId} with broadcastId: ${broadcastId}`);
        groupNamespace.to(`group_${groupId}`).emit('newGroupMessage', processedMessage);

        // No need to track message status anymore

        console.log('Message broadcast complete');
      } catch (err) {
        console.error('Failed to send group message:', err);
        socket.emit('error', { message: 'Failed to send message: ' + (err.message || 'Unknown error') });
      }
    });

    // Event: Add a member to a group
    socket.on('addMember', async (data, callback) => {
      try {
        const { groupId, userId } = data;
        const member = await GroupService.addMember(groupId, userId);
        const username = await getUsername(userId);
        const systemMessage = await EnhancedGroupChatService.sendSystemMessage(
          groupId,
          `${username} has joined the group`
        );
        groupNamespace.to(`group_${groupId}`).emit('newGroupMessage', systemMessage);
        callback({ status: 'success', member });
        groupNamespace.to(`group_${groupId}`).emit('memberAdded', member);
      } catch (err) {
        console.error('Error adding member:', err);
        callback({ status: 'error', message: err.message });
      }
    });

    // Event: Remove a member from a group
    socket.on('removeMember', async (data, callback) => {
      try {
        const { groupId, userId } = data;
        if (parseInt(groupId) === parseInt(worldChatId)) {
          return callback({ status: 'error', message: 'Cannot leave World Chat' });
        }
        const member = await GroupService.removeMember(groupId, userId);
        const username = await getUsername(userId);
        const systemMessage = await EnhancedGroupChatService.sendSystemMessage(
          groupId,
          `${username} has left the group`
        );
        groupNamespace.to(`group_${groupId}`).emit('newGroupMessage', systemMessage);
        callback({ status: 'success', member });
        groupNamespace.to(`group_${groupId}`).emit('memberRemoved', member);
      } catch (err) {
        console.error('Error removing member:', err);
        callback({ status: 'error', message: err.message });
      }
    });

    // Event: Create a new group
    socket.on('createGroup', async (data, callback) => {
      try {
        const { name, creatorId } = data;
        const group = await GroupService.createGroup(name, creatorId);
        callback({ status: 'success', group });
        groupNamespace.emit('groupCreated', group);
      } catch (err) {
        console.error('Error creating group:', err);
        callback({ status: 'error', message: err.message });
      }
    });

    // Event: Handle group typing events
    socket.on('groupTyping', (data) => {
      groupNamespace.to(`group_${data.groupId}`).emit('groupTyping', data);
    });

    // Event: Handle stop typing events
    socket.on('groupStopTyping', (data) => {
      console.log(`User ${socket.id} stopped typing in group ${data.groupId}:`, data);
      groupNamespace.to(`group_${data.groupId}`).emit('groupStopTyping', data);
    });



    // Event: Handle message hidden
    socket.on('messageHidden', ({ groupId, messageId }) => {
      groupNamespace.to(`group_${groupId}`).emit('messageHidden', { messageId });
    });

    // Event: Handle message deletion
    socket.on('deleteMessage', ({ groupId, messageId }) => {
      groupNamespace.to(`group_${groupId}`).emit('messageDeleted', { messageId });
    });

    // Event: Handle user mute
    socket.on('muteUser', ({ groupId, userId }) => {
      groupNamespace.to(`group_${groupId}`).emit('userMuted', { userId });
    });

    // Event: Handle admin change
    socket.on('adminChanged', async () => {
      try {
        const adminRes = await db.query('SELECT id, username FROM users WHERE current_admin = TRUE');
        if (adminRes.rows[0]) {
          groupNamespace.to('group_1').emit('adminUpdate', {
            adminId: adminRes.rows[0].id,
            adminUsername: adminRes.rows[0].username,
          });
        }
      } catch (err) {
        console.error('Error handling admin change:', err);
      }
    });

    // Handle disconnect
    socket.on('disconnect', (reason) => {
      console.log(`User disconnected from group namespace: ${socket.id}, reason: ${reason}`);
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error(`Socket error for ${socket.id}:`, error);
    });
  });
};

// Helper function to get username
async function getUsername(userId) {
  try {
    const result = await db.query('SELECT username FROM users WHERE id = $1', [userId]);
    return result.rows[0]?.username || 'Unknown';
  } catch (err) {
    console.error('Error fetching username:', err);
    return 'Unknown';
  }
}
{"name": "is-reference", "version": "3.0.3", "description": "Determine whether an AST node is a reference", "type": "module", "module": "src/index.js", "types": "types/index.d.ts", "exports": {"types": "./types/index.d.ts", "import": "./src/index.js"}, "files": ["src", "types"], "scripts": {"test": "uvu", "prepublishOnly": "npm test && dts-buddy"}, "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-<PERSON>/is-reference.git"}, "keywords": ["ast", "javascript", "estree", "acorn"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/is-reference/issues"}, "homepage": "https://github.com/<PERSON>-<PERSON>/is-reference#readme", "dependencies": {"@types/estree": "^1.0.6"}, "devDependencies": {"acorn": "^8.0.5", "acorn-class-fields": "^1.0.0", "acorn-static-class-features": "^1.0.0", "dts-buddy": "^0.5.3", "estree-walker": "^3.0.0", "typescript": "^5.6.3", "uvu": "^0.5.6"}}
<template>
    <div class="container">
      <!-- Hero Section -->
      <section class="hero">
        <div class="hero-content">
          <h1>Intellectual Property Policy</h1>
          <p><strong>Oyaa</strong> respects the intellectual property rights of its users and expects the same in return. This policy applies to all content shared on our platform, including proximity chats and public groups.</p>
        </div>
        <div class="hero-background"></div>
      </section>
  
      <!-- Main Content -->
      <main class="main-content">
        <!-- Intellectual Property Policy Content -->
        <div class="ip-policy-content">
          <section id="introduction" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Shield class="icon purple" />
                  <h2 class="card-title">Introduction</h2>
                </div>
                <p class="card-description">Our commitment to intellectual property rights</p>
              </div>
              <div class="card-content">
                <p>
                  At Oyaa, we are committed to fostering a community that respects intellectual property rights. This policy outlines our approach to protecting intellectual property on our platform and provides guidelines for our users to ensure compliance with copyright laws and other intellectual property regulations.
                </p>
                <p>
                  By using Oyaa, you agree to abide by this Intellectual Property Policy. We encourage all users to familiarize themselves with these guidelines to maintain a fair and legal environment for content sharing and creation.
                </p>
              </div>
            </div>
          </section>
  
          <section id="content-ownership" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <UserCircle class="icon blue" />
                  <h2 class="card-title">Content Ownership</h2>
                </div>
                <p class="card-description">Understanding your rights as a content creator</p>
              </div>
              <div class="card-content">
                <div class="alert alert-blue">
                  <h3>Your Content, Your Rights</h3>
                  <p>
                    You retain ownership of content you create and share on Oyaa. This includes text, images, videos, and any other media you post on our platform.
                  </p>
                </div>
                <p>
                  While you maintain ownership, by posting content on Oyaa, you grant us certain rights to use and display your content. This is necessary for us to provide and improve our services.
                </p>
              </div>
            </div>
          </section>
  
          <section id="user-license" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <FileCheck class="icon green" />
                  <h2 class="card-title">User License</h2>
                </div>
                <p class="card-description">The rights you grant to Oyaa</p>
              </div>
              <div class="card-content">
                <p>
                  By posting content on Oyaa, you grant us a non-exclusive, transferable, sub-licensable, royalty-free, worldwide license to host, use, distribute, modify, run, copy, publicly perform or display, translate, and create derivative works of your content. This license is for the purpose of operating, developing, providing, and improving our Services.
                </p>
                <div class="alert alert-yellow">
                  <h3>Important Note</h3>
                  <p>
                    This license ends when you delete your content or your account, except in cases where you've shared your content with others and they have not deleted it.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="prohibited-content" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <AlertTriangle class="icon red" />
                  <h2 class="card-title">Prohibited Content</h2>
                </div>
                <p class="card-description">Content that violates intellectual property rights</p>
              </div>
              <div class="card-content">
                <p>
                  To maintain a respectful and legal environment, the following types of content are prohibited on Oyaa:
                </p>
                <ul class="danger-list">
                  <li>Copyrighted material posted without the permission of the copyright owner</li>
                  <li>Trademark-infringing content that could confuse or mislead users</li>
                  <li>Patented inventions or processes shared without authorization</li>
                  <li>Trade secrets or confidential information of others</li>
                  <li>Content that violates publicity or privacy rights</li>
                </ul>
                <div class="alert alert-red">
                  <h3>World Chats and Public Groups</h3>
                  <p>
                    Do not post copyrighted material in world chats or public groups without the right permission from the owner. This includes but is not limited to music, videos, articles, and images.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="reporting-violations" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Flag class="icon yellow" />
                  <h2 class="card-title">Reporting Violations</h2>
                </div>
                <p class="card-description">How to report intellectual property infringement</p>
              </div>
              <div class="card-content">
                <p>
                  If you believe that your intellectual property rights have been violated on Oyaa, we encourage you to report it to us immediately. Here's how you can report violations:
                </p>
                <ol class="numbered-list">
                  <li>Contact our Customer Support team through the app or website</li>
                  <li>Provide a detailed description of the alleged infringement</li>
                  <li>Include links or screenshots of the infringing content</li>
                  <li>Provide proof of your ownership of the intellectual property</li>
                  <li>Include your contact information for follow-up</li>
                </ol>
                <div class="alert alert-green">
                  <h3>Prompt Action</h3>
                  <p>
                    We take all reports of intellectual property violations seriously and will investigate promptly. Depending on the outcome of our investigation, we may remove the infringing content, issue warnings, or take other appropriate actions.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="dmca-compliance" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Scale class="icon blue" />
                  <h2 class="card-title">DMCA Compliance</h2>
                </div>
                <p class="card-description">Our commitment to copyright protection</p>
              </div>
              <div class="card-content">
  <p>
    Oyaa adheres to the Nigerian Copyright Act of 1988 and promptly addresses valid notices of copyright infringement.
  </p>
  <div class="two-column">
    <div class="column">
      <h3>For Copyright Owners</h3>
      <p>
        If you believe your work has been copied in a way that constitutes copyright infringement, please contact us through Oyaa’s official channels and include all necessary details.
      </p>
    </div>
    <div class="column">
      <h3>For Users</h3>
      <p>
        If your content is removed because of a copyright claim, and you believe this is a mistake or that your work was wrongly identified, you can ask us to review and restore it.
      </p>
    </div>
  </div>
</div>

            </div>
          </section>
  
          <section id="consequences" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <AlertOctagon class="icon red" />
                  <h2 class="card-title">Consequences of Violations</h2>
                </div>
                <p class="card-description">Actions taken for intellectual property infringement</p>
              </div>
              <div class="card-content">
                <p>
                  Oyaa takes intellectual property violations seriously. Consequences for violating this policy may include:
                </p>
                <ul class="danger-list">
                  <li>Removal of infringing content</li>
                  <li>Issuance of warnings to the offending user</li>
                  <li>Temporary suspension of account privileges</li>
                  <li>Permanent termination of accounts for repeat offenders</li>
                  <li>Legal action in severe cases</li>
                </ul>
                <p>
                  We reserve the right to take any action we deem appropriate in response to intellectual property violations, including cooperation with law enforcement agencies when necessary.
                </p>
              </div>
            </div>
          </section>
  
          <section id="policy-changes" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <RefreshCw class="icon green" />
                  <h2 class="card-title">Changes to intellectual property Policy</h2>
                </div>
                <p class="card-description">How we update our intellectual property practices</p>
              </div>
              <div class="card-content">
                <p>
                  We may update our Intellectual Property Policy from time to time to reflect changes in our practices or legal requirements. We will notify users of any significant changes through our app or website.
                </p>
                <div class="alert alert-blue">
                  <h3>Stay Informed</h3>
                  <p>
                    It is your responsibility to review this policy periodically. Your continued use of Oyaa after changes to this policy constitutes acceptance of the updated terms.
                  </p>
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  import { 
    Shield,
    UserCircle,
    FileCheck,
    AlertTriangle,
    Flag,
    Scale,
    AlertOctagon,
    RefreshCw
  } from 'lucide-vue-next';
  
  // No reactive state or methods needed for this component
  </script>
  
  <style>
  /* Container with Variables and Base Styles */
  .container {
    /* Colors */
    --color-black: #000000;
    --color-white: #ffffff;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    --color-gray-950: #030712;
    --color-purple-300: #c4b5fd;
    --color-purple-400: #a78bfa;
    --color-purple-500: #8b5cf6;
    --color-purple-600: #7c3aed;
    --color-purple-700: #6d28d9;
    --color-purple-800: #5b21b6;
    --color-purple-900: #4c1d95;
    --color-purple-950: #2e1065;
    --color-blue-500: #3b82f6;
    --color-blue-600: #2563eb;
    --color-blue-700: #1d4ed8;
    --color-blue-800: #1e40af;
    --color-blue-900: #1e3a8a;
    --color-blue-950: #172554;
    --color-green-500: #22c55e;
    --color-green-600: #16a34a;
    --color-green-700: #15803d;
    --color-green-800: #166534;
    --color-green-900: #14532d;
    --color-green-950: #052e16;
    --color-yellow-500: #eab308;
    --color-yellow-600: #ca8a04;
    --color-yellow-700: #a16207;
    --color-yellow-800: #854d0e;
    --color-yellow-900: #713f12;
    --color-yellow-950: #422006;
    --color-red-500: #ef4444;
    --color-red-600: #dc2626;
    --color-red-700: #b91c1c;
    --color-red-800: #991b1b;
    --color-red-900: #7f1d1d;
    --color-red-950: #450a0a;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Original .container styles */
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;

    /* Moved from body */
    font-family: 'Rubik', sans-serif;
    background-color: var(--color-black);
    color: var(--color-white);
    line-height: 1.5;

    /* Ensure box-sizing consistency */
    box-sizing: border-box;
  }

  /* Scoped Heading Styles */
  .container h1,
  .container h2,
  .container h3,
  .container h4,
  .container h5,
  .container h6 {
    font-weight: 600;
    line-height: 1.2;
  }

  /* Scoped Link Styles */
  .container a {
    color: var(--color-gray-400);
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .container a:hover {
    color: var(--color-white);
  }

  /* Scoped List Styles */
  .container ul,
  .container ol {
    padding-left: var(--space-5);
  }

  /* Hero Section */
  .hero {
    position: relative;
    padding: var(--space-20) var(--space-4);
    border-bottom: 1px solid var(--color-gray-800);
    background: linear-gradient(to bottom right, var(--color-black), var(--color-gray-900), var(--color-purple-950));
    overflow: hidden;
  }

  .hero-content {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
  }

  .hero h1 {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-4);
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .hero p {
    max-width: 36rem;
    margin: 0 auto;
    color: var(--color-gray-400);
    font-size: var(--text-lg);
  }

  .hero-background {
    position: absolute;
    inset: 0;
    z-index: 0;
    opacity: 0.2;
    background: radial-gradient(circle at center, var(--color-purple-500), transparent);
  }

  /* Main Content */
  .main-content {
    flex: 1;
    padding: var(--space-12) var(--space-4);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
  }

  .ip-policy-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-10);
  }

  /* Section */
  .section {
    scroll-margin-top: var(--space-20);
  }

  /* Card */
  .card {
    background-color: var(--color-gray-900);
    border: 1px solid var(--color-gray-800);
    border-radius: var(--radius-lg);
    overflow: hidden;
  }

  .card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-gray-800);
  }

  .card-title-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-1);
  }

  .card-title {
    font-size: var(--text-xl);
    font-weight: 600;
  }

  .card-description {
    color: var(--color-gray-400);
    font-size: var(--text-sm);
  }

  .card-content {
    padding: var(--space-6);
  }

  .card-content > p {
    margin-bottom: var(--space-4);
  }

  /* Alerts */
  .alert {
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }

  .alert h3 {
    margin-bottom: var(--space-2);
    font-weight: 500;
    font-size: var(--text-base);
  }

  .alert-blue {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid var(--color-blue-900);
  }

  .alert-blue h3 {
    color: var(--color-blue-400);
  }

  .alert-yellow {
    background-color: rgba(234, 179, 8, 0.1);
    border: 1px solid var(--color-yellow-900);
  }

  .alert-yellow h3 {
    color: var(--color-yellow-400);
  }

  .alert-red {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--color-red-900);
  }

  .alert-red h3 {
    color: var(--color-red-400);
  }

  .alert-green {
    background-color: rgba(34, 197, 94, 0.1);
    border: 1px solid var(--color-green-900);
  }

  .alert-green h3 {
    color: var(--color-green-400);
  }

  /* Lists */
  .danger-list {
    list-style: none;
    padding-left: 0;
  }

  .danger-list li {
    position: relative;
    padding-left: var(--space-6);
    margin-bottom: var(--space-2);
  }

  .danger-list li::before {
    content: "⚠️";
    position: absolute;
    left: 0;
    color: var(--color-red-500);
  }

  .numbered-list {
    list-style-type: decimal;
    padding-left: var(--space-6);
  }

  .numbered-list li {
    margin-bottom: var(--space-2);
  }

  /* Two Column Layout */
  .two-column {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  @media (min-width: 768px) {
    .two-column {
      grid-template-columns: 1fr 1fr;
    }
  }

  .column h3 {
    margin-bottom: var(--space-3);
    font-size: var(--text-lg);
  }

  .column p {
    color: var(--color-gray-400);
    margin-bottom: var(--space-3);
  }

  /* Icons */
  .icon {
    width: 20px;
    height: 20px;
  }

  .icon.purple {
    color: var(--color-purple-500);
  }

  .icon.blue {
    color: var(--color-blue-500);
  }

  .icon.green {
    color: var(--color-green-500);
  }

  .icon.yellow {
    color: var(--color-yellow-500);
  }

  .icon.red {
    color: var(--color-red-500);
  }

  /* Responsive Adjustments */
  @media (min-width: 768px) {
    .hero h1 {
      font-size: var(--text-5xl);
    }

    .main-content {
      padding: var(--space-12) var(--space-8);
    }
  }

  @media (min-width: 1024px) {
    .hero h1 {
      font-size: var(--text-6xl);
    }
  }
</style>
// models/Chat.js
const fs = require('fs');  // (Make sure fs is required)
const { Client } = require('pg');
require('dotenv').config();

class Chat {
  constructor() {
    this.client = new Client({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      database: process.env.DB_NAME,
      ssl: {
        rejectUnauthorized: false, // Allow self-signed certificates
      },
    });
  }

  async connect() {
    await this.client.connect();
  }

  // Updated sendMessage to accept a timestamp
  async sendMessage(senderId, receiverId, message, sent_at, replyId = null) {
    const query = `
      INSERT INTO chats (sender_id, receiver_id, message, sent_at, reply_id)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *;
    `;
    const values = [senderId, receiverId, message, sent_at, replyId];
    const result = await this.client.query(query, values);
    return result.rows[0];
  }


  // Retrieve messages between two users (the sent_at field comes as an ISO string)
  async getMessages(userId, targetUserId) {
    try {
      const query = `
        SELECT
          chats.id,
          chats.message,
          chats.sender_id,
          chats.receiver_id,
          chats.sent_at,
          users.username AS sender_name,
          users.avatar AS sender_avatar
        FROM chats
        JOIN users ON chats.sender_id = users.id
        WHERE (chats.sender_id = $1 AND chats.receiver_id = $2)
           OR (chats.sender_id = $2 AND chats.receiver_id = $1)
        ORDER BY chats.sent_at DESC;
      `;
      const result = await db.query(query, [userId, targetUserId]);
      return result.rows;
    } catch (err) {
      throw new Error(`Failed to retrieve messages: ${err.message}`);
    }
  }



  async close() {
    await this.client.end();
  }
}

module.exports = Chat;

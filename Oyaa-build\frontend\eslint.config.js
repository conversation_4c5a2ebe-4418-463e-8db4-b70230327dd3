import vue from "eslint-plugin-vue";
import vueParser from "vue-eslint-parser";

export default [
  {
    ignores: ["node_modules", "dist"],
  },
  {
    files: ["**/*.vue", "**/*.js"], // Ensure it picks up Vue and JS files
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      parser: vueParser,
    },
    plugins: {
      vue,
    },
    rules: {
      "vue/multi-word-component-names": "off",
    },
  },
];

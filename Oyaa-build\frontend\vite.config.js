// vite.config.js
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import fs from 'fs';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      vue: 'vue/dist/vue.esm-bundler.js', // Added alias for Vue full build
    },
  },
  server: {
    host: true,
    port: 5175,
    https: {
      key: fs.readFileSync(path.resolve(__dirname, '../backend/server.key')),
      cert: fs.readFileSync(path.resolve(__dirname, '../backend/server.cert')),
    },
  },
});
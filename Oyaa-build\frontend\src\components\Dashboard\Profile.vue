<template>
  <div class="profile-container" v-if="!loading">
    <!-- Avatar Section with Edit Icon -->
    <div class="avatar-section">
      <div class="avatar-container">
        <img :src="avatarUrl" alt="User Avatar" class="avatar" />
        <button class="edit-avatar-btn" @click="openAvatarModal" aria-label="Edit avatar">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Username Section -->
    <div class="username-section">
      <h2>{{ user.username }}</h2>
    </div>

    <!-- Metrics Section -->
    <div class="metrics-section">
      <div class="metric">
        <span class="metric-number">{{ user.friend_count || 0 }}</span>
        <span class="metric-label">Friends</span>
      </div>
      <div class="metric">
        <span class="metric-number">{{ user.group_count || 0 }}</span>
        <span class="metric-label">Groups</span>
      </div>
      <div class="metric">
        <span class="metric-number">{{ user.local_count || 0 }}</span>
        <span class="metric-label">Locals</span>
      </div>
    </div>

    <!-- Description Section -->
    <div class="description-section">
      <p>{{ user.description || 'No description' }}</p>
    </div>

    <!-- Tags Section -->
    <div class="tags-section" v-if="user.tags && user.tags.length > 0">
      <span v-for="tag in user.tags" :key="tag" class="tag">{{ tag }}</span>
    </div>

    <!-- Edit Profile Action -->
    <div class="actions-section">
      <button @click="goToSettings">Edit Profile</button>
    </div>

    <!-- Modal for Avatar Selection -->
    <div v-if="showAvatarModal" class="modal" @click.self="closeAvatarModal">
      <div class="modal-content">
        <PickAvatar 
          :isDarkMode="true" 
          @avatar-selected="updateAvatar" 
          @close="closeAvatarModal" 
        />
      </div>
    </div>
  </div>
  <div v-else class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading profile...</p>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import axios from 'axios';
import PickAvatar from '@/components/settings/ContentAndActivity/PickAvatar.vue';

export default {
  components: {
    PickAvatar,
  },
  data() {
    return {
      loading: true,
      showAvatarModal: false,
    };
  },
  computed: {
    ...mapGetters('auth', ['user']),
    avatarUrl() {
      return this.user.avatar || '/Avatar/default.svg';
    },
  },
  async mounted() {
    if (!this.user || typeof this.user.friend_count === 'undefined') {
      await this.$store.dispatch('auth/initializeAuth');
      try {
        const token = this.$store.state.auth.token;
        const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/users/me`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        this.$store.commit('auth/LOGIN_SUCCESS', { user: response.data, token });
      } catch (error) {
        console.error('Profile.vue: Failed to fetch user data:', {
          status: error.response ? error.response.status : 'No response',
          data: error.response ? error.response.data : 'No data',
          message: error.message,
        });
      }
    }
    this.loading = false;
  },
  methods: {
    goToSettings() {
      this.$router.push('/profile/edit');
    },
    openAvatarModal() {
      this.showAvatarModal = true;
      document.body.classList.add('modal-open');
    },
    closeAvatarModal() {
      this.showAvatarModal = false;
      document.body.classList.remove('modal-open');
    },
    async updateAvatar(newAvatarSrc) {
      try {
        const token = this.$store.state.auth.token;
        await axios.put(
          `${import.meta.env.VITE_API_BASE_URL}/api/users/me`,
          { avatar: newAvatarSrc },
          { headers: { Authorization: `Bearer ${token}` } }
        );
        this.$store.commit('auth/UPDATE_USER', { avatar: newAvatarSrc });
        this.closeAvatarModal();
      } catch (error) {
        console.error('Failed to update avatar:', error);
      }
    },
  },
};
</script>

<style scoped>
.profile-container {
  padding: 20px 16px;
  max-width: 600px;
  margin: 0 auto;
  color: #e2e2e2;
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

/* Avatar Container */
.avatar-container {
  position: relative;
  display: inline-block;
}

/* Avatar Styling */
.avatar-section {
  text-align: center;
  margin-bottom: 20px;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 14px;
  object-fit: cover;
  border: 3px solid #6366f1;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.avatar:hover {
  transform: scale(1.03);
  box-shadow: 0 12px 28px rgba(99, 102, 241, 0.3);
}

/* Edit Avatar Button */
.edit-avatar-btn {
  position: absolute;
  bottom: -6px;
  right: -6px;
  background: #6366f1;
  border: none;
  border-radius: 10px;
  padding: 8px;
  cursor: pointer;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.edit-avatar-btn:hover {
  background: #4f46e5;
  transform: scale(1.1);
}

/* Username Styling */
.username-section {
  text-align: center;
  margin-bottom: 20px;
}

.username-section h2 {
  font-size: 22px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
  background: linear-gradient(90deg, var(--text-primary, #f2f2f2), var(--accent-primary, #6366f1));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

/* Metrics Styling */
.metrics-section {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
  gap: 30px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.metric-number {
  font-size: 20px;
  font-weight: 700;
  color: #6366f1;
}

.metric-label {
  font-size: 13px;
  color: #a0a0a0;
  font-weight: 500;
}

/* Description Styling */
.description-section {
  text-align: center;
  margin-bottom: 20px;
  padding: 0 10px;
}

.description-section p {
  margin: 0;
  line-height: 1.6;
  color: #a0a0a0;
  font-size: 14px;
}

/* Tags Styling */
.tags-section {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
}

.tag {
  background: rgba(99, 102, 241, 0.15);
  color: #6366f1;
  padding: 6px 12px;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.tag:hover {
  background: rgba(99, 102, 241, 0.25);
  transform: translateY(-2px);
}

/* Actions Styling */
.actions-section {
  text-align: center;
}

.actions-section button {
  background: #6366f1;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.actions-section button:hover {
  background: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
}

.actions-section button:active {
  transform: scale(0.98);
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: #1a1a22;
  border-radius: 16px;
  max-width: 90%;
  width: 450px;
  position: relative;
  border: 1px solid rgba(46, 46, 58, 0.8);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #a0a0a0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  border-top-color: #6366f1;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .profile-container {
    padding: 15px 12px;
  }
  
  .avatar {
    width: 80px;
    height: 80px;
    border-radius: 12px;
  }
  
  .edit-avatar-btn {
    padding: 6px;
  }
  
  .username-section h2 {
    font-size: 20px;
  }
  
  .metrics-section {
    gap: 20px;
  }
  
  .metric-number {
    font-size: 18px;
  }
  
  .metric-label {
    font-size: 12px;
  }
  
  .actions-section button {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* Extra small screens */
@media (max-width: 320px) {
  .profile-container {
    padding: 12px 8px;
  }
  
  .avatar {
    width: 70px;
    height: 70px;
  }
  
  .username-section h2 {
    font-size: 18px;
  }
  
  .metrics-section {
    gap: 15px;
  }
  
  .metric-number {
    font-size: 16px;
  }
  
  .tag {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .actions-section button {
    padding: 8px 16px;
    font-size: 13px;
  }
}
</style>
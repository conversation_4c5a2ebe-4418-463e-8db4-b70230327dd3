// frontend/src/tempChatSocket.js
import { io } from 'socket.io-client';

const tempChatSocket = io(`${import.meta.env.VITE_API_BASE_URL}/temp-chat`, {
  autoConnect: false, // Connect manually when needed
});

tempChatSocket.on('connect', () => {
  console.log('Connected to temp-chat namespace');
});

tempChatSocket.on('disconnect', () => {
  console.log('Disconnected from temp-chat namespace');
});

tempChatSocket.on('connect_error', (error) => {
  console.error('Temp-chat namespace connection error:', error);
});

tempChatSocket.on('newTempMessage', (message) => {
  console.log('New message received:', message);
  // Handled in components
});

tempChatSocket.on('lastTempMessageUpdate', (data) => {
  console.log('Last message update received:', data);
  // Components will handle this event
});

export default tempChatSocket;
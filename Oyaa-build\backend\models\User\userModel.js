// backend/models/userModel.js
const db = require('../../utils/db');

class UserModel {
  /**
   * (Optional) Establish a database connection.
   * Replace this no-op with your actual connection logic if needed.
   */
  async connect() {
    // If using a connection pool, you might not need this method.
    return Promise.resolve();
  }

  /**
   * (Optional) Close the database connection.
   * Replace this no-op with your actual disconnection logic if needed.
   */
  async close() {
    // If using a connection pool, you might not need this method.
    return Promise.resolve();
  }

  /**
   * Update the user's location in the database.
   * This query updates latitude, longitude, and the PostGIS geography column "location".
   */
  async updateLocation(userId, lat, lng) {
    // Check if location record exists for this user
    const checkQuery = 'SELECT id FROM locations WHERE user_id = $1';
    const checkResult = await db.query(checkQuery, [userId]);

    let query;
    if (checkResult.rows.length > 0) {
      // Update existing location
      query = `
        UPDATE locations
        SET latitude = $1::double precision,
            longitude = $2::double precision,
            location = ST_SetSRID(ST_MakePoint($2::double precision, $1::double precision),4326)::geography,
            last_updated = CURRENT_TIMESTAMP
        WHERE user_id = $3
        RETURNING *;
      `;
    } else {
      // Insert new location
      query = `
        INSERT INTO locations (user_id, latitude, longitude, location, last_updated)
        VALUES ($3, $1::double precision, $2::double precision,
                ST_SetSRID(ST_MakePoint($2::double precision, $1::double precision),4326)::geography,
                CURRENT_TIMESTAMP)
        RETURNING *;
      `;
    }

    const result = await db.query(query, [lat, lng, userId]);
    return result.rows[0];
  }

  async getUserLocation(userId) {
    const query = 'SELECT latitude, longitude FROM locations WHERE user_id = $1;';
    const result = await db.query(query, [userId]);
    return result.rows[0];
  }

  /**
   * Query nearby users using the Haversine formula.
   * (Your legacy method – not used when leveraging PostGIS.)
   */
  async findNearbyUsers(userId, limit = 10, offset = 0) {
    const userLocation = await this.getUserLocation(userId);
    if (!userLocation) {
      throw new Error('User location not found');
    }
    const { latitude, longitude } = userLocation;

    const query = `
      SELECT u.id, u.username, l.latitude, l.longitude,
      (6371 * acos(
          cos(radians($1)) * cos(radians(l.latitude)) * cos(radians(l.longitude) - radians($2)) +
          sin(radians($1)) * sin(radians(l.latitude))
      )) AS distance
      FROM users u
      JOIN locations l ON u.id = l.user_id
      WHERE u.id != $3
      ORDER BY distance
      LIMIT $4 OFFSET $5;
    `;
    const result = await db.query(query, [latitude, longitude, userId, limit, offset]);
    return result.rows;
  }

  /**
   * Query nearby users using PostGIS.
   * This method returns users within the specified radius (in meters) from the given point.
   * It orders results by distance and optionally excludes the current user.
   *
   * @param {number} lat - Latitude of the search point.
   * @param {number} lng - Longitude of the search point.
   * @param {number} radius - Search radius in meters.
   * @param {number} limit - Maximum number of results.
   * @param {number} offset - Number of results to skip.
   * @param {string|number} currentUserId - (Optional) The current user's id to exclude from results.
   */
  async getNearbyUsersPostGIS(lat, lng, radius, limit, offset, currentUserId = null) {
    const query = `
      SELECT u.id, u.username, l.latitude, l.longitude,
        ST_Distance(
          l.location,
          ST_SetSRID(ST_MakePoint($2, $1),4326)::geography
        ) AS distance
      FROM users u
      JOIN locations l ON u.id = l.user_id
      WHERE ST_DWithin(
        l.location,
        ST_SetSRID(ST_MakePoint($2, $1),4326)::geography,
        $3
      )
      ${currentUserId ? 'AND u.id <> $6' : ''}
      ORDER BY distance
      LIMIT $4 OFFSET $5;
    `;

    // Build parameters array. Note: $1=lat, $2=lng, $3=radius, $4=limit, $5=offset.
    const params = [lat, lng, radius, limit, offset];
    if (currentUserId) {
      params.push(currentUserId); // becomes $6 in the query
    }

    const result = await db.query(query, params);
    return result.rows;
  }
/**
   * Update the user's avatar.
   * Expects the new avatar URL or file path.
   */
static async updateAvatar(userId, avatarUrl) {
  const query = `
    UPDATE users
    SET avatar = $1
    WHERE id = $2
    RETURNING id, username, email, avatar;
  `;
  const result = await db.query(query, [avatarUrl, userId]);
  return result.rows[0];
}
  /**
   * Search a user by username.
   * Now also returns the email address.
   */
  static async searchUserByUsername(username) {
    const query = 'SELECT id, username, email FROM users WHERE username = $1;';
    const result = await db.query(query, [username]);

    return result.rows[0];
  }

  /**
   * Get a user by their ID.
   * Now also returns the email address.
   */
  static async getUserById(userId) {
    const query = `
      SELECT
        u.id,
        u.username,
        u.email,
        u.avatar,
        u.description,
        u.tags,
        u.handle,
        (SELECT COUNT(*) FROM (
          SELECT friend_id FROM friends WHERE user_id = u.id
          UNION
          SELECT user_id FROM friends WHERE friend_id = u.id
        ) AS unique_friends) AS friend_count,
        (SELECT COUNT(*) FROM group_members WHERE user_id = u.id) AS group_count,
        (SELECT COUNT(*) FROM locals WHERE user_id = u.id) AS local_count
      FROM users u
      WHERE u.id = $1;
    `;
    try {

      const result = await db.query(query, [userId]);

      return result.rows[0];
    } catch (error) {
      console.error('userModel: Error in getUserById:', {
        message: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  // Update user profile including handle with improved logging
  async updateProfile(userId, updates) {
    console.log('UserModel: Updating profile for user ID:', userId);
    console.log('UserModel: Update data received:', updates);

    const allowedFields = ['username', 'email', 'description', 'tags', 'avatar', 'password_hash', 'handle'];
    const setClauses = [];
    const values = [];
    let paramIndex = 1;

    // Build the SET clause dynamically
    for (const [key, value] of Object.entries(updates)) {
      if (allowedFields.includes(key) && value !== undefined) {
        setClauses.push(`${key} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
        console.log(`UserModel: Adding field to update: ${key} = ${value}`);
      }
    }

    // If username is updated but handle is not, set handle to username
    if (updates.username && !updates.handle) {
      setClauses.push(`handle = $${paramIndex}`);
      values.push(updates.username);
      paramIndex++;
      console.log(`UserModel: Setting handle to match username: ${updates.username}`);
    }

    // If no fields to update, throw an error or return the current user
    if (setClauses.length === 0) {
      console.error('UserModel: No fields to update');
      throw new Error('No fields to update');
    }

    // Add userId as the last parameter
    values.push(userId);
    const query = `
      UPDATE users
      SET ${setClauses.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, username, email, description, tags, avatar, handle;
    `;

    console.log('UserModel: Executing query:', query);
    console.log('UserModel: Query parameters:', values);

    try {
      const result = await db.query(query, values);

      if (result.rows.length === 0) {
        console.error('UserModel: No user found with ID:', userId);
        throw new Error(`User with ID ${userId} not found`);
      }

      console.log('UserModel: Profile updated successfully:', result.rows[0]);
      return result.rows[0];
    } catch (error) {
      console.error('UserModel: Error executing query:', error);
      throw error;
    }
  }
  async getUsersByIds(userIds) {
    const result = await db.query(
      `SELECT u.id, u.username, l.latitude, l.longitude
       FROM users u
       LEFT JOIN locations l ON u.id = l.user_id
       WHERE u.id = ANY($1)`,
      [userIds]
    );
    return result.rows;
  }
}

module.exports = UserModel;  // Export the class, not an instance.

const groupRequestsModel = require('../../models/Groups/groupRequestsModel');

class GroupRequestsService {
  async createRequest(groupId, userId) {
    return await groupRequestsModel.createRequest(groupId, userId);
  }

  async updateRequestStatus(groupId, userId, status) {
    return await groupRequestsModel.updateRequestStatus(groupId, userId, status);
  }

  async getRequestsByUser(userId) {
    return await groupRequestsModel.getRequestsByUser(userId);
  }

  async getRequestsByGroup(groupId) {
    return await groupRequestsModel.getRequestsByGroup(groupId);
  }
}

module.exports = new GroupRequestsService();

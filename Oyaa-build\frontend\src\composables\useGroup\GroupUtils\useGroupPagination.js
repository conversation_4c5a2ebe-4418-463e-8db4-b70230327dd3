// frontend/src/composables/useGroup/GroupUtils/useGroupPagination.js
import { ref } from 'vue';

export default function usePagination() {
  const currentPage = ref(1);
  const pageSize = 50; // Set to 50 messages per page
  const hasMore = ref(true);
  const totalFetched = ref(0);

  // Function to reset pagination state
  const reset = () => {
    currentPage.value = 1;
    hasMore.value = true;
    totalFetched.value = 0;
  };

  // Function to track fetched messages
  const trackFetched = (count) => {
    totalFetched.value += count;
  };

  return {
    currentPage,
    pageSize,
    hasMore,
    totalFetched,
    reset,
    trackFetched
  };
}
<!-- Oyaa-build/frontend/src/components/SignUp.vue -->
<template>
  <div class="sign-up-container" :class="{ 'dark-mode': isDarkMode }">
    <ParticleBackground :theme="isDarkMode" />
    <ThemeToggle @toggle="toggleTheme" :isDarkMode="isDarkMode" />
    <main class="main-content">
      <div class="content-wrapper">
        <form class="form" @submit.prevent="handleSubmit">
          <div class="header">
            <img :src="isDarkMode ? '/Oyaalogo-DB.svg' : '/Oyaa-logo-LB.svg'" alt="Oyaa Logo" class="logo-img" />
            <h1 class="app-name">Oyaa</h1>
          </div>



          <div class="flex-column">
            <label>Username</label>
          </div>
          <div class="inputForm">
            <span>@</span>
            <input
              placeholder="Enter your Username"
              class="input"
              type="text"
              v-model="username"
              required
              @input="clearError"
            />
          </div>

          <!-- Remaining form fields (Email, Password, Confirm Password) remain unchanged -->
          <div class="flex-column">
            <label>Email</label>
          </div>
          <div class="inputForm">
            <svg class="theme-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <input
              ref="emailInput"
              placeholder="Enter your Email"
              class="input email-input"
              type="email"
              v-model="email"
              required
              @input="clearError"
            />
            <span class="gmail-suffix" title="Add @gmail.com" @click="appendGmail">@gmail</span>
          </div>

          <div class="flex-column">
            <label>Password</label>
          </div>
          <div class="inputForm">
            <svg class="theme-icon" xmlns="http://www.w3.org/2000/svg" width="16" viewBox="-64 0 512 512" height="16">
              <path fill="currentColor" d="m336 512h-288c-26.453125 0-48-21.523438-48-48v-224c0-26.476562 21.546875-48 48-48h288c26.453125 0 48 21.523438 48 48v224c0 26.476562-21.546875 48-48 48zm-288-288c-8.8125 0-16 7.167969-16 16v224c0 8.832031 7.1875 16 16 16h288c8.8125 0 16-7.167969 16-16v-224c0-8.832031-7.1875-16-16-16zm0 0"></path>
              <path fill="currentColor" d="m304 224c-8.832031 0-16-7.167969-16-16v-80c0-52.929688-43.070312-96-96-96s-96 43.070312-96 96v80c0 8.832031-7.167969 16-16 16s-16-7.167969-16-16v-80c0-70.59375 57.40625-128 128-128s128 57.40625 128 128v80c0 8.832031-7.167969 16-16 16zm0 0"></path>
            </svg>
            <input
              placeholder="Enter your Password"
              class="input"
              :type="showPassword ? 'text' : 'password'"
              v-model="password"
              required
              @input="clearError"
            />
            <button type="button" class="eye-icon" @click="togglePasswordVisibility">
              <svg
                class="theme-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path v-if="showPassword" d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                <path v-else d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                <circle cx="12" cy="12" r="3" />
              </svg>
            </button>
          </div>

          <div class="flex-column">
            <label>Confirm Password</label>
          </div>
          <div class="inputForm">
            <svg class="theme-icon" xmlns="http://www.w3.org/2000/svg" width="16" viewBox="-64 0 512 512" height="16">
              <path fill="currentColor" d="m336 512h-288c-26.453125 0-48-21.523438-48-48v-224c0-26.476562 21.546875-48 48-48h288c26.453125 0 48 21.523438 48 48v224c0 26.476562-21.546875 48-48 48zm-288-288c-8.8125 0-16 7.167969-16 16v224c0 8.832031 7.1875 16 16 16h288c8.8125 0 16-7.167969 16-16v-224c0-8.832031-7.1875-16-16-16zm0 0"></path>
              <path fill="currentColor" d="m304 224c-8.832031 0-16-7.167969-16-16v-80c0-52.929688-43.070312-96-96-96s-96 43.070312-96 96v80c0 8.832031-7.167969 16-16 16s-16-7.167969-16-16v-80c0-70.59375 57.40625-128 128-128s128 57.40625 128 128v80c0 8.832031-7.167969 16-16 16zm0 0"></path>
            </svg>
            <input
              placeholder="Confirm your Password"
              class="input"
              :type="showConfirmPassword ? 'text' : 'password'"
              v-model="confirmPassword"
              required
              @input="clearError"
            />
            <button type="button" class="eye-icon" @click="toggleConfirmPasswordVisibility">
              <svg
                class="theme-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path v-if="showConfirmPassword" d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                <path v-else d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                <circle cx="12" cy="12" r="3" />
              </svg>
            </button>
          </div>

          <!-- Error messages -->
          <p v-if="localError" class="error">{{ localError }}</p>
          <p v-else-if="error" class="error">{{ error }}</p>

          <button type="submit" class="button-submit" :disabled="loading || authLoading">
            <span v-if="loading || authLoading">Signing up...</span>
            <span v-else>Sign Up</span>
          </button>

          <div class="divider">
            <span>OR</span>
          </div>

          <button type="button" class="google-button" @click="handleGoogleSignIn" :disabled="loading || authLoading">
            <img src="/google-icon.svg" alt="Google" class="google-icon" />
            <span v-if="loading || authLoading">Signing in...</span>
            <span v-else>Sign up with Google</span>
          </button>

          <p class="p">
            Already have an account? <router-link to="/signin" class="span">Sign In</router-link>
          </p>
        </form>


        <!-- Username Selection Modal for Google Sign-in -->
        <div v-if="needsUsername" class="modal-overlay">
          <div class="modal">
            <h2>{{ isNewUser ? 'Choose a Username' : 'Complete Your Sign In' }}</h2>
            <p v-if="isNewUser">Please choose a unique username for your Oyaa account. This username will be visible to other users and cannot be changed later.</p>
            <p v-else>You need to set a username to complete your sign in. This username will be visible to other users and cannot be changed later.</p>
            <p class="note">Note: We don't use your Google name automatically to ensure you have a unique identity in our application.</p>
            <div
              class="inputForm"
              :class="{ 'input-valid': usernameValid, 'input-error': usernameError && usernameError.length > 0 }"
            >
              <span>@</span>
              <input
                placeholder="Enter your Username"
                class="input"
                type="text"
                v-model="newUsername"
                required
                maxlength="20"
                @input="validateUsername"
                ref="usernameInput"
                autofocus
              />
              <span v-if="usernameValid" class="validation-icon valid-icon">✓</span>
              <span v-if="usernameError && usernameError.length > 0" class="validation-icon error-icon">✗</span>
            </div>
            <div v-if="usernameError" class="error-message">{{ usernameError }}</div>
            <div v-if="usernameValid" class="success-message">
              Username is available!
              {{ isNewUser ? 'Click "Save Username" to continue.' : 'Click "Complete Sign In" to continue.' }}
            </div>
            <div class="username-requirements">
              <p>Username requirements:</p>
              <ul>
                <li>3-20 characters long</li>
                <li>Letters, numbers, underscores only</li>
                <li>Must be unique</li>
                <li>This will be your display name in the app</li>
              </ul>
            </div>
            <div class="modal-buttons">
              <button
                type="button"
                class="reset-button"
                @click="handleUsernameSubmit"
                :disabled="usernameLoading || !newUsername || (usernameError && usernameError.length > 0) || !usernameValid"
              >
                <span v-if="usernameLoading">Saving...</span>
                <span v-else>{{ isNewUser ? 'Save Username' : 'Complete Sign In' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import ParticleBackground from '@/components/ParticleBackground.vue';
import ThemeToggle from '@/components/ThemeToggle.vue';
import { forceDashboardRedirect } from '../utils/authHandler';

const router = useRouter();
const store = useStore();
const isDarkMode = ref(localStorage.getItem('theme') === 'dark' || true);
const username = ref('');
const email = ref('');
const password = ref('');
const confirmPassword = ref('');
const localError = ref('');
const emailInput = ref(null);
const showPassword = ref(false);
const showConfirmPassword = ref(false);

// Use ref for local loading state instead of computed
const loading = ref(false);
// Use computed for auth store loading and error state
const authLoading = computed(() => store.state.auth.loading);
const error = computed(() => store.state.auth.error);
const needsUsername = computed(() => store.getters['auth/needsUsername']);
const isNewUser = computed(() => store.getters['auth/isNewUser']);
const firebaseUser = computed(() => store.getters['auth/firebaseUser']);

// Username selection modal
const newUsername = ref('');
const usernameLoading = ref(false);
const usernameError = ref('');
const usernameValid = ref(false);
const usernameInput = ref(null);

// Watch for needsUsername changes
watch(needsUsername, (newValue) => {
  if (newValue && firebaseUser.value) {
    // Pre-fill email from Google account
    email.value = firebaseUser.value.email || '';

    // Reset the username field when the modal appears
    newUsername.value = '';
    usernameError.value = '';
    usernameValid.value = false;

    // Focus the username input field after a short delay to ensure the modal is rendered
    setTimeout(() => {
      if (usernameInput.value) {
        usernameInput.value.focus();
      }
    }, 100);
  }
});

onMounted(() => {
  document.title = 'Oyaa - Sign Up';
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme) isDarkMode.value = savedTheme === 'dark';

  // Add viewport meta tag for better mobile display
  const viewportMeta = document.createElement('meta');
  viewportMeta.name = 'viewport';
  viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
  document.head.appendChild(viewportMeta);
});

const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value;
  localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light');
};



const handleSubmit = async () => {
  // Basic validation
  if (password.value !== confirmPassword.value) {
    localError.value = "Passwords do not match";
    return;
  }

  if (username.value.length < 3) {
    localError.value = "Username must be at least 3 characters";
    return;
  }

  // Clear any previous errors
  localError.value = "";

  // Show loading state
  loading.value = true;

  try {
    // If we're completing a Google sign-up
    if (needsUsername.value && firebaseUser.value) {
      console.log('Completing Google sign-up with username:', username.value);

      await store.dispatch('auth/completeGoogleSignUp', {
        username: username.value
      });

      console.log('Google sign-up completed successfully');

      // Force redirect to dashboard after a short delay
      setTimeout(() => {
        if (!forceDashboardRedirect()) {
          router.push('/dashboard');
        }
      }, 500);
    } else {
      // Regular email/password registration
      console.log('Starting regular email/password registration');

      // Set a flag in localStorage to indicate this is an email/password registration
      localStorage.setItem('emailPasswordSignUp', 'true');

      await store.dispatch('auth/register', {
        username: username.value,
        email: email.value,
        password: password.value
      });

      // Remove the flag after successful registration
      localStorage.removeItem('emailPasswordSignUp');

      console.log('Registration completed successfully');

      // Force redirect to dashboard after a short delay
      setTimeout(() => {
        if (!forceDashboardRedirect()) {
          router.push('/dashboard');
        }
      }, 500);
    }
  } catch (err) {
    console.error('Registration error:', err);
    if (err.message) {
      localError.value = err.message;
    }

    // Remove the flag if registration fails
    localStorage.removeItem('emailPasswordSignUp');
  } finally {
    // Hide loading state
    loading.value = false;
  }
};

const handleGoogleSignIn = async () => {
  try {
    // Show loading state
    loading.value = true;

    // Clear any previous errors
    clearError();

    console.log('Starting Google sign-in from SignUp component');

    const result = await store.dispatch('auth/signInWithGoogle');

    console.log('Google sign-in result:', result?.needsUsername ? 'Needs username' : 'No username needed');

    // With our changes, Google sign-in will always require username selection
    // The result.needsUsername will be true, and the username selection modal will appear

    // If we're in the SignUp component and needsUsername is true, show the username modal
    if (result?.needsUsername) {
      console.log('Showing username selection modal in SignUp component');

      // The store should already be updated by the signInWithGoogle action
      // Just verify that needsUsername is set correctly
      if (!store.getters['auth/needsUsername']) {
        console.warn('WARNING: needsUsername is false but should be true!');

        // Force needsUsername to true with the correct isNewUser flag
        store.commit('auth/SET_NEEDS_USERNAME', {
          firebaseUser: result.user,
          isNewUser: result.isNewUser || false
        });
      }

      return;
    }

    // This code will only run if for some reason needsUsername is false
    if (!result?.needsUsername) {
      console.log('No username needed, redirecting to dashboard');
      // Force redirect to dashboard after a short delay
      setTimeout(() => {
        if (!forceDashboardRedirect()) {
          router.push('/dashboard');
        }
      }, 500);
    }
  } catch (err) {
    // Display a user-friendly error message
    console.error('Google sign-in error in SignUp:', err);
    if (err.message) {
      // Set a local error message for specific Google sign-in errors
      if (err.message.includes('popup') || err.message.includes('network')) {
        localError.value = err.message;
      } else {
        // Let the store handle other errors
        console.warn('Google sign-in error:', err.message);
      }
    }
  } finally {
    // Hide loading state
    loading.value = false;
  }
};

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value;
};

const appendGmail = () => {
  const trimmedEmail = email.value.trim();
  if (trimmedEmail.includes('@')) {
    const [username] = trimmedEmail.split('@');
    email.value = `${username.trim()}@gmail.com`;
  } else if (trimmedEmail !== '') {
    email.value = `${trimmedEmail}@gmail.com`;
  }
  nextTick(() => {
    emailInput.value.focus();
  });
};

const clearError = () => {
  localError.value = '';
  store.commit('auth/CLEAR_ERROR');
};

// Debounce function for username validation
const debounce = (fn, delay) => {
  let timeoutId;
  return function(...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), delay);
  };
};

// Validate username format and availability
const validateUsername = debounce(async () => {
  // Reset validation state
  usernameValid.value = false;
  usernameError.value = '';

  // Check if username is empty
  if (!newUsername.value) {
    console.log('Username is empty, validation skipped');
    return;
  }

  // Check username format
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  if (!usernameRegex.test(newUsername.value)) {
    console.log('Username format invalid:', newUsername.value);
    usernameError.value = 'Username must be 3-20 characters and contain only letters, numbers, and underscores';
    return;
  }

  // Check username availability
  try {
    console.log('Checking username availability for:', newUsername.value);

    // Call the store action to check username availability
    const isAvailable = await store.dispatch('auth/checkUsernameAvailability', newUsername.value);
    console.log('Username availability result:', isAvailable);

    if (!isAvailable) {
      usernameError.value = 'This username is already taken';
      usernameValid.value = false;
    } else {
      // Username is available
      console.log('Username is valid and available:', newUsername.value);
      usernameValid.value = true;
      usernameError.value = ''; // Ensure error is cleared
    }
  } catch (err) {
    console.error('Username validation error:', err);

    // Provide a more specific error message if possible
    if (err.response && err.response.data && err.response.data.message) {
      usernameError.value = err.response.data.message;
    } else if (err.message) {
      usernameError.value = err.message;
    } else {
      usernameError.value = 'Error checking username availability';
    }

    // If we can't check availability due to server issues, assume it's valid
    // The backend will do a final check during registration
    if (err.message && err.message.includes('Network Error')) {
      console.log('Network error during validation, assuming username is valid');
      usernameValid.value = true;
      usernameError.value = ''; // Clear error message
    }
  }

  // Log final validation state
  console.log('Final validation state:', {
    username: newUsername.value,
    valid: usernameValid.value,
    error: usernameError.value
  });
}, 500);

// Handle username submission for Google sign-up
const handleUsernameSubmit = async () => {
  if (!newUsername.value) {
    usernameError.value = 'Please enter a username';
    return;
  }

  // Validate username format
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  if (!usernameRegex.test(newUsername.value)) {
    usernameError.value = 'Username must be 3-20 characters and contain only letters, numbers, and underscores';
    return;
  }

  usernameLoading.value = true;
  usernameError.value = '';
  usernameValid.value = false;

  try {
    console.log('Submitting username:', newUsername.value);

    // Make sure we have a Firebase user
    const firebaseUser = store.getters['auth/firebaseUser'];
    if (!firebaseUser) {
      throw new Error('No Firebase user found. Please sign in again.');
    }

    console.log('Firebase user found:', firebaseUser.uid);

    // Check username availability one more time before submission
    console.log('Checking username availability one more time');

    let isAvailable = true; // Default to true if we can't check

    try {
      isAvailable = await store.dispatch('auth/checkUsernameAvailability', newUsername.value);
      console.log('Username availability result:', isAvailable);

      if (!isAvailable) {
        usernameError.value = 'This username is already taken';
        usernameLoading.value = false;
        return;
      }
    } catch (availabilityError) {
      console.warn('Error checking username availability, proceeding anyway:', availabilityError);
      // If we can't check availability due to server issues, we'll proceed anyway
      // The backend will do a final check during registration
    }

    console.log('Username is available, completing Google sign-up');

    // Try to complete the Google sign-up process with the chosen username
    try {
      const result = await store.dispatch('auth/completeGoogleSignUp', {
        username: newUsername.value
      });

      console.log('Google sign-up completed successfully:', result);

      // Redirect to dashboard
      router.push('/dashboard');
    } catch (registrationError) {
      console.error('Error completing Google sign-up:', registrationError);

      if (registrationError.response?.data?.message) {
        usernameError.value = registrationError.response.data.message;
      } else if (registrationError.message) {
        usernameError.value = registrationError.message;
      } else {
        usernameError.value = 'Failed to complete sign-up';
      }
    }
  } catch (err) {
    console.error('Username submission error:', err);
    usernameError.value = err.message || 'An error occurred';
  } finally {
    usernameLoading.value = false;
  }
};
</script>

<style scoped>
.sign-up-container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  transition: background-color 0.3s ease;
  background: linear-gradient(to bottom right, #f8fafc, #e2e8f0);
  color: #1e293b;
}

.sign-up-container.dark-mode {
  background: linear-gradient(to bottom right, #18181b, #09090b);
  color: #ffffff;
}

.main-content {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
}

.content-wrapper {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 28rem;
  padding: 0 0.5rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #ffffff;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  font-family: 'Rubik', sans-serif;
  animation: fadeIn 0.8s forwards;

}

.dark-mode .form {
  background-color: #18181b;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.logo-img {
  width: 50px;
  height: auto;
  margin-bottom: 6px;
}

.app-name {
  font-size: 18px;
  color: #151717;
  margin: 0;
}

.dark-mode .app-name {
  color: #ffffff;
}

.flex-column > label {
  color: #151717;
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 3px;
}

.dark-mode .flex-column > label {
  color: #ffffff;
}

.inputForm {
  border: 1.5px solid #ecedec;
  border-radius: 8px;
  height: 38px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  transition: border 0.2s ease-in-out;
  position: relative;
}

.dark-mode .inputForm {
  border: 1.5px solid #444;
}

.input {
  margin-left: 8px;
  border: none;
  width: 100%;
  height: 100%;
  font-size: 13px;
  background: transparent;
  color: #151717;
}

.dark-mode .input {
  color: #ffffff;
}

.input:focus {
  outline: none;
}

.input::placeholder {
  color: #888;
  font-size: 12px;
}

.dark-mode .input::placeholder {
  color: #aaa;
}

.inputForm:focus-within {
  border: 1.5px solid #2d79f3;
}

.theme-icon {
  color: #151717;
  width: 16px;
  height: 16px;
}

.dark-mode .theme-icon {
  color: #ffffff;
}

.email-input {
  padding-right: 70px;
}

.gmail-suffix {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #f0f0f0;
  padding: 4px 6px;
  border-radius: 4px;
  color: #151717;
  font-size: 11px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease-in-out;
}

.dark-mode .gmail-suffix {
  background-color: #333;
  color: #ffffff;
}

.gmail-suffix:hover {
  background-color: #e0e0e0;
}

.dark-mode .gmail-suffix:hover {
  background-color: #444;
}

.button-submit {
  background-color: #151717;
  border: none;
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  border-radius: 8px;
  height: 38px;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 4px;
}

.dark-mode .button-submit {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
}

.button-submit:hover:not(:disabled) {
  background-color: #292929;
}

.dark-mode .button-submit:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.2);
}

.button-submit:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.p {
  text-align: center;
  color: #151717;
  font-size: 11px;
  margin: 4px 0;
}

.dark-mode .p {
  color: #ffffff;
}

.span {
  font-size: 11px;
  color: #2d79f3;
  font-weight: 500;
  cursor: pointer;
}

.error {
  color: red;
  text-align: center;
  font-size: 11px;
  margin: 2px 0;
}



.eye-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 10px 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #e2e8f0;
}

.dark-mode .divider::before,
.dark-mode .divider::after {
  border-bottom: 1px solid #444;
}

.divider span {
  padding: 0 10px;
  font-size: 12px;
  color: #888;
}

.google-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: white;
  border: 1px solid #e2e8f0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  height: 38px;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .google-button {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid #444;
  color: #fff;
}

.google-button:hover:not(:disabled) {
  background-color: #f8f9fa;
  border-color: #d2d6dc;
}

.dark-mode .google-button:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}

.google-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.google-icon {
  width: 18px;
  height: 18px;
}



@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Username selection modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.dark-mode .modal {
  background-color: #1f2937;
  color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #111827;
}

.dark-mode .modal h2 {
  color: white;
}

.modal p {
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #4b5563;
}

.dark-mode .modal p {
  color: #d1d5db;
}

.note {
  font-size: 0.8rem;
  color: #6b7280;
  font-style: italic;
  margin-bottom: 1.5rem;
}

.dark-mode .note {
  color: #9ca3af;
}

.input-valid {
  border-color: #10b981 !important;
}

.input-error {
  border-color: #ef4444 !important;
}

.validation-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
}

.valid-icon {
  color: #10b981;
}

.error-icon {
  color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.success-message {
  color: #10b981;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.username-requirements {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f3f4f6;
  border-radius: 8px;
  font-size: 0.8rem;
}

.dark-mode .username-requirements {
  background-color: #374151;
}

.username-requirements p {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #4b5563;
}

.dark-mode .username-requirements p {
  color: #e5e7eb;
}

.username-requirements ul {
  padding-left: 1.5rem;
  margin: 0;
}

.username-requirements li {
  margin-bottom: 0.25rem;
  color: #4b5563;
}

.dark-mode .username-requirements li {
  color: #d1d5db;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
  gap: 0.75rem;
}

.reset-button {
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reset-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.reset-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.dark-mode .reset-button:disabled {
  background-color: #1e40af;
  opacity: 0.6;
}

/* Mobile-specific styles */
@media (max-width: 480px) {
  .content-wrapper {
    padding: 0;
    max-width: 100%;
  }

  .form {
    padding: 16px;
    border-radius: 12px;
    gap: 8px;
  }


}

/* Tablet and desktop styles */
@media (min-width: 640px) {
  .content-wrapper {
    padding: 0 1rem;
  }

  .form {
    padding: 30px;
    gap: 15px;
  }

  .logo-img {
    width: 80px;
  }

  .app-name {
    font-size: 24px;
  }

  .inputForm {
    height: 50px;
  }

  .button-submit {
    height: 50px;
    font-size: 15px;
  }

  .flex-column > label {
    font-size: 16px;
  }

  .input {
    font-size: 16px;
  }

  .input::placeholder {
    font-size: 14px;
  }

  .span, .p {
    font-size: 14px;
  }

  .theme-icon {
    width: 20px;
    height: 20px;
  }


}

/* Fix for iOS viewport height issue */
@supports (-webkit-touch-callout: none) {
  .main-content {
    min-height: -webkit-fill-available;
  }
}
</style>
<script lang="ts">
	import { onMount, createEventDispatcher, tick } from 'svelte';
	import type { Message } from '../types/chat';
	import { createVirtualScrollManager } from '../utils/virtualScroll';
	import MessageItem from './MessageItem.svelte';
	import MessageInput from './MessageInput.svelte';

	export let messages: Message[] = [];

	const dispatch = createEventDispatcher<{
		sendMessage: { text: string; media?: any };
		debugMetrics: {
			visibleItems: any[];
			totalHeight: number;
			containerHeight: number;
			scrollTop: number;
			virtualScrollManager: any;
		};
	}>();

	let scrollContainer: HTMLDivElement;
	let virtualContainer: HTMLDivElement;
	let isAutoScrollEnabled = true;
	let isScrolledToBottom = true;

	const virtualScroll = createVirtualScrollManager(80, 3);
	let visibleItems: any[] = [];
	let totalHeight = 0;
	let containerHeight = 0;

	$: {
		virtualScroll.setItems(messages);
		updateVirtualScroll();
	}

	onMount(() => {
		if (scrollContainer) {
			scrollContainer.addEventListener('scroll', handleScroll, { passive: true });

			// Initial scroll to bottom
			tick().then(() => {
				scrollToBottom();
			});

			// Observe container size changes
			const resizeObserver = new ResizeObserver(entries => {
				for (const entry of entries) {
					containerHeight = entry.contentRect.height;
					updateVirtualScroll();
				}
			});
			resizeObserver.observe(scrollContainer);

			return () => {
				scrollContainer?.removeEventListener('scroll', handleScroll);
				resizeObserver.disconnect();
			};
		}
	});

	function handleScroll() {
		if (!scrollContainer) return;

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;

		// Check if scrolled to bottom (with small tolerance)
		isScrolledToBottom = scrollHeight - scrollTop - clientHeight < 10;

		virtualScroll.updateScrollPosition(scrollTop, clientHeight);
		updateVirtualScroll();
	}

	function updateVirtualScroll() {
		visibleItems = virtualScroll.getVisibleItems();
		totalHeight = virtualScroll.getTotalHeight();

		// Emit debug metrics
		dispatch('debugMetrics', {
			visibleItems,
			totalHeight,
			containerHeight,
			scrollTop: scrollContainer?.scrollTop || 0,
			virtualScrollManager: virtualScroll
		});
	}

	function scrollToBottom() {
		if (scrollContainer) {
			scrollContainer.scrollTop = scrollContainer.scrollHeight;
		}
	}

	function handleSendMessage(event: CustomEvent<{ text: string; media?: any }>) {
		dispatch('sendMessage', event.detail);

		// Auto-scroll to bottom when sending a message
		tick().then(() => {
			if (isAutoScrollEnabled) {
				scrollToBottom();
			}
		});
	}

	function handleMessageHeightChange(event: CustomEvent<{ id: string; height: number }>) {
		const { id, height } = event.detail;
		virtualScroll.setItemHeight(id, height);
		updateVirtualScroll();

		// If we're at the bottom, maintain scroll position
		if (isScrolledToBottom) {
			tick().then(() => scrollToBottom());
		}
	}

	// Auto-scroll when new messages arrive (only if already at bottom)
	$: if (messages.length > 0 && isScrolledToBottom && isAutoScrollEnabled) {
		tick().then(() => scrollToBottom());
	}
</script>

<div class="chat-interface">
	<div
		class="messages-container"
		bind:this={scrollContainer}
	>
		<div
			class="virtual-container"
			bind:this={virtualContainer}
			style="height: {totalHeight}px; position: relative;"
		>
			{#each visibleItems as item (item.id)}
				<div
					class="message-wrapper"
					style="position: absolute; top: {item.offset}px; width: 100%;"
				>
					<MessageItem
						message={item.data}
						on:heightChange={handleMessageHeightChange}
					/>
				</div>
			{/each}
		</div>
	</div>

	{#if !isScrolledToBottom}
		<button
			class="scroll-to-bottom"
			on:click={scrollToBottom}
			aria-label="Scroll to bottom"
		>
			↓
		</button>
	{/if}

	<MessageInput on:sendMessage={handleSendMessage} />
</div>

<style>
	.chat-interface {
		display: flex;
		flex-direction: column;
		height: 100%;
		position: relative;
		background: #fff;
	}

	.messages-container {
		flex: 1;
		overflow-y: auto;
		overflow-x: hidden;
		scroll-behavior: smooth;
		position: relative;
	}

	.virtual-container {
		min-height: 100%;
	}

	.message-wrapper {
		will-change: transform;
	}

	.scroll-to-bottom {
		position: absolute;
		bottom: 80px;
		right: 20px;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background: #007bff;
		color: white;
		border: none;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 18px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		transition: all 0.2s ease;
		z-index: 10;
	}

	.scroll-to-bottom:hover {
		background: #0056b3;
		transform: scale(1.1);
	}

	/* Scrollbar styling */
	.messages-container::-webkit-scrollbar {
		width: 6px;
	}

	.messages-container::-webkit-scrollbar-track {
		background: #f1f1f1;
	}

	.messages-container::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
	}

	.messages-container::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
	}
</style>

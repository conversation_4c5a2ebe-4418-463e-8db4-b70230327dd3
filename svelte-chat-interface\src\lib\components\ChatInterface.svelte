<script lang="ts">
	import { onMount, createEventDispatcher, tick } from 'svelte';
	import type { Message } from '../types/chat';
	import { VList } from 'virtua/svelte';
	import MessageItem from './MessageItem.svelte';
	import MessageInput from './MessageInput.svelte';

	export let messages: Message[] = [];

	const dispatch = createEventDispatcher<{
		sendMessage: { text: string; media?: any };
		debugMetrics: {
			visibleItems: any[];
			totalHeight: number;
			containerHeight: number;
			scrollTop: number;
			virtualScrollManager: any;
		};
	}>();

	let vlist: any;
	let isAutoScrollEnabled = true;
	let isScrolledToBottom = true;

	// Debug metrics for Virtua
	let visibleItems: any[] = [];
	let totalHeight = 0;
	let containerHeight = 0;
	let scrollTop = 0;
	let startIndex = 0;
	let endIndex = 0;

	onMount(() => {
		// Initial scroll to bottom
		tick().then(() => {
			scrollToBottom();
		});
	});

	// Update debug metrics when messages change
	$: {
		if (messages.length > 0) {
			updateDebugMetrics();
		}
	}

	function updateDebugMetrics() {
		// Update debug metrics for the debug menu
		dispatch('debugMetrics', {
			visibleItems,
			totalHeight: messages.length * 80, // Estimate
			containerHeight,
			scrollTop,
			virtualScrollManager: {
				getMetrics: () => ({
					totalItems: messages.length,
					visibleItems: endIndex - startIndex,
					virtualizationRatio: messages.length > 0 ? ((messages.length - (endIndex - startIndex)) / messages.length) : 0
				})
			}
		});
	}

	function scrollToBottom() {
		if (vlist) {
			vlist.scrollToIndex(messages.length - 1, { align: 'end' });
		}
	}

	function handleSendMessage(event: CustomEvent<{ text: string; media?: any }>) {
		dispatch('sendMessage', event.detail);

		// Auto-scroll to bottom when sending a message
		tick().then(() => {
			if (isAutoScrollEnabled) {
				scrollToBottom();
			}
		});
	}

	function handleMessageHeightChange(_event: CustomEvent<{ id: string; height: number }>) {
		// Virtua handles height changes automatically
		// Just maintain scroll position if at bottom
		if (isScrolledToBottom) {
			tick().then(() => scrollToBottom());
		}
	}

	// Auto-scroll when new messages arrive (only if already at bottom)
	$: if (messages.length > 0 && isScrolledToBottom && isAutoScrollEnabled) {
		tick().then(() => scrollToBottom());
	}
</script>

<div class="chat-interface">
	<div class="messages-container">
		<VList
			bind:this={vlist}
			data={messages}
			style="height: 100%;"
		>
			{#snippet children(item, index)}
				<MessageItem
					message={item}
					on:heightChange={handleMessageHeightChange}
				/>
			{/snippet}
		</VList>
	</div>

	{#if !isScrolledToBottom}
		<button
			class="scroll-to-bottom"
			on:click={scrollToBottom}
			aria-label="Scroll to bottom"
		>
			↓
		</button>
	{/if}

	<MessageInput on:sendMessage={handleSendMessage} />
</div>

<style>
	.chat-interface {
		display: flex;
		flex-direction: column;
		height: 100%;
		position: relative;
		background: #fff;
	}

	.messages-container {
		flex: 1;
		overflow-y: auto;
		overflow-x: hidden;
		scroll-behavior: smooth; /* Re-enable smooth scrolling */
		position: relative;
		/* Optimized performance settings */
		contain: layout style;
		transform: translateZ(0); /* Force hardware acceleration */
		/* Better scrolling performance */
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
	}



	.scroll-to-bottom {
		position: absolute;
		bottom: 80px;
		right: 20px;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background: #007bff;
		color: white;
		border: none;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 18px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		transition: all 0.2s ease;
		z-index: 10;
	}

	.scroll-to-bottom:hover {
		background: #0056b3;
		transform: scale(1.1);
	}

	/* Scrollbar styling */
	.messages-container::-webkit-scrollbar {
		width: 6px;
	}

	.messages-container::-webkit-scrollbar-track {
		background: #f1f1f1;
	}

	.messages-container::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
	}

	.messages-container::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
	}
</style>

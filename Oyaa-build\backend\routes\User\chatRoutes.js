// routes/chatRoutes.js
const express = require('express');
const router = express.Router();
const chatController = require('../../controllers/User/chatController');

// Start a chat
router.post('/start', chatController.start);

// Send a message
router.post('/send', chatController.send);

// Retrieve chat messages
router.get('/retrieve', chatController.retrieve);
// Retrieve the last message from each conversation for a given user
router.get('/last-chats', chatController.getLastChats);

module.exports = router;
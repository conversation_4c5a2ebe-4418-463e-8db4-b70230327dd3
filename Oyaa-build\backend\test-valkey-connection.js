// test-valkey-connection.js
require('dotenv').config();
const redis = require('redis');

// Use VALKEY_URI from the environment
const valkeyUri = process.env.VALKEY_URI;

console.log('Attempting to connect to Val<PERSON> with URI:', valkeyUri);

const clientOptions = { url: valkeyUri };

const valkeyClient = redis.createClient(clientOptions);

// Connect the client
(async () => {
  try {
    await valkeyClient.connect();
    console.log('✅ Valkey client connected successfully!');
    
    // Test setting and getting a value
    await valkeyClient.set('test_key', 'test_value');
    const value = await valkeyClient.get('test_key');
    console.log('Test key value:', value);
    
    // Clean up
    await valkeyClient.del('test_key');
    await valkeyClient.disconnect();
    console.log('Valkey client disconnected');
  } catch (err) {
    console.error('❌ Error connecting to <PERSON><PERSON>:', err.message);
    process.exit(1);
  }
})();

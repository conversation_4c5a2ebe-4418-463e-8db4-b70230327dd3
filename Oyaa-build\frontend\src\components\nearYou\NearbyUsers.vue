<template>
  <div class="nearby-users-container">
    <!-- Added header with back button -->
    <div class="page-header">
      <button class="back-button" @click="$router.push('/dashboard')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>Nearby Users</h1>
      <div class="header-spacer"></div>
    </div>
    
    <div class="nearby-users">
      <!-- Header displaying current user info -->
      <header class="user-header">
        <div class="user-info">
          <div class="avatar" :style="currentUserAvatar">
            <span v-if="!currentUser || !currentUser.avatarUrl">
              {{ currentUser ? getInitials(currentUser.username) : 'Y' }}
            </span>
          </div>
          <span class="username">You</span>
        </div>
      </header>

      <!-- Navigation Button with Maps Icon -->
      <div class="view-toggle">
        <button @click="goToMap" class="view-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
          View Map
        </button>
      </div>

      <!-- Loading, error, and content states -->
      <div v-if="initialLoad" class="skeleton-container">
        <!-- Show 5 skeleton items while the first fetch is in progress -->
        <NearbyUserItem
          v-for="n in 5"
          :key="n"
          :user="{}"
          :loading="true"
        />
      </div>
      <div v-else-if="error" class="status-message error">{{ error }}</div>
      <div v-else>
        <div v-if="filteredUsers.length === 0" class="empty-state">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"></path>
          </svg>
          <p>No nearby users found</p>
          <p class="hint">Try expanding your search radius or check back later</p>
        </div>
        <div v-else class="users-list">
          <NearbyUserItem
            v-for="user in filteredUsers"
            :key="user.id"
            :user="user"
          />
          <button
            v-if="hasMore"
            @click="loadMore"
            class="load-more"
            :disabled="loading"
          >
            <span v-if="loading">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loader">
                <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
              </svg>
              Loading...
            </span>
            <span v-else>Load More</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import NearbyUserItem from './NearbyUserItem.vue';

export default {
  name: "NearbyUsers",
  components: {
    NearbyUserItem,
  },
  data() {
    return {
      users: [],
      loading: false,
      initialLoad: true, // NEW: tracks first load state
      error: null,
      limit: 20,
      offset: 0,
      radius: 1000, // default search radius in meters
      hasMore: true,
      coordinates: {
        lat: null,
        lng: null,
      },
    };
  },
  computed: {
    // Get the current user from the store
    currentUser() {
      return this.$store.getters['auth/user'];
    },
    // Exclude the current user from the list of nearby users
    filteredUsers() {
      if (this.currentUser) {
        return this.users.filter(user => user.id !== this.currentUser.id);
      }
      return this.users;
    },
    // Use the user's avatar URL for the header background
    currentUserAvatar() {
      return this.currentUser && this.currentUser.avatarUrl
        ? { backgroundImage: `url(${this.currentUser.avatarUrl})` }
        : {};
    },
  },
  created() {
    // Get current location then fetch nearby users
    this.getCurrentLocation()
      .catch(err => {
        console.warn("Geolocation failed:", err);
      })
      .finally(() => {
        this.fetchNearbyUsers();
      });
  },
  mounted() {
    // Preload the map view component
    import(
      /* webpackChunkName: "nearby-map", webpackPrefetch: true */
      "./NearbyUsersMap.vue"
    )
      .then(() => {
        console.log("NearbyUsersMap component preloaded");
      })
      .catch(err => {
        console.error("Error preloading NearbyUsersMap:", err);
      });
  },
  methods: {
    getInitials(name) {
      if (!name) return '';
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    },
    async getCurrentLocation() {
      return new Promise((resolve, reject) => {
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            async position => {
              this.coordinates.lat = position.coords.latitude;
              this.coordinates.lng = position.coords.longitude;
              console.log("Geolocation success:", this.coordinates);
              // Update backend with new location
              await this.updateUserLocation();
              resolve();
            },
            error => {
              console.error("Geolocation error:", error);
              reject(error);
            }
          );
        } else {
          reject(new Error("Geolocation not supported"));
        }
      });
    },
    async updateUserLocation() {
      try {
        const userId = this.$store.getters['auth/userId'];
        if (!userId) {
          console.error("No authenticated user found in the store.");
          return;
        }
        console.log("Updating location for user ID:", userId);
        await axios.post(
          `${import.meta.env.VITE_API_BASE_URL}/api/location/update`,
          {
            userId,
            latitude: this.coordinates.lat,
            longitude: this.coordinates.lng,
          }
        );
      } catch (error) {
        console.error("Error updating location:", error);
      }
    },
    async fetchNearbyUsers() {
      this.loading = true;
      this.error = null;
      try {
        const params = {
          limit: this.limit,
          offset: this.offset,
          radius: this.radius,
          ...(this.coordinates.lat && this.coordinates.lng && {
            lat: this.coordinates.lat,
            lng: this.coordinates.lng,
          }),
        };
        const token = this.$store.getters['auth/token'];
        if (!token) {
          console.error("No token found, user might not be authenticated.");
          return;
        }
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/proximity/nearby`,
          {
            params,
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const { results } = response.data;
        if (this.offset === 0) {
          this.users = results;
        } else {
          this.users = this.users.concat(results);
        }
        if (results.length < this.limit) {
          this.hasMore = false;
        }
      } catch (error) {
        console.error("Error fetching nearby users:", error);
        this.error =
          (error.response && error.response.data && error.response.data.error) ||
          error.message ||
          "An error occurred while fetching nearby users.";
      } finally {
        this.loading = false;
        // Mark initial load complete once the first API call is done
        if (this.offset === 0) {
          this.initialLoad = false;
        }
      }
    },
    loadMore() {
      this.offset += this.limit;
      this.fetchNearbyUsers();
    },
    goToMap() {
      this.$router.push({ name: 'NearbyUsersMap' });
    },
  },
};
</script>

<style scoped>
.nearby-users-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

/* New header styles */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.header-spacer {
  width: 36px; /* Same width as back button for balance */
}

.nearby-users {
  padding: 1.5rem;
  background-color: var(--bg-primary);
  min-height: calc(100vh - 60px); /* Adjust for header height */
  color: var(--text-primary);
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--bg-tertiary);
  border-radius: 12px;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background-color: var(--accent-tertiary);
  margin-right: 0.75rem;
  flex-shrink: 0;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  font-weight: 500;
}

.username {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.view-toggle {
  margin-bottom: 1.25rem;
  text-align: right;
}

.view-button {
  padding: 0.625rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all var(--transition-fast);
}

.view-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
  transform: translateY(-1px);
}

.view-button:active {
  transform: translateY(0);
}

.status-message {
  text-align: center;
  margin: 2rem 0;
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--bg-secondary);
}

.status-message.error {
  color: #f87171;
  border: 1px solid rgba(248, 113, 113, 0.2);
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  margin: 1.5rem auto 0;
  padding: 0.75rem;
  font-size: 0.875rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.load-more:hover:not(:disabled) {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.load-more:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loader {
  animation: spin 1.2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.empty-state svg {
  color: var(--accent-tertiary);
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.empty-state .hint {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.users-list {
  display: flex;
  flex-direction: column;
}

.skeleton-container {
  margin-top: 1rem;
}
</style>
<template>
    <div class="attachment-menu">
      <button 
        v-for="option in attachmentOptions" 
        :key="option.type"
        class="attachment-option"
        @click="selectAttachment(option.type)"
        :aria-label="`Attach ${option.label}`"
      >
        <div class="attachment-icon" :style="{ backgroundColor: option.color }">
          <component :is="option.icon" class="icon" />
        </div>
        <span class="attachment-label">{{ option.label }}</span>
      </button>
    </div>
  </template>
  
  <script setup>
  import { ImageIcon, FileIcon, MusicIcon, MapPinIcon, CalendarIcon } from 'lucide-vue-next';
  
  const emit = defineEmits(['select-attachment']);
  
  const attachmentOptions = [
    { type: 'image', label: 'Image', icon: ImageIcon, color: '#4CAF50' },
    { type: 'file', label: 'File', icon: FileIcon, color: '#2196F3' },
    { type: 'audio', label: 'Audio', icon: MusicIcon, color: '#FF9800' },
    { type: 'location', label: 'Location', icon: MapPinIcon, color: '#E91E63' },
    { type: 'schedule', label: 'Schedule', icon: CalendarIcon, color: '#9C27B0' }
  ];
  
  const selectAttachment = (type) => {
    emit('select-attachment', type);
  };
  </script>
  
  <style>
  .attachment-menu {
    position: absolute;
    bottom: 100%;
    right: 0;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: 0 -4px 12px var(--shadow);
    margin-bottom: 8px;
    padding: 8px;
    z-index: 10;
    animation: slideUp 0.2s ease;
  }
  
  .attachment-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: transparent;
    border: none;
    border-radius: 4px;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .attachment-option:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .attachment-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 12px;
  }
  
  .attachment-label {
    font-size: 14px;
  }
  
  @keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  </style>
  
  
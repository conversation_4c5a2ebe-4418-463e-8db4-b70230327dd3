const authService = require('../../services/User/authService');
const logger = require('../../utils/logger');

class AuthController {
  /**
   * Register method - now uses Firebase
   */
  async register(req, res) {
    try {
      // Extract the Firebase ID token from the Authorization header
      const idToken = req.headers.authorization?.split(' ')[1];
      if (!idToken) {
        return res.status(401).json({ message: 'No Firebase token provided' });
      }

      // Verify the Firebase ID token
      const decodedToken = await authService.verifyFirebaseToken(idToken);

      // Extract user data from request body
      const { username, avatar } = req.body;
      const firebaseUid = decodedToken.uid;
      const email = decodedToken.email;

      // Register the user in our database
      const result = await authService.register(username, email, firebaseUid, avatar);

      res.status(201).json({ message: 'User registered', ...result });
    } catch (err) {
      logger.error('Register error:', err);
      res.status(400).json({ message: err.message });
    }
  }

  /**
   * Login method - now uses Firebase
   */
  async login(req, res) {
    try {
      // Extract the Firebase ID token from the Authorization header
      const idToken = req.headers.authorization?.split(' ')[1];
      if (!idToken) {
        return res.status(401).json({ message: 'No Firebase token provided' });
      }

      // Verify the Firebase ID token
      const decodedToken = await authService.verifyFirebaseToken(idToken);

      // Login the user in our database
      const result = await authService.login(decodedToken.uid);

      res.status(200).json({ message: 'Login successful', ...result });
    } catch (err) {
      logger.error('Login error:', err);
      res.status(401).json({ message: err.message });
    }
  }

  /**
   * Register a user with Firebase authentication
   */
  async firebaseRegister(req, res) {
    try {
      // Extract the Firebase ID token from the Authorization header
      const idToken = req.headers.authorization?.split(' ')[1];
      if (!idToken) {
        return res.status(401).json({ message: 'No Firebase token provided' });
      }

      // Verify the Firebase ID token
      const decodedToken = await authService.verifyFirebaseToken(idToken);

      // Extract user data from request body
      const { username, avatar, handle } = req.body;
      const firebaseUid = decodedToken.uid;
      const email = decodedToken.email;

      // Check if the user already exists in our database
      const existingUser = await authService.findUserByFirebaseUid(firebaseUid);

      // If username is not provided and the user doesn't exist yet, require username selection
      if (!username && !existingUser) {
        logger.info(`New user with Firebase UID: ${firebaseUid} needs to select a username`);
        return res.status(403).json({
          message: 'Please select a username to complete registration',
          needsRegistration: true,
          firebaseUid: firebaseUid,
          email: email
        });
      }

      logger.info(`Processing Firebase registration for UID: ${firebaseUid}, email: ${email}, username: ${username || 'not provided'}`);

      // Use the enhanced firebaseRegister method that handles existing users
      const result = await authService.firebaseRegister(firebaseUid, email, username, avatar, handle);

      // Determine if this was a new registration or an update
      const isNewUser = !await authService.findUserByFirebaseUid(firebaseUid);
      const message = isNewUser ? 'User registered' : 'Username updated';

      logger.info(`${message} for Firebase UID: ${firebaseUid}`);

      res.status(201).json({ message, ...result });
    } catch (err) {
      logger.error('Register error:', err);
      res.status(400).json({ message: err.message });
    }
  }

  /**
   * Login a user with Firebase authentication
   * If the user doesn't exist, prompt for username selection for registration
   */
  async firebaseLogin(req, res) {
    try {
      // Extract the Firebase ID token from the Authorization header
      const idToken = req.headers.authorization?.split(' ')[1];
      if (!idToken) {
        return res.status(401).json({ message: 'No Firebase token provided' });
      }

      // Verify the Firebase ID token
      const decodedToken = await authService.verifyFirebaseToken(idToken);
      const firebaseUid = decodedToken.uid;
      const email = decodedToken.email || `user-${firebaseUid}@example.com`;

      try {
        // Check if the user exists in our database
        const existingUser = await authService.findUserByFirebaseUid(firebaseUid);

        if (existingUser) {
          // User exists, proceed with normal login
          logger.info(`User with Firebase UID ${firebaseUid} found, proceeding with login`);
          const result = await authService.login(firebaseUid);
          return res.status(200).json({ message: 'Login successful', ...result });
        } else {
          // User doesn't exist, this is a new user that needs to register
          logger.info(`User with Firebase UID ${firebaseUid} not found, requiring username selection for registration`);

          // Get avatar from Firebase user if available
          const avatar = decodedToken.picture || null;

          // Return a response indicating the user needs to select a username to complete registration
          return res.status(403).json({
            message: 'Please select a username to complete registration',
            needsRegistration: true,
            isNewUser: true, // Explicitly mark as a new user
            firebaseUid: firebaseUid,
            email: email,
            avatar: avatar
          });
        }
      } catch (err) {
        // Handle other errors
        logger.error(`Error during Firebase login for UID ${firebaseUid}:`, err);
        throw err;
      }
    } catch (err) {
      logger.error('Firebase login error:', err);
      res.status(401).json({ message: err.message });
    }
  }

  /**
   * Check if a username is available
   */
  async checkUsername(req, res) {
    try {
      const { username } = req.params;

      // Validate input
      if (!username) {
        return res.status(400).json({
          available: false,
          message: 'Username is required'
        });
      }

      // Check username format
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
      if (!usernameRegex.test(username)) {
        return res.status(400).json({
          available: false,
          message: 'Username must be 3-20 characters and contain only letters, numbers, and underscores'
        });
      }

      // Check availability
      const available = await authService.checkUsernameAvailability(username);

      logger.info(`Username availability check for "${username}": ${available ? 'Available' : 'Taken'}`);

      res.status(200).json({
        available,
        message: available ? 'Username is available' : 'Username is already taken'
      });
    } catch (err) {
      logger.error('Username check error:', err);
      res.status(400).json({
        available: false,
        message: err.message
      });
    }
  }
  /**
   * Update a user's username
   */
  async updateUsername(req, res) {
    try {
      // Get the user ID from the authenticated request
      const userId = req.user.id;

      // Get the new username and handle from the request body
      const { username, handle } = req.body;

      // Validate input
      if (!username) {
        return res.status(400).json({ message: 'Username is required' });
      }

      // Check username format
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
      if (!usernameRegex.test(username)) {
        return res.status(400).json({
          message: 'Username must be 3-20 characters and contain only letters, numbers, and underscores'
        });
      }

      // Check if the username is available
      const available = await authService.checkUsernameAvailability(username);
      if (!available) {
        return res.status(400).json({ message: 'Username is already taken' });
      }

      // Update the username and handle
      const result = await authService.updateUsername(userId, username, handle);

      logger.info(`Username updated for user ${userId} to ${username}`);

      res.status(200).json({
        message: 'Username updated successfully',
        user: result.user
      });
    } catch (err) {
      logger.error('Username update error:', err);
      res.status(400).json({ message: err.message });
    }
  }

  /**
   * Middleware to protect routes (e.g., check if the user is logged in)
   * Now uses only Firebase tokens
   */
  async protect(req, res, next) {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ message: 'You are not logged in!' });
      }

      const token = authHeader.split(' ')[1];

      try {
        // Verify the Firebase token
        const decodedFirebaseToken = await authService.verifyFirebaseToken(token);
        const firebaseUid = decodedFirebaseToken.uid;

        try {
          // If successful, find the user in our database
          const user = await authService.login(firebaseUid);

          // Attach the user data to the request object
          req.user = {
            userId: user.user.id,
            firebaseUid: firebaseUid,
            email: user.user.email,
            username: user.user.username,
            avatar: user.user.avatar,
            handle: user.user.handle || user.user.username // Fallback to username if handle is not set
          };
          next();
        } catch (userError) {
          // If user not found in our database but Firebase token is valid,
          // we'll redirect to registration instead of returning an error
          if (userError.message.includes('User not found')) {
            logger.warn(`User with Firebase UID ${firebaseUid} has valid token but no account`);
            return res.status(403).json({
              message: 'Please complete registration first',
              needsRegistration: true,
              isNewUser: true, // Explicitly mark as a new user
              firebaseUid: firebaseUid,
              email: decodedFirebaseToken.email
            });
          } else {
            throw userError;
          }
        }
      } catch (error) {
        logger.error('Token verification error:', error);
        return res.status(401).json({ message: 'Invalid or expired token. Please log in again.' });
      }
    } catch (err) {
      logger.error('Auth middleware error:', err);
      res.status(401).json({ message: err.message });
    }
  }

  /**
   * Middleware to restrict access to specific roles
   */
  restrictTo(...roles) {
    return (req, res, next) => {
      if (!roles.includes(req.user.role)) {
        return res.status(403).json({
          message: 'You do not have permission to perform this action',
        });
      }
      next();
    };
  }

  /**
   * Get user info by Firebase UID
   */
  async getUserInfo(req, res) {
    try {
      // The user object is attached by the auth middleware
      if (!req.user || !req.user.firebaseUid) {
        return res.status(401).json({ message: 'Unauthorized' });
      }

      // Get user info from database
      const user = await authService.findUserByFirebaseUid(req.user.firebaseUid);

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Return user info
      res.status(200).json({
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          avatar: user.avatar,
          firebaseUid: user.firebase_uid,
          handle: user.handle || user.username // Fallback to username if handle is not set
        }
      });
    } catch (err) {
      logger.error('Error getting user info:', err);
      res.status(500).json({ message: 'Server error', error: err.message });
    }
  }
}

module.exports = new AuthController();

<template>
  <div class="reply-preview">
    <span class="reply-text">
      <strong>Replying to:</strong>
      <span v-if="displayContent">{{ displayContent }}</span>
      <template v-if="displayMedia">
        <span v-if="displayContent" class="media-indicator">
          <i class="fas" :class="iconClass" :title="mediaTypeLabel"></i>
        </span>
        <template v-else>
          <img
            v-if="isImage"
            :src="displayMedia.mediaUrl"
            alt="Media preview"
            class="reply-media-preview"
          />
          <img
            v-else-if="isVideo && hasThumbnail"
            :src="displayMedia.thumbnailUrl"
            alt="Video thumbnail"
            class="reply-media-preview"
          />
          <span v-else class="media-preview-icon">
            <i class="fas" :class="iconClass" :title="mediaTypeLabel"></i>
          </span>
        </template>
      </template>
    </span>
    <button class="cancel-reply" @click="$emit('cancel-reply')" aria-label="Cancel Reply">
      <i class="fas fa-times"></i>
    </button>
  </div>
</template>

<script>
export default {
  name: 'ReplyPreview',
  props: {
    reply: {
      type: Object,
      required: true,
    },
  },
  computed: {
    displayContent() {
      return this.reply.reply_message || this.reply.message || this.reply.content || '';
    },
    displayMedia() {
      return this.reply.media || this.reply.reply_media || null;
    },
    isImage() {
      const media = this.displayMedia;
      return media && media.mediaType && media.mediaType.startsWith('image');
    },
    isVideo() {
      const media = this.displayMedia;
      return media && media.mediaType && media.mediaType.startsWith('video');
    },
    hasThumbnail() {
      return this.displayMedia && this.displayMedia.thumbnailUrl;
    },
    iconClass() {
      if (!this.displayMedia) return '';
      const type = this.displayMedia.mediaType || '';
      if (type.startsWith('video')) return 'fa-video';
      if (type.startsWith('audio')) return 'fa-music';
      if (type.startsWith('image')) return 'fa-image';
      return 'fa-file';
    },
    mediaTypeLabel() {
      if (!this.displayMedia) return '';
      const type = this.displayMedia.mediaType || '';
      if (type.startsWith('video')) return 'Video';
      if (type.startsWith('audio')) return 'Audio';
      if (type.startsWith('image')) return 'Image';
      return 'File';
    },
  },
};
</script>

<style scoped>
.reply-preview {
  background-color: #2f3136;
  border-left: 4px solid #3498db;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.reply-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}
.reply-media-preview {
  max-width: 50px;
  max-height: 50px;
  border-radius: 4px;
  object-fit: cover;
}
.media-preview-icon i {
  font-size: 1.5rem;
}
.media-indicator i {
  font-size: 0.8rem;
  margin-left: 0.5rem;
}
.cancel-reply {
  background: transparent;
  border: none;
  color: #dcddde;
  cursor: pointer;
  font-size: 1rem;
}
.cancel-reply:hover {
  color: #ffffff;
}
</style>
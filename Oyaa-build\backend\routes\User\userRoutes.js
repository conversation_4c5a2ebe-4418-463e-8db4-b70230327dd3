const express = require('express');
const userController = require('../../controllers/User/userController');
const authMiddleware = require('../../middleware/authMiddleware');
const router = express.Router();

// Specific routes (no parameters or fixed paths)
router.get('/me', authMiddleware, userController.getCurrentUser);
router.put('/me', authMiddleware, userController.updateProfile);
router.put('/location', userController.updateLocation);
router.get('/search/:username', userController.searchUserByUsername);
router.put('/avatar', userController.updateAvatar);
router.post('/block-user', authMiddleware, userController.blockUser);
router.get('/blocked-users', authMiddleware, userController.getBlockedUsers);

// Parameterized routes with numeric IDs only
router.get('/nearby-users/:userId(\\d+)', userController.getNearbyUsers);
router.get('/:id(\\d+)', userController.getUserById);
router.delete('/block-user/:blockedId(\\d+)', authMiddleware, userController.unblockUser);
router.get('/tags/suggestions', userController.getTagSuggestions);
router.get('/search-by-tags', authMiddleware, userController.searchUsersByTags);
router.get('/search-fuzzy/:username', userController.searchUserFuzzy);
router.get('/suggest', userController.suggestUsernames);
module.exports = router;
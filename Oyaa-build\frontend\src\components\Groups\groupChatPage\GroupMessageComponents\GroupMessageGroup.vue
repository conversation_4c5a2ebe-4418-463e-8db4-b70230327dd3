<template>
  <div
    class="message-group"
    :class="{
      'own-message-group': isOwnMessage,
      'audio-only-group': audioOnlyMessage
    }"
    :id="`message-${message.id}`"
  >
    <!-- User avatar and info - only shown for first message in a group from other users -->
    <div v-if="!isOwnMessage" class="user-avatar-container">
      <div v-if="showAvatar" class="user-avatar">
        <img
          v-if="senderAvatar"
          :src="senderAvatar"
          :alt="`${senderName}'s avatar`"
          loading="lazy"
        />
        <div v-else class="avatar-placeholder" :aria-label="`${senderName}'s avatar placeholder`">
          {{ getInitials(senderName) }}
        </div>
      </div>
      <div v-else class="avatar-spacer" aria-hidden="true"></div>
    </div>

    <div class="message-bubbles-container">
      <!-- Sender name - only shown for first message in a group from other users -->
      <div v-if="!isOwnMessage && showName" class="sender-name">
        {{ senderName }}
      </div>

      <!-- Message bubble - only shown if there's text content or non-audio media -->
      <div
        v-if="hasTextContent || hasNonAudioMedia"
        class="message-bubble"
        :class="{
          'own-message': isOwnMessage,
          'other-message': !isOwnMessage,
          'deleted-message': message.deleted
        }"
        @contextmenu.prevent="$emit('show-context-menu', $event, message)"
      >
        <!-- Message content -->
        <div class="message-content">
          <span v-if="message.deleted" class="deleted-text">This message has been deleted</span>
          <span v-else>{{ message.content || message.message }}</span>
        </div>

        <!-- Non-audio media -->
        <div v-if="hasNonAudioMedia" class="message-media">
          <div
            v-for="(media, index) in nonAudioMedia"
            :key="index"
            class="media-wrapper"
          >
            <!-- Image media -->
            <group-media-preview
              v-if="media.mediaType"
              :media="media"
              :message-id="message.id"
              :group-id="message.group_id"
              :sender-name="senderName"
              @open-gallery="$emit('open-media', { media: normalizedMedia, index })"
            />

            <!-- Legacy media (fallback) -->
            <template v-else>
              <img
                :src="media.url || media.mediaUrl"
                :alt="`Media attachment ${index + 1}`"
                class="media-image"
                loading="lazy"
                @click="$emit('open-media', { media: normalizedMedia, index })"
              />
              <div class="media-overlay">
                <span class="media-view-text">View</span>
              </div>
            </template>
          </div>
        </div>

        <!-- Message footer -->
        <div class="message-footer">
          <!-- Timestamp -->
          <span class="message-time" :title="fullDateTime">{{ formattedTime }}</span>

          <!-- Status indicators (only for own messages) -->
          <span v-if="isOwnMessage" class="message-status" aria-live="polite">
            <span v-if="message.status === 'sending'" class="status-sending" aria-label="Sending message">
              <span class="loading-dots"></span>
            </span>
            <span v-else-if="message.status === 'sent' || message.sent_count > 0" class="status-sent" aria-label="Message sent">✓</span>
            <span v-else-if="message.status === 'delivered' || message.delivered_count > 0" class="status-delivered" aria-label="Message delivered">✓✓</span>
          </span>
        </div>
      </div>

      <!-- Audio media - displayed outside the bubble -->
      <div
        v-for="(media, index) in audioMedia"
        :key="`audio-${index}`"
        class="audio-container"
        :class="{ 'own-audio': isOwnMessage, 'audio-only': audioOnlyMessage }"
      >
        <group-howler-audio-player
          :media-url="media.url || media.mediaUrl"
          :is-sender="isOwnMessage"
          :duration="media.duration || 0"
          :lazy-load="true"
          class="standalone-audio-player"
        />

        <!-- Timestamp for audio-only messages -->
        <div v-if="audioOnlyMessage" class="audio-timestamp" :title="fullDateTime">
          {{ formattedTime }}
          <span v-if="isOwnMessage && message.status === 'sending'" class="status-sending" aria-label="Sending message">
            <span class="loading-dots"></span>
          </span>
          <span v-else-if="isOwnMessage && (message.status === 'sent' || message.sent_count > 0)" class="status-sent" aria-label="Message sent">✓</span>
          <span v-else-if="isOwnMessage && (message.status === 'delivered' || message.delivered_count > 0)" class="status-delivered" aria-label="Message delivered">✓✓</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onBeforeUnmount } from 'vue';
import GroupMediaPreview from './GroupMediaPreview.vue';
import GroupHowlerAudioPlayer from './GroupHowlerAudioPlayer.vue';

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  currentUser: {
    type: Object,
    required: true
  },
  registerRef: {
    type: Function,
    required: true
  },
  showAvatar: {
    type: Boolean,
    default: true
  },
  showName: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['show-context-menu', 'open-media']);

const isOwnMessage = computed(() => {
  return props.message.sender_id === props.currentUser.id;
});

const senderName = computed(() => {
  return props.message.sender_name || props.message.sender?.username || 'Unknown User';
});

const senderAvatar = computed(() => {
  return props.message.sender_avatar || props.message.sender?.avatar || null;
});

const formattedTime = computed(() => {
  if (!props.message.sent_at && !props.message.created_at) return '';
  const timestamp = props.message.sent_at || props.message.created_at;
  return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
});

const fullDateTime = computed(() => {
  if (!props.message.sent_at && !props.message.created_at) return '';
  const timestamp = props.message.sent_at || props.message.created_at;
  return new Date(timestamp).toLocaleString([], {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
});

const hasTextContent = computed(() => {
  return !!props.message.message || !!props.message.content;
});

const hasMedia = computed(() => {
  if (!props.message.media) return false;

  // Check if media is an array with at least one item
  if (Array.isArray(props.message.media)) {
    return props.message.media.length > 0 && props.message.media.some(m => m !== null);
  }

  // Check if media is an object with a URL property
  if (typeof props.message.media === 'object') {
    return !!(
      props.message.media.url ||
      props.message.media.mediaUrl ||
      props.message.media.media_url
    );
  }

  return false;
});

// Separate audio media from other media types
const audioMedia = computed(() => {
  if (!hasMedia.value) return [];

  if (Array.isArray(normalizedMedia.value)) {
    return normalizedMedia.value.filter(media =>
      isAudioFile(media.url || media.mediaUrl)
    );
  }

  return [];
});

const nonAudioMedia = computed(() => {
  if (!hasMedia.value) return [];

  if (Array.isArray(normalizedMedia.value)) {
    return normalizedMedia.value.filter(media =>
      !isAudioFile(media.url || media.mediaUrl)
    );
  }

  return [];
});

const hasNonAudioMedia = computed(() => {
  return nonAudioMedia.value.length > 0;
});

const audioOnlyMessage = computed(() => {
  return !hasTextContent.value &&
         !hasNonAudioMedia.value &&
         audioMedia.value.length > 0;
});

// Normalize media array to ensure consistent format
const normalizedMedia = computed(() => {
  if (!props.message.media) return [];

  // If media is already an array, process it
  if (Array.isArray(props.message.media)) {
    return props.message.media.map(media => {
      if (!media) return null;
      // Ensure each media item has the required properties
      return {
        ...media,
        mediaUrl: media.url || media.mediaUrl || media.media_url,
        mediaType: getMediaType(media.url || media.mediaUrl || media.media_url || media.media_type),
        senderName: senderName.value,
        senderAvatar: senderAvatar.value,
        createdAt: props.message.created_at || props.message.sent_at,
        messageId: props.message.id,
        groupId: props.message.group_id
      };
    }).filter(media => media !== null);
  }

  // If media is a single object, convert to array
  if (typeof props.message.media === 'object') {
    return [{
      ...props.message.media,
      mediaUrl: props.message.media.url || props.message.media.mediaUrl || props.message.media.media_url,
      mediaType: getMediaType(props.message.media.url || props.message.media.mediaUrl || props.message.media.media_url || props.message.media.media_type),
      senderName: senderName.value,
      senderAvatar: senderAvatar.value,
      createdAt: props.message.created_at || props.message.sent_at,
      messageId: props.message.id,
      groupId: props.message.group_id
    }];
  }

  // If media is something else, return empty array
  return [];
});

// Determine media type from URL or extension
const getMediaType = (url) => {
  if (!url) return null;

  // If media_type is already provided in the URL (from Cloudinary)
  if (url.includes('cloudinary.com') && url.includes('/video/upload/') &&
      (url.endsWith('.webm') || url.endsWith('.mp3') || url.endsWith('.ogg') || url.endsWith('.m4a'))) {
    return 'audio/mp3';
  }

  // Check for image extensions
  if (/\.(jpe?g|png|gif|webp|bmp|svg)$/i.test(url)) {
    return 'image/jpeg';
  }

  // Check for video extensions
  if (/\.(mp4|webm|mov|avi|wmv|flv|mkv)$/i.test(url)) {
    return 'video/mp4';
  }

  // Check for audio extensions
  if (/\.(mp3|wav|ogg|m4a|aac|flac)$/i.test(url)) {
    return 'audio/mp3';
  }

  return null;
};

// Check if file is an audio file
const isAudioFile = (url) => {
  if (!url) return false;

  // Check if it's a Cloudinary audio URL (they use video resource type for audio)
  if (url.includes('cloudinary.com') && url.includes('/video/upload/') &&
      (url.endsWith('.webm') || url.endsWith('.mp3') || url.endsWith('.ogg') || url.endsWith('.m4a'))) {
    return true;
  }

  // Check for standard audio extensions
  return /\.(mp3|wav|ogg|m4a|aac|flac|webm)$/i.test(url);
};

const getInitials = (name) => {
  if (!name) return '?';
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

onMounted(() => {
  // Register this component's DOM element with the parent
  props.registerRef(props.message.id, document.getElementById(`message-${props.message.id}`));
});

onBeforeUnmount(() => {
  // Unregister this component's DOM element
  props.registerRef(props.message.id, null);
});
</script>

<style scoped>
.message-group {
  display: flex;
  margin-bottom: 8px;
  width: 100%;
  position: relative;
}

@media (max-width: 768px) {
  .message-group {
    margin-bottom: 6px;
  }
}

@media (max-width: 480px) {
  .message-group {
    margin-bottom: 4px;
  }
}

.own-message-group {
  justify-content: flex-end;
}

.user-avatar-container {
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
  width: 36px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
  .user-avatar-container {
    margin-right: 6px;
    width: 32px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    border-width: 1.5px;
  }
}

@media (max-width: 480px) {
  .user-avatar-container {
    margin-right: 4px;
    width: 28px;
  }

  .user-avatar {
    width: 28px;
    height: 28px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    border-width: 1px;
  }
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #555, #444);
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
  .avatar-placeholder {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .avatar-placeholder {
    font-size: 10px;
  }
}

.avatar-spacer {
  width: 36px;
  height: 36px;
  visibility: hidden;
}

@media (max-width: 768px) {
  .avatar-spacer {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .avatar-spacer {
    width: 28px;
    height: 28px;
  }
}

.message-bubbles-container {
  display: flex;
  flex-direction: column;
  max-width: 75%;
}

.sender-name {
  font-size: 13px;
  font-weight: 600;
  color: #4FC3F7;
  margin-bottom: 4px;
  padding-left: 12px;
  letter-spacing: 0.01em;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  opacity: 0.95;
  transition: opacity 0.2s ease;
}

.message-group:hover .sender-name {
  opacity: 1;
}

@media (max-width: 768px) {
  .sender-name {
    font-size: 12px;
    margin-bottom: 3px;
    padding-left: 10px;
  }
}

@media (max-width: 480px) {
  .sender-name {
    font-size: 11px;
    margin-bottom: 2px;
    padding-left: 8px;
    letter-spacing: 0;
  }
}

.message-bubble {
  padding: 10px 14px;
  border-radius: 18px;
  position: relative;
  word-wrap: break-word;
  margin-bottom: 2px;
  animation: fadeIn 0.2s ease-out;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  /* Ensure empty bubbles don't collapse */
  min-height: 20px;
  transition: all 0.2s ease;
}

@media (max-width: 768px) {
  .message-bubble {
    padding: 8px 12px;
    border-radius: 16px;
  }
}

@media (max-width: 480px) {
  .message-bubble {
    padding: 6px 10px;
    border-radius: 14px;
    margin-bottom: 1px;
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

.own-message {
  background-color: #0084FF;
  background-image: linear-gradient(135deg, #0084FF, #0099FF);
  color: white;
  border-bottom-right-radius: 6px;
  align-self: flex-end;
  margin-left: auto;
  box-shadow: 0 1px 2px rgba(0, 132, 255, 0.3);
}

.other-message {
  background-color: #2A2A2A;
  background-image: linear-gradient(135deg, #2A2A2A, #383838);
  color: white;
  border-bottom-left-radius: 6px;
  align-self: flex-start;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Telegram-style consecutive messages in a group */
.message-group {
  margin-bottom: 1px;
}

.message-group + .message-group {
  margin-top: 8px;
}

/* First message in a sequence */
.message-group:first-child .message-bubble.own-message {
  border-radius: 18px;
  border-bottom-right-radius: 6px;
}

.message-group:first-child .message-bubble.other-message {
  border-radius: 18px;
  border-bottom-left-radius: 6px;
}

/* Middle messages in a sequence */
.message-group:not(:first-child):not(:last-child) .message-bubble.own-message {
  border-radius: 18px;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.message-group:not(:first-child):not(:last-child) .message-bubble.other-message {
  border-radius: 18px;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

/* Last message in a sequence */
.message-group:last-child .message-bubble.own-message {
  border-radius: 18px;
  border-top-right-radius: 6px;
}

.message-group:last-child .message-bubble.other-message {
  border-radius: 18px;
  border-top-left-radius: 6px;
}

/* Responsive adjustments for message grouping */
@media (max-width: 768px) {
  .message-group + .message-group {
    margin-top: 6px;
  }

  .message-group:first-child .message-bubble.own-message,
  .message-group:first-child .message-bubble.other-message {
    border-radius: 16px;
  }

  .message-group:first-child .message-bubble.own-message {
    border-bottom-right-radius: 5px;
  }

  .message-group:first-child .message-bubble.other-message {
    border-bottom-left-radius: 5px;
  }
}

@media (max-width: 480px) {
  .message-group + .message-group {
    margin-top: 4px;
  }

  .message-group:first-child .message-bubble.own-message,
  .message-group:first-child .message-bubble.other-message {
    border-radius: 14px;
  }

  .message-group:first-child .message-bubble.own-message {
    border-bottom-right-radius: 4px;
  }

  .message-group:first-child .message-bubble.other-message {
    border-bottom-left-radius: 4px;
  }
}

.deleted-message {
  background-color: rgba(56, 56, 56, 0.5);
}

.deleted-text {
  font-style: italic;
  color: rgba(255, 255, 255, 0.6);
}

.message-content {
  font-size: 15px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  /* Add min-height to ensure empty content doesn't collapse */
  min-height: 1.4em;
  letter-spacing: 0.01em;
  font-weight: 400;
}

.own-message .message-content {
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.other-message .message-content {
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

/* Responsive font sizes for smaller screens */
@media (max-width: 768px) {
  .message-content {
    font-size: 14px;
    line-height: 1.45;
  }
}

@media (max-width: 480px) {
  .message-content {
    font-size: 13px;
    line-height: 1.4;
    letter-spacing: 0;
  }
}

.message-media {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.media-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  max-width: 200px;
}

.media-image {
  max-width: 200px;
  max-height: 150px;
  display: block;
  width: 100%;
  transition: transform 0.3s ease;
}

@media (max-width: 768px) {
  .message-media {
    margin-top: 6px;
    gap: 3px;
  }

  .media-wrapper {
    max-width: 180px;
    border-radius: 10px;
  }

  .media-image {
    max-width: 180px;
    max-height: 130px;
  }
}

@media (max-width: 480px) {
  .message-media {
    margin-top: 4px;
    gap: 2px;
  }

  .media-wrapper {
    max-width: 160px;
    border-radius: 8px;
  }

  .media-image {
    max-width: 160px;
    max-height: 120px;
  }
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.media-view-text {
  color: white;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.5);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
}

@media (max-width: 768px) {
  .media-view-text {
    padding: 3px 10px;
    border-radius: 10px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .media-view-text {
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 10px;
  }
}

.media-wrapper:hover .media-image {
  transform: scale(1.05);
}

.media-wrapper:hover .media-overlay {
  opacity: 1;
}

/* Audio container and player styles */
.audio-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  margin: 3px 0 5px 0;
  padding: 0;
  transition: transform 0.2s ease;
}

.audio-container:hover {
  transform: translateY(-1px);
}

.own-audio {
  align-items: flex-end;
}

.audio-only {
  margin: 6px 0;
}

.audio-only-group {
  margin-bottom: 6px;
}

.standalone-audio-player {
  width: 100%;
  min-width: 220px;
  max-width: 300px;
  border-radius: 14px;
  overflow: hidden;
  margin: 2px 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.2s ease, transform 0.2s ease;
}

.standalone-audio-player:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.audio-timestamp {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  margin: 2px 12px;
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
  letter-spacing: 0.01em;
  transition: opacity 0.2s ease;
  opacity: 0.9;
}

.audio-container:hover .audio-timestamp {
  opacity: 1;
}

/* Responsive styles for smaller screens */
@media (max-width: 480px) {
  .audio-container {
    margin: 2px 0 4px 0;
  }

  .audio-only {
    margin: 4px 0;
  }

  .standalone-audio-player {
    min-width: 180px;
    max-width: 260px;
    margin: 2px 4px;
    border-radius: 12px;
  }

  .audio-timestamp {
    margin: 1px 8px;
    font-size: 9px;
    letter-spacing: 0;
  }
}

@media (max-width: 360px) {
  .standalone-audio-player {
    min-width: 160px;
    max-width: 220px;
    margin: 2px 2px;
    border-radius: 10px;
  }

  .audio-timestamp {
    margin: 1px 6px;
    font-size: 8px;
  }
}

.message-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 4px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  transition: opacity 0.2s ease;
}

.own-message .message-footer {
  color: rgba(255, 255, 255, 0.7);
}

.message-bubble:hover .message-footer {
  opacity: 1;
}

.message-time {
  margin-right: 4px;
  font-weight: 500;
  letter-spacing: 0.02em;
}

@media (max-width: 768px) {
  .message-footer {
    margin-top: 3px;
    font-size: 9px;
  }
}

@media (max-width: 480px) {
  .message-footer {
    margin-top: 2px;
    font-size: 8px;
  }

  .message-time {
    letter-spacing: 0;
  }
}

.message-status {
  display: flex;
  align-items: center;
  margin-left: 1px;
}

.status-sending {
  display: inline-block;
  opacity: 0.8;
}

.loading-dots {
  display: inline-block;
  position: relative;
  width: 16px;
  height: 10px;
}

.loading-dots::after {
  content: '...';
  position: absolute;
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60%, 100% { content: '...'; }
}

.status-sent {
  color: rgba(255, 255, 255, 0.85);
  font-size: 0.9em;
  margin-top: -1px;
}

.status-delivered {
  color: #4FC3F7;
  font-size: 0.9em;
  margin-top: -1px;
  text-shadow: 0 0 2px rgba(79, 195, 247, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .message-bubbles-container {
    max-width: 85%;
  }
}

@media (max-width: 480px) {
  .message-bubbles-container {
    max-width: 90%;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }

  .avatar-spacer {
    width: 32px;
    height: 32px;
  }

  .user-avatar-container {
    width: 32px;
  }

  .message-content {
    font-size: 14px;
  }
}
</style>
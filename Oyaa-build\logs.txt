Setting up typing listener
useChatSocket.js:34 Setting up stopTyping listener
main.js:32 App mounting after auth initialization
App.vue:70 Closing SSE connection
appSlice.js:129 User not authenticated, skipping fetchRequests
firebaseService.js:44 Firebase persistence set to LOCAL
App.vue:63 Initializing SSE connection
App.vue:21 Initializing offline manager
OfflineManager.js:27 OfflineManager: Initialized, online status: true
GlobalNotificationCenter.vue:209 joinRoom acknowledgment: Joined room: user_6
main.js:24 Auth initialization timed out, mounting app anyway
overrideMethod @ hook.js:608
(anonymous) @ main.js:24
setTimeout
(anonymous) @ main.js:23
(anonymous) @ main.js:22Understand this warning
tempChatSocket.js:9 Connected to temp-chat namespace
GroupsList.vue:128 GroupsList - Fetched groups: Proxy(Array) {0: {…}, 1: {…}}
GroupsList.vue:137 GroupsList - Navigating to GroupChatPage with groupId: 1
groupSocket.js:163 Attempting to connect to group socket...
GroupChatPage.vue:97 GroupChatPage - Cleared message load attempts
GroupChatPage.vue:807 GroupChatPage - Socket not connected on mount, connecting...
groupSocket.js:31 Connected to groups namespace, socket ID: cu69WtuPFlreX-bPAAAx
tempChatSocket.js:13 Disconnected from temp-chat namespace
groupSocket.js:114 Disconnected from groups namespace, reason: transport close
groupSocket.js:123 Scheduling manual reconnect in 1000ms (attempt 1)
GroupChatPage.vue:340 GroupChatPage - Current user: Proxy(Object) {id: 6, username: 'cccccc', email: '<EMAIL>', avatar: 'https://lh3.googleusercontent.com/a/ACg8ocIEw3B32BzTDMd5M6Whj1JWIx2CRbcslWHkYDt98YHBbWj-KXmb=s96-c', description: null, …}
GroupChatPage.vue:68 GroupChatPage - Fetching group: 1
GroupChatPage.vue:74 GroupChatPage - Fetched group: {id: 1, name: 'World Chat', creator_id: null, description: 'Just have fun', avatar: null, …}
GroupChatPage.vue:485 GroupChatPage - Joining group room: 1
GroupChatPage.vue:489 GroupChatPage - Socket not connected, connecting first...
GroupChatPage.vue:185 GroupChatPage - Fetching messages for group: 1
GroupChatPage.vue:97 GroupChatPage - Cleared message load attempts
GroupChatPage.vue:211 GroupChatPage - Fetching messages from Supabase for group: 1
GroupChatPage.vue:485 GroupChatPage - Joining group room: 1
GroupChatPage.vue:489 GroupChatPage - Socket not connected, connecting first...
GroupChatPage.vue:97 GroupChatPage - Cleared message load attempts
GroupChatPage.vue:185 GroupChatPage - Fetching messages for group: 1
GroupChatPage.vue:97 GroupChatPage - Cleared message load attempts
GroupChatPage.vue:211 GroupChatPage - Fetching messages from Supabase for group: 1
HtmlChatList.vue:622 HtmlChatList: hasMoreMessages changed to true
HtmlChatList.vue:583 HtmlChatList: Component mounted
HtmlChatWindow.vue:353 HtmlChatWindow: Component mounted
HtmlChatWindow.vue:354 Debug state: {htmlChatFps: 0, visibleMessagesCount: 0, scrollPercentage: 0, scrollDirection: 'none', isAtBottom: true}
HtmlChatList.vue:587 HtmlChatList: Scroll event listener added
2supabaseRealtimeService.js:100 Fetching messages for group 1, limit: 100, beforeId: none
groupSocket.js:127 Attempting manual reconnect (attempt 1)
HtmlChatList.vue:591 HtmlChatList: Setting up initial scroll observer
tempChatSocket.js:9 Connected to temp-chat namespace
groupSocket.js:31 Connected to groups namespace, socket ID: L09cTe-pcB3hJ_L0AAA0
GroupChatPage.vue:820 GroupChatPage - Reconnected, rejoining group room: 1
GroupChatPage.vue:485 GroupChatPage - Joining group room: 1
2GroupChatPage.vue:503 GroupChatPage - Socket connected, now joining group room
FpsCounter.vue:432 Socket ping response: {time: 1745599728222, status: 'ok'}
FpsCounter.vue:437 Connection check response: {serverTime: 1745599728223, rooms: Array(0)}
GroupChatPage.vue:520 GroupChatPage - Successfully joined group room: {success: true, message: 'Joined group room: group_1', rooms: Array(1)}
GroupChatPage.vue:520 GroupChatPage - Successfully joined group room: {success: true, message: 'Joined group room: group_1', rooms: Array(1)}
GroupChatPage.vue:520 GroupChatPage - Successfully joined group room: {success: true, message: 'Joined group room: group_1', rooms: Array(1)}
supabaseRealtimeService.js:127 Fetched 100 messages for group 1
GroupChatPage.vue:227 GroupChatPage - Fetched 100 messages from Supabase, hasMoreMessages: true
HtmlChatList.vue:653 HtmlChatList: Messages changed from 0 to 100. OlderAdded: true, NewAdded: true, Initial: true
HtmlChatList.vue:500 HtmlChatList: Scrolled to bottom using jumpToLatestMessages
HtmlChatList.vue:291 HtmlChatList: Could not find element for message ID 257 to observe.
overrideMethod @ hook.js:608
setupScrollObserver @ HtmlChatList.vue:291
(anonymous) @ HtmlChatList.vue:693
Promise.then
nextTick @ chunk-D3VXKJN7.js?v=9f34070d:2597
watch.deep @ HtmlChatList.vue:683
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
baseWatchOptions.call @ chunk-D3VXKJN7.js?v=9f34070d:8520
job @ chunk-D3VXKJN7.js?v=9f34070d:2237
flushPreFlushCbs @ chunk-D3VXKJN7.js?v=9f34070d:2664
updateComponentPreRender @ chunk-D3VXKJN7.js?v=9f34070d:7793
componentUpdateFn @ chunk-D3VXKJN7.js?v=9f34070d:7712
run @ chunk-D3VXKJN7.js?v=9f34070d:726
updateComponent @ chunk-D3VXKJN7.js?v=9f34070d:7587
processComponent @ chunk-D3VXKJN7.js?v=9f34070d:7522
patch @ chunk-D3VXKJN7.js?v=9f34070d:7027
patchBlockChildren @ chunk-D3VXKJN7.js?v=9f34070d:7381
patchElement @ chunk-D3VXKJN7.js?v=9f34070d:7299
processElement @ chunk-D3VXKJN7.js?v=9f34070d:7158
patch @ chunk-D3VXKJN7.js?v=9f34070d:7015
componentUpdateFn @ chunk-D3VXKJN7.js?v=9f34070d:7735
run @ chunk-D3VXKJN7.js?v=9f34070d:726
updateComponent @ chunk-D3VXKJN7.js?v=9f34070d:7587
processComponent @ chunk-D3VXKJN7.js?v=9f34070d:7522
patch @ chunk-D3VXKJN7.js?v=9f34070d:7027
patchBlockChildren @ chunk-D3VXKJN7.js?v=9f34070d:7381
patchElement @ chunk-D3VXKJN7.js?v=9f34070d:7299
processElement @ chunk-D3VXKJN7.js?v=9f34070d:7158
patch @ chunk-D3VXKJN7.js?v=9f34070d:7015
componentUpdateFn @ chunk-D3VXKJN7.js?v=9f34070d:7735
run @ chunk-D3VXKJN7.js?v=9f34070d:726
runIfDirty @ chunk-D3VXKJN7.js?v=9f34070d:764
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2716
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queueJob @ chunk-D3VXKJN7.js?v=9f34070d:2625
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:7777
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1199
set @ chunk-D3VXKJN7.js?v=9f34070d:1480
SET_MESSAGES @ GroupChat.js:15
wrappedMutationHandler @ vuex.js?v=9f34070d:238
commitIterator @ vuex.js?v=9f34070d:822
(anonymous) @ vuex.js?v=9f34070d:821
_withCommit @ vuex.js?v=9f34070d:960
commit @ vuex.js?v=9f34070d:820
boundCommit @ vuex.js?v=9f34070d:779
local.commit @ vuex.js?v=9f34070d:196
setMessages @ GroupChat.js:62
wrappedActionHandler @ vuex.js?v=9f34070d:244
dispatch @ vuex.js?v=9f34070d:861
boundDispatch @ vuex.js?v=9f34070d:776
fetchFreshMessages @ GroupChatPage.vue:224
await in fetchFreshMessages
fetchMessages @ GroupChatPage.vue:192
fetchGroupInfo @ GroupChatPage.vue:85
await in fetchGroupInfo
(anonymous) @ GroupChatPage.vue:844
await in (anonymous)
(anonymous) @ chunk-D3VXKJN7.js?v=9f34070d:5146
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
hook.__weh.hook.__weh @ chunk-D3VXKJN7.js?v=9f34070d:5126
flushPostFlushCbs @ chunk-D3VXKJN7.js?v=9f34070d:2693
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2735
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queuePostFlushCb @ chunk-D3VXKJN7.js?v=9f34070d:2644
queueEffectWithSuspense @ chunk-D3VXKJN7.js?v=9f34070d:9644
baseWatchOptions.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:8524
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:2254
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
notify @ chunk-D3VXKJN7.js?v=9f34070d:1072
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1046
set value @ chunk-D3VXKJN7.js?v=9f34070d:1918
finalizeNavigation @ vue-router.js?v=9f34070d:2517
(anonymous) @ vue-router.js?v=9f34070d:2427
Promise.then
pushWithRedirect @ vue-router.js?v=9f34070d:2395
push @ vue-router.js?v=9f34070d:2321
navigateToGroupChat @ GroupsList.vue:138
onClick @ GroupsList.vue:59
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
invoker @ chunk-D3VXKJN7.js?v=9f34070d:11447Understand this warning
HtmlChatList.vue:706 HtmlChatList: Jumping to bottom. Initial: true, NewAdded: true, AtBottom: true
HtmlChatList.vue:302 HtmlChatList: Observing message 257 after delay (attempt 1).
GroupChatPage.vue:122 GroupChatPage - Setting up Supabase real-time subscription for group 1
supabaseRealtimeService.js:507 Cleaning up all Supabase subscriptions
supabaseRealtimeService.js:534 All channel removal operations completed
GroupChatPage.vue:130 GroupChatPage - Performed full subscription cleanup before new subscription
supabaseRealtimeService.js:151 Setting up Supabase Realtime subscription for group 1
GroupChatPage.vue:158 GroupChatPage - Supabase real-time subscription set up successfully
GroupChatPage.vue:849 GroupChatPage - Joining group room after initialization: 1
GroupChatPage.vue:485 GroupChatPage - Joining group room: 1
GroupChatPage.vue:185 GroupChatPage - Fetching messages for group: 1
GroupChatPage.vue:97 GroupChatPage - Cleared message load attempts
GroupChatPage.vue:211 GroupChatPage - Fetching messages from Supabase for group: 1
supabaseRealtimeService.js:100 Fetching messages for group 1, limit: 100, beforeId: none
supabaseRealtimeService.js:127 Fetched 100 messages for group 1
GroupChatPage.vue:227 GroupChatPage - Fetched 100 messages from Supabase, hasMoreMessages: true
GroupChatPage.vue:108 GroupChatPage - Cleaning up existing Supabase subscription
supabaseRealtimeService.js:317 Removing message listener for group 1
supabaseRealtimeService.js:325 No more listeners for group 1, removing subscription
supabaseRealtimeService.js:330 Safely removing channel for group 1
supabaseRealtimeService.js:300 Supabase subscription status for group 1: CLOSED
HtmlChatList.vue:653 HtmlChatList: Messages changed from 100 to 100. OlderAdded: false, NewAdded: false, Initial: false
supabaseRealtimeService.js:332 WebSocket connection to 'wss://puysetqwpyinxqhjiojw.supabase.co/realtime/v1/websocket?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1eXNldHF3cHlpbnhxaGppb2p3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5OTEwMTQsImV4cCI6MjA2MDU2NzAxNH0.kmLMZtGERqOW1WneCpEBLyrtXMmwbJOmWvzaLaPECaE&eventsPerSecond=10&vsn=1.0.0' failed: WebSocket is closed before the connection is established.
disconnect @ @supabase_supabase-js.js?v=9f34070d:2618
removeChannel @ @supabase_supabase-js.js?v=9f34070d:2638
await in removeChannel
removeChannel @ @supabase_supabase-js.js?v=9f34070d:7086
unsubscribe @ supabaseRealtimeService.js:332
setupSupabaseSubscription @ GroupChatPage.vue:110
fetchFreshMessages @ GroupChatPage.vue:237
await in fetchFreshMessages
fetchMessages @ GroupChatPage.vue:192
(anonymous) @ GroupChatPage.vue:952
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
baseWatchOptions.call @ chunk-D3VXKJN7.js?v=9f34070d:8520
job @ chunk-D3VXKJN7.js?v=9f34070d:2237
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2716
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queueJob @ chunk-D3VXKJN7.js?v=9f34070d:2625
baseWatchOptions.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:8532
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:2254
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1199
set @ chunk-D3VXKJN7.js?v=9f34070d:1480
SET_GROUP @ GroupChat.js:37
wrappedMutationHandler @ vuex.js?v=9f34070d:238
commitIterator @ vuex.js?v=9f34070d:822
(anonymous) @ vuex.js?v=9f34070d:821
_withCommit @ vuex.js?v=9f34070d:960
commit @ vuex.js?v=9f34070d:820
boundCommit @ vuex.js?v=9f34070d:779
local.commit @ vuex.js?v=9f34070d:196
setGroup @ GroupChat.js:77
wrappedActionHandler @ vuex.js?v=9f34070d:244
dispatch @ vuex.js?v=9f34070d:861
boundDispatch @ vuex.js?v=9f34070d:776
fetchGroupInfo @ GroupChatPage.vue:73
await in fetchGroupInfo
(anonymous) @ GroupChatPage.vue:844
await in (anonymous)
(anonymous) @ chunk-D3VXKJN7.js?v=9f34070d:5146
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
hook.__weh.hook.__weh @ chunk-D3VXKJN7.js?v=9f34070d:5126
flushPostFlushCbs @ chunk-D3VXKJN7.js?v=9f34070d:2693
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2735
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queuePostFlushCb @ chunk-D3VXKJN7.js?v=9f34070d:2644
queueEffectWithSuspense @ chunk-D3VXKJN7.js?v=9f34070d:9644
baseWatchOptions.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:8524
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:2254
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
notify @ chunk-D3VXKJN7.js?v=9f34070d:1072
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1046
set value @ chunk-D3VXKJN7.js?v=9f34070d:1918
finalizeNavigation @ vue-router.js?v=9f34070d:2517
(anonymous) @ vue-router.js?v=9f34070d:2427
Promise.then
pushWithRedirect @ vue-router.js?v=9f34070d:2395
push @ vue-router.js?v=9f34070d:2321
navigateToGroupChat @ GroupsList.vue:138
onClick @ GroupsList.vue:59
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
invoker @ chunk-D3VXKJN7.js?v=9f34070d:11447Understand this warning
HtmlChatList.vue:291 HtmlChatList: Could not find element for message ID 257 to observe.
overrideMethod @ hook.js:608
setupScrollObserver @ HtmlChatList.vue:291
(anonymous) @ HtmlChatList.vue:693
Promise.then
nextTick @ chunk-D3VXKJN7.js?v=9f34070d:2597
watch.deep @ HtmlChatList.vue:683
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
baseWatchOptions.call @ chunk-D3VXKJN7.js?v=9f34070d:8520
job @ chunk-D3VXKJN7.js?v=9f34070d:2237
flushPreFlushCbs @ chunk-D3VXKJN7.js?v=9f34070d:2664
updateComponentPreRender @ chunk-D3VXKJN7.js?v=9f34070d:7793
componentUpdateFn @ chunk-D3VXKJN7.js?v=9f34070d:7712
run @ chunk-D3VXKJN7.js?v=9f34070d:726
updateComponent @ chunk-D3VXKJN7.js?v=9f34070d:7587
processComponent @ chunk-D3VXKJN7.js?v=9f34070d:7522
patch @ chunk-D3VXKJN7.js?v=9f34070d:7027
patchBlockChildren @ chunk-D3VXKJN7.js?v=9f34070d:7381
patchElement @ chunk-D3VXKJN7.js?v=9f34070d:7299
processElement @ chunk-D3VXKJN7.js?v=9f34070d:7158
patch @ chunk-D3VXKJN7.js?v=9f34070d:7015
componentUpdateFn @ chunk-D3VXKJN7.js?v=9f34070d:7735
run @ chunk-D3VXKJN7.js?v=9f34070d:726
updateComponent @ chunk-D3VXKJN7.js?v=9f34070d:7587
processComponent @ chunk-D3VXKJN7.js?v=9f34070d:7522
patch @ chunk-D3VXKJN7.js?v=9f34070d:7027
patchBlockChildren @ chunk-D3VXKJN7.js?v=9f34070d:7381
patchElement @ chunk-D3VXKJN7.js?v=9f34070d:7299
processElement @ chunk-D3VXKJN7.js?v=9f34070d:7158
patch @ chunk-D3VXKJN7.js?v=9f34070d:7015
componentUpdateFn @ chunk-D3VXKJN7.js?v=9f34070d:7735
run @ chunk-D3VXKJN7.js?v=9f34070d:726
runIfDirty @ chunk-D3VXKJN7.js?v=9f34070d:764
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2716
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queueJob @ chunk-D3VXKJN7.js?v=9f34070d:2625
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:7777
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1199
set @ chunk-D3VXKJN7.js?v=9f34070d:1480
SET_MESSAGES @ GroupChat.js:15
wrappedMutationHandler @ vuex.js?v=9f34070d:238
commitIterator @ vuex.js?v=9f34070d:822
(anonymous) @ vuex.js?v=9f34070d:821
_withCommit @ vuex.js?v=9f34070d:960
commit @ vuex.js?v=9f34070d:820
boundCommit @ vuex.js?v=9f34070d:779
local.commit @ vuex.js?v=9f34070d:196
setMessages @ GroupChat.js:62
wrappedActionHandler @ vuex.js?v=9f34070d:244
dispatch @ vuex.js?v=9f34070d:861
boundDispatch @ vuex.js?v=9f34070d:776
fetchFreshMessages @ GroupChatPage.vue:224
await in fetchFreshMessages
fetchMessages @ GroupChatPage.vue:192
(anonymous) @ GroupChatPage.vue:952
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
baseWatchOptions.call @ chunk-D3VXKJN7.js?v=9f34070d:8520
job @ chunk-D3VXKJN7.js?v=9f34070d:2237
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2716
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queueJob @ chunk-D3VXKJN7.js?v=9f34070d:2625
baseWatchOptions.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:8532
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:2254
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1199
set @ chunk-D3VXKJN7.js?v=9f34070d:1480
SET_GROUP @ GroupChat.js:37
wrappedMutationHandler @ vuex.js?v=9f34070d:238
commitIterator @ vuex.js?v=9f34070d:822
(anonymous) @ vuex.js?v=9f34070d:821
_withCommit @ vuex.js?v=9f34070d:960
commit @ vuex.js?v=9f34070d:820
boundCommit @ vuex.js?v=9f34070d:779
local.commit @ vuex.js?v=9f34070d:196
setGroup @ GroupChat.js:77
wrappedActionHandler @ vuex.js?v=9f34070d:244
dispatch @ vuex.js?v=9f34070d:861
boundDispatch @ vuex.js?v=9f34070d:776
fetchGroupInfo @ GroupChatPage.vue:73
await in fetchGroupInfo
(anonymous) @ GroupChatPage.vue:844
await in (anonymous)
(anonymous) @ chunk-D3VXKJN7.js?v=9f34070d:5146
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
hook.__weh.hook.__weh @ chunk-D3VXKJN7.js?v=9f34070d:5126
flushPostFlushCbs @ chunk-D3VXKJN7.js?v=9f34070d:2693
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2735
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queuePostFlushCb @ chunk-D3VXKJN7.js?v=9f34070d:2644
queueEffectWithSuspense @ chunk-D3VXKJN7.js?v=9f34070d:9644
baseWatchOptions.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:8524
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:2254
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
notify @ chunk-D3VXKJN7.js?v=9f34070d:1072
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1046
set value @ chunk-D3VXKJN7.js?v=9f34070d:1918
finalizeNavigation @ vue-router.js?v=9f34070d:2517
(anonymous) @ vue-router.js?v=9f34070d:2427
Promise.then
pushWithRedirect @ vue-router.js?v=9f34070d:2395
push @ vue-router.js?v=9f34070d:2321
navigateToGroupChat @ GroupsList.vue:138
onClick @ GroupsList.vue:59
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
invoker @ chunk-D3VXKJN7.js?v=9f34070d:11447Understand this warning
supabaseRealtimeService.js:333 Successfully removed channel for group 1
GroupChatPage.vue:520 GroupChatPage - Successfully joined group room: {success: true, message: 'Joined group room: group_1', rooms: Array(1)}
HtmlChatList.vue:302 HtmlChatList: Observing message 257 after delay (attempt 1).
GroupChatPage.vue:122 GroupChatPage - Setting up Supabase real-time subscription for group 1
supabaseRealtimeService.js:507 Cleaning up all Supabase subscriptions
supabaseRealtimeService.js:534 All channel removal operations completed
GroupChatPage.vue:130 GroupChatPage - Performed full subscription cleanup before new subscription
supabaseRealtimeService.js:151 Setting up Supabase Realtime subscription for group 1
GroupChatPage.vue:158 GroupChatPage - Supabase real-time subscription set up successfully
supabaseRealtimeService.js:127 Fetched 100 messages for group 1
GroupChatPage.vue:227 GroupChatPage - Fetched 100 messages from Supabase, hasMoreMessages: true
GroupChatPage.vue:108 GroupChatPage - Cleaning up existing Supabase subscription
supabaseRealtimeService.js:317 Removing message listener for group 1
supabaseRealtimeService.js:325 No more listeners for group 1, removing subscription
supabaseRealtimeService.js:330 Safely removing channel for group 1
supabaseRealtimeService.js:300 Supabase subscription status for group 1: CLOSED
HtmlChatList.vue:653 HtmlChatList: Messages changed from 100 to 100. OlderAdded: false, NewAdded: false, Initial: false
supabaseRealtimeService.js:332 WebSocket connection to 'wss://puysetqwpyinxqhjiojw.supabase.co/realtime/v1/websocket?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1eXNldHF3cHlpbnhxaGppb2p3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5OTEwMTQsImV4cCI6MjA2MDU2NzAxNH0.kmLMZtGERqOW1WneCpEBLyrtXMmwbJOmWvzaLaPECaE&eventsPerSecond=10&vsn=1.0.0' failed: WebSocket is closed before the connection is established.
disconnect @ @supabase_supabase-js.js?v=9f34070d:2618
removeChannel @ @supabase_supabase-js.js?v=9f34070d:2638
await in removeChannel
removeChannel @ @supabase_supabase-js.js?v=9f34070d:7086
unsubscribe @ supabaseRealtimeService.js:332
setupSupabaseSubscription @ GroupChatPage.vue:110
fetchFreshMessages @ GroupChatPage.vue:237
await in fetchFreshMessages
fetchMessages @ GroupChatPage.vue:192
(anonymous) @ GroupChatPage.vue:853
setTimeout
(anonymous) @ GroupChatPage.vue:847
await in (anonymous)
(anonymous) @ chunk-D3VXKJN7.js?v=9f34070d:5146
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
hook.__weh.hook.__weh @ chunk-D3VXKJN7.js?v=9f34070d:5126
flushPostFlushCbs @ chunk-D3VXKJN7.js?v=9f34070d:2693
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2735
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queuePostFlushCb @ chunk-D3VXKJN7.js?v=9f34070d:2644
queueEffectWithSuspense @ chunk-D3VXKJN7.js?v=9f34070d:9644
baseWatchOptions.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:8524
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:2254
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
notify @ chunk-D3VXKJN7.js?v=9f34070d:1072
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1046
set value @ chunk-D3VXKJN7.js?v=9f34070d:1918
finalizeNavigation @ vue-router.js?v=9f34070d:2517
(anonymous) @ vue-router.js?v=9f34070d:2427
Promise.then
pushWithRedirect @ vue-router.js?v=9f34070d:2395
push @ vue-router.js?v=9f34070d:2321
navigateToGroupChat @ GroupsList.vue:138
onClick @ GroupsList.vue:59
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
invoker @ chunk-D3VXKJN7.js?v=9f34070d:11447Understand this warning
HtmlChatList.vue:291 HtmlChatList: Could not find element for message ID 257 to observe.
overrideMethod @ hook.js:608
setupScrollObserver @ HtmlChatList.vue:291
(anonymous) @ HtmlChatList.vue:693
Promise.then
nextTick @ chunk-D3VXKJN7.js?v=9f34070d:2597
watch.deep @ HtmlChatList.vue:683
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
baseWatchOptions.call @ chunk-D3VXKJN7.js?v=9f34070d:8520
job @ chunk-D3VXKJN7.js?v=9f34070d:2237
flushPreFlushCbs @ chunk-D3VXKJN7.js?v=9f34070d:2664
updateComponentPreRender @ chunk-D3VXKJN7.js?v=9f34070d:7793
componentUpdateFn @ chunk-D3VXKJN7.js?v=9f34070d:7712
run @ chunk-D3VXKJN7.js?v=9f34070d:726
updateComponent @ chunk-D3VXKJN7.js?v=9f34070d:7587
processComponent @ chunk-D3VXKJN7.js?v=9f34070d:7522
patch @ chunk-D3VXKJN7.js?v=9f34070d:7027
patchBlockChildren @ chunk-D3VXKJN7.js?v=9f34070d:7381
patchElement @ chunk-D3VXKJN7.js?v=9f34070d:7299
processElement @ chunk-D3VXKJN7.js?v=9f34070d:7158
patch @ chunk-D3VXKJN7.js?v=9f34070d:7015
componentUpdateFn @ chunk-D3VXKJN7.js?v=9f34070d:7735
run @ chunk-D3VXKJN7.js?v=9f34070d:726
updateComponent @ chunk-D3VXKJN7.js?v=9f34070d:7587
processComponent @ chunk-D3VXKJN7.js?v=9f34070d:7522
patch @ chunk-D3VXKJN7.js?v=9f34070d:7027
patchBlockChildren @ chunk-D3VXKJN7.js?v=9f34070d:7381
patchElement @ chunk-D3VXKJN7.js?v=9f34070d:7299
processElement @ chunk-D3VXKJN7.js?v=9f34070d:7158
patch @ chunk-D3VXKJN7.js?v=9f34070d:7015
componentUpdateFn @ chunk-D3VXKJN7.js?v=9f34070d:7735
run @ chunk-D3VXKJN7.js?v=9f34070d:726
runIfDirty @ chunk-D3VXKJN7.js?v=9f34070d:764
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2716
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queueJob @ chunk-D3VXKJN7.js?v=9f34070d:2625
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:7777
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1199
set @ chunk-D3VXKJN7.js?v=9f34070d:1480
SET_MESSAGES @ GroupChat.js:15
wrappedMutationHandler @ vuex.js?v=9f34070d:238
commitIterator @ vuex.js?v=9f34070d:822
(anonymous) @ vuex.js?v=9f34070d:821
_withCommit @ vuex.js?v=9f34070d:960
commit @ vuex.js?v=9f34070d:820
boundCommit @ vuex.js?v=9f34070d:779
local.commit @ vuex.js?v=9f34070d:196
setMessages @ GroupChat.js:62
wrappedActionHandler @ vuex.js?v=9f34070d:244
dispatch @ vuex.js?v=9f34070d:861
boundDispatch @ vuex.js?v=9f34070d:776
fetchFreshMessages @ GroupChatPage.vue:224
await in fetchFreshMessages
fetchMessages @ GroupChatPage.vue:192
(anonymous) @ GroupChatPage.vue:853
setTimeout
(anonymous) @ GroupChatPage.vue:847
await in (anonymous)
(anonymous) @ chunk-D3VXKJN7.js?v=9f34070d:5146
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
hook.__weh.hook.__weh @ chunk-D3VXKJN7.js?v=9f34070d:5126
flushPostFlushCbs @ chunk-D3VXKJN7.js?v=9f34070d:2693
flushJobs @ chunk-D3VXKJN7.js?v=9f34070d:2735
Promise.then
queueFlush @ chunk-D3VXKJN7.js?v=9f34070d:2630
queuePostFlushCb @ chunk-D3VXKJN7.js?v=9f34070d:2644
queueEffectWithSuspense @ chunk-D3VXKJN7.js?v=9f34070d:9644
baseWatchOptions.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:8524
effect2.scheduler @ chunk-D3VXKJN7.js?v=9f34070d:2254
trigger @ chunk-D3VXKJN7.js?v=9f34070d:754
endBatch @ chunk-D3VXKJN7.js?v=9f34070d:812
notify @ chunk-D3VXKJN7.js?v=9f34070d:1072
trigger @ chunk-D3VXKJN7.js?v=9f34070d:1046
set value @ chunk-D3VXKJN7.js?v=9f34070d:1918
finalizeNavigation @ vue-router.js?v=9f34070d:2517
(anonymous) @ vue-router.js?v=9f34070d:2427
Promise.then
pushWithRedirect @ vue-router.js?v=9f34070d:2395
push @ vue-router.js?v=9f34070d:2321
navigateToGroupChat @ GroupsList.vue:138
onClick @ GroupsList.vue:59
callWithErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2508
callWithAsyncErrorHandling @ chunk-D3VXKJN7.js?v=9f34070d:2515
invoker @ chunk-D3VXKJN7.js?v=9f34070d:11447Understand this warning
supabaseRealtimeService.js:333 Successfully removed channel for group 1
HtmlChatList.vue:309 HtmlChatList: Retry 1/5 for message 257
GroupChatPage.vue:122 GroupChatPage - Setting up Supabase real-time subscription for group 1
supabaseRealtimeService.js:507 Cleaning up all Supabase subscriptions
supabaseRealtimeService.js:534 All channel removal operations completed
GroupChatPage.vue:130 GroupChatPage - Performed full subscription cleanup before new subscription
supabaseRealtimeService.js:151 Setting up Supabase Realtime subscription for group 1
GroupChatPage.vue:158 GroupChatPage - Supabase real-time subscription set up successfully
HtmlChatList.vue:309 HtmlChatList: Retry 2/5 for message 257
HtmlChatList.vue:309 HtmlChatList: Retry 3/5 for message 257
HtmlChatList.vue:309 HtmlChatList: Retry 4/5 for message 257
supabaseRealtimeService.js:300 Supabase subscription status for group 1: SUBSCRIBED
HtmlChatList.vue:312 HtmlChatList: Failed to find element for message ID 257 after 5 retries.

backend
]: Client disconnected from groups namespace: cu69WtuPFlreX-bPAAAx, reason: forced close
]: authMiddleware: Firebase user authenticated with userId: 6
getCurrentUser: Fetching user with ID: 6
[getGroupById] Group found: {
  id: 1,
  name: 'World Chat',
  creator_id: null,
  description: 'Just have fun',
  avatar: null,
  is_world_chat: true,
  latitude: null,
  longitude: null,
  location: null,
  tags: null,
  created_at: 2025-04-21T07:33:46.708Z,
  updated_at: 2025-04-21T07:33:46.708Z
}
]: authMiddleware: Firebase user authenticated with userId: 6
]: Fetching messages for group 1, limit: 100, beforeId: none
Back-end: New WebSocket connection for temporary chat (socket id: 7JApBiJmmyBigsq1AAAz)
]: New client connected to groups namespace: L09cTe-pcB3hJ_L0AAA0
]: Socket L09cTe-pcB3hJ_L0AAA0 joining group room: group_1
]: Socket L09cTe-pcB3hJ_L0AAA0 joining group room: group_1
]: Socket L09cTe-pcB3hJ_L0AAA0 joining group room: group_1
]: Returning 100 messages for group 1
]: authMiddleware: Firebase user authenticated with userId: 6
]: Fetching messages for group 1, limit: 100, beforeId: none
]: Socket L09cTe-pcB3hJ_L0AAA0 joining group room: group_1
]: Returning 100 messages for group 1
]: authMiddleware: Firebase user authenticated with userId: 6
]: Fetching messages for group 1, limit: 100, beforeId: none
]: Returning 100 messages for group 1

// frontend/src/utils/axios.js
import axios from 'axios';
import { getIdToken } from '../services/firebaseService';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Create axios instance with base URL
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token
axiosInstance.interceptors.request.use(
  async (config) => {
    try {
      // Try to get Firebase token first
      const token = await getIdToken().catch(() => null);
      
      // If Firebase token is available, use it
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      } else {
        // Otherwise, try to use the legacy token from localStorage
        const legacyToken = localStorage.getItem('token');
        if (legacyToken) {
          config.headers.Authorization = `Bearer ${legacyToken}`;
        }
      }
    } catch (error) {
      console.error('Error setting auth token:', error);
      
      // If getting Firebase token fails, try to use the legacy token
      const legacyToken = localStorage.getItem('token');
      if (legacyToken) {
        config.headers.Authorization = `Bearer ${legacyToken}`;
      }
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401) {
      // Clear token and redirect to login page
      localStorage.removeItem('token');
      window.location.href = '/signin';
    }
    
    return Promise.reject(error);
  }
);

export default axiosInstance;

// frontend/src/composables/useSSE.js
import { ref, onUnmounted, watch } from 'vue';

export function useSSE(userId, onUpdate) {
  const eventSource = ref(null);

  const setupSSE = (newUserId) => {
    // Close the existing connection if it exists
    if (eventSource.value) {
      console.log('Closing existing SSE connection...');
      eventSource.value.close();
    }

    if (!newUserId) {
      console.error('SSE setup failed: userId is undefined.');
      return;
    }

    // Create a new SSE connection with the user ID
    eventSource.value = new EventSource(
      `${import.meta.env.VITE_API_BASE_URL}/sse?userId=${newUserId}`
    );

    eventSource.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('SSE message received:', data);
        if (data.type === 'friendRequestUpdate') {
          onUpdate(data);
        }
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };

    eventSource.value.onerror = (error) => {
      console.error('SSE connection error:', error);
      // Auto-reconnect after 3 seconds
      setTimeout(() => {
        if (userId.value) setupSSE(userId.value);
      }, 3000);
    };
  };

  // Watch for changes to userId and update the SSE connection
  watch(
    () => userId.value,
    (newUserId) => {
      if (newUserId) {
        setupSSE(newUserId);
      }
    },
    { immediate: true } // Call setupSSE immediately when the composable is first used
  );

  // Cleanup on unmount
  onUnmounted(() => {
    if (eventSource.value) {
      console.log('Cleaning up SSE connection...');
      eventSource.value.close();
    }
  });

  return {
    setupSSE,
  };
}

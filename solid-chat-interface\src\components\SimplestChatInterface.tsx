import { Component, createSignal, onMount, onCleanup, createEffect, For } from 'solid-js';
import { Message, PerformanceMetrics } from '../types/chat';
import MessageItem from './MessageItem';
import MessageInput from './MessageInput';
import './ChatInterface.css';

interface SimplestChatInterfaceProps {
  messages: Message[];
  onSendMessage: (text: string, media?: any) => void;
  onMetricsUpdate: (metrics: PerformanceMetrics) => void;
}

const SimplestChatInterface: Component<SimplestChatInterfaceProps> = (props) => {
  let scrollElement: HTMLDivElement | undefined;
  const [isScrolledToBottom, setIsScrolledToBottom] = createSignal(true);

  // Telegram's message windowing state
  const [messageWindow, setMessageWindow] = createSignal(1000); // Start with 1000 messages
  const [isLoadingMore, setIsLoadingMore] = createSignal(false);

  // Performance tracking
  const [fps, setFps] = createSignal(0);
  const [scrollJank, setScrollJank] = createSignal(0);
  let frameCount = 0;
  let lastTime = performance.now();
  let animationId: number;

  // FPS monitoring
  onMount(() => {
    const updateFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(updateFPS);
    };

    updateFPS();
    setTimeout(() => scrollToBottom(), 100);
  });

  onCleanup(() => {
    if (animationId) cancelAnimationFrame(animationId);
  });

  // Telegram's approach: Smart message windowing
  createEffect(() => {
    const messageCount = props.messages.length;
    const currentWindow = messageWindow();

    // Calculate actual rendered messages based on current window
    const renderedMessages = Math.min(currentWindow, messageCount);
    const virtualizationRatio = messageCount > renderedMessages ?
      ((messageCount - renderedMessages) / messageCount) * 100 : 0;

    let performanceStatus = 'excellent';
    if (messageCount > 10000) {
      performanceStatus = 'windowed-large';
    } else if (messageCount > 2000) {
      performanceStatus = 'windowed';
    } else if (messageCount > 1000) {
      performanceStatus = 'large';
    }

    console.log(`📊 Performance: ${messageCount} messages, window: ${currentWindow}, rendered: ${renderedMessages} (${performanceStatus})`);

    props.onMetricsUpdate({
      fps: fps(),
      renderTime: 0,
      memoryUsage: getMemoryUsage(),
      scrollJank: scrollJank(),
      domNodes: document.querySelectorAll('*').length,
      virtualization: {
        totalMessages: messageCount,
        renderedMessages: renderedMessages,
        virtualizationRatio: virtualizationRatio,
        visibleRange: {
          start: Math.max(0, messageCount - renderedMessages),
          end: messageCount - 1
        }
      }
    });
  });

  const getMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024)
      };
    }
    return { used: 0, total: 0 };
  };

  const handleScroll = () => {
    if (!scrollElement) return;
    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    setIsScrolledToBottom(scrollHeight - scrollTop - clientHeight < 10);
  };

  const scrollToBottom = () => {
    if (scrollElement) {
      scrollElement.scrollTop = scrollElement.scrollHeight;
    }
  };

  const handleSendMessage = (text: string, media?: any) => {
    props.onSendMessage(text, media);
    setTimeout(() => scrollToBottom(), 50);
  };

  // Telegram's "Load More" functionality
  const handleLoadMore = () => {
    if (isLoadingMore()) return;

    setIsLoadingMore(true);
    const currentWindow = messageWindow();
    const messageCount = props.messages.length;
    const remainingMessages = messageCount - currentWindow;

    // Save current scroll position
    const scrollTop = scrollElement?.scrollTop || 0;
    const scrollHeight = scrollElement?.scrollHeight || 0;

    console.log(`📜 Loading more: current window ${currentWindow}, remaining ${remainingMessages}`);

    // Simulate loading delay (like Telegram)
    setTimeout(() => {
      // Expand window by 500 messages (Telegram's strategy)
      const newWindow = Math.min(currentWindow + 500, messageCount);
      setMessageWindow(newWindow);

      // Restore scroll position after DOM update
      setTimeout(() => {
        if (scrollElement) {
          const newScrollHeight = scrollElement.scrollHeight;
          const heightDiff = newScrollHeight - scrollHeight;
          scrollElement.scrollTop = scrollTop + heightDiff;
        }
        setIsLoadingMore(false);

        console.log(`✅ Loaded more: window expanded to ${newWindow} messages`);
      }, 50);
    }, 300); // 300ms delay like Telegram
  };

  // Auto-scroll when new messages arrive
  createEffect(() => {
    if (props.messages.length > 0 && isScrolledToBottom()) {
      setTimeout(() => scrollToBottom(), 50);
    }
  });

  return (
    <div class="chat-interface">
      <div
        ref={scrollElement}
        class="messages-container"
        onScroll={handleScroll}
        style={{
          height: '500px',
          overflow: 'auto',
        }}
      >
        {/* TELEGRAM'S APPROACH: Smart message windowing */}
        <div style={{ width: '100%', padding: '8px 0' }}>
          {/* Show "Load More" button for older messages */}
          {props.messages.length > messageWindow() && (
            <div style={{ padding: '16px', 'text-align': 'center' }}>
              <button
                style={{
                  padding: '8px 16px',
                  background: isLoadingMore() ? '#666' : '#0088cc',
                  color: 'white',
                  border: 'none',
                  'border-radius': '4px',
                  cursor: isLoadingMore() ? 'not-allowed' : 'pointer',
                  opacity: isLoadingMore() ? '0.7' : '1'
                }}
                onClick={handleLoadMore}
                disabled={isLoadingMore()}
              >
                {isLoadingMore()
                  ? '⏳ Loading...'
                  : `📜 Load ${Math.min(500, props.messages.length - messageWindow()).toLocaleString()} more messages`
                }
              </button>
            </div>
          )}

          {/* Render messages within current window (Telegram's windowing) */}
          <For each={props.messages.slice(-messageWindow())}>
            {(message, index) => (
              <div class="message-item" data-index={index()}>
                <MessageItem message={message} />
              </div>
            )}
          </For>
        </div>
      </div>

      {!isScrolledToBottom() && (
        <button
          class="scroll-to-bottom"
          onClick={scrollToBottom}
        >
          ↓
        </button>
      )}

      <MessageInput onSendMessage={handleSendMessage} />
    </div>
  );
};

export default SimplestChatInterface;

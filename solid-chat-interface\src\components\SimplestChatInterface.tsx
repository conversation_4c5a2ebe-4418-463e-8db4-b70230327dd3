import { Component, createSignal, onMount, onCleanup, createEffect, For } from 'solid-js';
import { Message, PerformanceMetrics } from '../types/chat';
import MessageItem from './MessageItem';
import MessageInput from './MessageInput';
import './ChatInterface.css';

interface SimplestChatInterfaceProps {
  messages: Message[];
  onSendMessage: (text: string, media?: any) => void;
  onMetricsUpdate: (metrics: PerformanceMetrics) => void;
}

const SimplestChatInterface: Component<SimplestChatInterfaceProps> = (props) => {
  let scrollElement: HTMLDivElement | undefined;
  const [isScrolledToBottom, setIsScrolledToBottom] = createSignal(true);

  // Performance tracking
  const [fps, setFps] = createSignal(0);
  const [scrollJank, setScrollJank] = createSignal(0);
  let frameCount = 0;
  let lastTime = performance.now();
  let animationId: number;

  // FPS monitoring
  onMount(() => {
    const updateFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(updateFPS);
    };

    updateFPS();
    setTimeout(() => scrollToBottom(), 100);
  });

  onCleanup(() => {
    if (animationId) cancelAnimationFrame(animationId);
  });

  // Telegram's approach: Smart message windowing
  createEffect(() => {
    const messageCount = props.messages.length;

    // Telegram's strategy: Render recent messages, window older ones
    let renderedMessages = messageCount;
    let virtualizationRatio = 0;
    let performanceStatus = 'excellent';

    // Apply Telegram's windowing for large datasets
    if (messageCount > 2000) {
      // Render last 1000 messages for optimal performance
      renderedMessages = Math.min(1000, messageCount);
      virtualizationRatio = ((messageCount - renderedMessages) / messageCount) * 100;
      performanceStatus = 'windowed';

      console.log(`🪟 Telegram windowing: showing last ${renderedMessages}/${messageCount} messages`);
    } else if (messageCount > 1000) {
      performanceStatus = 'large';
    }

    console.log(`📊 Performance: ${messageCount} messages (${performanceStatus})`);

    props.onMetricsUpdate({
      fps: fps(),
      renderTime: 0,
      memoryUsage: getMemoryUsage(),
      scrollJank: scrollJank(),
      domNodes: document.querySelectorAll('*').length,
      virtualization: {
        totalMessages: messageCount,
        renderedMessages: renderedMessages,
        virtualizationRatio: virtualizationRatio,
        visibleRange: {
          start: Math.max(0, messageCount - renderedMessages),
          end: messageCount - 1
        }
      }
    });
  });

  const getMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024)
      };
    }
    return { used: 0, total: 0 };
  };

  const handleScroll = () => {
    if (!scrollElement) return;
    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    setIsScrolledToBottom(scrollHeight - scrollTop - clientHeight < 10);
  };

  const scrollToBottom = () => {
    if (scrollElement) {
      scrollElement.scrollTop = scrollElement.scrollHeight;
    }
  };

  const handleSendMessage = (text: string, media?: any) => {
    props.onSendMessage(text, media);
    setTimeout(() => scrollToBottom(), 50);
  };

  // Auto-scroll when new messages arrive
  createEffect(() => {
    if (props.messages.length > 0 && isScrolledToBottom()) {
      setTimeout(() => scrollToBottom(), 50);
    }
  });

  return (
    <div class="chat-interface">
      <div
        ref={scrollElement}
        class="messages-container"
        onScroll={handleScroll}
        style={{
          height: '500px',
          overflow: 'auto',
        }}
      >
        {/* TELEGRAM'S APPROACH: Smart message windowing */}
        <div style={{ width: '100%', padding: '8px 0' }}>
          {/* Show "Load More" button for older messages */}
          {props.messages.length > 2000 && (
            <div style={{ padding: '16px', text-align: 'center' }}>
              <button
                style={{
                  padding: '8px 16px',
                  background: '#0088cc',
                  color: 'white',
                  border: 'none',
                  'border-radius': '4px',
                  cursor: 'pointer'
                }}
                onClick={() => {
                  console.log(`📜 Load more: ${props.messages.length - 1000} older messages available`);
                  // In real implementation, this would load older messages
                }}
              >
                📜 Load {(props.messages.length - 1000).toLocaleString()} older messages
              </button>
            </div>
          )}

          {/* Render recent messages (Telegram's windowing) */}
          <For each={props.messages.length > 2000 ? props.messages.slice(-1000) : props.messages}>
            {(message, index) => (
              <div class="message-item" data-index={index()}>
                <MessageItem message={message} />
              </div>
            )}
          </For>
        </div>
      </div>

      {!isScrolledToBottom() && (
        <button
          class="scroll-to-bottom"
          onClick={scrollToBottom}
        >
          ↓
        </button>
      )}

      <MessageInput onSendMessage={handleSendMessage} />
    </div>
  );
};

export default SimplestChatInterface;

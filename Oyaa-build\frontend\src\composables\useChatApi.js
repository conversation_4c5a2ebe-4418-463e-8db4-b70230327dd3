// src/composables/useChatApi.js
import axios from 'axios';

export default function useChatApi() {
  const fetchFriend = async (friendId, token) => {
    const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/users/${friendId}`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  };

  const fetchMessages = async (userId, targetUserId, page, pageSize, token) => {
    const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/chat/retrieve`, {
      params: { userId, targetUserId, page, pageSize },
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  };

  return { fetchFriend, fetchMessages };
}

<template>
  <div class="attachments">
    <!-- Hidden file input -->
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      @change="handleFileUpload"
      accept=".jpeg,.jpg,.png,.gif,.mp4,.mov,.avi,.mp3,.m4a,.wav,.ogg,.aac,.webm"
    />
    
    <!-- File preview (visible only when a file is selected) -->
    <div v-if="file" class="file-preview">
      <div class="preview-content">
        <img
          v-if="fileType === 'image' && previewUrl"
          :src="previewUrl"
          class="preview-thumbnail"
          alt="File preview"
        />
        <i v-else-if="fileType === 'video'" class="fas fa-video"></i>
        <i v-else-if="fileType === 'audio'" class="fas fa-music"></i>
        <i v-else class="fas fa-file"></i>
        <span class="filename">{{ file.name }}</span>
      </div>
      <button class="remove-button" @click="removeFile">
        <span class="close-icon">×</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref,} from 'vue';

const fileInput = ref(null);
const file = ref(null);
const previewUrl = ref(null);
const fileType = ref(null);

const emit = defineEmits(['file-changed']);

const triggerFileInput = () => {
  fileInput.value.click();
};

const handleFileUpload = (event) => {
  const selectedFile = event.target.files[0];
  if (selectedFile) {
    file.value = selectedFile;
    if (selectedFile.type.startsWith('image/')) {
      fileType.value = 'image';
      previewUrl.value = URL.createObjectURL(selectedFile);
    } else if (selectedFile.type.startsWith('video/')) {
      fileType.value = 'video';
      previewUrl.value = null;
    } else if (selectedFile.type.startsWith('audio/')) {
      fileType.value = 'audio';
      previewUrl.value = null;
    } else {
      fileType.value = 'other';
      previewUrl.value = null;
    }
    emit('file-changed', selectedFile);
  }
};

const removeFile = () => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value);
    previewUrl.value = null;
  }
  file.value = null;
  fileType.value = null;
  emit('file-changed', null);
};

defineExpose({ triggerFileInput, removeFile });
</script>

<style scoped>
.attachments {
  background-color: #2b2d31;
  padding: 0 16px;
}

.file-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #1e1f22;
  border-radius: 8px;
  padding: 8px;
  max-width: 320px;
}

.preview-content {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
}

.preview-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
  background-color: #2b2d31;
}

.fas {
  font-size: 24px;
  color: #dbdee1;
}

.filename {
  color: #dbdee1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.remove-button {
  background: none;
  border: none;
  color: #b5bac1;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  transition: background-color 0.2s;
}

.remove-button:hover {
  background-color: #2b2d31;
  color: #ffffff;
}

.close-icon {
  font-size: 18px;
  line-height: 1;
}
</style>
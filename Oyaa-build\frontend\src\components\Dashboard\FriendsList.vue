<template>
  <div class="friends-container">
    <SearchBar @search="handleSearch" />
    <div class="list">
      <ul v-if="loading" class="friends-list">
        <FriendSkeleton v-for="i in 5" :key="i" />
      </ul>
      <div v-else>
        <div v-if="friends.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <p>You don't have any friends yet.</p>
          <p>Click the action button below to add friends.</p>
        </div>
        <div v-else-if="filteredFriends.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </div>
          <p>No friends match your search.</p>
        </div>
        <ul v-else class="friends-list">
          <FriendItem
            v-for="friend in filteredFriends"
            :key="friend.id"
            :friend="friend"
            @openChat="openChat"
          />
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { mapGetters, mapState } from 'vuex';
import chatSocket from '@/chatSocket';
import FriendItem from './FriendsList/FriendItem.vue';
import FriendSkeleton from './FriendsList/FriendSkeleton.vue';
import SearchBar from './SearchBar.vue';

export default {
  props: {
    friendRequestsCount: { type: Number, required: true },
  },
  components: {
    FriendItem,
    FriendSkeleton,
    SearchBar,
  },
  data() {
    return {
      friends: [],
      loading: false,
      searchQuery: '',
    };
  },
  computed: {
    ...mapGetters('auth', ['user']),
    ...mapState('chat', ['lastChats', 'typingStatuses']),
    mergedFriends() {
      return this.friends.map(friend => {
        const lastChat = this.lastChats[friend.id];
        const isTyping = this.typingStatuses[friend.id];
        return {
          ...friend,
          isTyping,
          lastMessage: isTyping
            ? `${friend.username} is typing...`
            : (lastChat ? lastChat.message : 'No messages yet'),
          lastMessageTime: lastChat ? lastChat.sent_at : null,
        };
      });
    },
    sortedFriends() {
      return [...this.mergedFriends].sort((a, b) => {
        const aTime = a.lastMessageTime ? new Date(a.lastMessageTime) : new Date(0);
        const bTime = b.lastMessageTime ? new Date(b.lastMessageTime) : new Date(0);
        return bTime - aTime;
      });
    },
    filteredFriends() {
      if (!this.searchQuery) return this.sortedFriends;
      return this.sortedFriends.filter(friend =>
        friend.username.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    },
  },
  async mounted() {
    // Check if user exists and has an ID
    // Try to get the user from the store
    const userId = this.$store.getters['auth/userId'];

    if (this.user && this.user.id) {
      await Promise.all([this.fetchFriends(), this.fetchLastChats()]);
      chatSocket.emit('joinPersonalRoom', this.user.id);
      chatSocket.on('lastMessageUpdate', this.handleLastMessageUpdate);
    } else if (userId) {
      // If we have a userId but no user object, use the userId directly
      console.log('User object not available, but userId is available:', userId);
      this.user = { id: userId };
      await Promise.all([this.fetchFriends(), this.fetchLastChats()]);
      chatSocket.emit('joinPersonalRoom', userId);
      chatSocket.on('lastMessageUpdate', this.handleLastMessageUpdate);
    } else {
      console.warn('User or user ID not available yet in FriendsList component');
      // Set up a watcher to fetch data once user becomes available
      this.$watch('user', (newUser) => {
        if (newUser && newUser.id) {
          console.log('User now available, fetching friends data');
          Promise.all([this.fetchFriends(), this.fetchLastChats()]);
          chatSocket.emit('joinPersonalRoom', newUser.id);
          chatSocket.on('lastMessageUpdate', this.handleLastMessageUpdate);
        }
      }, { deep: true });

      // Also watch the auth/userId getter
      this.$watch(
        () => this.$store.getters['auth/userId'],
        (newUserId) => {
          if (newUserId && (!this.user || !this.user.id)) {
            console.log('User ID now available, fetching friends data');
            this.user = { id: newUserId };
            Promise.all([this.fetchFriends(), this.fetchLastChats()]);
            chatSocket.emit('joinPersonalRoom', newUserId);
            chatSocket.on('lastMessageUpdate', this.handleLastMessageUpdate);
          }
        }
      );
    }
  },
  beforeUnmount() {
    chatSocket.off('lastMessageUpdate', this.handleLastMessageUpdate);
  },
  methods: {
    async fetchFriends() {
      if (!this.user || !this.user.id) {
        console.warn('Cannot fetch friends: No valid user ID available');
        return;
      }

      this.loading = true;
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/friends/${this.user.id}`,
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        );
        this.friends = response.data.friends || [];
      } catch (error) {
        console.error('Error fetching friends:', error);
        this.friends = [];
      } finally {
        this.loading = false;
      }
    },
    async fetchLastChats() {
      if (!this.user || !this.user.id) {
        console.warn('Cannot fetch last chats: No valid user ID available');
        return;
      }

      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/chat/last-chats`,
          {
            params: { userId: this.user.id },
            headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
          }
        );
        this.$store.commit('chat/SET_LAST_CHATS', response.data.chats || []);
      } catch (error) {
        console.error('Error fetching last chats:', error);
      }
    },
    handleLastMessageUpdate(data) {
      this.$store.dispatch('chat/updateLastChat', data);
    },
    openChat(friendId) {
      if (this.friends.some(f => f.id === friendId)) {
        this.$router.push(`/chat/${friendId}`);
      } else {
        console.error('Friend not found in list');
      }
    },
    handleSearch(query) {
      this.searchQuery = query;
    },
  },
};
</script>

<style scoped>
/* Existing styles remain unchanged */
</style>

<style scoped>
.friends-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.list {
  flex: 1;

  padding-bottom: 15vh;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.friends-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #a0a0a0;
  height: 50vh;
}

.empty-icon {
  margin-bottom: 16px;
  color: #6366f1;
  opacity: 0.7;
}

.empty-state p {
  margin: 4px 0;
  font-size: 15px;
}

.empty-state p:first-of-type {
  color: #e2e2e2;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .empty-state {
    padding: 20px 16px;
  }

  .empty-icon svg {
    width: 40px;
    height: 40px;
  }

  .empty-state p {
    font-size: 14px;
  }

  .empty-state p:first-of-type {
    font-size: 15px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .empty-icon svg {
    width: 32px;
    height: 32px;
  }

  .empty-state p {
    font-size: 13px;
  }

  .empty-state p:first-of-type {
    font-size: 14px;
  }
}
</style>
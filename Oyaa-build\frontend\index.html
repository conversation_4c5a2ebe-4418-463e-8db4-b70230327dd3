<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- Use your custom favicon -->
    <link rel="icon" type="image/x-icon" href="/Oyaa-icon.ico" />
    <!-- Improved viewport meta tag with maximum-scale to prevent unwanted zooming -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <!-- Add mobile web app capability -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <title>Oyaa</title>
    <!-- Font Awesome CDN -->
    <link 
      rel="stylesheet" 
      href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0-beta3/css/all.min.css">
    <!-- Rubik Font from Google Fonts -->
    <link 
      rel="stylesheet" 
      href="https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap">
    <!-- OverlayScrollbars CSS from CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/overlayscrollbars/css/OverlayScrollbars.min.css" />
  </head>
  <body>
    <div id="app"></div>
    <!-- OverlayScrollbars JS from CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/overlayscrollbars/js/OverlayScrollbars.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/recorder-js@1.0.7/dist/recorder.min.js"></script>
   
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
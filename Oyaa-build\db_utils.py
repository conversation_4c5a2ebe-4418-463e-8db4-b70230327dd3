"""
PostgreSQL Database Utility Script

This script provides functions to connect to and manage your PostgreSQL database on Aiven Cloud.
It allows you to read, edit, create, and remove tables, as well as execute custom queries.

Usage:
    1. Import this script in your code: from db_utils import PostgresDB
    2. Create a database instance: db = PostgresDB()
    3. Use the provided methods to interact with your database
"""

import psycopg2
from psycopg2.extras import RealDictCursor
from typing import Dict, List, Any, Optional, Union, Tuple


class PostgresDB:
    """PostgreSQL database connection and management class for Aiven Cloud."""

    def __init__(self, connection_string=None):
        """
        Initialize the PostgreSQL connection.

        Args:
            connection_string: Database connection string (optional, uses default if not provided)
        """
        # Use provided connection string or default to your Aiven Cloud connection
        self.connection_string = connection_string or 'postgres://avnadmin:<EMAIL>:13427/defaultdb?sslmode=require'

    def connect(self):
        """Create and return a new database connection."""
        return psycopg2.connect(self.connection_string)

    def execute_query(self, query: str, params: tuple = None, fetch: bool = True,
                     as_dict: bool = True) -> Union[List[Dict], List[tuple], None]:
        """
        Execute a SQL query and return the results.

        Args:
            query: SQL query string
            params: Parameters for the query
            fetch: Whether to fetch and return results
            as_dict: Return results as dictionaries (otherwise as tuples)

        Returns:
            Query results as a list of dictionaries or tuples, or None for non-SELECT queries
        """
        conn = None
        try:
            conn = self.connect()
            cursor_factory = RealDictCursor if as_dict else None
            with conn.cursor(cursor_factory=cursor_factory) as cursor:
                cursor.execute(query, params)

                if fetch:
                    results = cursor.fetchall()
                    return results
                else:
                    conn.commit()
                    return None

        except Exception as e:
            if conn:
                conn.rollback()
            print(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    # Table Operations

    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the database.

        Args:
            table_name: Name of the table to check

        Returns:
            True if the table exists, False otherwise
        """
        query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = %s
            );
        """
        result = self.execute_query(query, (table_name,))
        return result[0]['exists'] if result else False

    def create_table(self, table_name: str, columns: Dict[str, str],
                    primary_key: Optional[str] = None) -> bool:
        """
        Create a new table in the database.

        Args:
            table_name: Name of the table to create
            columns: Dictionary mapping column names to their data types
            primary_key: Name of the primary key column (optional)

        Returns:
            True if successful, False otherwise
        """
        if self.table_exists(table_name):
            print(f"Table '{table_name}' already exists.")
            return False

        # Build column definitions
        column_defs = []
        for col_name, col_type in columns.items():
            if primary_key and col_name == primary_key:
                column_defs.append(f"{col_name} {col_type} PRIMARY KEY")
            else:
                column_defs.append(f"{col_name} {col_type}")

        # Create the table
        query = f"""
            CREATE TABLE {table_name} (
                {', '.join(column_defs)}
            );
        """

        try:
            self.execute_query(query, fetch=False)
            print(f"Table '{table_name}' created successfully.")
            return True
        except Exception as e:
            print(f"Error creating table '{table_name}': {e}")
            return False

    def drop_table(self, table_name: str, if_exists: bool = True) -> bool:
        """
        Drop a table from the database.

        Args:
            table_name: Name of the table to drop
            if_exists: Only drop if the table exists

        Returns:
            True if successful, False otherwise
        """
        if_exists_clause = "IF EXISTS" if if_exists else ""
        query = f"DROP TABLE {if_exists_clause} {table_name};"

        try:
            self.execute_query(query, fetch=False)
            print(f"Table '{table_name}' dropped successfully.")
            return True
        except Exception as e:
            print(f"Error dropping table '{table_name}': {e}")
            return False

    def list_tables(self) -> List[str]:
        """
        List all tables in the database.

        Returns:
            List of table names
        """
        query = """
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public';
        """
        results = self.execute_query(query)
        return [row['table_name'] for row in results]

    def get_table_schema(self, table_name: str) -> List[Dict]:
        """
        Get the schema of a table.

        Args:
            table_name: Name of the table

        Returns:
            List of column information dictionaries
        """
        query = """
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = %s
            ORDER BY ordinal_position;
        """
        return self.execute_query(query, (table_name,))

    # Data Operations

    def insert_data(self, table_name: str, data: Dict[str, Any]) -> int:
        """
        Insert a single row of data into a table.

        Args:
            table_name: Name of the table
            data: Dictionary mapping column names to values

        Returns:
            ID of the inserted row if available, otherwise 0
        """
        columns = list(data.keys())
        values = list(data.values())
        placeholders = [f"%s" for _ in range(len(columns))]

        query = f"""
            INSERT INTO {table_name} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            RETURNING id;
        """

        try:
            result = self.execute_query(query, tuple(values))
            return result[0]['id'] if result and 'id' in result[0] else 0
        except Exception as e:
            print(f"Error inserting data into '{table_name}': {e}")
            return 0

    def insert_many(self, table_name: str, columns: List[str], values: List[tuple]) -> bool:
        """
        Insert multiple rows of data into a table.

        Args:
            table_name: Name of the table
            columns: List of column names
            values: List of value tuples, each corresponding to a row

        Returns:
            True if successful, False otherwise
        """
        placeholders = [f"%s" for _ in range(len(columns))]
        query = f"""
            INSERT INTO {table_name} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)});
        """

        conn = None
        try:
            conn = self.connect()
            with conn.cursor() as cursor:
                cursor.executemany(query, values)
                conn.commit()
            print(f"Successfully inserted {len(values)} rows into '{table_name}'.")
            return True
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"Error inserting multiple rows into '{table_name}': {e}")
            return False
        finally:
            if conn:
                conn.close()

    def select_all(self, table_name: str, columns: List[str] = None,
                  where: str = None, params: tuple = None,
                  order_by: str = None, limit: int = None) -> List[Dict]:
        """
        Select data from a table with optional filtering and ordering.

        Args:
            table_name: Name of the table
            columns: List of columns to select (default: all columns)
            where: WHERE clause (without the 'WHERE' keyword)
            params: Parameters for the WHERE clause
            order_by: ORDER BY clause (without the 'ORDER BY' keywords)
            limit: Maximum number of rows to return

        Returns:
            List of row dictionaries
        """
        cols = ', '.join(columns) if columns else '*'
        query = f"SELECT {cols} FROM {table_name}"

        if where:
            query += f" WHERE {where}"
        if order_by:
            query += f" ORDER BY {order_by}"
        if limit:
            query += f" LIMIT {limit}"

        return self.execute_query(query, params)

    def update_data(self, table_name: str, data: Dict[str, Any],
                   where: str, params: tuple = None) -> int:
        """
        Update data in a table.

        Args:
            table_name: Name of the table
            data: Dictionary mapping column names to new values
            where: WHERE clause (without the 'WHERE' keyword)
            params: Parameters for the WHERE clause

        Returns:
            Number of rows updated
        """
        set_clause = ', '.join([f"{col} = %s" for col in data.keys()])
        query = f"UPDATE {table_name} SET {set_clause}"

        if where:
            query += f" WHERE {where}"

        # Combine data values with where clause parameters
        all_params = tuple(data.values())
        if params:
            all_params += params

        conn = None
        try:
            conn = self.connect()
            with conn.cursor() as cursor:
                cursor.execute(query, all_params)
                rows_updated = cursor.rowcount
                conn.commit()
                return rows_updated
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"Error updating data in '{table_name}': {e}")
            return 0
        finally:
            if conn:
                conn.close()

    def delete_data(self, table_name: str, where: str = None, params: tuple = None) -> int:
        """
        Delete data from a table.

        Args:
            table_name: Name of the table
            where: WHERE clause (without the 'WHERE' keyword)
            params: Parameters for the WHERE clause

        Returns:
            Number of rows deleted
        """
        query = f"DELETE FROM {table_name}"

        if where:
            query += f" WHERE {where}"

        conn = None
        try:
            conn = self.connect()
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                rows_deleted = cursor.rowcount
                conn.commit()
                return rows_deleted
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"Error deleting data from '{table_name}': {e}")
            return 0
        finally:
            if conn:
                conn.close()

    def execute_raw_query(self, query: str, params: tuple = None):
        """
        Execute a raw SQL query with full control.

        Args:
            query: SQL query string
            params: Parameters for the query

        Returns:
            Query results or None
        """
        return self.execute_query(query, params)


# Example usage
if __name__ == "__main__":
    # Create database instance
    db = PostgresDB()

    # Test connection by getting PostgreSQL version
    try:
        result = db.execute_query("SELECT VERSION()")
        print(f"Connected to: {result[0]['version']}")

        # List all tables
        tables = db.list_tables()
        print(f"Tables in database: {tables}")

    except Exception as e:
        print(f"Connection error: {e}")

const groupMembersModel = require('../../models/Groups/groupMembersModel');

class GroupMembersService {
  async addMember(groupId, userId, role = 'member') {
    const alreadyMember = await groupMembersModel.isMember(groupId, userId);
    if (alreadyMember) {
      throw new Error('User is already a member of this group.');
    }
    return await groupMembersModel.addMember(groupId, userId, role);
  }

  async removeMember(groupId, userId) {
    // Prevent removal from World Chat (groupId = 1)
    if (parseInt(groupId) === 1) {
      throw new Error('Cannot leave World Chat');
    }
    // Proceed with removal for other groups
    return await GroupMembersModel.removeMember(groupId, userId);
  }

  async getMembers(groupId) {
    return await groupMembersModel.getMembers(groupId);
  }

  async updateMemberRole(groupId, userId, newRole) {
    return await groupMembersModel.updateMemberRole(groupId, userId, newRole);
  }
}

module.exports = new GroupMembersService();
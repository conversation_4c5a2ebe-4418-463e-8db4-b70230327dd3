import { Component, createSignal } from 'solid-js';
import { Message, PerformanceMetrics } from '../types/chat';
import './DebugMenu.css';

interface DebugMenuProps {
  messages: Message[];
  metrics: PerformanceMetrics;
  onGenerateMessages: (count: number) => void;
  onClearMessages: () => void;
}

const DebugMenu: Component<DebugMenuProps> = (props) => {
  const [isOpen, setIsOpen] = createSignal(false);

  const copyMetrics = () => {
    const metricsData = {
      timestamp: new Date().toISOString(),
      performance: {
        fps: props.metrics.fps,
        renderTime: `${props.metrics.renderTime.toFixed(2)}ms`,
        memoryUsage: `${props.metrics.memoryUsage.used}MB / ${props.metrics.memoryUsage.total}MB`,
        scrollJank: props.metrics.scrollJank
      },
      virtualization: {
        totalMessages: props.metrics.virtualization.totalMessages,
        renderedMessages: props.metrics.virtualization.renderedMessages,
        virtualizationRatio: `${props.metrics.virtualization.virtualizationRatio.toFixed(1)}%`,
        visibleRange: `${props.metrics.virtualization.visibleRange.start} - ${props.metrics.virtualization.visibleRange.end}`,
        totalHeight: 'N/A (TanStack Virtual)',
        averageItemHeight: '80px (estimated)'
      },
      scroll: {
        scrollTop: 'N/A',
        containerHeight: 'N/A',
        scrollPercentage: 'N/A',
        isSmooth: true
      },
      browser: {
        userAgent: navigator.userAgent,
        viewport: `${window.innerWidth}x${window.innerHeight}`,
        devicePixelRatio: window.devicePixelRatio
      }
    };

    navigator.clipboard.writeText(JSON.stringify(metricsData, null, 2)).then(() => {
      alert('Metrics copied to clipboard!');
    }).catch(() => {
      console.log('Metrics:', metricsData);
      alert('Metrics logged to console (clipboard not available)');
    });
  };

  const mediaMessages = () => props.messages.filter(m => m.media && m.type !== 'text');

  return (
    <div class={`debug-menu ${isOpen() ? 'open' : ''}`}>
      <button 
        class="debug-toggle"
        onClick={() => setIsOpen(!isOpen())}
      >
        🐛
      </button>

      {isOpen() && (
        <div class="debug-panel">
          <div class="debug-header">
            <h3>SolidJS Performance Debug</h3>
            <button class="copy-btn" onClick={copyMetrics}>📋 Copy All</button>
          </div>

          <div class="debug-section">
            <h4>🚀 Performance</h4>
            <div class="metric">
              <span class="label">FPS:</span>
              <span class={`value ${props.metrics.fps < 30 ? 'warning' : ''} ${props.metrics.fps < 15 ? 'critical' : ''}`}>
                {props.metrics.fps}
              </span>
            </div>
            <div class="metric">
              <span class="label">Render Time:</span>
              <span class="value">TanStack Optimized</span>
            </div>
            <div class="metric">
              <span class="label">Memory:</span>
              <span class="value">{props.metrics.memoryUsage.used}MB / {props.metrics.memoryUsage.total}MB</span>
            </div>
            <div class="metric">
              <span class="label">Scroll Jank:</span>
              <span class={`value ${props.metrics.scrollJank > 10 ? 'warning' : ''}`}>
                {props.metrics.scrollJank}
              </span>
            </div>
            <div class="metric">
              <span class="label">DOM Nodes:</span>
              <span class={`value ${props.metrics.domNodes > 1000 ? 'warning' : ''}`}>
                {props.metrics.domNodes.toLocaleString()}
              </span>
            </div>
          </div>

          <div class="debug-section">
            <h4>📊 Virtualization (TanStack)</h4>
            <div class="metric">
              <span class="label">Total Messages:</span>
              <span class="value">{props.metrics.virtualization.totalMessages.toLocaleString()}</span>
            </div>
            <div class="metric">
              <span class="label">Rendered:</span>
              <span class="value">{props.metrics.virtualization.renderedMessages}</span>
            </div>
            <div class="metric">
              <span class="label">Virtualized:</span>
              <span class="value">{props.metrics.virtualization.virtualizationRatio.toFixed(1)}%</span>
            </div>
            <div class="metric">
              <span class="label">Visible Range:</span>
              <span class="value">
                {props.metrics.virtualization.visibleRange.start} - {props.metrics.virtualization.visibleRange.end}
              </span>
            </div>
            <div class="metric">
              <span class="label">Media Items:</span>
              <span class="value">{mediaMessages().length} (lazy loaded)</span>
            </div>
          </div>

          <div class="debug-section">
            <h4>⚡ Performance Status</h4>
            {props.metrics.fps < 15 ? (
              <div class="alert critical">🔴 Critical: FPS too low</div>
            ) : props.metrics.fps < 30 ? (
              <div class="alert warning">🟡 Warning: FPS below optimal</div>
            ) : (
              <div class="alert good">🟢 Good: FPS optimal</div>
            )}
            
            {props.metrics.scrollJank > 50 && (
              <div class="alert warning">🟡 High scroll jank detected</div>
            )}
            
            {props.metrics.domNodes > 2000 && (
              <div class="alert warning">🟡 High DOM node count</div>
            )}
            
            {mediaMessages().length > 0 && (
              <div class="alert good">🟢 Media lazy loading active</div>
            )}

            <div class="alert good">🟢 TanStack Virtual active</div>
          </div>

          <div class="debug-section">
            <h4>🧪 Test Data</h4>
            <div class="button-grid">
              <button class="test-btn" onClick={() => props.onGenerateMessages(1000)}>
                +1K Messages
              </button>
              <button class="test-btn" onClick={() => props.onGenerateMessages(10000)}>
                +10K Messages
              </button>
              <button class="test-btn" onClick={() => props.onGenerateMessages(50000)}>
                +50K Messages
              </button>
              <button class="test-btn" onClick={() => props.onGenerateMessages(100000)}>
                +100K Messages
              </button>
              <button class="test-btn danger" onClick={props.onClearMessages}>
                Clear All
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DebugMenu;

<template>
  <div class="group-info">
    <div class="avatar">
      <img v-if="group?.avatar" :src="group.avatar" alt="Group avatar" />
      <div v-else class="avatar-placeholder">
        <span>{{ getInitials(group?.name) }}</span>
      </div>
    </div>
    <div class="info">
      <div class="name">{{ group?.name }}</div>
      <div class="status-container">
        <span v-if="!isTyping" class="status-dot" :class="{ 'active': isActive }"></span>
        <div v-if="isTyping" class="typing-indicator">
          <span class="typing-dot"></span>
          <span class="typing-dot"></span>
          <span class="typing-dot"></span>
        </div>
        <p v-if="isTyping" class="typing-text">{{ typingText }}</p>
        <p v-else-if="group?.description" class="description">{{ truncateDescription(group.description) }}</p>
        <p v-else class="description">{{ group?.memberCount || 0 }} members</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  group: {
    type: Object,
    required: true,
  },
  typingUsers: {
    type: Array,
    default: () => [],
  },
});

// Simulate active status - in a real app, this would come from props or a store
const isActive = computed(() => true);

// Check if anyone is typing
const isTyping = computed(() => props.typingUsers && props.typingUsers.length > 0);

// Generate the typing text based on who is typing
const typingText = computed(() => {
  if (!props.typingUsers || props.typingUsers.length === 0) return '';
  
  if (props.typingUsers.length === 1) {
    return `${props.typingUsers[0]} is typing...`;
  } else if (props.typingUsers.length === 2) {
    return `${props.typingUsers[0]} and ${props.typingUsers[1]} are typing...`;
  } else {
    return `${props.typingUsers[0]} and ${props.typingUsers.length - 1} others are typing...`;
  }
});

const getInitials = (name) => {
  if (!name) return '?';
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

const truncateDescription = (description) => {
  if (!description) return '';
  return description.length > 30 ? description.substring(0, 30) + '...' : description;
};
</script>

<style scoped>
.group-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #3498db;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.info {
  display: flex;
  flex-direction: column;
  min-width: 0; /* Enables text truncation */
}

.name {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #a0a0a0;
  flex-shrink: 0;
}

.status-dot.active {
  background-color: #2ecc71;
}

.description {
  font-size: 13px;
  color: #a0a0a0;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
}

.typing-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #2ecc71;
  animation: typingAnimation 1.5s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

.typing-text {
  font-size: 13px;
  color: #2ecc71;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .avatar {
    width: 36px;
    height: 36px;
  }
  
  .name {
    font-size: 15px;
  }
  
  .description, .typing-text {
    font-size: 12px;
  }
}
</style>
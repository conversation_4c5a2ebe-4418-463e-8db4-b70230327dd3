/* Telegram's exact bubble styling system */

:root {
  /* Telegram's CSS variables */
  --messages-line-height: 1.3125;
  --message-primary-color: #3390ec;
  --message-primary-color-rgb: 51, 144, 236;
  --message-status-color: rgba(255, 255, 255, 0.7);
  --message-highlighting-color-rgb: 51, 144, 236;
  --primary-text-color: #000;
  --messages-text-size: 16px;
  --messages-secondary-text-size: 14px;
  --messages-secondary-line-height: 1.2857;
  --messages-service-text-size: 14px;
  --font-weight-bold: 500;
  
  /* Telegram's spacing variables */
  --bubble-margin: 0.125rem;
  --bubble-margin-big: 0.375rem;
  --bubble-border-radius-medium: 5px;
  --bubble-border-radius-big: 15px;
  --message-padding-top: 4px;
  --message-padding-bottom: 5px;
  --message-padding-horizontal: 8px;
  --reply-padding-top: 8px;
  --reply-padding-horizontal: 8px;
  --time-margin-left: -3px;
  
  /* Dark theme colors */
  --surface-color: #212121;
  --bubble-in-background: #2b2b2b;
  --bubble-out-background: var(--message-primary-color);
  --bubble-in-text-color: #ffffff;
  --bubble-out-text-color: #ffffff;
  --bubble-time-in-color: rgba(255, 255, 255, 0.7);
  --bubble-time-out-color: rgba(255, 255, 255, 0.7);
}

/* Telegram's bubble container */
.bubble {
  --line-height: var(--messages-line-height);
  --primary-color: var(--message-primary-color);
  --primary-color-rgb: var(--message-primary-color-rgb);
  --secondary-text-color: var(--message-status-color);
  --peer-color-rgb: var(--message-primary-color-rgb);
  --peer-border-background: rgb(var(--peer-color-rgb));
  --max-width: 100%;
  
  position: relative;
  z-index: 1;
  margin: 0 auto var(--bubble-margin);
  user-select: none;
  display: flex;
  flex-wrap: wrap;
  max-width: 480px; /* Telegram's max bubble width */
  word-wrap: break-word;
  border-radius: var(--bubble-border-radius-big);
  transition: background-color 0.15s ease;
}

/* Incoming message bubbles */
.bubble.is-in {
  background-color: var(--bubble-in-background);
  color: var(--bubble-in-text-color);
  align-self: flex-start;
  margin-left: 0;
  margin-right: auto;
  border-bottom-left-radius: var(--bubble-border-radius-medium);
}

/* Outgoing message bubbles */
.bubble.is-out {
  background-color: var(--bubble-out-background);
  color: var(--bubble-out-text-color);
  align-self: flex-end;
  margin-left: auto;
  margin-right: 0;
  border-bottom-right-radius: var(--bubble-border-radius-medium);
}

/* Telegram's message content */
.bubble .message {
  font-size: var(--messages-text-size);
  margin: var(--message-padding-top) var(--message-padding-horizontal) var(--message-padding-bottom);
  max-width: 100%;
  color: inherit;
  line-height: var(--line-height);
  word-break: break-word;
  white-space: pre-wrap;
  position: relative;
}

/* Message grouping - reduce margin for consecutive messages */
.bubble.is-group-first {
  margin-top: var(--bubble-margin-big);
}

.bubble.is-group-middle,
.bubble.is-group-last {
  margin-top: 2px;
}

.bubble.is-group-middle.is-in {
  border-top-left-radius: var(--bubble-border-radius-medium);
  border-bottom-left-radius: var(--bubble-border-radius-medium);
}

.bubble.is-group-middle.is-out {
  border-top-right-radius: var(--bubble-border-radius-medium);
  border-bottom-right-radius: var(--bubble-border-radius-medium);
}

.bubble.is-group-last.is-in {
  border-top-left-radius: var(--bubble-border-radius-medium);
}

.bubble.is-group-last.is-out {
  border-top-right-radius: var(--bubble-border-radius-medium);
}

/* Telegram's time styling */
.bubble .time {
  font-size: 12px;
  color: var(--bubble-time-in-color);
  margin-left: var(--time-margin-left);
  float: right;
  margin-top: 2px;
  line-height: 1;
  position: relative;
}

.bubble.is-out .time {
  color: var(--bubble-time-out-color);
}

/* Message status indicators */
.bubble .message-status {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
  opacity: 0.7;
}

.bubble .message-status.sent::after {
  content: "✓";
  font-size: 12px;
}

.bubble .message-status.delivered::after {
  content: "✓✓";
  font-size: 12px;
}

.bubble .message-status.read::after {
  content: "✓✓";
  font-size: 12px;
  color: var(--message-primary-color);
}

/* Reply preview styling */
.bubble .reply-preview {
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 3px solid var(--message-primary-color);
  padding: var(--reply-padding-top) var(--reply-padding-horizontal);
  margin-bottom: var(--message-padding-top);
  border-radius: var(--bubble-border-radius-medium);
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.bubble .reply-preview:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.bubble .reply-preview .reply-author {
  font-size: var(--messages-secondary-text-size);
  font-weight: var(--font-weight-bold);
  color: var(--message-primary-color);
  margin-bottom: 2px;
}

.bubble .reply-preview .reply-text {
  font-size: var(--messages-secondary-text-size);
  line-height: var(--messages-secondary-line-height);
  opacity: 0.8;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
}

/* Service messages (date headers, system messages) */
.bubble.service {
  background: transparent;
  align-self: center;
  margin: var(--bubble-margin-big) auto;
  max-width: none;
}

.bubble.service .service-msg {
  color: #fff;
  background-color: rgba(var(--message-highlighting-color-rgb), 0.8);
  font-size: var(--messages-service-text-size);
  padding: 0.28125rem 0.625rem;
  line-height: calc(var(--messages-service-text-size) + 5px);
  border-radius: inherit;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-break: break-word;
  backdrop-filter: blur(20px);
}

/* Media message styling */
.bubble .media-container {
  margin: calc(var(--message-padding-top) * -1) calc(var(--message-padding-horizontal) * -1) var(--message-padding-bottom);
  border-radius: inherit;
  overflow: hidden;
  position: relative;
}

.bubble .media-container img,
.bubble .media-container video {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: inherit;
}

/* Reactions styling */
.bubble .reactions {
  margin-top: var(--message-padding-top);
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.bubble .reaction {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.bubble .reaction:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.bubble .reaction.is-chosen {
  background-color: rgba(var(--message-primary-color-rgb), 0.3);
}

/* Animation for new messages */
.bubble.is-new {
  animation: bubbleIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes bubbleIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Hover effects */
.bubble:hover {
  background-color: color-mix(in srgb, var(--bubble-in-background) 90%, white 10%);
}

.bubble.is-out:hover {
  background-color: color-mix(in srgb, var(--bubble-out-background) 90%, white 10%);
}

/* Selection styling */
.bubble.is-selected {
  background-color: rgba(var(--message-primary-color-rgb), 0.3) !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .bubble {
    max-width: calc(100vw - 120px);
  }
  
  .bubble .message {
    margin: 6px 10px 7px;
  }
}

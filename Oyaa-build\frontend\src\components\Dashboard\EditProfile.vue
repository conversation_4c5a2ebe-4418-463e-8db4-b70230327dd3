<template>
  <div class="edit-profile-container">
    <div class="edit-profile-header">
      <button class="back-button" @click="$router.push('/dashboard?tab=profile')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>Edit Profile</h1>
      <button class="save-button" @click="saveProfile">Save</button>
    </div>
  
    <div class="profile-form">
      <div class="form-section">
        <div class="form-group">
          <label for="email">Email</label>
          <input 
            id="email" 
            v-model="form.email" 
            type="email" 
            required 
          />
        </div>
  
        <div class="form-group">
          <label for="description">Bio</label>
          <textarea 
            id="description" 
            v-model="form.description" 
            maxlength="80"
            placeholder="Tell others about yourself"
          ></textarea>
          <span class="character-count">{{ form.description.length }}/80</span>
        </div>
  
        <div class="form-group tags-section">
          <label>Interests</label>
          <div class="tags-container">
            <div class="tags-list">
              <div 
                v-for="(tag, index) in form.tags" 
                :key="index" 
                class="tag"
              >
                <span>{{ tag }}</span>
                <button type="button" @click="removeTag(index)" class="remove-tag" aria-label="Remove tag">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
              
              <div class="add-tag-input">
                <input 
                  v-model="newTag" 
                  @keyup.enter="addTag" 
                  placeholder="Add interest" 
                  maxlength="20"
                />
                <button 
                  type="button" 
                  @click="addTag" 
                  class="add-tag-button"
                  :disabled="!newTag.trim() || form.tags.length >= 10"
                >
                  Add
                </button>
              </div>
            </div>
            <p class="tags-hint">You can add up to 10 interests</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import axios from 'axios';

export default {
  data() {
    return {
      form: {
        username: '',
        email: '',
        description: '',
        tags: [],
        avatar: ''
      },
      newTag: ''
    };
  },
  computed: {
    ...mapGetters('auth', ['user'])
  },
  mounted() {
    if (this.user) {
      this.form.username = this.user.username;
      this.form.email = this.user.email;
      this.form.description = this.user.description || '';
      this.form.tags = this.user.tags || [];
      this.form.avatar = this.user.avatar || '';
    } else {
      this.$router.push('/Signin');
    }
  },
  methods: {
    addTag() {
      if (this.newTag.trim() && this.form.tags.length < 10) {
        this.form.tags.push(this.newTag.trim());
        this.newTag = '';
      }
    },
    removeTag(index) {
      this.form.tags.splice(index, 1);
    },
    async saveProfile() {
      try {
        const response = await axios.put(
          `${import.meta.env.VITE_API_BASE_URL}/api/users/me`,
          this.form,
          {
            headers: { Authorization: `Bearer ${this.$store.state.auth.token}` }
          }
        );
        this.$store.commit('auth/UPDATE_USER', response.data.user);
        this.$router.push('/dashboard?tab=profile');
      } catch (error) {
        if (error.response && error.response.status === 409) {
          alert(error.response.data.message);
        } else {
          console.error('Failed to update profile:', error);
          alert('Failed to update profile. Please try again.');
        }
      }
    }
  }
};
</script>


  
<style scoped>
.edit-profile-container {
  background: #0f0f13;
  color: #e2e2e2;
  min-height: 100vh;
  font-family: 'Inter', 'Segoe UI', sans-serif;
}
  
.edit-profile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-bottom: 1px solid rgba(46, 46, 58, 0.8);
  position: sticky;
  top: 0;
  background-color: #0f0f13;
  z-index: 10;
  height: 56px;
}
  
.edit-profile-header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
}
  
.back-button {
  background: none;
  border: none;
  color: #a0a0a0;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-button:hover {
  color: #e2e2e2;
  background: rgba(255, 255, 255, 0.1);
}
  
.save-button {
  background: #6366f1;
  border: none;
  color: white;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.save-button:hover {
  background: #4f46e5;
  transform: translateY(-2px);
}

.save-button:active {
  transform: scale(0.98);
}
  
.profile-form {
  padding: 20px 16px;
  max-width: 600px;
  margin: 0 auto;
}
  
.form-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
  
.form-group {
  display: flex;
  flex-direction: column;
  position: relative;
}
  
.form-group label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #a0a0a0;
}
  
input, textarea {
  width: 100%;
  background-color: rgba(26, 26, 34, 0.8);
  border: 1px solid rgba(46, 46, 58, 0.8);
  border-radius: 10px;
  padding: 12px;
  color: #e2e2e2;
  font-size: 15px;
  transition: all 0.3s ease;
  font-family: 'Inter', 'Segoe UI', sans-serif;
  box-sizing: border-box;
}
  
input:focus, textarea:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}
  
textarea {
  min-height: 100px;
  resize: none;
}
  
.character-count {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 12px;
  color: #a0a0a0;
  pointer-events: none;
}
  
.tags-section {
  margin-top: 8px;
}
  
.tags-container {
  background-color: rgba(26, 26, 34, 0.8);
  border: 1px solid rgba(46, 46, 58, 0.8);
  border-radius: 10px;
  padding: 16px;
}
  
.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
  
.tag {
  display: flex;
  align-items: center;
  background-color: rgba(99, 102, 241, 0.15);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 8px;
  padding: 6px 10px;
  font-size: 14px;
  color: #6366f1;
}
  
.remove-tag {
  background: none;
  border: none;
  color: #a0a0a0;
  margin-left: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 2px;
}

.remove-tag:hover {
  color: #e2e2e2;
}
  
.add-tag-input {
  display: flex;
  margin-top: 12px;
  width: 100%;
}
  
.add-tag-input input {
  flex: 1;
  border-radius: 8px 0 0 8px;
  border-right: none;
}
  
.add-tag-button {
  background-color: #6366f1;
  color: white;
  border: none;
  border-radius: 0 8px 8px 0;
  padding: 0 16px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-tag-button:hover:not(:disabled) {
  background-color: #4f46e5;
}
  
.add-tag-button:disabled {
  background-color: #2e2e3a;
  color: #a0a0a0;
  cursor: not-allowed;
}

.tags-hint {
  margin-top: 12px;
  font-size: 12px;
  color: #a0a0a0;
  margin-bottom: 0;
}
  
/* Mobile optimizations */
@media (max-width: 768px) {
  .edit-profile-header {
    padding: 10px;
    height: 50px;
  }
  
  .edit-profile-header h1 {
    font-size: 16px;
  }
  
  .back-button {
    padding: 6px;
  }
  
  .back-button svg {
    width: 18px;
    height: 18px;
  }
  
  .save-button {
    font-size: 13px;
    padding: 6px 12px;
  }
  
  .profile-form {
    padding: 16px 12px;
  }
  
  .form-section {
    gap: 20px;
  }
  
  .form-group label {
    font-size: 13px;
  }
  
  input, textarea {
    padding: 10px;
    font-size: 14px;
  }
  
  textarea {
    min-height: 80px;
  }
  
  .tags-container {
    padding: 12px;
  }
  
  .tag {
    padding: 4px 8px;
    font-size: 13px;
  }
}

/* Extra small screens */
@media (max-width: 320px) {
  .edit-profile-header {
    padding: 8px;
    height: 46px;
  }
  
  .edit-profile-header h1 {
    font-size: 15px;
  }
  
  .back-button svg {
    width: 16px;
    height: 16px;
  }
  
  .save-button {
    font-size: 12px;
    padding: 5px 10px;
  }
  
  .profile-form {
    padding: 12px 8px;
  }
  
  .form-group label {
    font-size: 12px;
  }
  
  input, textarea {
    padding: 8px;
    font-size: 13px;
  }
  
  .add-tag-button {
    padding: 0 10px;
    font-size: 12px;
  }
  
  .tags-hint {
    font-size: 11px;
  }
}

/* Fix for iOS input zoom */
@media screen and (max-width: 768px) {
  input, select, textarea {
    font-size: 16px !important;
  }
}

/* Fix for viewport height issues on mobile */
@supports (-webkit-touch-callout: none) {
  .edit-profile-container {
    min-height: -webkit-fill-available;
  }
}
</style>
<template>
  <div class="nearby-groups-container">
    <!-- Added header with back button -->
    <div class="page-header">
      <button class="back-button" @click="$router.push('/dashboard')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>Groups Near You</h1>
      <div class="header-spacer"></div>
    </div>

    <div class="nearby-groups">
      <div class="header">
        <h2>Groups Near You</h2>
        <button @click="fetchNearbyGroups" class="refresh-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="refresh-icon">
            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
            <path d="M3 3v5h5"></path>
          </svg>
          Refresh
        </button>
      </div>
      
      <div v-if="loading" class="loading-state">
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loader">
          <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
        </svg>
        <p>Finding groups near you...</p>
      </div>
      
      <div v-else-if="groups.length > 0" class="groups-list">
        <div v-for="group in groups" :key="group.id" class="group-item">
          <div class="group-avatar">
            <span>{{ getGroupInitials(group.name) }}</span>
          </div>
          <div class="group-details">
            <h3>{{ group.name }}</h3>
            <p>{{ group.description || 'No description available' }}</p>
            <div class="group-meta" v-if="group.distance">
              <span class="distance">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                {{ formatDistance(group.distance) }}
              </span>
              <span class="members" v-if="group.memberCount">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                {{ group.memberCount }} members
              </span>
            </div>
          </div>
          <button @click="joinGroup(group.id)" class="join-button">Join Group</button>
        </div>
      </div>
      
      <div v-else class="empty-state">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
        <p>No groups found nearby</p>
        <p class="hint">Try expanding your search radius or check back later</p>
      </div>
      
      <div v-if="message" class="message" :class="{ 'error': isError }">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      groups: [],
      loading: false,
      message: '',
      isError: false,
      latitude: null,
      longitude: null,
    };
  },
  methods: {
    getGroupInitials(name) {
      if (!name) return '';
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    },
    formatDistance(distance) {
      return distance >= 1000
        ? (distance / 1000).toFixed(2) + " km"
        : distance.toFixed(0) + " m";
    },
    async fetchNearbyGroups() {
      this.loading = true;
      this.message = '';
      this.isError = false;
      try {
        await this.getUserLocation();
        if (!this.latitude || !this.longitude) {
          this.message = 'Unable to get your location. Please allow location access.';
          this.isError = true;
          return;
        }
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/groups/nearby`,
          {
            params: {
              lat: this.latitude,
              lng: this.longitude,
              radius: 10000, // 10km default radius
              limit: 20,     // Max 20 groups
              offset: 0,     // Start from the first result
            },
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          }
        );
        this.groups = response.data.groups;
      } catch (error) {
        this.message = error.response?.data?.message || 'Error fetching nearby groups';
        this.isError = true;
        console.error('Error fetching nearby groups:', error);
      } finally {
        this.loading = false;
      }
    },
    getUserLocation() {
      return new Promise((resolve, reject) => {
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              this.latitude = position.coords.latitude;
              this.longitude = position.coords.longitude;
              resolve();
            },
            (error) => {
              this.message = 'Location access denied. Cannot fetch nearby groups.';
              this.isError = true;
              console.error('Geolocation error:', error);
              reject(error);
            }
          );
        } else {
          this.message = 'Geolocation is not supported by this browser.';
          this.isError = true;
          reject(new Error('Geolocation not supported'));
        }
      });
    },
    async joinGroup(groupId) {
      try {
        const userId = this.$store.state.auth.user.id;
        await axios.post(
          `${import.meta.env.VITE_API_BASE_URL}/api/group-members/add`,
          { groupId, userId },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          }
        );
        this.message = 'Successfully joined the group!';
        this.isError = false;
        setTimeout(() => {
          this.$router.push({ name: 'GroupChatPage', params: { groupId } });
        }, 1000);
      } catch (error) {
        this.message = error.response?.data?.message || 'Error joining group';
        this.isError = true;
        console.error('Error joining group:', error);
      }
    },
  },
  mounted() {
    this.fetchNearbyGroups();
  },
};
</script>

<style scoped>
.nearby-groups-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
  display: flex;
  flex-direction: column;
}

/* Added header styles */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  background-color: var(--bg-primary);
  z-index: 20;
  width: 100%;
  height: 56px;
  box-sizing: border-box;
}

.page-header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
}

.back-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.back-button:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
}

.header-spacer {
  width: 36px; /* Same width as back button for balanced layout */
}

.nearby-groups {
  max-width: 800px;
  margin: 0 auto;
  padding: 1.5rem;
  min-height: calc(100vh - 56px);
  width: 100%;
  box-sizing: border-box;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.refresh-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
  transform: translateY(-1px);
}

.refresh-button:active {
  transform: translateY(0);
}

.refresh-icon {
  animation: none;
}

.refresh-button:active .refresh-icon {
  animation: spin 1s linear;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loader {
  animation: spin-continuous 1.2s linear infinite;
  color: var(--accent-primary);
  margin-bottom: 1rem;
}

@keyframes spin-continuous {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.groups-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  transition: all var(--transition-fast);
}

.group-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-tertiary);
}

.group-avatar {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  background-color: var(--accent-tertiary);
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  font-weight: 500;
  font-size: 1rem;
  flex-shrink: 0;
}

.group-details {
  flex-grow: 1;
  min-width: 0;
}

.group-details h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.group-details p {
  margin: 0.25rem 0 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.group-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.distance, .members {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.join-button {
  margin-left: auto;
  padding: 0.5rem 1rem;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.join-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.join-button:active {
  transform: translateY(0);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.empty-state svg {
  color: var(--accent-tertiary);
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.empty-state .hint {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.message {
  margin-top: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  background-color: rgba(52, 211, 153, 0.1);
  color: #34d399;
}

.message.error {
  background-color: rgba(248, 113, 113, 0.1);
  color: #f87171;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .page-header {
    padding: 10px 12px;
    height: 50px;
  }
  
  .page-header h1 {
    font-size: 16px;
  }
  
  .back-button {
    padding: 6px;
  }
  
  .back-button svg {
    width: 18px;
    height: 18px;
  }
  
  .nearby-groups {
    padding: 1rem;
  }
  
  .header h2 {
    font-size: 1.125rem;
  }
  
  .refresh-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }
  
  .group-item {
    padding: 0.75rem;
  }
  
  .group-avatar {
    width: 40px;
    height: 40px;
    margin-right: 0.75rem;
  }
  
  .group-details h3 {
    font-size: 0.9375rem;
  }
  
  .group-details p {
    font-size: 0.8125rem;
  }
  
  .join-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
}

/* Small phones */
@media (max-width: 375px) {
  .page-header {
    padding: 8px 10px;
    height: 46px;
  }
  
  .page-header h1 {
    font-size: 15px;
  }
  
  .back-button svg {
    width: 16px;
    height: 16px;
  }
  
  .nearby-groups {
    padding: 0.75rem;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .header h2 {
    font-size: 1rem;
  }
  
  .group-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .join-button {
    padding: 0.3125rem 0.625rem;
    font-size: 0.75rem;
  }
}

/* Fix for iOS viewport height issues */
@supports (-webkit-touch-callout: none) {
  .nearby-groups-container {
    min-height: -webkit-fill-available;
  }
}
</style>
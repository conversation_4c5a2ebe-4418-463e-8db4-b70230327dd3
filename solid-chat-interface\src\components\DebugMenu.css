.debug-menu {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.debug-toggle {
  background: #333;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.debug-toggle:hover {
  background: #555;
  transform: scale(1.1);
}

.debug-panel {
  position: absolute;
  top: 50px;
  left: 0;
  background: rgba(0, 0, 0, 0.95);
  color: #00ff00;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  min-width: 300px;
  max-height: 80vh;
  overflow-y: auto;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #333;
  padding-bottom: 8px;
}

.debug-header h3 {
  margin: 0;
  color: #00ff00;
  font-size: 14px;
}

.copy-btn {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 10px;
  transition: background 0.2s;
}

.copy-btn:hover {
  background: #0056b3;
}

.debug-section {
  margin-bottom: 16px;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  color: #ffff00;
  font-size: 12px;
  border-bottom: 1px solid #444;
  padding-bottom: 4px;
}

.metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  padding: 2px 0;
}

.label {
  color: #ccc;
}

.value {
  color: #00ff00;
  font-weight: bold;
}

.value.warning {
  color: #ffaa00;
}

.value.critical {
  color: #ff0000;
}

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.test-btn {
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s;
}

.test-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.test-btn.danger {
  background: #dc3545;
  grid-column: 1 / -1;
}

.test-btn.danger:hover {
  background: #c82333;
}

.alert {
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 10px;
  margin-bottom: 4px;
  border-left: 3px solid;
}

.alert.good {
  background: rgba(40, 167, 69, 0.1);
  border-color: #28a745;
  color: #28a745;
}

.alert.warning {
  background: rgba(255, 193, 7, 0.1);
  border-color: #ffc107;
  color: #ffc107;
}

.alert.critical {
  background: rgba(220, 53, 69, 0.1);
  border-color: #dc3545;
  color: #dc3545;
}

/* Scrollbar for debug panel */
.debug-panel::-webkit-scrollbar {
  width: 4px;
}

.debug-panel::-webkit-scrollbar-track {
  background: #333;
}

.debug-panel::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 2px;
}

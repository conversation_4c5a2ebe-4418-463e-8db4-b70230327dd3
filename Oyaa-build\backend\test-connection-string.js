// test-connection-string.js
const { Client } = require('pg');
const fs = require('fs');

// Read the SSL certificate from the ca.pem file
const caCert = fs.readFileSync('./ca.pem').toString();

// Use the full connection string directly
const connectionString = 'postgres://avnadmin:<EMAIL>:13427/defaultdb?sslmode=require';

// Create client with connection string
const client = new Client({
  connectionString,
  ssl: {
    rejectUnauthorized: false, // Allow self-signed certificates
  },
});

// Connect to the database
console.log('Attempting to connect to database with connection string...');

client.connect()
  .then(() => {
    console.log('✅ Database connected successfully!');
    // Execute a simple query to verify the connection
    return client.query('SELECT NOW() as current_time');
  })
  .then(result => {
    console.log('Query result:', result.rows[0]);
    client.end();
  })
  .catch(err => {
    console.error('❌ Database connection error:', err);
    process.exit(1);
  });

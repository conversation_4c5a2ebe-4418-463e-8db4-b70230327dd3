<template>
    <div class="emoji-section">
      <!-- Recent Emojis -->
      <div v-if="recentEmojis.length > 0 && !searchQuery" class="emoji-category">
        <div class="category-title">Recent</div>
        <div class="emoji-grid">
          <div 
            v-for="(emoji, index) in recentEmojis" 
            :key="`recent-${index}`"
            class="emoji-item"
            @click="selectEmoji(emoji)"
            @mouseenter="showTooltip(emoji, $event)"
            @mouseleave="hideTooltip"
          >
            {{ emoji.i }}
          </div>
        </div>
      </div>
  
      <!-- Search Results -->
      <div v-if="searchQuery && filteredEmojis.length === 0" class="empty-search">
        No emojis found
      </div>
  
      <!-- Emoji Categories -->
      <template v-if="!searchQuery">
        <div v-for="category in emojiCategories" :key="category.id" class="emoji-category">
          <div class="category-title">{{ category.name }}</div>
          <div class="emoji-grid">
            <div 
              v-for="emoji in category.emojis" 
              :key="`${category.id}-${emoji.i}`"
              class="emoji-item"
              @click="selectEmoji(emoji)"
              @mouseenter="showTooltip(emoji, $event)"
              @mouseleave="hideTooltip"
            >
              {{ emoji.i }}
            </div>
          </div>
        </div>
      </template>
  
      <!-- Search Results -->
      <template v-else>
        <div class="emoji-category">
          <div class="emoji-grid">
            <div 
              v-for="emoji in filteredEmojis" 
              :key="`search-${emoji.i}`"
              class="emoji-item"
              @click="selectEmoji(emoji)"
              @mouseenter="showTooltip(emoji, $event)"
              @mouseleave="hideTooltip"
            >
              {{ emoji.i }}
            </div>
          </div>
        </div>
      </template>
  
      <!-- Category Navigation -->
      <div class="category-nav">
        <button 
          v-for="category in emojiCategories" 
          :key="`nav-${category.id}`"
          class="category-nav-item"
          :class="{ 'active': activeCategory === category.id }"
          @click="scrollToCategory(category.id)"
          :title="category.name"
        >
          <span v-html="category.icon"></span>
        </button>
      </div>
  
      <!-- Emoji Tooltip -->
      <div 
        v-if="tooltip.visible" 
        class="emoji-tooltip"
        :style="{ top: `${tooltip.y}px`, left: `${tooltip.x}px` }"
      >
        {{ tooltip.text }}
      </div>
    </div>
  </template>
  
  <script>
  import { ref, computed, onMounted, watch } from 'vue';
  
  // Sample emoji data structure - in a real app, you'd import a complete emoji dataset
  const emojiData = {
    smileys: [
      { i: '😀', n: ['grinning face'], r: 'grinning' },
      { i: '😃', n: ['smiling face with open mouth'], r: 'smiley' },
      { i: '😄', n: ['smiling face with open mouth and smiling eyes'], r: 'smile' },
      { i: '😁', n: ['grinning face with smiling eyes'], r: 'grin' },
      { i: '😆', n: ['smiling face with open mouth and tightly-closed eyes'], r: 'laughing' },
      { i: '😅', n: ['smiling face with open mouth and cold sweat'], r: 'sweat_smile' },
      { i: '🤣', n: ['rolling on the floor laughing'], r: 'rofl' },
      { i: '😂', n: ['face with tears of joy'], r: 'joy' },
      { i: '🙂', n: ['slightly smiling face'], r: 'slightly_smiling_face' },
      { i: '🙃', n: ['upside-down face'], r: 'upside_down_face' },
      { i: '😉', n: ['winking face'], r: 'wink' },
      { i: '😊', n: ['smiling face with smiling eyes'], r: 'blush' },
      { i: '😇', n: ['smiling face with halo'], r: 'innocent' },
      { i: '😍', n: ['smiling face with heart-shaped eyes'], r: 'heart_eyes' },
      { i: '🥰', n: ['smiling face with hearts'], r: 'smiling_face_with_three_hearts' },
      { i: '😘', n: ['face throwing a kiss'], r: 'kissing_heart' },
      { i: '😗', n: ['kissing face'], r: 'kissing' },
      { i: '☺️', n: ['white smiling face'], r: 'relaxed' },
      { i: '😚', n: ['kissing face with closed eyes'], r: 'kissing_closed_eyes' },
      { i: '😙', n: ['kissing face with smiling eyes'], r: 'kissing_smiling_eyes' },
      { i: '🥲', n: ['smiling face with tear'], r: 'smiling_face_with_tear' },
    ],
    people: [
      { i: '👋', n: ['waving hand sign'], r: 'wave' },
      { i: '🤚', n: ['raised back of hand'], r: 'raised_back_of_hand' },
      { i: '🖐️', n: ['raised hand with fingers splayed'], r: 'raised_hand_with_fingers_splayed' },
      { i: '✋', n: ['raised hand'], r: 'raised_hand' },
      { i: '🖖', n: ['raised hand with part between middle and ring fingers'], r: 'vulcan_salute' },
      { i: '👌', n: ['ok hand sign'], r: 'ok_hand' },
      { i: '🤌', n: ['pinched fingers'], r: 'pinched_fingers' },
      { i: '🤏', n: ['pinching hand'], r: 'pinching_hand' },
      { i: '✌️', n: ['victory hand'], r: 'v' },
      { i: '🤞', n: ['hand with index and middle fingers crossed'], r: 'crossed_fingers' },
    ],
    nature: [
      { i: '🐶', n: ['dog face'], r: 'dog' },
      { i: '🐱', n: ['cat face'], r: 'cat' },
      { i: '🐭', n: ['mouse face'], r: 'mouse' },
      { i: '🐹', n: ['hamster face'], r: 'hamster' },
      { i: '🐰', n: ['rabbit face'], r: 'rabbit' },
      { i: '🦊', n: ['fox face'], r: 'fox_face' },
      { i: '🐻', n: ['bear face'], r: 'bear' },
      { i: '🐼', n: ['panda face'], r: 'panda_face' },
      { i: '🐨', n: ['koala'], r: 'koala' },
      { i: '🐯', n: ['tiger face'], r: 'tiger' },
    ],
    food: [
      { i: '🍏', n: ['green apple'], r: 'green_apple' },
      { i: '🍎', n: ['red apple'], r: 'apple' },
      { i: '🍐', n: ['pear'], r: 'pear' },
      { i: '🍊', n: ['tangerine'], r: 'tangerine' },
      { i: '🍋', n: ['lemon'], r: 'lemon' },
      { i: '🍌', n: ['banana'], r: 'banana' },
      { i: '🍉', n: ['watermelon'], r: 'watermelon' },
      { i: '🍇', n: ['grapes'], r: 'grapes' },
      { i: '🍓', n: ['strawberry'], r: 'strawberry' },
      { i: '🫐', n: ['blueberries'], r: 'blueberries' },
    ],
    activity: [
      { i: '⚽', n: ['soccer ball'], r: 'soccer' },
      { i: '🏀', n: ['basketball and hoop'], r: 'basketball' },
      { i: '🏈', n: ['american football'], r: 'football' },
      { i: '⚾', n: ['baseball'], r: 'baseball' },
      { i: '🥎', n: ['softball'], r: 'softball' },
      { i: '🎾', n: ['tennis racquet and ball'], r: 'tennis' },
      { i: '🏐', n: ['volleyball'], r: 'volleyball' },
      { i: '🏉', n: ['rugby football'], r: 'rugby_football' },
      { i: '🥏', n: ['flying disc'], r: 'flying_disc' },
      { i: '🎱', n: ['billiards'], r: '8ball' },
    ],
    travel: [
      { i: '🚗', n: ['automobile'], r: 'car' },
      { i: '🚕', n: ['taxi'], r: 'taxi' },
      { i: '🚙', n: ['recreational vehicle'], r: 'blue_car' },
      { i: '🚌', n: ['bus'], r: 'bus' },
      { i: '🚎', n: ['trolleybus'], r: 'trolleybus' },
      { i: '🏎️', n: ['racing car'], r: 'racing_car' },
      { i: '🚓', n: ['police car'], r: 'police_car' },
      { i: '🚑', n: ['ambulance'], r: 'ambulance' },
      { i: '🚒', n: ['fire engine'], r: 'fire_engine' },
      { i: '🚐', n: ['minibus'], r: 'minibus' },
    ],
    objects: [
      { i: '⌚', n: ['watch'], r: 'watch' },
      { i: '📱', n: ['mobile phone'], r: 'iphone' },
      { i: '📲', n: ['mobile phone with rightwards arrow at left'], r: 'calling' },
      { i: '💻', n: ['personal computer'], r: 'computer' },
      { i: '⌨️', n: ['keyboard'], r: 'keyboard' },
      { i: '🖥️', n: ['desktop computer'], r: 'desktop_computer' },
      { i: '🖨️', n: ['printer'], r: 'printer' },
      { i: '🖱️', n: ['three button mouse'], r: 'computer_mouse' },
      { i: '🖲️', n: ['trackball'], r: 'trackball' },
      { i: '🕹️', n: ['joystick'], r: 'joystick' },
    ],
    symbols: [
      { i: '❤️', n: ['heavy black heart'], r: 'heart' },
      { i: '🧡', n: ['orange heart'], r: 'orange_heart' },
      { i: '💛', n: ['yellow heart'], r: 'yellow_heart' },
      { i: '💚', n: ['green heart'], r: 'green_heart' },
      { i: '💙', n: ['blue heart'], r: 'blue_heart' },
      { i: '💜', n: ['purple heart'], r: 'purple_heart' },
      { i: '🖤', n: ['black heart'], r: 'black_heart' },
      { i: '🤍', n: ['white heart'], r: 'white_heart' },
      { i: '🤎', n: ['brown heart'], r: 'brown_heart' },
      { i: '💔', n: ['broken heart'], r: 'broken_heart' },
    ],
    flags: [
      { i: '🏁', n: ['chequered flag'], r: 'checkered_flag' },
      { i: '🚩', n: ['triangular flag on post'], r: 'triangular_flag_on_post' },
      { i: '🎌', n: ['crossed flags'], r: 'crossed_flags' },
      { i: '🏴', n: ['waving black flag'], r: 'black_flag' },
      { i: '🏳️', n: ['waving white flag'], r: 'white_flag' },
      { i: '🏳️‍🌈', n: ['rainbow flag'], r: 'rainbow_flag' },
      { i: '🏳️‍⚧️', n: ['transgender flag'], r: 'transgender_flag' },
      { i: '🏴‍☠️', n: ['pirate flag'], r: 'pirate_flag' },
      { i: '🇦🇨', n: ['ascension island flag'], r: 'ascension_island' },
      { i: '🇦🇩', n: ['andorra flag'], r: 'andorra' },
    ]
  };
  
  // LocalStorage key for recent emojis
  const RECENT_EMOJIS_KEY = 'whatsapp-recent-emojis';
  const MAX_RECENT_EMOJIS = 15;
  
  export default {
    name: 'EmojiSection',
    props: {
      searchQuery: {
        type: String,
        default: ''
      }
    },
    emits: ['select'],
    setup(props, { emit }) {
      const recentEmojis = ref([]);
      const activeCategory = ref('smileys');
      const tooltip = ref({
        visible: false,
        text: '',
        x: 0,
        y: 0
      });
  
      // Define emoji categories with icons
      const emojiCategories = [
        { id: 'smileys', name: 'Smileys & People', icon: '😀', emojis: emojiData.smileys },
        { id: 'people', name: 'People & Body', icon: '👋', emojis: emojiData.people },
        { id: 'nature', name: 'Animals & Nature', icon: '🐶', emojis: emojiData.nature },
        { id: 'food', name: 'Food & Drink', icon: '🍏', emojis: emojiData.food },
        { id: 'activity', name: 'Activities', icon: '⚽', emojis: emojiData.activity },
        { id: 'travel', name: 'Travel & Places', icon: '🚗', emojis: emojiData.travel },
        { id: 'objects', name: 'Objects', icon: '💻', emojis: emojiData.objects },
        { id: 'symbols', name: 'Symbols', icon: '❤️', emojis: emojiData.symbols },
        { id: 'flags', name: 'Flags', icon: '🏁', emojis: emojiData.flags },
      ];
  
      // Filtered emojis based on search query
      const filteredEmojis = computed(() => {
        if (!props.searchQuery) return [];
        
        const query = props.searchQuery.toLowerCase();
        const results = [];
        
        // Search through all categories
        emojiCategories.forEach(category => {
          category.emojis.forEach(emoji => {
            // Search in names and shortcodes
            if (emoji.n.some(name => name.toLowerCase().includes(query)) || 
                emoji.r.toLowerCase().includes(query)) {
              results.push(emoji);
            }
          });
        });
        
        return results;
      });
  
      // Load recent emojis from localStorage
      const loadRecentEmojis = () => {
        try {
          const storedEmojis = localStorage.getItem(RECENT_EMOJIS_KEY);
          if (storedEmojis) {
            recentEmojis.value = JSON.parse(storedEmojis).slice(0, MAX_RECENT_EMOJIS);
          }
        } catch (error) {
          console.error('Error loading recent emojis:', error);
        }
      };
  
      // Save emoji to recent emojis
      const saveToRecentEmojis = (emoji) => {
        try {
          // Add to the beginning of the array and remove duplicates
          const newRecents = [emoji, ...recentEmojis.value.filter(e => e.i !== emoji.i)]
            .slice(0, MAX_RECENT_EMOJIS);
          
          recentEmojis.value = newRecents;
          localStorage.setItem(RECENT_EMOJIS_KEY, JSON.stringify(newRecents));
        } catch (error) {
          console.error('Error saving recent emoji:', error);
        }
      };
  
      // Select emoji and emit event
      const selectEmoji = (emoji) => {
        saveToRecentEmojis(emoji);
        emit('select', emoji);
      };
  
      // Show tooltip with emoji name
      const showTooltip = (emoji, event) => {
        const rect = event.target.getBoundingClientRect();
        tooltip.value = {
          visible: true,
          text: emoji.n[0],
          x: rect.left + (rect.width / 2),
          y: rect.top - 30
        };
      };
  
      // Hide tooltip
      const hideTooltip = () => {
        tooltip.value.visible = false;
      };
  
      // Scroll to category
      const scrollToCategory = (categoryId) => {
        activeCategory.value = categoryId;
        const element = document.getElementById(`category-${categoryId}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      };
  
      // Intersection observer to detect active category
      const setupCategoryObserver = () => {
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const categoryId = entry.target.id.replace('category-', '');
                activeCategory.value = categoryId;
              }
            });
          },
          { threshold: 0.5 }
        );
  
        emojiCategories.forEach(category => {
          const element = document.getElementById(`category-${category.id}`);
          if (element) {
            observer.observe(element);
          }
        });
      };
  
      onMounted(() => {
        loadRecentEmojis();
        setupCategoryObserver();
      });
  
      return {
        recentEmojis,
        emojiCategories,
        activeCategory,
        filteredEmojis,
        tooltip,
        selectEmoji,
        showTooltip,
        hideTooltip,
        scrollToCategory
      };
    }
  };
  </script>
  
  <style scoped>
  .emoji-section {
    height: 100%;
    position: relative;
  }
  
  .emoji-category {
    padding: 8px 0;
  }
  
  .category-title {
    padding: 6px 16px;
    font-size: 14px;
    color: #8a9aa4;
    font-weight: 500;
    position: sticky;
    top: 0;
    background: #222e35;
    z-index: 2;
  }
  
  .emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    padding: 4px 12px;
  }
  
  .emoji-item {
    font-size: 24px;
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.2s, transform 0.15s;
    user-select: none;
  }
  
  .emoji-item:hover {
    background-color: #374248;
    transform: scale(1.15);
    z-index: 1;
  }
  
  .emoji-item:active {
    transform: scale(0.95);
  }
  
  .category-nav {
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
    background: rgba(34, 46, 53, 0.8);
    padding: 8px 4px;
    border-radius: 20px;
    z-index: 3;
  }
  
  .category-nav-item {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    background: transparent;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.2s, background-color 0.2s;
  }
  
  .category-nav-item:hover {
    opacity: 1;
    background-color: #374248;
  }
  
  .category-nav-item.active {
    opacity: 1;
    background-color: #00a884;
  }
  
  .emoji-tooltip {
    position: fixed;
    background: #1f2c33;
    color: #e0e0e0;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    transform: translateX(-50%);
    z-index: 10;
    pointer-events: none;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .emoji-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #1f2c33 transparent transparent transparent;
  }
  
  .empty-search {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    color: #8a9aa4;
    font-size: 14px;
  }
  
  /* Mobile optimizations */
  @media (max-width: 768px) {
    .emoji-grid {
      grid-template-columns: repeat(7, 1fr);
    }
    
    .emoji-item {
      font-size: 22px;
      height: 36px;
      width: 36px;
    }
    
    .category-nav {
      right: 4px;
      padding: 6px 3px;
    }
    
    .category-nav-item {
      width: 20px;
      height: 20px;
      font-size: 14px;
    }
  }
  
  /* Small mobile optimizations */
  @media (max-width: 480px) {
    .emoji-grid {
      grid-template-columns: repeat(6, 1fr);
      padding: 4px 8px;
    }
    
    .emoji-item {
      font-size: 20px;
      height: 32px;
      width: 32px;
    }
    
    .category-title {
      padding: 4px 12px;
      font-size: 12px;
    }
  }
  </style>
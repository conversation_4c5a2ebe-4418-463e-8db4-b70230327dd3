<template>
  <div class="avatar-selector">
    <h3 class="avatar-selector-title">Select Group Avatar</h3>

    <!-- Category Navigation -->
    <div class="category-nav">
      <button
        class="nav-button"
        @click="navigateCategory('prev')"
        :disabled="activeCategory === avatarCategories[0].name"
        aria-label="Previous category"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="m15 18-6-6 6-6" />
        </svg>
      </button>
      <div class="category-title">{{ activeCategory }}</div>
      <button
        class="nav-button"
        @click="navigateCategory('next')"
        :disabled="activeCategory === avatarCategories[avatarCategories.length - 1].name"
        aria-label="Next category"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="m9 18 6-6-6-6" />
        </svg>
      </button>
    </div>

    <!-- Avatar Grid -->
    <div class="scroll-area">
      <div class="avatar-grid">
        <button
          v-for="avatar in filteredAvatars"
          :key="avatar.id"
          @click="selectAvatar(avatar)"
          :class="['avatar-button', { 'selected': selectedAvatar === avatar.path }]"
          :aria-label="'Select ' + avatar.id + ' avatar'"
        >
          <img :src="avatar.path" alt="avatar" class="avatar-img" />
          <div v-if="selectedAvatar === avatar.path" class="selected-indicator">
            <svg
              class="check-icon"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <polyline points="20 6 9 17 4 12" />
            </svg>
          </div>
        </button>
      </div>
    </div>

    <!-- Category Pills -->
    <div class="category-pills">
      <button
        v-for="category in avatarCategories"
        :key="category.name"
        @click="setActiveCategory(category.name)"
        :class="['category-pill', { 'active': activeCategory === category.name }]"
      >
        {{ category.name }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PickGroupAvatar',
  props: {
    selectedAvatar: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      activeCategory: 'Sports & Fitness',
      avatarCategories: [
        {
          name: 'Sports & Fitness',
          items: ['Basketball', 'Bicycle', 'Soccer-ball', 'Tennis-racket', 'Running-shoe', 'Yoga-pose'],
        },
        {
          name: 'Hobbies & Interests',
          items: ['Book', 'Music note', 'Paintbrush', 'Camera', 'Game-controller', 'Compass'],
        },
        {
          name: 'Professional & Educational',
          items: ['Briefcase', 'Graduation-cap', 'Lightbulb', 'Gear', 'Computer'],
        },
        {
          name: 'Community & Social',
          items: ['Handshake', 'Heart', 'Speech-bubble', 'Globe', 'Coffee-cup', 'world-chat'],
        },
        {
          name: 'Nature & Environment',
          items: ['Leaf', 'Mountain', 'Wave', 'Tree'],
        },
        {
          name: 'Abstract & Generic',
          items: ['Geometric-shapes', 'Interconnected-nodes', 'Group-of-silhouettes', 'Star', 'Puzzle-piece'],
        },
        {
          name: 'Objects & Symbols',
          items: ['Calendar', 'Film-reel', 'Microphone', 'Rocket', 'Toolbox'],
        },
      ],
    };
  },
  computed: {
    allAvatars() {
      return this.avatarCategories.flatMap((category) =>
        category.items.map((item) => ({
          id: item,
          path: `/Avatar/groups-Avatar/${item}.svg`,
          category: category.name,
        }))
      );
    },
    filteredAvatars() {
      return this.allAvatars.filter((avatar) => avatar.category === this.activeCategory);
    },
  },
  mounted() {
    if (this.selectedAvatar) {
      const selectedAvatarObj = this.allAvatars.find((a) => a.path === this.selectedAvatar);
      if (selectedAvatarObj) {
        this.activeCategory = selectedAvatarObj.category;
      }
    }
  },
  methods: {
    selectAvatar(avatar) {
      this.$emit('avatar-selected', avatar.path);
    },
    navigateCategory(direction) {
      const currentIndex = this.avatarCategories.findIndex((c) => c.name === this.activeCategory);
      if (direction === 'next' && currentIndex < this.avatarCategories.length - 1) {
        this.activeCategory = this.avatarCategories[currentIndex + 1].name;
      } else if (direction === 'prev' && currentIndex > 0) {
        this.activeCategory = this.avatarCategories[currentIndex - 1].name;
      }
    },
    setActiveCategory(categoryName) {
      this.activeCategory = categoryName;
    },
  },
};
</script>

<style scoped>
.avatar-selector {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  width: 100%;
  max-width: 48rem;
  background-color: var(--bg-tertiary);
  border-radius: 16px;
  padding: 24px;
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

.avatar-selector-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  text-align: center;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.category-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.nav-button {
  color: var(--text-secondary);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  padding: 8px;
  border-radius: 8px;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button:hover:not(:disabled) {
  color: var(--accent-primary);
  background: var(--accent-tertiary);
  transform: translateX(-2px);
}

.nav-button:last-child:hover:not(:disabled) {
  transform: translateX(2px);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.category-title {
  flex: 1;
  text-align: center;
  color: var(--text-primary);
  font-weight: 500;
  padding: 0 16px;
}

.scroll-area {
  height: 320px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: auto;
  background: var(--bg-secondary);
  margin-bottom: 20px;
  scrollbar-width: thin;
  scrollbar-color: var(--accent-tertiary) transparent;
}

.scroll-area::-webkit-scrollbar {
  width: 6px;
}

.scroll-area::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-area::-webkit-scrollbar-thumb {
  background-color: var(--accent-tertiary);
  border-radius: 6px;
}

.avatar-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 16px;
  padding: 16px;
}

.avatar-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 12px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  margin: 0 auto;
}

.avatar-button:hover {
  background-color: var(--accent-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.avatar-button.selected {
  border: 2px solid var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}

.avatar-img {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.selected-indicator {
  position: absolute;
  bottom: -6px;
  right: -6px;
  background-color: var(--accent-primary);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.check-icon {
  width: 12px;
  height: 12px;
}

.category-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-top: 8px;
}

.category-pill {
  padding: 6px 12px;
  font-size: 13px;
  border-radius: 20px;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.category-pill:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  transform: translateY(-2px);
}

.category-pill.active {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}
</style>
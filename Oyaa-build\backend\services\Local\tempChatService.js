// backend/services/tempChatService.js
const TempChat = require('../../models/Local/TempChat');

class TempChatService {
  constructor() {
    // Create an instance of TempChat and connect to the DB
    this.tempChat = new TempChat();
    this.tempChat.connect().catch(err => console.error("DB connection error:", err));
  }

  async sendMessage(senderId, receiverId, message) {
    try {
      const sent_at = new Date().toISOString();
      const result = await this.tempChat.saveMessage(senderId, receiverId, message, sent_at);
      console.log("Message saved to DB:", result);
      return result;
    } catch (err) {
      console.error("Error in sendMessage:", err);
      throw new Error(`Failed to send message: ${err.message}`);
    }
  }
  
  async getMessages(userId, targetUserId) {
    try {
      const messages = await this.tempChat.getMessages(userId, targetUserId);
      return messages;
    } catch (err) {
      throw new Error(`Failed to retrieve messages: ${err.message}`);
    }
  }
}

module.exports = new TempChatService();

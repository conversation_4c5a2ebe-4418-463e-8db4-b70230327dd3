<template>
  <div class="fab-container">
    <div v-if="isOpen" class="fab-backdrop" @click="closeMenu" aria-hidden="true"></div>
    <button
      class="fab-button"
      :class="{ 'is-open': isOpen }"
      @click="toggleMenu($event)"
      :aria-expanded="isOpen"
      aria-label="Toggle actions menu"
    >
      <svg v-if="isOpen" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
      <svg v-else xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="12" y1="5" x2="12" y2="19"></line>
        <line x1="5" y1="12" x2="19" y2="12"></line>
      </svg>
    </button>
    <ActionMenu v-if="isOpen" :active-tab="activeTab" @action="handleAction" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import ActionMenu from './ActionMenu.vue';

const props = defineProps({
  activeTab: { type: String, default: 'friends' },
});

const emit = defineEmits(['action']);

const isOpen = ref(false);

const toggleMenu = (event) => {
  event.stopPropagation();
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    nextTick(() => {
      const firstAction = document.querySelector('.action-bubble');
      if (firstAction) firstAction.focus();
    });
  }
};

const closeMenu = () => {
  if (isOpen.value) {
    isOpen.value = false;
    nextTick(() => {
      const fabButton = document.querySelector('.fab-button');
      if (fabButton) fabButton.focus();
    });
  }
};

const handleAction = (action) => {
  closeMenu();
  emit('action', action);
};

const handleClickOutside = (event) => {
  const fabContainer = document.querySelector('.fab-container');
  if (isOpen.value && fabContainer && !fabContainer.contains(event.target)) {
    closeMenu();
  }
};

const handleKeyDown = (e) => {
  if (e.key === 'Escape' && isOpen.value) {
    closeMenu();
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  window.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
/* Unchanged from original */
.fab-container {
  position: fixed;
  bottom: 76px;
  right: 16px;
  z-index: 1000;
}

.fab-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
  transition: opacity 0.3s ease;
  z-index: 1;
}

.fab-button {
  position: relative;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: #6366f1;
  border: none;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.4);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 2;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.fab-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(99, 102, 241, 0.5);
}

.fab-button:active {
  transform: scale(0.95);
}

.fab-button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px #0f0f13, 0 0 0 4px #6366f1;
}

.fab-button.is-open {
  background: #4f46e5;
  transform: rotate(45deg);
}

.fab-button.is-open:hover {
  transform: rotate(45deg) scale(1.05);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .fab-container {
    bottom: calc(65px + env(safe-area-inset-bottom, 0px)); /* Adjust for bottom nav and safe area */
    right: 12px;
  }
  
  .fab-button {
    width: 50px;
    height: 50px;
    border-radius: 14px;
  }
  
  .fab-button svg {
    width: 22px;
    height: 22px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .fab-container {
    right: 10px;
  }
  
  .fab-button {
    width: 44px;
    height: 44px;
    border-radius: 12px;
  }
  
  .fab-button svg {
    width: 20px;
    height: 20px;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .fab-button,
  .fab-backdrop {
    transition: none;
  }
  
  .fab-button.is-open {
    transform: none;
  }
}
</style>
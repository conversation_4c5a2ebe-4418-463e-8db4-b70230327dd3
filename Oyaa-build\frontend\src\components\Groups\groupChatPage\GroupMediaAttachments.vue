<!-- GroupMediaAttachments.vue -->
<template>
  <div class="group-media-attachments">
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      @change="handleFileUpload"
      accept="image/*,video/*,audio/*" 
    />
    <div v-if="file" class="file-preview">
      <div class="preview-content">
        <img
          v-if="isImage"
          :src="previewUrl"
          class="preview-image"
          alt="Image preview"
        />
        <video
          v-else-if="isVideo"
          :src="previewUrl"
          class="preview-video"
          controls
          poster
        ></video>
        <div v-else-if="isAudio" class="media-icon">
          <i class="fas fa-file-audio" :title="file.name"></i>
        </div>
        <div v-else class="media-icon">
          <i class="fas fa-file" :title="file.name"></i>
        </div>
        <span class="filename">{{ file.name }}</span>
      </div>
      <button class="remove-button" @click="removeFile" title="Remove attachment">
        <span class="close-icon">×</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import Compressor from 'compressorjs';

const emit = defineEmits(['file-changed']);

const fileInput = ref(null);
const file = ref(null);
const previewUrl = ref(null);

const isImage = computed(() => file.value && file.value.type.startsWith('image/'));
const isVideo = computed(() => file.value && file.value.type.startsWith('video/'));
const isAudio = computed(() => file.value && file.value.type.startsWith('audio/'));

const triggerFileInput = () => {
  if (fileInput.value) fileInput.value.click();
};

const handleFileUpload = (event) => {
  const selectedFile = event.target.files[0];
  if (selectedFile && selectedFile.type.startsWith('image/')) {
    compressImage(selectedFile);
  } else {
    setFile(selectedFile);
  }
};

const compressImage = (imageFile) => {
  new Compressor(imageFile, {
    quality: 0.8,
    maxWidth: 1024,
    maxHeight: 1024,
    mimeType: 'image/webp',
    success(result) {
      const compressedFile = new File([result], imageFile.name.replace(/\.\w+$/, '.webp'), {
        type: 'image/webp',
        lastModified: Date.now(),
      });
      setFile(compressedFile);
    },
    error(err) {
      console.error('Image compression failed:', err.message);
      setFile(imageFile);
    },
  });
};

const removeFile = () => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value);
    previewUrl.value = null;
  }
  file.value = null;
  emit('file-changed', null);
};

const setFile = (fileOrBlob) => {
  if (fileOrBlob) {
    file.value = fileOrBlob;
    previewUrl.value = URL.createObjectURL(fileOrBlob);
    emit('file-changed', fileOrBlob);
  }
};

defineExpose({ triggerFileInput, removeFile, setFile });
</script>

<style scoped>
.group-media-attachments {
  background-color: #2b2d31;
  padding: 0 16px;
}

.file-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #1e1f22;
  border-radius: 8px;
  padding: 8px;
  max-width: 320px;
}

.preview-content {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
}

.preview-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
  background-color: #2b2d31;
}

.preview-video {
  width: 80px;
  height: 60px;
  border-radius: 4px;
  background-color: #2b2d31;
}

.media-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b2d31;
  border-radius: 4px;
  color: #dbdee1;
  font-size: 24px;
}

.filename {
  color: #dbdee1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.remove-button {
  background: none;
  border: none;
  color: #b5bac1;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  transition: background-color 0.2s;
}

.remove-button:hover {
  background-color: #ff4d4f;
  color: #ffffff;
}

.close-icon {
  font-size: 18px;
  line-height: 1;
}
</style>
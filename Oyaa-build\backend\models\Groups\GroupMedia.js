// backend/models/GroupMedia.js
const db = require('../../utils/db');

class GroupMedia {
  static async createGroupMedia({ userId, groupChatId, mediaUrl, publicId, mediaType, thumbnailUrl, duration }) {
    const query = `
      INSERT INTO group_media (user_id, group_chat_id, media_url, public_id, media_type, thumbnail_url, duration, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
      RETURNING *;
    `;
    const values = [userId, groupChatId, mediaUrl, publicId, mediaType, thumbnailUrl, duration];
    const result = await db.query(query, values);
    return result.rows[0];
  }

  static async getMediaByGroupChatId(groupChatId) {
    const query = `
      SELECT gm.id, gm.user_id, gm.group_chat_id, gm.media_url, gm.public_id, gm.media_type, 
             gm.thumbnail_url, gm.duration, gm.created_at, u.username
      FROM group_media gm
      LEFT JOIN users u ON gm.user_id = u.id
      WHERE gm.group_chat_id = $1
      ORDER BY gm.created_at ASC;
    `;
    const result = await db.query(query, [groupChatId]);
    return result.rows;
  }

  static async getMediaByUserId(userId) {
    const query = `
      SELECT id, user_id, group_chat_id, media_url, public_id, media_type, thumbnail_url, duration, created_at 
      FROM group_media
      WHERE user_id = $1
      ORDER BY created_at DESC;
    `;
    const result = await db.query(query, [userId]);
    return result.rows;
  }

  static async deleteGroupMedia(mediaId) {
    const query = `
      DELETE FROM group_media
      WHERE id = $1
      RETURNING *;
    `;
    const result = await db.query(query, [mediaId]);
    return result.rows[0];
  }
  
  // New method to get all media for a specific group
  static async getAllMediaByGroupId(groupId) {
    const query = `
      SELECT gm.id, gm.user_id, gm.group_chat_id, gm.media_url, gm.public_id, 
             gm.media_type, gm.thumbnail_url, gm.duration, gm.created_at, 
             u.username, gc.message
      FROM group_media gm
      LEFT JOIN users u ON gm.user_id = u.id
      LEFT JOIN group_chats gc ON gm.group_chat_id = gc.id
      WHERE gc.group_id = $1
      ORDER BY gm.created_at DESC;
    `;
    const result = await db.query(query, [groupId]);
    return result.rows;
  }
  
  // New method to get media by message IDs
  static async getMediaByMessageIds(messageIds) {
    const query = `
      SELECT gm.id, gm.user_id, gm.group_chat_id, gm.media_url, gm.public_id, 
             gm.media_type, gm.thumbnail_url, gm.duration, gm.created_at,
             u.username, gc.message
      FROM group_media gm
      LEFT JOIN users u ON gm.user_id = u.id
      LEFT JOIN group_chats gc ON gm.group_chat_id = gc.id
      WHERE gm.group_chat_id = ANY($1)
      ORDER BY gm.created_at DESC;
    `;
    const result = await db.query(query, [messageIds]);
    return result.rows;
  }
}

module.exports = GroupMedia;
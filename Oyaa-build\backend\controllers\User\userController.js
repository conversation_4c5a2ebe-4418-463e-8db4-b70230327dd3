const db = require('../../utils/db');

const userService = require('../../services/User/userService');

class UserController {
  async updateLocation(req, res) {
    try {
      const { userId, latitude, longitude } = req.body;
      const updatedUser = await userService.updateLocation(userId, latitude, longitude);
      res.status(200).json({ message: 'Location updated', user: updatedUser });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async getNearbyUsers(req, res) {
    try {
      const { userId } = req.params;
      const { limit = 10, offset = 0 } = req.query;
      const nearbyUsers = await userService.getNearbyUsers(userId, limit, offset);
      res.status(200).json({ nearbyUsers });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async searchUserByUsername(req, res) {
    try {
      const { username } = req.params;
      const query = 'SELECT id, username, avatar, description FROM users WHERE LOWER(username) = LOWER($1);';
      const result = await db.query(query, [username]);
      const user = result.rows[0];
      if (!user) return res.status(404).json({ message: "User not found" });
      res.status(200).json({ user });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }


  async getUserById(req, res) {
    try {
      const userId = parseInt(req.params.id, 10);
      if (isNaN(userId)) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }
      const user = await userService.getUserById(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }
      res.status(200).json(user);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  /**
   * Update a user's avatar.
   */
  async updateAvatar(req, res) {
    try {
      const { userId, avatarUrl } = req.body;
      const updatedUser = await userService.updateAvatar(userId, avatarUrl);
      res.status(200).json({ message: 'Avatar updated', user: updatedUser });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async getCurrentUser(req, res) {
    try {
      const userId = req.user.userId;
      console.log('getCurrentUser: Fetching user with ID:', userId);
      const parsedUserId = parseInt(userId, 10);
      if (isNaN(parsedUserId)) {
        console.error('getCurrentUser: Invalid userId:', userId);
        return res.status(400).json({ error: 'Invalid user ID' });
      }
      const user = await userService.getUserById(parsedUserId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }
      res.status(200).json(user);
    } catch (error) {
      console.error('getCurrentUser: Error:', error.message);
      res.status(500).json({ error: 'Failed to fetch user: ' + error.message });
    }
  }

  // Update user profile including handle with improved error handling
  async updateProfile(req, res) {
    try {
      // Check if user object exists in the request
      if (!req.user) {
        console.error('No user object in request');
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = req.user.userId;
      const firebaseUid = req.user.firebaseUid;

      console.log('Updating profile for user:', { userId, firebaseUid });
      console.log('Update data received:', req.body);

      // Validate the userId
      if (!userId || isNaN(parseInt(userId))) {
        console.error('Invalid userId:', userId);

        // If we have a firebaseUid but no userId, try to look up the user
        if (firebaseUid) {
          console.log('Attempting to look up user by Firebase UID:', firebaseUid);
          try {
            const userQuery = 'SELECT id FROM users WHERE firebase_uid = $1';
            const userResult = await db.query(userQuery, [firebaseUid]);

            if (userResult.rows.length > 0) {
              const lookupUserId = userResult.rows[0].id;
              console.log('Found user ID from Firebase UID:', lookupUserId);

              // Continue with the update using the looked-up userId
              const updatedUser = await userService.updateProfile(lookupUserId, req.body);
              console.log('Profile updated successfully:', updatedUser);

              return res.status(200).json({
                message: 'Profile updated successfully',
                user: updatedUser
              });
            } else {
              console.error('User not found with Firebase UID:', firebaseUid);
              return res.status(404).json({ message: 'User not found' });
            }
          } catch (lookupError) {
            console.error('Error looking up user by Firebase UID:', lookupError);
            return res.status(500).json({
              message: 'Error looking up user information',
              error: lookupError.message
            });
          }
        }

        return res.status(400).json({ message: 'Invalid user ID' });
      }

      const updatedUser = await userService.updateProfile(userId, req.body);
      console.log('Profile updated successfully:', updatedUser);

      // Update the user object in the request for subsequent middleware
      if (req.user) {
        req.user.username = updatedUser.username;
        req.user.handle = updatedUser.handle || updatedUser.username;
        if (updatedUser.avatar) req.user.avatar = updatedUser.avatar;
      }

      res.status(200).json({
        message: 'Profile updated successfully',
        user: updatedUser
      });
    } catch (err) {
      if (err.code === '23505') { // Unique constraint violation
        console.error('Unique constraint violation:', err.detail || err.message);
        return res.status(409).json({
          message: 'Username or email already taken',
          detail: err.detail || err.message
        });
      }
      console.error('Error updating profile:', err);
      res.status(500).json({
        message: 'Failed to update profile',
        error: err.message,
        detail: err.detail || null
      });
    }
  }
  async blockUser(req, res) {
    try {
      const blockerId = req.user.userId; // Use req.user.userId instead of req.user.id
      const { blockedId } = req.body;
      if (blockerId === blockedId) {
        return res.status(400).json({ message: 'Cannot block yourself' });
      }
      await db.query(
        'INSERT INTO blocked_users (blocker_id, blocked_id) VALUES ($1, $2) ON CONFLICT DO NOTHING',
        [blockerId, blockedId]
      );
      res.status(200).json({ message: 'User blocked' });
    } catch (err) {
      console.error('Error blocking user:', err);
      res.status(500).json({ message: 'Failed to block user' });
    }
  }


  async unblockUser(req, res) {
    try {
      const blockerId = req.user.userId; // Changed from req.user.id
      const { blockedId } = req.params;
      await db.query(
        'DELETE FROM blocked_users WHERE blocker_id = $1 AND blocked_id = $2',
        [blockerId, blockedId]
      );
      res.status(200).json({ message: 'User unblocked' });
    } catch (err) {
      console.error('Error unblocking user:', err);
      res.status(500).json({ message: 'Failed to unblock user' });
    }
  }

  async getBlockedUsers(req, res) {
    try {
      const blockerId = req.user.userId;
      const query = `
        SELECT u.id, u.username, u.avatar
        FROM users u
        JOIN blocked_users bu ON u.id = bu.blocked_id
        WHERE bu.blocker_id = $1
        ORDER BY u.username ASC
      `;
      const result = await db.query(query, [blockerId]);
      res.status(200).json({ blockedUsers: result.rows });
    } catch (err) {
      console.error('Error fetching blocked users:', err);
      res.status(500).json({ message: 'Failed to fetch blocked users' });
    }
  }
    // Fetch tag suggestions for autocomplete
    async getTagSuggestions(req, res) {
      const query = req.query.query || '';
      if (query.length < 2) {
        return res.json({ tags: [] });
      }
      try {
        const result = await db.query(
          `SELECT DISTINCT tag
           FROM (
             SELECT unnest(tags) AS tag
             FROM users
           ) AS subquery
           WHERE tag ILIKE $1
           ORDER BY tag
           LIMIT 10`,
          [`${query}%`]
        );
        // Preserve the original case for display purposes
        const tags = result.rows.map(row => row.tag);
        console.log('Tag suggestions found:', tags);
        res.json({ tags });
      } catch (error) {
        console.error('Error fetching tag suggestions:', error);
        res.status(500).json({ message: 'Internal server error' });
      }
    }

   // backend/controllers/User/userController.js
   async searchUsersByTags(req, res) {
    // Normalize tags to lowercase for case-insensitive comparison
    let selectedTags = Array.isArray(req.query.tags) ? req.query.tags : req.query.tags ? [req.query.tags] : [];
    // Convert all tags to lowercase for case-insensitive matching
    selectedTags = selectedTags.map(tag => tag.toLowerCase());

    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;
    const currentUserId = req.user.userId;

    if (selectedTags.length === 0) {
      return res.status(400).json({ message: 'Please select at least one tag' });
    }

    try {
      // Use LOWER() function to make the comparison case-insensitive
      const query = `
        SELECT
          u.id,
          u.username,
          u.avatar,
          (SELECT COUNT(*)::int
           FROM unnest(u.tags) AS tag
           WHERE LOWER(tag) = ANY($1)) AS match_count,
          (SELECT array_agg(tag)
           FROM unnest(u.tags) AS tag
           WHERE LOWER(tag) = ANY($1)) AS matching_tags,
          EXISTS (
            SELECT 1 FROM friends
            WHERE (user_id = $2 AND friend_id = u.id) OR (user_id = u.id AND friend_id = $2)
          ) AS is_friend,
          EXISTS (
            SELECT 1 FROM friend_requests
            WHERE sender_id = $2 AND receiver_id = u.id AND status = 'pending'
          ) AS has_sent_request
        FROM users u
        WHERE EXISTS (
          SELECT 1 FROM unnest(u.tags) AS tag WHERE LOWER(tag) = ANY($1)
        )
          AND u.id != $2    -- Exclude current user
        ORDER BY match_count DESC
        LIMIT $3 OFFSET $4
      `;

      console.log('Searching for tags (lowercase):', selectedTags);
      const result = await db.query(query, [selectedTags, currentUserId, limit, offset]);
      res.json({ users: result.rows });
    } catch (error) {
      console.error('Error searching users by tags:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
  async searchUserFuzzy(req, res) {
    try {
      const { username } = req.params;
      const query = `
        SELECT id, username, similarity(username, $1) AS sim
        FROM users
        WHERE similarity(username, $1) > 0.3
        ORDER BY sim DESC
        LIMIT 5;
      `;
      const result = await db.query(query, [username]);
      res.status(200).json({ users: result.rows });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }
  // In userController.js
async suggestUsernames(req, res) {
  try {
    const { q } = req.query;
    const query = `
      SELECT id, username
      FROM users
      WHERE LOWER(username) LIKE LOWER($1 || '%')
      LIMIT 10;
    `;
    const result = await db.query(query, [q]);
    res.status(200).json({ suggestions: result.rows });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
}
}

module.exports = new UserController();
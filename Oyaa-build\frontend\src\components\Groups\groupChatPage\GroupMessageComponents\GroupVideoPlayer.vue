<template>
  <div class="video-player-container" :class="{ 'is-loading': isLoading, 'has-error': hasError }">
    <!-- Loading indicator -->
    <div v-if="isLoading" class="video-loading">
      <div class="loading-spinner"></div>
      <span>Loading video...</span>
    </div>
    
    <!-- Error state -->
    <div v-if="hasError" class="video-error">
      <i class="fas fa-exclamation-circle"></i>
      <span>Failed to load video</span>
      <button @click="retryLoading" class="retry-button">Retry</button>
    </div>
    
    <!-- Video player -->
    <video
      v-show="!hasError"
      ref="videoElement"
      :id="videoId"
      class="video-element"
      controls
      preload="metadata"
      :poster="getPosterImage()"
      @loadeddata="handleVideoLoaded"
      @error="handleVideoError"
      @play="handlePlay"
      @pause="handlePause"
      @ended="handleEnded"
    >
      <source :src="mediaUrl" :type="mediaType">
      Your browser does not support the video tag.
    </video>
    
    <!-- Custom controls overlay (for future enhancement) -->
    <div v-if="false" class="custom-controls">
      <!-- Placeholder for future custom controls -->
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';

export default {
  name: 'GroupVideoPlayer',
  props: {
    mediaUrl: {
      type: String,
      required: true
    },
    mediaType: {
      type: String,
      default: 'video/mp4'
    },
    videoId: {
      type: String,
      default: 'video-player'
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    posterUrl: {
      type: String,
      default: ''
    }
  },
  emits: ['loaded', 'error', 'play', 'pause', 'ended'],
  setup(props, { emit }) {
    const videoElement = ref(null);
    const isLoading = ref(true);
    const hasError = ref(false);
    const isPlaying = ref(false);
    
    // Generate a poster image from the video URL if not provided
    const getPosterImage = () => {
      if (props.posterUrl) return props.posterUrl;
      
      // Generate thumbnail for Cloudinary videos
      if (props.mediaUrl && props.mediaUrl.includes('cloudinary.com')) {
        const urlParts = props.mediaUrl.split('/upload/');
        if (urlParts.length === 2) {
          return `${urlParts[0]}/upload/c_thumb,w_640,h_360,g_auto/${urlParts[1].replace(/\.\w+$/, '.jpg')}`;
        }
      }
      
      return '';
    };
    
    // Video event handlers
    const handleVideoLoaded = () => {
      isLoading.value = false;
      emit('loaded', {
        duration: videoElement.value?.duration || 0,
        width: videoElement.value?.videoWidth || 0,
        height: videoElement.value?.videoHeight || 0
      });
      
      // Autoplay if specified
      if (props.autoplay && videoElement.value) {
        try {
          videoElement.value.play().catch(err => {
            console.warn('Autoplay prevented:', err);
          });
        } catch (err) {
          console.warn('Autoplay error:', err);
        }
      }
    };
    
    const handleVideoError = (event) => {
      console.error('Video error:', event);
      isLoading.value = false;
      hasError.value = true;
      emit('error', event);
    };
    
    const handlePlay = () => {
      isPlaying.value = true;
      emit('play');
    };
    
    const handlePause = () => {
      isPlaying.value = false;
      emit('pause');
    };
    
    const handleEnded = () => {
      isPlaying.value = false;
      emit('ended');
    };
    
    const retryLoading = () => {
      if (!videoElement.value) return;
      
      isLoading.value = true;
      hasError.value = false;
      
      // Reload the video
      videoElement.value.load();
    };
    
    // Watch for changes in media URL
    watch(() => props.mediaUrl, () => {
      isLoading.value = true;
      hasError.value = false;
      
      // Reset video element when URL changes
      if (videoElement.value) {
        videoElement.value.pause();
        videoElement.value.currentTime = 0;
        videoElement.value.load();
      }
    });
    
    // Lifecycle hooks
    onMounted(() => {
      // Initialize video element
      if (videoElement.value) {
        videoElement.value.addEventListener('loadedmetadata', () => {
          // Preload a bit of the video for smoother playback start
          if (videoElement.value.preload === 'metadata') {
            videoElement.value.preload = 'auto';
          }
        });
      }
    });
    
    onBeforeUnmount(() => {
      // Clean up
      if (videoElement.value) {
        videoElement.value.pause();
        videoElement.value.src = '';
        videoElement.value.load();
      }
    });
    
    return {
      videoElement,
      isLoading,
      hasError,
      isPlaying,
      getPosterImage,
      handleVideoLoaded,
      handleVideoError,
      handlePlay,
      handlePause,
      handleEnded,
      retryLoading
    };
  }
};
</script>

<style scoped>
.video-player-container {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #000;
}

.video-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  gap: 16px;
  z-index: 2;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.video-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
  color: #e74c3c;
  gap: 12px;
  z-index: 3;
}

.video-error i {
  font-size: 2rem;
}

.retry-button {
  margin-top: 8px;
  background-color: rgba(231, 76, 60, 0.2);
  border: 1px solid rgba(231, 76, 60, 0.5);
  color: #e74c3c;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: rgba(231, 76, 60, 0.3);
}

/* Custom video controls (for future enhancement) */
.custom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16px;
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-player-container:hover .custom-controls {
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .loading-spinner {
    width: 32px;
    height: 32px;
  }
  
  .video-error i {
    font-size: 1.5rem;
  }
}
</style>
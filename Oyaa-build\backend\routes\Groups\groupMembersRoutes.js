const express = require('express');
const router = express.Router();
const groupMembersController = require('../../controllers/Group/groupMembersController');

// Add a member to a group
router.post('/add', groupMembersController.addMember);

// Remove a member from a group
router.post('/remove', groupMembersController.removeMember);

// Get all members of a group
router.get('/:groupId', groupMembersController.getMembers);

// Update a member's role
router.post('/update-role', groupMembersController.updateMemberRole);

module.exports = router;

<!-- frontend/src/components/ThemeToggle.vue -->
<template>
  <div class="theme-toggle">
    <button @click="toggleTheme" aria-label="Toggle theme">
      <img v-if="isDarkMode" src="/SVG/sun.svg" alt="Switch to light mode" class="icon" />
      <img v-else src="/SVG/moon.svg" alt="Switch to dark mode" class="icon" />
    </button>
  </div>
</template>

<script setup>
const props = defineProps({
  isDarkMode: Boolean,
});

const emit = defineEmits(['toggle']);

const toggleTheme = () => {
  emit('toggle');
};
</script>

<style scoped>
.theme-toggle {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
}

button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.dark-mode button {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
}

button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark-mode button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.icon {
  width: 20px;
  height: 20px;
}
</style>
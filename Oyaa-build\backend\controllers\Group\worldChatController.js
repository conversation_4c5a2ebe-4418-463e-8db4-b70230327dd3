// backend/controllers/Group/worldChatController.js
const WorldChatService = require('../../services/Groups/worldChatService');
const db = require('../../utils/db'); // Ensure db is imported if not already

class WorldChatController {
  async sendMessage(req, res) {
    try {
      const { message, sent_at, reply, media } = req.body;
      const senderId = req.user.userId; // Changed from req.user.id

      const userRes = await db.query(
        'SELECT muted_until FROM users WHERE id = $1',
        [senderId]
      );
      if (userRes.rows[0]?.muted_until && new Date() < userRes.rows[0].muted_until) {
        return res.status(403).json({ message: 'You are muted and cannot send messages' });
      }

      if ((!message || message.trim() === '') && !media) {
        return res.status(400).json({ message: 'Either message text or media must be provided' });
      }

      let parsedReply = reply;
      if (reply && typeof reply === 'string') {
        try {
          parsedReply = JSON.parse(reply);
        } catch (e) {
          parsedReply = null;
        }
      }
      const replyId = parsedReply?.id || null;

      const chatMessage = await WorldChatService.sendMessage(senderId, message, sent_at, replyId, media);
      const io = req.app.get('io');
      const worldChat = await WorldChatService.getWorldChat();
      if (io) {
        io.of('/groups').to(`group_${worldChat.id}`).emit('newGroupMessage', chatMessage);
      }
      res.status(200).json({ message: 'Message sent', chatMessage });
    } catch (err) {
      console.error('[sendMessage] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async getMessages(req, res) {
    try {
      const { page = 1, pageSize = 50 } = req.query;
      const messages = await WorldChatService.getMessages(page, pageSize);
      res.status(200).json({ messages });
    } catch (err) {
      console.error('[getMessages] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async deleteMessage(req, res) {
    try {
      const { messageId } = req.params;
      const userId = req.user.userId; // Changed from req.user.id
      if (userId !== '1') { // Replace '1' with your admin ID (consider parsing if IDs are numbers)
        return res.status(403).json({ message: 'Admin access required' });
      }
      const worldChat = await WorldChatService.getWorldChat();
      const result = await db.query(
        'UPDATE group_chats SET deleted = TRUE WHERE id = $1 AND group_id = $2 RETURNING *',
        [messageId, worldChat.id]
      );
      if (result.rows.length === 0) {
        throw new Error('Message not found');
      }
      const io = req.app.get('io');
      if (io) {
        io.of('/groups').to(`group_${worldChat.id}`).emit('messageDeleted', { messageId });
      }
      res.status(200).json({ message: 'Message deleted', chatMessage: result.rows[0] });
    } catch (err) {
      console.error('[deleteMessage] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async muteUser(req, res) {
    try {
      const { userId } = req.params;
      const adminId = req.user.userId; // Changed from req.user.id
      const result = await WorldChatService.muteUser(userId, adminId);
      const io = req.app.get('io');
      const worldChat = await WorldChatService.getWorldChat();
      if (io) {
        io.of('/groups').to(`group_${worldChat.id}`).emit('userMuted', { userId });
      }
      res.status(200).json(result);
    } catch (err) {
      console.error('[muteUser] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async reportMessage(req, res) {
    try {
      const { messageId } = req.params;
      const userId = req.user.userId; // Changed from req.user.id
      const worldChat = await WorldChatService.getWorldChat();

      // Check if the user already reported this message
      const existingReport = await db.query(
        'SELECT * FROM message_reports WHERE message_id = $1 AND user_id = $2',
        [messageId, userId]
      );
      if (existingReport.rows.length > 0) {
        return res.status(400).json({ message: 'You have already reported this message' });
      }

      // Record the report
      await db.query(
        'INSERT INTO message_reports (message_id, user_id, reported_at) VALUES ($1, $2, NOW())',
        [messageId, userId]
      );

      // Check report count and hide if threshold reached
      const reportCountRes = await db.query(
        'SELECT COUNT(*) FROM message_reports WHERE message_id = $1',
        [messageId]
      );
      const reportCount = parseInt(reportCountRes.rows[0].count, 10);
      if (reportCount >= 5) {
        await db.query(
          'UPDATE group_chats SET hidden = TRUE WHERE id = $1 AND group_id = $2',
          [messageId, worldChat.id]
        );
        const io = req.app.get('io');
        if (io) {
          io.of('/groups').to(`group_${worldChat.id}`).emit('messageHidden', { messageId });
        }
      }

      res.status(200).json({ message: 'Message reported' });
    } catch (err) {
      console.error('[reportMessage] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async getReportedMessages(req, res) {
    try {
      const userId = req.user.userId; // Changed from req.user.id
      if (userId !== '1') { // Replace '1' with your admin ID (consider parsing if IDs are numbers)
        return res.status(403).json({ message: 'Admin access required' });
      }
      const worldChat = await WorldChatService.getWorldChat();
      const reportedMessages = await db.query(
        `SELECT gc.*, COUNT(mr.id) as report_count
         FROM group_chats gc
         JOIN message_reports mr ON gc.id = mr.message_id
         WHERE gc.group_id = $1 AND gc.hidden = TRUE
         GROUP BY gc.id`,
        [worldChat.id]
      );
      res.status(200).json({ reportedMessages: reportedMessages.rows });
    } catch (err) {
      console.error('[getReportedMessages] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async banUser(req, res) {
    try {
      const { userId } = req.params;
      const adminId = req.user.userId; // Changed from req.user.id
      if (adminId !== '1') { // Replace '1' with your admin ID (consider parsing if IDs are numbers)
        return res.status(403).json({ message: 'Admin access required' });
      }

      // Check if banned column exists
      try {
        await db.query('UPDATE users SET banned = TRUE WHERE id = $1', [userId]);
      } catch (error) {
        // If banned column doesn't exist, use muted_until instead
        if (error.message.includes('column "banned" does not exist')) {
          // Set muted_until to a date far in the future (effectively banning)
          const farFuture = new Date();
          farFuture.setFullYear(farFuture.getFullYear() + 10); // Ban for 10 years
          await db.query('UPDATE users SET muted_until = $1 WHERE id = $2', [farFuture, userId]);
        } else {
          throw error;
        }
      }

      res.status(200).json({ message: 'User banned' });
    } catch (err) {
      console.error('[banUser] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }
}

module.exports = new WorldChatController();
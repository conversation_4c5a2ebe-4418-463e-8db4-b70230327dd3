// src/composables/useChatSocket.js
import chatSocket from '@/chatSocket';

export default function useChatSocket() {
  const joinChat = (userId, targetUserId) => {
    console.log(`Joining chat room between ${userId} and ${targetUserId}`);
    chatSocket.emit('joinChat', { userId, targetUserId });
    
    // Also join personal room for last message updates
    console.log(`Joining personal room for user ${userId}`);
    chatSocket.emit('joinPersonalRoom', userId);
  };

  const onNewMessage = (callback) => {
    console.log('Setting up newMessage listener');
    // First remove any existing listeners to avoid duplicates
    chatSocket.off('newMessage');
    chatSocket.on('newMessage', (message) => {
      console.log('Received newMessage event:', message);
      callback(message);
    });
  };

  const onTyping = (callback) => {
    console.log('Setting up typing listener');
    chatSocket.off('typing');
    chatSocket.on('typing', (data) => {
      console.log('Received typing event:', data);
      callback(data);
    });
  };

  const onStopTyping = (callback) => {
    console.log('Setting up stopTyping listener');
    chatSocket.off('stopTyping');
    chatSocket.on('stopTyping', (data) => {
      console.log('Received stopTyping event:', data);
      callback(data);
    });
  };

  const sendMessage = (message) => {
    console.log('Emitting sendMessage event with data:', message);
    if (!message.sender_id || !message.receiver_id) {
      console.error('Cannot send message: Missing sender_id or receiver_id');
      return;
    }
    chatSocket.emit('sendMessage', message);
  };

  const sendTyping = (payload) => {
    if (!payload.senderId || !payload.receiverId) {
      console.error('Cannot send typing: Missing senderId or receiverId');
      return;
    }
    console.log('Emitting typing event:', payload);
    chatSocket.emit('typing', payload);
  };

  const sendStopTyping = (payload) => {
    if (!payload.senderId || !payload.receiverId) {
      console.error('Cannot send stopTyping: Missing senderId or receiverId');
      return;
    }
    console.log('Emitting stopTyping event:', payload);
    chatSocket.emit('stopTyping', payload);
  };
  
  // Check if socket is connected
  const isConnected = () => {
    return chatSocket.connected;
  };
  
  // Manually reconnect if needed
  const reconnect = () => {
    if (!chatSocket.connected) {
      console.log('Attempting to reconnect chat socket');
      chatSocket.connect();
    }
  };

  return {
    joinChat,
    onNewMessage,
    onTyping,
    onStopTyping,
    sendMessage,
    sendTyping,
    sendStopTyping,
    isConnected,
    reconnect
  };
}

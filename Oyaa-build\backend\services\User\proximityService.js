// backend/services/proximityService.js
const User = require('../../models/User/userModel');
const logger = require('../../utils/logger');

class ProximityService {
  /**
   * Update the user's location in PostgreSQL using PostGIS.
   * Assumes that your users table has a "location" column of type geography(Point,4326)
   */
  async updateUserLocation(userId, lat, lng) {
    const user = new User();
    try {
      await user.connect();

      // Update location in PostgreSQL – you need to implement this method in your user model
      await user.updateLocation(userId, lat, lng);

      logger.info(`Location updated for user ${userId}: (${lat}, ${lng})`);
      return { success: true };
    } catch (error) {
      logger.error(`ProximityService.updateUserLocation: ${error.message}`);
      throw new Error('Location update failed');
    } finally {
      await user.close();
    }
  }

  /**
   * Get nearby users using PostGIS.
   * This method uses a SQL query to select users within the specified radius.
   *
   * If coordinates are provided in the query params, use them;
   * otherwise, fall back to the requesting user’s stored location.
   */
  async getNearbyUsers(userId, limit = 10, offset = 0, radius = 5000, coordinates) {
    const user = new User();
    try {
      await user.connect();

      let lat, lng;
      if (coordinates && coordinates.lat && coordinates.lng) {
        lat = parseFloat(coordinates.lat);
        lng = parseFloat(coordinates.lng);
      } else {
        // Get requesting user's location from PostgreSQL
        const currentUser = await user.getUserLocation(userId);
        if (!currentUser || !currentUser.latitude || !currentUser.longitude) {
          throw new Error('User location not found');
        }
        lat = parseFloat(currentUser.latitude);
        lng = parseFloat(currentUser.longitude);
      }

      // Run a PostGIS query to find nearby users.
      // The query below assumes:
      //   - Your users table contains a "location" column of type geography(Point,4326).
      //   - You want to return users along with the distance from the provided point.
      const nearbyUsers = await user.getNearbyUsersPostGIS(lat, lng, radius, limit, offset);

      return nearbyUsers;
    } catch (error) {
      logger.error(`ProximityService.getNearbyUsers: ${error.message}`);
      throw new Error('Failed to find nearby users');
    } finally {
      await user.close();
    }
  }
}

module.exports = new ProximityService();

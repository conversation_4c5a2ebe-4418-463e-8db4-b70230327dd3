<template>
  <div :class="['message-group', { 'sent-group': isSent }]">
    <!-- Render sender meta if needed -->
    <div v-if="showMeta" class="message-meta">
      <img
        v-if="group.sender_avatar"
        :src="group.sender_avatar"
        :alt="group.sender_name"
        class="avatar"
      />
      <div v-else class="avatar">{{ getInitials(group.sender_name) }}</div>
      <span class="username">{{ group.sender_name }}</span>
    </div>
    <div class="group-messages">
      <component
        v-for="msg in group.messages"
        :is="isSent ? UserChatBubble : OtherUserChatBubble"
        :key="msg.id"
        :message="msg"
        @reply="$emit('reply', $event)"
        @react="$emit('react', $event)"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import UserChatBubble from './UserChatBubble.vue';
import OtherUserChatBubble from './OtherUserChatBubble.vue';

const props = defineProps({
  group: { type: Object, required: true },
  currentUser: { type: Object, required: true },
  showMeta: { type: Boolean, default: true },
});
const emit = defineEmits(['reply', 'react']);

const isSent = computed(() => props.group.sender_id === props.currentUser.id);

const getInitials = (name) => {
  if (!name) return '';
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};
</script>

<style scoped>
.message-group {
  margin-bottom: 1rem;
}
.message-meta {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  margin-left: 1vw;
}
.sent-group .message-meta {
  flex-direction: row-reverse;
}
.sent-group .message-meta .avatar {
  margin-left: 0.5rem;
  margin-right: 1vw;
}
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 0.5rem;
  background-color: #7289da;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 0.8rem;
}
.username {
  font-weight: 600;
  margin-right: 0.5rem;
}
.group-messages {
  display: flex;
  flex-direction: column;
}
</style>
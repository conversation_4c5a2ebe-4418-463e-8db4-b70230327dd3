// backend/services/localService.js
const Local = require('../../models/Local/Local');
const userService = require('../User/userService'); // This service fetches user data from the users table

class LocalService {
  constructor() {
    this.local = new Local();
    this.local.connect().catch(err =>
      console.error("Locals DB connection error:", err)
    );
  }

  async addOrUpdateLocal(userId, localContact) {
    try {
      // We assume that localContact has at least the local's id.
      const localId = localContact.id;

      // Retrieve the username from the localContact (if available)
      // Provide a default username if none is available
      const localUsername = localContact.username || 'User_' + localId;

      // Optionally, you can store additional details in the 'data' column.
      const data = localContact;

      // Pass the retrieved username to the model.
      const result = await this.local.addLocal(userId, localId, localUsername, data);
      console.log("Local contact added/updated:", result);
      return result;
    } catch (err) {
      console.error("Error in addOrUpdateLocal:", err);
      throw new Error(`Failed to add local contact: ${err.message}`);
    }
  }


  async getLocals(userId) {
    try {
      // Get the raw locals from the DB
      const locals = await this.local.getLocals(userId);

      // Enrich each local record by fetching the username from the user model.
      const enrichedLocals = await Promise.all(
        locals.map(async (local) => {
          try {
            const userDetails = await userService.getUserById(local.local_id);
            // Merge the username into the local object.
            local.local_username = userDetails ? userDetails.username : 'Unknown';
          } catch (error) {
            console.error(`Error fetching user details for id ${local.local_id}:`, error);
            local.local_username = 'Unknown';
          }
          return local;
        })
      );
      return enrichedLocals;
    } catch (err) {
      throw new Error(`Failed to retrieve locals: ${err.message}`);
    }
  }
  // This method is intentionally removed as it's a duplicate
}

module.exports = new LocalService();

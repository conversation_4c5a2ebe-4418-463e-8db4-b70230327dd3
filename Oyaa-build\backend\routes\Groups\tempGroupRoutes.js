// backend/routes/Groups/tempGroupRoutes.js
const express = require('express');
const tempGroupController = require('../../controllers/Group/tempGroupController');
const authMiddleware = require('../../middleware/authMiddleware');
const router = express.Router();

router.post('/create', authMiddleware, tempGroupController.createTempGroup);
router.post('/join', tempGroupController.joinTempGroup);
router.get('/created', authMiddleware, tempGroupController.getCreatedTempGroups);

module.exports = router;
<template>
  <div
    :class="['message-bubble', 'other-message', { 'with-media': hasMedia }]"
    @contextmenu.prevent="$emit('contextmenu', $event, message)"
  >
    <!-- Sender info -->
    <div class="message-sender">
      {{ senderName }}
    </div>

    <!-- Message content -->
    <div 
      :class="['message-content', message.deleted ? 'deleted-message' : '']"
      :aria-label="message.deleted ? 'Deleted message' : ''"
    >
      {{ message.deleted ? "This message has been deleted" : (message.content || message.message) }}
    </div>

    <!-- Message media (if any) -->
    <div v-if="hasMedia" class="message-media">
      <div 
        v-for="(media, index) in message.media"
        :key="index"
        class="media-wrapper"
      >
        <img
          :src="media.url"
          :alt="`Media attachment ${index + 1}`"
          class="media-image"
          loading="lazy"
          @click="$emit('open-media', media)"
        />
        <div class="media-overlay">
          <span class="media-view-text">View</span>
        </div>
      </div>
    </div>

    <!-- Message footer -->
    <div class="message-footer">
      <!-- Timestamp -->
      <span class="message-time" :title="fullDateTime">{{ formattedTime }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
  senderName: {
    type: String,
    required: true,
  },
  formattedTime: {
    type: String,
    required: true,
  }
});

const emit = defineEmits(['contextmenu', 'open-media']);

const hasMedia = computed(() => {
  return props.message?.media && Array.isArray(props.message.media) && props.message.media.length > 0;
});

const fullDateTime = computed(() => {
  if (!props.message.sent_at && !props.message.created_at) return '';
  const timestamp = props.message.sent_at || props.message.created_at;
  return new Date(timestamp).toLocaleString([], { 
    weekday: 'long',
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    hour: '2-digit', 
    minute: '2-digit'
  });
});
</script>

<style scoped>
/* Styles for other user messages */
.message-bubble {
  max-width: 85%;
  padding: 7px 10px;
  border-radius: 14px 14px 14px 4px;
  margin-bottom: 6px;
  position: relative;
  word-wrap: break-word;
  animation: fadeIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.message-bubble:hover {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

.other-message {
  align-self: flex-start;
  background-color: #383838;
  color: white;
  margin-left: 6px;
}

.message-sender {
  font-size: 11px;
  color: #4FC3F7;
  margin-bottom: 3px;
  font-weight: 500;
}

.message-content {
  font-size: 13px;
  line-height: 1.3;
  white-space: pre-wrap;
  word-break: break-word;
  margin-bottom: 3px;
}

.deleted-message {
  font-style: italic;
  color: rgba(255, 255, 255, 0.5);
  opacity: 0.8;
}

.message-media {
  margin-top: 6px;
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
}

.media-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  max-width: 160px;
}

.media-image {
  max-width: 160px;
  max-height: 120px;
  display: block;
  width: 100%;
  transition: transform 0.3s ease;
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.media-view-text {
  color: white;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.5);
  padding: 3px 8px;
  border-radius: 8px;
  font-size: 10px;
}

.media-wrapper:hover .media-image,
.media-wrapper:active .media-image {
  transform: scale(1.03);
}

.media-wrapper:hover .media-overlay,
.media-wrapper:active .media-overlay {
  opacity: 1;
}

.with-media .message-content {
  margin-bottom: 4px;
}

.message-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 3px;
  font-size: 9px;
  color: rgba(255, 255, 255, 0.7);
  align-self: flex-end;
}

/* Responsive adjustments */
@media (min-width: 420px) {
  .message-bubble {
    max-width: 80%;
    padding: 8px 12px;
    border-radius: 16px 16px 16px 4px;
  }
  
  .message-sender {
    font-size: 9px;
    margin-bottom: 4px;
  }
  
  .message-content {
    font-size: 9px;
    line-height: 1.4;
  }
  
  .media-image {
    max-width: 180px;
    max-height: 140px;
  }
  
  .media-view-text {
    font-size: 11px;
    padding: 4px 10px;
  }
  
  .message-footer {
    font-size: 10px;
  }
}
</style>
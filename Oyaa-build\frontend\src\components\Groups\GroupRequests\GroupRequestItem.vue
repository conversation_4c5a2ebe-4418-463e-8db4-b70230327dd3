<template>
  <li :class="['request-item', { 
    'processing': request.processing,
    'accepted': request.status === 'accepted',
    'rejected': request.status === 'rejected'
  }]">
    <div class="request-content">
      <div class="avatar">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      </div>
      
      <div class="user-details">
        <span class="username">{{ request.sender_username }}</span>
        <span v-if="request.status" class="status-message">
          <svg v-if="request.status === 'accepted'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          {{ statusText }}
        </span>
      </div>
    </div>
    
    <div v-if="!request.status" class="action-buttons">
      <ActionButton
        @click="$emit('accept', request)"
        :disabled="request.processing"
        type="accept"
        :loading="request.processing"
      />
      <ActionButton
        @click="$emit('reject', request)"
        :disabled="request.processing"
        type="reject"
        :loading="request.processing"
      />
    </div>
  </li>
</template>

<script>
import ActionButton from './ActionButton.vue';

export default {
  components: { ActionButton },
  props: {
    request: {
      type: Object,
      required: true,
    },
  },
  computed: {
    statusText() {
      return {
        accepted: 'Accepted!',
        rejected: 'Rejected',
      }[this.request.status];
    },
  },
};
</script>

<style scoped>
.request-item {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --success: #10b981;
  --success-dark: #059669;
  --success-bg: rgba(16, 185, 129, 0.15);
  --danger: #ef4444;
  --danger-dark: #dc2626;
  --danger-bg: rgba(239, 68, 68, 0.15);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  border-radius: 0.75rem;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.request-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.625rem;
  background-color: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  flex-shrink: 0;
}

.avatar svg {
  width: 1.25rem;
  height: 1.25rem;
}

.user-details {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.username {
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-message {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  animation: fadeIn 0.3s ease;
}

.status-message svg {
  width: 1rem;
  height: 1rem;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* States */
.processing {
  opacity: 0.7;
  pointer-events: none;
}

.accepted {
  background-color: var(--success-bg);
  border-left: 3px solid var(--success);
}

.accepted .status-message svg {
  color: var(--success);
}

.rejected {
  background-color: var(--danger-bg);
  border-left: 3px solid var(--danger);
}

.rejected .status-message svg {
  color: var(--danger);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Media queries for responsive design */
@media (min-width: 640px) {
  .request-item {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .action-buttons {
    margin-left: 1rem;
  }
}

/* Ensure buttons are properly sized on small screens */
@media (max-width: 360px) {
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
}
</style>
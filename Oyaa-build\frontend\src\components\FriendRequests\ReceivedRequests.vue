<template>
  <div class="friend-requests-container">
    <!-- Debug info (hidden in production) -->
    <div v-if="isDevelopment && receivedRequests.length > 0" class="debug-info">
      <details>
        <summary>Debug Info ({{ receivedRequests.length }} requests)</summary>
        <pre>{{ JSON.stringify(receivedRequests, null, 2) }}</pre>
      </details>
    </div>
    <div class="header">
      <h2>Friend Requests</h2>
      <div :class="['live-indicator', connectionStatus]">
        <span class="live-dot"></span>
        <span class="live-text">
          {{ connectionStatus === 'connected' ? 'Live Updates' :
             connectionStatus === 'connecting' ? 'Connecting...' : 'Connection Error' }}
        </span>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-state">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="loader"
      >
        <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
      </svg>
      <span>Loading requests...</span>
    </div>

    <!-- Error message -->
    <div v-else-if="error" class="error-message">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="error-icon"
      >
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
      {{ error }}
    </div>

    <!-- Empty state -->
    <div v-else-if="pendingRequests.length === 0" class="empty-state">
      <pre v-if="isDevelopment" style="text-align: left; font-size: 10px; color: #666;">
        Debug Info:
        receivedRequests: {{ receivedRequests.length }}
        pendingReceivedRequests: {{ pendingReceivedRequests.length }}
        connectionStatus: {{ connectionStatus }}
      </pre>
      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
        <circle cx="8.5" cy="7" r="4"></circle>
        <line x1="20" y1="8" x2="20" y2="14"></line>
        <line x1="23" y1="11" x2="17" y2="11"></line>
      </svg>
      <p>No friend requests</p>
      <p class="hint">When someone sends you a friend request, it will appear here</p>
      <div :class="['connection-status', connectionStatus]">
        <span class="status-dot"></span>
        <span v-if="connectionStatus === 'connecting'">Connecting...</span>
        <span v-else-if="connectionStatus === 'connected'">Live updates active</span>
        <span v-else>Connection error</span>
      </div>
    </div>

    <!-- Friend requests list -->
    <ul v-else class="friend-requests-list">
      <li v-for="request in pendingRequests" :key="request.id" class="request-item">
        <div class="request-user">
          <div class="user-avatar">
            <img v-if="request.sender_avatar" :src="request.sender_avatar" alt="User Avatar" />
            <span v-else>{{ getInitials(request.sender_username) }}</span>
          </div>
          <div class="user-info">
            <h3>{{ request.sender_username }}</h3>
            <p v-if="request.sender_description">{{ request.sender_description }}</p>
          </div>
        </div>
        <div class="request-actions">
          <button
            @click="acceptRequest(request)"
            class="action-button accept"
            :disabled="request.processing"
          >
            <span v-if="request.processing">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="spinner">
                <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
              </svg>
            </span>
            <span v-else>Accept</span>
          </button>
          <button
            @click="rejectRequest(request)"
            class="action-button reject"
            :disabled="request.processing"
          >
            <span v-if="request.processing">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="spinner">
                <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
              </svg>
            </span>
            <span v-else>Reject</span>
          </button>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';
// Import HTTP services for friend request actions
import { acceptFriendRequest, rejectFriendRequest } from '@/services/friendRequestService';

export default {
  name: 'ReceivedRequests',

  data() {
    return {
      connectionStatus: 'connected', // Always show as connected since we're using SSE
      processingRequests: new Set()
    };
  },

  computed: {
    ...mapState('friendRequests', {
      receivedRequests: state => state.receivedRequests,
      isLoading: state => state.loading.received,
      error: state => state.error.received
    }),

    ...mapGetters('friendRequests', ['pendingReceivedRequests']),

    // Check if we're in development mode
    isDevelopment() {
      return process.env.NODE_ENV === 'development';
    },

    // Alias for pendingReceivedRequests for cleaner template
    pendingRequests() {


      const result = this.pendingReceivedRequests.map(request => ({
        ...request,
        processing: this.processingRequests.has(request.id)
      }));


      return result;
    }
  },

  created() {


    // Fetch friend requests once at startup
    this.fetchReceivedRequests();
  },

  methods: {
    ...mapActions('friendRequests', ['fetchReceivedRequests']),

    getInitials(name) {
      if (!name) return '';
      const parts = name.split(' ');
      if (parts.length >= 2) {
        return (parts[0][0] + parts[1][0]).toUpperCase();
      } else {
        return name.substring(0, 2).toUpperCase();
      }
    },



    // Accept a friend request
    async acceptRequest(request) {
      // Prevent duplicate processing
      if (this.processingRequests.has(request.id)) {

        return;
      }

      try {
        // Mark as processing
        this.processingRequests.add(request.id);


        // Use HTTP to accept the request

        await acceptFriendRequest(request.id);


        // No toast for successful actions - notifications will handle this

        // The store will be updated via SSE event or the HTTP service will refresh the lists
      } catch (error) {
        console.error('Error accepting friend request:', error);

        // Show error toast
        this.$store.dispatch('app/showToast', {
          message: `Failed to accept friend request: ${error.message}`,
          type: 'error',
          duration: 5000
        });
      } finally {
        // Remove from processing set

        this.processingRequests.delete(request.id);
      }
    },

    // Reject a friend request
    async rejectRequest(request) {
      // Prevent duplicate processing
      if (this.processingRequests.has(request.id)) {

        return;
      }

      try {
        // Mark as processing
        this.processingRequests.add(request.id);


        // Use HTTP to reject the request

        await rejectFriendRequest(request.id);


        // No toast for successful actions - notifications will handle this

        // The store will be updated via SSE event or the HTTP service will refresh the lists
      } catch (error) {
        console.error('Error rejecting friend request:', error);

        // Show error toast
        this.$store.dispatch('app/showToast', {
          message: `Failed to reject friend request: ${error.message}`,
          type: 'error',
          duration: 5000
        });
      } finally {
        // Remove from processing set

        this.processingRequests.delete(request.id);
      }
    }
  }
};
</script>

<style scoped>
.debug-info {
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 0.75rem;
}

.debug-info details {
  cursor: pointer;
}

.debug-info pre {
  margin-top: 0.5rem;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 0.7rem;
  text-align: left;
  max-height: 200px;
  overflow: auto;
}

.friend-requests-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --error-color: #f87171;
  --success-color: #34d399;
  --warning-color: #fbbf24;

  padding: 1.5rem;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.live-indicator.connected {
  background-color: rgba(52, 211, 153, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(52, 211, 153, 0.2);
}

.live-indicator.connecting {
  background-color: rgba(251, 191, 36, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.live-indicator.error {
  background-color: rgba(248, 113, 113, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(248, 113, 113, 0.2);
}

.live-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
}

.connected .live-dot {
  background-color: var(--success-color);
}

.connecting .live-dot {
  background-color: var(--warning-color);
}

.error .live-dot {
  background-color: var(--error-color);
}

.live-dot::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
}

.connected .live-dot::after {
  background-color: rgba(52, 211, 153, 0.3);
  animation: pulse 1.5s ease-in-out infinite;
}

.connecting .live-dot::after {
  background-color: rgba(251, 191, 36, 0.3);
  animation: pulse 0.8s ease-in-out infinite;
}

.error .live-dot::after {
  background-color: rgba(248, 113, 113, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% { transform: scale(0.8); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 0.4; }
  100% { transform: scale(0.8); opacity: 0.8; }
}

.live-text {
  font-weight: 500;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-secondary);
  gap: 1rem;
}

.loader, .spinner {
  animation: spin 1.2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  background-color: rgba(248, 113, 113, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(248, 113, 113, 0.2);
  border-radius: 8px;
}

.error-icon {
  color: var(--error-color);
  flex-shrink: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem 1rem;
  color: var(--text-secondary);
}

.empty-state svg {
  color: var(--accent-tertiary);
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.empty-state .hint {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  background-color: var(--bg-tertiary);
}

.connection-status.connecting .status-dot {
  background-color: var(--warning-color);
}

.connection-status.connected .status-dot {
  background-color: var(--success-color);
}

.connection-status.error .status-dot {
  background-color: var(--error-color);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: inline-block;
}

.friend-requests-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.request-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.request-item:last-child {
  border-bottom: none;
}

.request-item:hover {
  background-color: var(--bg-tertiary);
}

.request-user {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
  background-color: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  font-weight: 500;
  font-size: 1rem;
  flex-shrink: 0;
  overflow: hidden; /* Clips the image */
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-info h3 {
  margin: 0;
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--text-primary);
}

.user-info p {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.request-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  border: none;
  min-width: 5rem;
}

.action-button.accept {
  background-color: var(--accent-primary);
  color: white;
}

.action-button.accept:hover:not(:disabled) {
  background-color: var(--accent-secondary);
}

.action-button.reject {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.action-button.reject:hover:not(:disabled) {
  background-color: rgba(248, 113, 113, 0.1);
  color: var(--error-color);
  border-color: rgba(248, 113, 113, 0.2);
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 640px) {
  .request-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .request-actions {
    align-self: flex-end;
  }
}
</style>

// backend/controllers/Group/enhancedGroupChatController.js
const EnhancedGroupChatService = require('../../services/Groups/enhancedGroupChatService');
const GroupService = require('../../services/Groups/groupService');
const WorldChatModel = require('../../models/Groups/worldChatModel');
const logger = require('../../utils/logger');
const db = require('../../utils/db');
const { omniQuery } = require('../../config/alloydb.config');
const { groupChatValkeySyncGroupCache } = require('../../config/groupChatValkey.config');

class EnhancedGroupChatController {
  /**
   * Endpoint to send a message in a group chat.
   * Supports an optional reply object (as stringified JSON) similar to the normal chat.
   */
  async sendMessage(req, res) {
    try {
      logger.info('[enhancedSendMessage] Request body:', req.body);
      const { groupId, message, sent_at, reply, media } = req.body;
      const senderId = req.user.userId;

      // Check if user is muted
      const userRes = await db.query(
        'SELECT muted_until FROM users WHERE id = $1',
        [senderId]
      );

      if (userRes.rows[0]?.muted_until && new Date() < userRes.rows[0].muted_until) {
        return res.status(403).json({ message: 'You are muted and cannot send messages' });
      }

      // Validate input
      if ((!message || message.trim() === '') && !media) {
        return res.status(400).json({ message: 'Either message text or media must be provided' });
      }

      // Check if user is a member of the group (except for World Chat)
      if (parseInt(groupId) !== 1) { // World Chat has ID 1
        const isMember = await GroupService.isMember(groupId, senderId);
        if (!isMember) {
          return res.status(403).json({ message: 'You are not a member of this group' });
        }
      }

      // Parse reply if it's a string
      let parsedReply = reply;
      if (reply && typeof reply === 'string') {
        try {
          parsedReply = JSON.parse(reply);
        } catch (e) {
          parsedReply = null;
        }
      }
      const replyId = parsedReply && parsedReply.id ? parsedReply.id : null;

      // Send message using enhanced service
      const chatMessage = await EnhancedGroupChatService.sendMessage(
        senderId,
        groupId,
        message,
        sent_at,
        replyId,
        media
      );

      logger.info('[enhancedSendMessage] Chat message created:', chatMessage);

      // No need to update message status anymore

      // Broadcast to group members via WebSocket
      const io = req.app.get('io');
      if (io) {
        // Fetch group members
        const membersResult = await db.query(
          'SELECT user_id FROM group_members WHERE group_id = $1',
          [groupId]
        );
        const memberIds = membersResult.rows.map(row => row.user_id);

        // Fetch users who have blocked the sender
        const blockedResult = await db.query(
          'SELECT blocker_id FROM blocked_users WHERE blocked_id = $1 AND blocker_id = ANY($2)',
          [senderId, memberIds]
        );
        const blockedByIds = blockedResult.rows.map(row => row.blocker_id);

        // Determine recipients (members who haven't blocked the sender)
        const recipients = memberIds.filter(id => !blockedByIds.includes(id));

        // Emit to each recipient's individual user room
        recipients.forEach(userId => {
          io.of('/groups').to(`user_${userId}`).emit('newGroupMessage', chatMessage);
          logger.debug(`Emitted newGroupMessage to user_${userId}`);
        });

        // Also emit to the group room for any listeners
        io.of('/groups').to(`group_${groupId}`).emit('newGroupMessage', chatMessage);
        logger.debug(`Emitted newGroupMessage to group_${groupId}`);
      }

      res.status(200).json({ message: 'Message sent', chatMessage });
    } catch (err) {
      logger.error('[enhancedSendMessage] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  /**
   * Endpoint to retrieve messages for a group chat with pagination.
   * Supports both page-based pagination and "before" message ID pagination.
   */
  async getMessages(req, res) {
    try {
      const { groupId } = req.params;
      const { page = 1, pageSize = 100, before } = req.query;
      const userId = req.user.userId;

      // Validate input
      if (!groupId) {
        return res.status(400).json({ message: 'Group ID is required' });
      }

      // Check if user is a member of the group (except for World Chat)
      if (parseInt(groupId) !== 1) { // World Chat has ID 1
        const isMember = await GroupService.isMember(groupId, userId);
        if (!isMember) {
          return res.status(403).json({ message: 'You are not a member of this group' });
        }
      }

      // Get messages using enhanced service
      let result;

      if (before) {
        // If "before" parameter is provided, use it for pagination
        result = await EnhancedGroupChatService.getMessagesBefore(
          groupId,
          before,
          parseInt(pageSize)
        );
      } else {
        // Otherwise use standard page-based pagination
        result = await EnhancedGroupChatService.getMessages(
          groupId,
          parseInt(page),
          parseInt(pageSize)
        );
      }

      const { messages, hasMoreMessages } = result;

      // Check if this is the World Chat
      const isWorldChat = await WorldChatModel.isWorldChat(groupId);

      res.status(200).json({
        messages,
        isWorldChat,
        hasMoreMessages,
        // Include total count if available
        totalCount: result.totalCount
      });
    } catch (err) {
      logger.error('[enhancedGetMessages] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  /**
   * Endpoint to retrieve the last active group chats for a user.
   */
  async getLastChats(req, res) {
    try {
      const userId = req.user.userId;

      const lastChats = await EnhancedGroupChatService.getLastChats(userId);

      res.status(200).json({ chats: lastChats });
    } catch (err) {
      logger.error('[enhancedGetLastChats] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }



  /**
   * Endpoint to send a system message to a group chat.
   * Only admins can send system messages.
   */
  async sendSystemMessage(req, res) {
    try {
      const { groupId, message } = req.body;
      const userId = req.user.userId;

      // Validate input
      if (!groupId || !message) {
        return res.status(400).json({ message: 'Group ID and message are required' });
      }

      // Check if user is an admin of the group
      const isAdmin = await GroupService.isAdmin(groupId, userId);
      if (!isAdmin) {
        return res.status(403).json({ message: 'Only group admins can send system messages' });
      }

      // Send system message
      const systemMessage = await EnhancedGroupChatService.sendSystemMessage(groupId, message);

      // Broadcast to group members via WebSocket
      const io = req.app.get('io');
      if (io) {
        io.of('/groups').to(`group_${groupId}`).emit('newGroupMessage', systemMessage);
      }

      res.status(200).json({ message: 'System message sent', systemMessage });
    } catch (err) {
      logger.error('[enhancedSendSystemMessage] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  /**
   * Endpoint to synchronize the cache with the database for a group chat.
   * This ensures the cache reflects the current state of the database.
   */
  async syncGroupCache(req, res) {
    try {
      const { groupId } = req.params;
      const userId = req.user.userId;

      // Validate input
      if (!groupId) {
        return res.status(400).json({ message: 'Group ID is required' });
      }

      // Check if user is an admin of the group
      const isAdmin = await GroupService.isAdmin(groupId, userId);
      if (!isAdmin) {
        return res.status(403).json({ message: 'Only group admins can synchronize the cache' });
      }

      // Synchronize the cache with the database
      const result = await groupChatValkeySyncGroupCache(groupId, omniQuery, db);

      if (result) {
        res.status(200).json({ message: `Cache synchronized for group ${groupId}` });
      } else {
        res.status(500).json({ message: 'Failed to synchronize cache' });
      }
    } catch (err) {
      logger.error('[syncGroupCache] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  /**
   * Endpoint to edit a message.
   * Only the sender can edit their own message.
   */
  async editMessage(req, res) {
    try {
      const { messageId } = req.params;
      const { message } = req.body;
      const userId = req.user.userId;

      logger.info(`[editMessage] Editing message ${messageId} with new content: ${message}`);

      // Validate input
      if (!message || message.trim() === '') {
        return res.status(400).json({ message: 'Message content is required' });
      }

      try {
        // Call the service to edit the message
        const updatedMessage = await EnhancedGroupChatService.editMessage(messageId, userId, message);

        // Emit the updated message to all group members
        const io = req.app.get('io');
        if (io) {
          io.of('/groups').to(`group_${updatedMessage.group_id}`).emit('messageUpdated', updatedMessage);
          logger.info(`[editMessage] Emitted messageUpdated to group_${updatedMessage.group_id}`);
        }

        res.status(200).json({
          message: 'Message updated successfully',
          chatMessage: updatedMessage
        });
      } catch (error) {
        if (error.message === 'Message not found') {
          return res.status(404).json({ message: 'Message not found' });
        } else if (error.message === 'You can only edit your own messages') {
          return res.status(403).json({ message: 'You can only edit your own messages' });
        } else if (error.message === 'Message contains inappropriate content') {
          return res.status(400).json({ message: 'Message contains inappropriate content' });
        } else {
          throw error;
        }
      }
    } catch (err) {
      logger.error('[editMessage] Error:', err);
      res.status(500).json({ message: 'An unexpected error occurred while updating the message.' });
    }
  }

  /**
   * Endpoint to delete a message.
   * Only the sender can delete their own message.
   */
  async deleteMessage(req, res) {
    try {
      const { messageId } = req.params;
      const userId = req.user.userId;

      logger.info(`[deleteMessage] Deleting message ${messageId}`);

      try {
        // Call the service to delete the message
        const result = await EnhancedGroupChatService.deleteMessage(messageId, userId);

        // Emit the deleted message to all group members
        const io = req.app.get('io');
        if (io) {
          io.of('/groups').to(`group_${result.groupId}`).emit('messageDeleted', {
            messageId: result.messageId,
            groupId: result.groupId
          });
          logger.info(`[deleteMessage] Emitted messageDeleted to group_${result.groupId}`);
        }

        res.status(200).json({
          message: 'Message deleted successfully',
          messageId: result.messageId,
          groupId: result.groupId
        });
      } catch (error) {
        if (error.message === 'Message not found') {
          return res.status(404).json({ message: 'Message not found' });
        } else if (error.message === 'You can only delete your own messages') {
          return res.status(403).json({ message: 'You can only delete your own messages' });
        } else {
          throw error;
        }
      }
    } catch (err) {
      logger.error('[deleteMessage] Error:', err);
      res.status(500).json({ message: 'An unexpected error occurred while deleting the message.' });
    }
  }
}

module.exports = new EnhancedGroupChatController();

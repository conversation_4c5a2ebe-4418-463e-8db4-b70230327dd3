import { Component, createSignal, onMount } from 'solid-js';
import { MediaFile } from '../types/chat';
import './MediaContent.css';

interface MediaContentProps {
  media: MediaFile;
}

const MediaContent: Component<MediaContentProps> = (props) => {
  let mediaElement: HTMLDivElement | undefined;
  const [isVisible, setIsVisible] = createSignal(false);
  const [isLoaded, setIsLoaded] = createSignal(false);
  const [isError, setIsError] = createSignal(false);
  const [userInteracted, setUserInteracted] = createSignal(false);
  const [isLoading, setIsLoading] = createSignal(false);

  onMount(() => {
    // Use Intersection Observer only for visibility detection
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            // Don't auto-load media, wait for user interaction
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: '100px' // Detect visibility early but don't load
      }
    );

    if (mediaElement) {
      observer.observe(mediaElement);
    }

    return () => {
      observer.disconnect();
    };
  });

  const handleUserInteraction = () => {
    if (!userInteracted()) {
      setUserInteracted(true);
      setIsLoading(true);
    }
  };

  const handleLoad = () => {
    setIsLoaded(true);
    setIsLoading(false);
  };

  const handleError = () => {
    setIsError(true);
    setIsLoading(false);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string): string => {
    switch (type) {
      case 'image': return '🖼️';
      case 'audio': return '🎵';
      case 'video': return '🎬';
      case 'file': return '📄';
      default: return '📎';
    }
  };

  return (
    <div class="media-content" ref={mediaElement}>
      {props.media.type === 'image' && (
        <div class="image-container">
          {userInteracted() ? (
            <>
              <img
                src={props.media.url}
                alt={props.media.name}
                class={`media-image ${isLoaded() ? 'loaded' : ''} ${isError() ? 'error' : ''}`}
                onLoad={handleLoad}
                onError={handleError}
                loading="lazy"
              />
              {isLoading() && !isLoaded() && !isError() && (
                <div class="loading-overlay">
                  <div class="loading-spinner"></div>
                  <span>Loading image...</span>
                </div>
              )}
              {isError() && (
                <div class="error-placeholder">
                  <span>❌ Failed to load image</span>
                </div>
              )}
            </>
          ) : (
            <button class="media-placeholder image-placeholder" onClick={handleUserInteraction}>
              <div class="placeholder-content">
                <span class="placeholder-icon">🖼️</span>
                <span class="placeholder-text">Click to load image</span>
                <span class="placeholder-name">{props.media.name}</span>
                {props.media.size && (
                  <span class="placeholder-size">{formatFileSize(props.media.size)}</span>
                )}
              </div>
            </button>
          )}
        </div>
      )}

      {props.media.type === 'video' && (
        <div class="video-container">
          {userInteracted() ? (
            <>
              <video
                src={props.media.url}
                controls
                preload="metadata"
                class={`media-video ${isLoaded() ? 'loaded' : ''} ${isError() ? 'error' : ''}`}
                onLoadedData={handleLoad}
                onError={handleError}
              >
                <track kind="captions" />
              </video>
              {isLoading() && !isLoaded() && !isError() && (
                <div class="loading-overlay">
                  <div class="loading-spinner"></div>
                  <span>Loading video...</span>
                </div>
              )}
            </>
          ) : (
            <button class="media-placeholder video-placeholder" onClick={handleUserInteraction}>
              <div class="placeholder-content">
                <span class="placeholder-icon">🎬</span>
                <span class="placeholder-text">Click to load video</span>
                <span class="placeholder-name">{props.media.name}</span>
                {props.media.size && (
                  <span class="placeholder-size">{formatFileSize(props.media.size)}</span>
                )}
              </div>
            </button>
          )}
        </div>
      )}

      {props.media.type === 'audio' && (
        <div class="audio-container">
          {userInteracted() ? (
            <>
              <audio
                src={props.media.url}
                controls
                preload="metadata"
                class="media-audio"
                onLoadedData={handleLoad}
                onError={handleError}
              >
                Your browser does not support the audio element.
              </audio>
              {isLoading() && !isLoaded() && !isError() && (
                <div class="loading-overlay">
                  <div class="loading-spinner"></div>
                  <span>Loading audio...</span>
                </div>
              )}
            </>
          ) : (
            <button class="media-placeholder audio-placeholder" onClick={handleUserInteraction}>
              <div class="placeholder-content">
                <span class="placeholder-icon">🎵</span>
                <span class="placeholder-text">Click to load audio</span>
                <span class="placeholder-name">{props.media.name}</span>
                {props.media.duration && (
                  <span class="placeholder-duration">
                    {Math.floor(props.media.duration / 60)}:{(props.media.duration % 60).toString().padStart(2, '0')}
                  </span>
                )}
              </div>
            </button>
          )}
        </div>
      )}

      {props.media.type === 'file' && (
        <div class="file-container">
          <a href={props.media.url} download={props.media.name} class="file-link">
            <div class="file-info">
              <span class="file-icon">{getFileIcon(props.media.type)}</span>
              <div class="file-details">
                <span class="file-name">{props.media.name}</span>
                {props.media.size && (
                  <span class="file-size">{formatFileSize(props.media.size)}</span>
                )}
              </div>
            </div>
          </a>
        </div>
      )}
    </div>
  );
};

export default MediaContent;

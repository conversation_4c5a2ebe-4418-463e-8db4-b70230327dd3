<template>
  <div class="friend-requests-icon" @click="goToFriendRequests">
    <UserIcon />
    <span v-if="friendRequestsCount > 0" class="badge">{{ friendRequestsCount }}</span>
  </div>
</template>

<script>
import { UserIcon } from 'lucide-vue-next';

export default {
  components: {
    UserIcon
  },
  props: {
    friendRequestsCount: {
      type: Number,
      required: true,
    },
  },
  methods: {
    goToFriendRequests() {
      this.$router.push('/friend-requests');
    },
  },
};
</script>

<style scoped>
.friend-requests-icon {
  position: relative;
  display: inline-block;
  cursor: pointer;
  padding: 8px;
  border-radius: 10px;
  transition: all 0.3s ease;
  touch-action: manipulation; /* Improve touch behavior */
}

.friend-requests-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.friend-requests-icon:active {
  transform: scale(0.95);
}

.friend-requests-icon svg {
  width: 22px;
  height: 22px;
  color: #a0a0a0;
  transition: color 0.3s ease;
}

.friend-requests-icon:hover svg {
  color: #e2e2e2;
}

.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #6366f1;
  color: #ffffff;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.friend-requests-icon:hover .badge {
  transform: scale(1.1);
  background-color: #4f46e5;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .friend-requests-icon {
    padding: 6px;
  }
  
  .friend-requests-icon svg {
    width: 20px;
    height: 20px;
  }
  
  .badge {
    min-width: 16px;
    height: 16px;
    font-size: 9px;
    padding: 1px 5px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .friend-requests-icon {
    padding: 5px;
  }
  
  .friend-requests-icon svg {
    width: 18px;
    height: 18px;
  }
}
</style>
const express = require('express');
const router = express.Router();
const proximityController = require('../../controllers/User/proximityController');
const authController = require('../../controllers/User/authController');

const authMiddleware = require('../../middleware/authMiddleware');
const { celebrate, Joi } = require('celebrate');
const rateLimit = require('express-rate-limit');
const cacheMiddleware = require('../../middleware/cacheMiddleware');

// Rate limiting configuration
const updateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // 5 requests per minute
  message: 'Too many location updates, please try again later'
});

// Validation schemas
const locationUpdateSchema = celebrate({
  params: Joi.object({
    userId: Joi.string().required()
  }),
  body: Joi.object({
    lat: Joi.number().min(-90).max(90).required(),
    lng: Joi.number().min(-180).max(180).required(),
    radius: Joi.number().min(100).max(10000).default(5000)
  })
});

const nearbyUsersSchema = celebrate({
  query: <PERSON>i.object({
    lat: Joi.number().min(-90).max(90),
    lng: Joi.number().min(-180).max(180),
    radius: Joi.number().min(100).max(10000).default(1000),
    limit: Joi.number().min(1).max(100).default(20),
    offset: Joi.number().min(0).default(0)
  })
});

// Routes
router.post(
  '/location/:userId',
  updateLimiter,
  authController.protect,
  authController.restrictTo('user'),
  locationUpdateSchema,
  proximityController.updateLocation
);

router.get(
  '/nearby',
  authMiddleware,
  cacheMiddleware(300), // 5-minute cache
  nearbyUsersSchema,
  proximityController.getNearbyUsers
);

module.exports = router;
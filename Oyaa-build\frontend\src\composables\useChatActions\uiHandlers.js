// frontend/src/composables/useChatActions/uiHandlers.js
import { computed } from 'vue';

export function useUIHandlers(state) {
  const handleUpdateSearch = (query) => {
    state.searchQuery.value = query;
  };

  const filteredMessages = computed(() => {
    if (!state.searchQuery.value) return state.messages.value;
    return state.messages.value.filter((msg) =>
      msg.message.toLowerCase().includes(state.searchQuery.value.toLowerCase())
    );
  });

  const handleReply = (message) => {
    state.replyingMessage.value = {
      id: message.id,
      message: message.message,
      sender_name: message.sender_name || state.friend.value?.username || 'Unknown',
    };
  };

  const clearReply = () => {
    state.replyingMessage.value = null;
  };

  return { handleUpdateSearch, filteredMessages, handleReply, clearReply };
}

// backend/services/groupMediaService.js
const GroupMedia = require('../../models/Groups/GroupMedia');

class GroupMediaService {
  /**
   * Creates a new group media record.
   */
  async uploadGroupMedia({ userId, groupChatId, mediaUrl, publicId, mediaType, thumbnailUrl, duration }) {
    return await GroupMedia.createGroupMedia({ userId, groupChatId, mediaUrl, publicId, mediaType, thumbnailUrl, duration });
  }

  /**
   * Retrieves group media records for a given group chat message.
   */
  async getMediaByGroupChatId(groupChatId) {
    return await GroupMedia.getMediaByGroupChatId(groupChatId);
  }

  /**
   * Retrieves group media records for a given user.
   */
  async getMediaByUserId(userId) {
    return await GroupMedia.getMediaByUserId(userId);
  }

  /**
   * Deletes a group media record.
   */
  async deleteGroupMedia(mediaId) {
    return await GroupMedia.deleteGroupMedia(mediaId);
  }
  
  /**
   * Retrieves all media for a specific group, regardless of message.
   */
  async getAllMediaByGroupId(groupId) {
    return await GroupMedia.getAllMediaByGroupId(groupId);
  }
  
  /**
   * Retrieves media for a list of message IDs.
   */
  async getMediaByMessageIds(messageIds) {
    return await GroupMedia.getMediaByMessageIds(messageIds);
  }
}

module.exports = new GroupMediaService();

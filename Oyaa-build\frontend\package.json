{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src"}, "dependencies": {"@giphy/js-fetch-api": "^5.6.0", "@heroicons/vue": "^2.2.0", "@reduxjs/toolkit": "^2.5.0", "@vueuse/core": "^13.0.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "compressorjs": "^1.2.1", "date-fns": "^4.1.0", "emoji-mart-vue-fast": "^15.0.4", "howler": "^2.2.4", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lodash-es": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-vue-next": "^0.475.0", "overlayscrollbars": "^2.11.1", "react-redux": "^9.2.0", "socket.io-client": "^4.8.1", "uuid": "^11.0.5", "v-click-outside": "^3.2.0", "video.js": "^8.21.0", "virtua": "^0.40.3", "vue": "^3.5.13", "vue-multiselect": "^3.2.0", "vue-router": "^4.5.0", "vue-select": "^4.0.0-beta.6", "vue-virtual-scroll-list": "^2.3.5", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-emoji-picker": "^1.1.8", "vue3-virtual-scroll-list": "^0.2.1", "vue3-virtual-scroller": "^0.2.3", "vuetify": "^3.7.12", "vuex": "^4.1.0", "wavesurfer": "^1.3.4", "wavesurfer.js": "^7.9.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.6", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-vue": "^9.32.0", "postcss": "^8.5.2", "tailwindcss": "^4.0.6", "vite": "^6.0.5", "vue-eslint-parser": "^9.4.3"}}
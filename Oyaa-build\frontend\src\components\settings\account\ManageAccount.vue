<template>
  <div class="manage-account">
    <div class="header">
      <button class="back-button" @click="$router.push('/settings')">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>Manage Account</h1>
    </div>

    <div class="card">
      <form @submit.prevent="updateAccount">
        <div class="form-group">
          <label for="username">New Username</label>
          <div class="input-wrapper">
            <input
              type="text"
              id="username"
              v-model="username"
              :placeholder="usernamePlaceholder"
              @input="validateUsername"
              maxlength="20"
            />
            <span v-if="usernameValid" class="validation-icon valid-icon">✓</span>
            <span v-if="usernameError && usernameError.length > 0" class="validation-icon error-icon">✗</span>
          </div>
          <span class="helper-text">3-20 characters, letters, numbers, and underscores only</span>
          <span v-if="usernameError" class="error-text">{{ usernameError }}</span>
          <span v-if="usernameValid && username.trim()" class="success-text">Username is available!</span>
        </div>

        <div class="form-group">
          <label for="handle">Display Name (Handle)</label>
          <div class="input-wrapper">
            <input
              type="text"
              id="handle"
              v-model="handle"
              :placeholder="handlePlaceholder"
              maxlength="30"
            />
          </div>
          <span class="helper-text">This is how others will see you. Leave empty to use the same as username.</span>
        </div>

        <div class="form-group">
          <label for="password">New Password</label>
          <div class="input-wrapper">
            <input
              type="password"
              id="password"
              v-model="password"
              placeholder="Enter new password"
            />
          </div>
          <span class="helper-text">Leave empty to keep current password</span>
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirm Password</label>
          <div class="input-wrapper">
            <input
              type="password"
              id="confirmPassword"
              v-model="confirmPassword"
              placeholder="Confirm new password"
            />
          </div>
        </div>

        <button type="submit" :disabled="loading || !isFormValid" class="submit-button">
          <span v-if="loading" class="loading-spinner"></span>
          <span>{{ loading ? 'Updating...' : 'Update Account' }}</span>
        </button>

        <div v-if="error" class="error-message">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          {{ error }}
        </div>

        <div v-if="success" class="success-message">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
          {{ success }}
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import axios from 'axios';

// Debounce function for username validation
const debounce = (fn, delay) => {
  let timeoutId;
  return function(...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), delay);
  };
};

export default {
  name: 'ManageAccount',
  setup() {
    const store = useStore();

    // Reactive variables
    const username = ref(''); // Start with an empty string
    const handle = ref(''); // Handle/display name
    const password = ref('');
    const confirmPassword = ref('');
    const loading = ref(false);
    const error = ref('');
    const success = ref('');
    const usernameValid = ref(false);
    const usernameError = ref('');
    const usernameChecking = ref(false);

    // Computed properties for the placeholders
    const currentUsername = computed(() => store.getters['auth/username'] || 'Unknown');
    const currentHandle = computed(() => store.getters['auth/handle'] || currentUsername.value);
    const usernamePlaceholder = computed(() => `Current: ${currentUsername.value}`);
    const handlePlaceholder = computed(() => `Current: ${currentHandle.value}`);

    // Username validation
    const validateUsername = debounce(async () => {
      // Reset validation state
      usernameValid.value = false;
      usernameError.value = '';

      const newUsername = username.value.trim();

      // Skip validation if empty (user wants to keep current username)
      if (!newUsername) {
        return;
      }

      // Skip validation if unchanged
      if (newUsername === currentUsername.value) {
        usernameValid.value = true;
        return;
      }

      // Check username format
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
      if (!usernameRegex.test(newUsername)) {
        usernameError.value = 'Username must be 3-20 characters and contain only letters, numbers, and underscores';
        return;
      }

      // Check username availability
      try {
        usernameChecking.value = true;
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/auth/check-username/${newUsername}`
        );

        if (response.data.available) {
          usernameValid.value = true;
        } else {
          usernameError.value = 'This username is already taken';
        }
      } catch (err) {
        console.error('Error checking username availability:', err);
        usernameError.value = 'Error checking username availability';
      } finally {
        usernameChecking.value = false;
      }
    }, 500);

    // Form validation
    const isFormValid = computed(() => {
      // Basic password validation
      const passwordValid = !password.value || password.value === confirmPassword.value;

      // Username validation
      const usernameValidOrEmpty = !username.value.trim() || usernameValid.value;

      return passwordValid && usernameValidOrEmpty && !usernameChecking.value;
    });

    // Update account function with improved logging and error handling
    const updateAccount = async () => {
      if (!isFormValid.value) {
        error.value = 'Please ensure all fields are correctly filled.';
        return;
      }

      loading.value = true;
      error.value = '';
      success.value = '';

      try {
        const token = store.getters['auth/token'];
        const updates = {};
        const newUsername = username.value.trim();
        const newHandle = handle.value.trim();

        console.log('Current username:', currentUsername.value);
        console.log('Current handle:', currentHandle.value);
        console.log('New username:', newUsername);
        console.log('New handle:', newHandle);

        // Only include username if it's not empty and different from current
        if (newUsername !== '' && newUsername !== currentUsername.value) {
          updates.username = newUsername;
          console.log('Adding username to updates:', newUsername);
        }

        // Only include handle if it's not empty and different from current
        if (newHandle !== '' && newHandle !== currentHandle.value) {
          updates.handle = newHandle;
          console.log('Adding handle to updates:', newHandle);
        }

        // Include password if provided
        if (password.value) {
          updates.password = password.value;
          console.log('Adding password to updates (masked)');
        }

        // If no changes, inform the user and exit
        if (Object.keys(updates).length === 0) {
          console.log('No changes to update');
          success.value = 'No changes to update.';
          loading.value = false;
          return;
        }

        console.log('Sending update request with data:', { ...updates, password: updates.password ? '[MASKED]' : undefined });

        // Log the API URL and token (masked)
        const apiUrl = `${import.meta.env.VITE_API_BASE_URL}/api/users/me`;
        console.log('API URL:', apiUrl);
        console.log('Token (masked):', token ? `${token.substring(0, 10)}...` : 'No token');

        // Send the update request with detailed error handling
        let response;
        try {
          response = await axios.put(
            apiUrl,
            updates,
            {
              headers: { Authorization: `Bearer ${token}` },
              timeout: 10000 // 10 second timeout
            }
          );

          // Log the full response for debugging
          console.log('Full API response:', response);
        } catch (axiosError) {
          console.error('Axios error details:', {
            status: axiosError.response?.status,
            statusText: axiosError.response?.statusText,
            data: axiosError.response?.data,
            message: axiosError.message,
            config: {
              url: axiosError.config?.url,
              method: axiosError.config?.method,
              headers: axiosError.config?.headers,
              data: axiosError.config?.data
            }
          });
          throw axiosError;
        }

        console.log('Update response:', response.data);

        // Update Vuex store if username or handle was changed
        if (updates.username || updates.handle) {
          const updateData = {};
          if (updates.username) updateData.username = updates.username;
          if (updates.handle) updateData.handle = updates.handle || updates.username;

          console.log('Updating store with:', updateData);
          store.commit('auth/UPDATE_USER', updateData);

          // Force a refresh of the auth state
          setTimeout(() => {
            store.commit('auth/REFRESH_AUTH_STATE');
            console.log('Auth state refreshed');
          }, 100);
        }

        success.value = response.data.message || 'Account updated successfully.';
        console.log('Update successful:', success.value);

        // Show a toast notification
        if (store.dispatch) {
          store.dispatch('app/showToast', {
            message: 'Profile updated successfully',
            type: 'success',
            duration: 3000
          });
        }

        // Reset form fields
        username.value = '';
        handle.value = '';
        password.value = '';
        confirmPassword.value = '';
        usernameValid.value = false;
        usernameError.value = '';
      } catch (err) {
        console.error('Error updating account:', err);

        // Extract detailed error information
        const errorResponse = err.response?.data;
        const errorMessage = errorResponse?.message || 'Error updating account';
        const errorDetail = errorResponse?.detail || errorResponse?.error || err.message;

        error.value = errorMessage;
        console.error('Error details:', errorDetail);

        // Show a toast notification
        if (store.dispatch) {
          store.dispatch('app/showToast', {
            message: errorMessage,
            type: 'error',
            duration: 5000
          });
        }
      } finally {
        loading.value = false;
      }
    };

    return {
      username,
      handle,
      password,
      confirmPassword,
      loading,
      error,
      success,
      usernameValid,
      usernameError,
      updateAccount,
      validateUsername,
      isFormValid,
      usernamePlaceholder,
      handlePlaceholder,
    };
  },
};
</script>

<style scoped>
.manage-account {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --success: #10b981;
  --success-bg: rgba(16, 185, 129, 0.15);
  --danger: #ef4444;
  --danger-bg: rgba(239, 68, 68, 0.15);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  position: relative;
}

.header h1 {
  flex: 1;
  text-align: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-button {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  cursor: pointer;
  outline: none;
  padding: 8px;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation; /* Improve touch behavior */
}

.back-button:hover {
  background: var(--accent-tertiary);
  color: var(--accent-primary);
  transform: translateX(-2px);
}

.back-button:active {
  transform: scale(0.95);
}

.card {
  background: var(--bg-tertiary);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: 24px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 15px;
}

.input-wrapper {
  position: relative;
  margin-bottom: 4px;
}

input {
  width: 100%;
  padding: 12px 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  color: var(--text-primary);
  font-size: 15px;
  transition: all var(--transition-fast);
  box-sizing: border-box;
  -webkit-appearance: none; /* Remove default iOS styling */
  appearance: none;
}

input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}

input::placeholder {
  color: var(--text-secondary);
}

.helper-text {
  display: block;
  font-size: 13px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.error-text {
  display: block;
  font-size: 13px;
  color: var(--danger);
  margin-top: 4px;
  animation: fadeIn 0.3s ease;
}

.success-text {
  display: block;
  font-size: 13px;
  color: var(--success);
  margin-top: 4px;
  animation: fadeIn 0.3s ease;
}

.validation-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  animation: fadeIn 0.3s ease;
}

.valid-icon {
  color: var(--success);
}

.error-icon {
  color: var(--danger);
}

.submit-button {
  width: 100%;
  padding: 14px;
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: var(--shadow-sm);
  touch-action: manipulation; /* Improve touch behavior */
  -webkit-appearance: none; /* Remove default iOS styling */
  appearance: none;
}

.submit-button:hover:not(:disabled) {
  background: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.submit-button:active:not(:disabled) {
  transform: scale(0.98);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message, .success-message {
  margin-top: 16px;
  padding: 12px 16px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  animation: fadeIn 0.3s ease;
}

.error-message {
  background: var(--danger-bg);
  color: var(--danger);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.success-message {
  background: var(--success-bg);
  color: var(--success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .manage-account {
    padding: 16px;
  }

  .header {
    margin-bottom: 24px;
  }

  .header h1 {
    font-size: 18px;
  }

  .back-button {
    padding: 6px;
  }

  .back-button svg {
    width: 22px;
    height: 22px;
  }

  .card {
    padding: 20px;
    border-radius: 14px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  label {
    font-size: 14px;
  }

  input {
    padding: 10px 14px;
    font-size: 14px;
    border-radius: 8px;
  }

  .helper-text {
    font-size: 12px;
  }

  .submit-button {
    padding: 12px;
    font-size: 15px;
    border-radius: 8px;
  }

  .error-message, .success-message {
    padding: 10px 14px;
    font-size: 13px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .manage-account {
    padding: 12px;
  }

  .header h1 {
    font-size: 16px;
  }

  .card {
    padding: 16px;
  }

  input {
    padding: 8px 12px;
    font-size: 13px;
  }

  .submit-button {
    padding: 10px;
    font-size: 14px;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .back-button,
  input,
  .submit-button {
    transition: none;
  }

  .loading-spinner {
    animation: none;
  }

  .error-message, .success-message {
    animation: none;
  }
}
</style>
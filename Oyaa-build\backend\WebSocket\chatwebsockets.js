// backend/chatwebsockets.js
const chatService = require('../services/User/chatService');
// Import the image link processor
const { processImageMessage } = require('../services/User/imageLinkHandler');

function getChatRoomName(userId1, userId2) {
  const sortedIds = [userId1, userId2].sort((a, b) => a.localeCompare(b));
  return `chat_${sortedIds[0]}_${sortedIds[1]}`;
}

module.exports = (io) => {
  const chatNamespace = io.of('/chat'); // Namespace for person-to-person chat

  chatNamespace.on('connection', (socket) => {
    console.log('New WebSocket connection for chat:', socket.id);

    socket.on('joinChat', ({ userId, targetUserId }) => {
      if (!userId || !targetUserId) {
        console.error('joinChat event missing userId or targetUserId');
        return;
      }
      const room = getChatRoomName(userId, targetUserId);
      socket.join(room);
      console.log(`User ${userId} joined chat room ${room}`);
    });

    socket.on('joinPersonalRoom', (userId) => {
      if (!userId) {
        console.error('joinPersonalRoom event missing userId');
        return;
      }
      const personalRoom = `user_${userId}`;
      socket.join(personalRoom);
      console.log(`User ${userId} joined personal room ${personalRoom}`);
    });

    socket.on('leaveChat', ({ userId, targetUserId }) => {
      if (!userId || !targetUserId) {
        console.error('leaveChat event missing userId or targetUserId');
        return;
      }
      const room = getChatRoomName(userId, targetUserId);
      socket.leave(room);
      console.log(`User ${userId} left chat room ${room}`);
    });

   socket.on('sendMessage', async (data) => {
    console.log('Received message event with data:', data);
    const { sender_id, receiver_id, message, media, reply } = data;
    
    if (!sender_id || !receiver_id || (!message && !media)) {
      console.error('sendMessage event missing required fields:', data);
      socket.emit('error', { message: 'Missing required message fields' });
      return;
    }
    
    try {
      console.log(`Saving message from ${sender_id} to ${receiver_id}`);
      const savedMessage = await chatService.sendMessage(sender_id, receiver_id, message, reply, media);
      console.log('Message saved to database:', savedMessage);
      
      // Process message for image links if needed
      const processedMessage = processImageMessage(savedMessage);
      
      // Get the chat room name
      const room = getChatRoomName(sender_id, receiver_id);
      
      // Broadcast the message to the room
      console.log(`Broadcasting message to room: ${room}`);
      chatNamespace.to(room).emit('newMessage', processedMessage);
      
      // Emit to specific users' personal rooms for last message updates
      const senderRoom = `user_${sender_id}`;
      const receiverRoom = `user_${receiver_id}`;

      // Emit last message update for sender
      console.log(`Sending lastMessageUpdate to sender room: ${senderRoom}`);
      chatNamespace.to(senderRoom).emit('lastMessageUpdate', {
        friendId: receiver_id,
        message: processedMessage,
      });
      
      // Emit last message update for receiver
      console.log(`Sending lastMessageUpdate to receiver room: ${receiverRoom}`);
      chatNamespace.to(receiverRoom).emit('lastMessageUpdate', {
        friendId: sender_id,
        message: processedMessage,
      });
      
      console.log('Message handling complete');
    } catch (error) {
      console.error('Error sending message:', error);
      socket.emit('error', { message: 'Failed to send message: ' + error.message });
    }
  });
    
    socket.on('typing', ({ senderId, receiverId }) => {
      if (!senderId || !receiverId) {
        console.error('typing event missing senderId or receiverId');
        return;
      }
      const room = getChatRoomName(senderId, receiverId);
      socket.to(room).emit('typing', { userId: senderId });
      console.log(`User ${senderId} is typing in room ${room}`);
    });

    socket.on('stopTyping', ({ senderId, receiverId }) => {
      if (!senderId || !receiverId) {
        console.error('stopTyping event missing senderId or receiverId');
        return;
      }
      const room = getChatRoomName(senderId, receiverId);
      socket.to(room).emit('stopTyping', { userId: senderId });
      console.log(`User ${senderId} stopped typing in room ${room}`);
    });
    

    socket.on('disconnect', () => {
      console.log('WebSocket disconnected for chat:', socket.id);
    });
  });
};

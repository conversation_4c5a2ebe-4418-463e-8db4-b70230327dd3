// Remove JWT import as we're fully migrating to Firebase
const { admin, initializeFirebaseAdmin } = require('../config/firebase.config');
const logger = require('../utils/logger');
const db = require('../utils/db');

// Initialize Firebase Admin SDK
const firebaseAdmin = initializeFirebaseAdmin();

const authMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      logger.warn('authMiddleware: No valid Authorization header');
      return res.status(401).json({ message: 'No token provided, please log in.' });
    }

    const token = authHeader.split(' ')[1];

    try {
      // Verify Firebase token
      const decodedFirebaseToken = await firebaseAdmin.auth().verifyIdToken(token);
      const firebaseUid = decodedFirebaseToken.uid;

      logger.info(`authMiddleware: Firebase token verified for UID: ${firebaseUid}`);

      // Look up the user in the database to get the numeric user ID
      try {
        const userQuery = 'SELECT id, username, email, avatar, handle FROM users WHERE firebase_uid = $1';
        const userResult = await db.query(userQuery, [firebaseUid]);

        if (userResult.rows.length === 0) {
          logger.warn(`authMiddleware: User with Firebase UID ${firebaseUid} not found in database`);
          return res.status(403).json({
            message: 'Please complete registration first',
            needsRegistration: true,
            isNewUser: true,
            firebaseUid: firebaseUid,
            email: decodedFirebaseToken.email
          });
        }

        const user = userResult.rows[0];

        // Set user information in the request
        req.user = {
          userId: user.id,
          firebaseUid: firebaseUid,
          email: user.email || decodedFirebaseToken.email,
          username: user.username,
          avatar: user.avatar || decodedFirebaseToken.picture,
          handle: user.handle || user.username, // Fallback to username if handle is not set
          name: decodedFirebaseToken.name,
          picture: decodedFirebaseToken.picture,
          firebase: true // Flag to indicate this is a Firebase user
        };

        logger.info(`authMiddleware: User found in database with ID: ${user.id}`);
        next();
      } catch (dbError) {
        logger.error('authMiddleware: Database error:', dbError);
        throw new Error('Error retrieving user information. Please try again.');
      }
    } catch (error) {
      logger.error('Firebase token verification error:', error);
      throw new Error('Invalid or expired token. Please log in again.');
    }
  } catch (error) {
    logger.error('Auth Middleware error:', error);
    res.status(401).json({ message: 'Unauthorized access. ' + error.message });
  }
};

module.exports = authMiddleware;

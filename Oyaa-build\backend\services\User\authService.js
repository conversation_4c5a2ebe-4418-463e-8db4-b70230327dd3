// Remove JWT import as we're fully migrating to Firebase
const AuthModel = require('../../models/User/authModel');
const GroupModel = require('../../models/Groups/groupModel');
const GroupService = require('../Groups/groupService');
const groupMembersService = require('../Groups/groupMembersService');
const { admin, initializeFirebaseAdmin } = require('../../config/firebase.config');
const db = require('../../utils/db');
const logger = require('../../utils/logger');

// Initialize Firebase Admin SDK
const firebaseAdmin = initializeFirebaseAdmin();

class AuthService {
  /**
   * Register method - creates a user in our database
   * This should be called after creating the user in Firebase
   */
  async register(username, email, firebaseUid, avatar, handle = null) {
    // Check for existing username or email
    const existingUser = await AuthModel.findUserByUsername(username);
    if (existingUser) {
      throw new Error('Username already exists');
    }

    const existingEmail = await AuthModel.findUserByEmail(email);
    if (existingEmail) {
      throw new Error('Email already exists');
    }

    // Create the new user
    try {
      // Use the createFirebaseUser method instead of the legacy createUser
      const newUser = await AuthModel.createFirebaseUser(firebaseUid, username, email, avatar, handle);

      // Add user to World Chat
      await this.addUserToWorldChat(newUser.id);

      return {
        user: {
          id: newUser.id,
          username: newUser.username,
          email: newUser.email,
          avatar: newUser.avatar,
          firebaseUid: newUser.firebase_uid,
          handle: newUser.handle
        }
      };
    } catch (err) {
      if (err.code === '23505') { // PostgreSQL unique constraint violation
        if (err.constraint === 'unique_lower_username') {
          throw new Error('Username already exists');
        } else if (err.constraint === 'unique_lower_email') {
          throw new Error('Email already exists');
        } else if (err.constraint === 'unique_lower_handle') {
          throw new Error('Handle already exists');
        }
      }
      throw err; // Re-throw other errors
    }
  }

  /**
   * Login method - finds a user by Firebase UID
   * This should be called after authenticating with Firebase
   */
  async login(firebaseUid) {
    const user = await AuthModel.findUserByFirebaseUid(firebaseUid);
    if (!user) {
      throw new Error('User not found. Please register first.');
    }

    return {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        firebaseUid: user.firebase_uid,
        handle: user.handle || user.username // Fallback to username if handle is not set
      }
    };
  }

  /**
   * Find a user by Firebase UID
   * @param {string} firebaseUid - Firebase UID
   * @returns {Object} - User object
   */
  async findUserByFirebaseUid(firebaseUid) {
    return await AuthModel.findUserByFirebaseUid(firebaseUid);
  }

  /**
   * Register a user with Firebase authentication
   * @param {string} firebaseUid - Firebase user ID
   * @param {string} email - User's email
   * @param {string} username - User's chosen username
   * @param {string} avatar - User's avatar URL
   * @param {string} handle - User's display name/handle (optional)
   * @returns {Object} - New user object
   */
  async firebaseRegister(firebaseUid, email, username, avatar, handle = null) {
    try {
      // First check if the user already exists by Firebase UID
      const existingUser = await AuthModel.findUserByFirebaseUid(firebaseUid);

      if (existingUser) {
        // User exists, update their username and handle if provided
        return this.updateUsername(existingUser.id, username, handle);
      }

      // Check if email exists but with a different Firebase UID
      const existingEmail = await AuthModel.findUserByEmail(email);
      if (existingEmail) {
        // Update the Firebase UID for this email
        await this.updateUserFirebaseUid(existingEmail.id, firebaseUid);

        // Update the username and handle
        return this.updateUsername(existingEmail.id, username, handle);
      }

      // If user doesn't exist, register them
      return this.register(username, email, firebaseUid, avatar, handle);
    } catch (err) {
      logger.error('Firebase register error:', err);
      throw err;
    }
  }

  /**
   * Update a user's username
   * @param {number} userId - User ID in our database
   * @param {string} username - New username
   * @param {string} handle - New handle (optional, defaults to username)
   * @returns {Object} - Updated user
   */
  async updateUsername(userId, username, handle = null) {
    try {
      // Check if the username is already taken
      const existingUsername = await AuthModel.findUserByUsername(username);
      if (existingUsername && existingUsername.id !== userId) {
        throw new Error('Username already exists');
      }

      // Use the provided handle or default to username
      const userHandle = handle || username;

      // Update the username and handle in the database
      const result = await db.query(
        'UPDATE users SET username = $1, handle = $2 WHERE id = $3 RETURNING id, username, email, avatar, firebase_uid, handle',
        [username, userHandle, userId]
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      logger.info(`Updated username for user ${userId} to ${username} and handle to ${userHandle}`);

      return {
        user: {
          id: result.rows[0].id,
          username: result.rows[0].username,
          email: result.rows[0].email,
          avatar: result.rows[0].avatar,
          firebaseUid: result.rows[0].firebase_uid,
          handle: result.rows[0].handle
        }
      };
    } catch (err) {
      logger.error('Error updating username:', err);
      throw err;
    }
  }

  /**
   * Login a user with Firebase authentication
   * @param {string} firebaseUid - Firebase user ID
   * @returns {Object} - User object
   */
  async firebaseLogin(firebaseUid) {
    // This method now just calls the main login method
    return this.login(firebaseUid);
  }

  /**
   * Verify a Firebase ID token
   * @param {string} idToken - Firebase ID token
   * @returns {Object} - Firebase user claims
   */
  async verifyFirebaseToken(idToken) {
    try {
      const decodedToken = await firebaseAdmin.auth().verifyIdToken(idToken);
      return decodedToken;
    } catch (error) {
      logger.error('Error verifying Firebase token:', error);
      throw new Error('Invalid Firebase token');
    }
  }

  /**
   * Check if a username is available
   * @param {string} username - Username to check
   * @returns {boolean} - True if username is available
   */
  async checkUsernameAvailability(username) {
    const existingUser = await AuthModel.findUserByUsername(username);
    return !existingUser;
  }

  /**
   * Find a user by email
   * @param {string} email - User's email
   * @returns {Object} - User object or null
   */
  async findUserByEmail(email) {
    return await AuthModel.findUserByEmail(email);
  }

  /**
   * Update a user's Firebase UID
   * @param {number} userId - User ID in our database
   * @param {string} firebaseUid - Firebase UID to set
   * @returns {Object} - Updated user
   */
  async updateUserFirebaseUid(userId, firebaseUid) {
    try {
      // Update the user's Firebase UID in the database
      const result = await db.query(
        'UPDATE users SET firebase_uid = $1 WHERE id = $2 RETURNING id, username, email, avatar, firebase_uid, handle',
        [firebaseUid, userId]
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      logger.info(`Updated Firebase UID for user ${userId} to ${firebaseUid}`);
      return result.rows[0];
    } catch (err) {
      logger.error('Error updating Firebase UID:', err);
      throw err;
    }
  }

  /**
   * Add a user to the World Chat group
   * @param {number} userId - User ID
   */
  async addUserToWorldChat(userId) {
    // Ensure World Chat exists with the correct name, description, and is_world_chat flag
    let worldChatGroup = await GroupModel.getGroupByName('World Chat');
    if (!worldChatGroup) {
      const defaultGroupAvatar = '/Avatar/groups-Avatar/world-chat.svg';
      // Pass an empty array for tags and true for isWorldChat
      worldChatGroup = await GroupService.createGroup('World Chat', null, 'Just have fun', defaultGroupAvatar, [], true);
    } else {
      if (worldChatGroup.description !== 'Just have fun' || !worldChatGroup.is_world_chat) {
        await db.query(
          'UPDATE groups SET description = $1, is_world_chat = $2 WHERE id = $3',
          ['Just have fun', true, worldChatGroup.id]
        );
        worldChatGroup.description = 'Just have fun';
        worldChatGroup.is_world_chat = true;
      }
    }

    // Add the new user to World Chat
    await groupMembersService.addMember(worldChatGroup.id, userId, 'member');
  }
}

module.exports = new AuthService();
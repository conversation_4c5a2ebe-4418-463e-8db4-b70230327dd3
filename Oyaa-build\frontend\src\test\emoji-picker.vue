<template>
    <div class="emoji-picker">
      <div class="emoji-categories">
        <button 
          v-for="category in categories" 
          :key="category.name"
          class="category-button"
          :class="{ active: activeCategory === category.name }"
          @click="activeCategory = category.name"
          :aria-label="`${category.name} emojis`"
        >
          <span class="category-icon">{{ category.icon }}</span>
        </button>
      </div>
      <div class="emoji-search">
        <input 
          type="text" 
          placeholder="Search emojis..." 
          v-model="searchQuery"
          aria-label="Search emojis"
        />
      </div>
      <div class="emoji-grid">
        <button 
          v-for="emoji in filteredEmojis" 
          :key="emoji"
          class="emoji-button"
          @click="selectEmoji(emoji)"
          :aria-label="`${emoji} emoji`"
        >
          {{ emoji }}
        </button>
      </div>
    </div>
  </template>
  
  <script>
  import { ref, computed } from 'vue';
  
  export default {
    name: 'EmojiPicker',
    emits: ['select-emoji'],
    setup(props, { emit }) {
      // Sample emoji data - in a real app, this would be more comprehensive
      const categories = [
        { name: 'Recent', icon: '🕒' },
        { name: 'Smileys', icon: '😀' },
        { name: 'People', icon: '👋' },
        { name: 'Animals', icon: '🐶' },
        { name: 'Food', icon: '🍔' },
        { name: 'Activities', icon: '⚽' },
        { name: 'Travel', icon: '✈️' },
        { name: 'Objects', icon: '💡' },
        { name: 'Symbols', icon: '❤️' },
        { name: 'Flags', icon: '🏳️' }
      ];
  
      const emojisByCategory = {
        'Recent': ['👍', '❤️', '😊', '🙏', '😂'],
        'Smileys': ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍'],
        'People': ['👋', '👌', '✌️', '🤞', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏'],
        'Animals': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼'],
        'Food': ['🍏', '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇'],
        'Activities': ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉'],
        'Travel': ['✈️', '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓'],
        'Objects': ['💡', '📱', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️'],
        'Symbols': ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '❣️'],
        'Flags': ['🏳️', '🏴', '🏁', '🚩', '🏳️‍🌈', '🏳️‍⚧️', '🇺🇳']
      };
  
      const activeCategory = ref('Smileys');
      const searchQuery = ref('');
  
      const filteredEmojis = computed(() => {
        if (searchQuery.value) {
          // In a real implementation, this would search through emoji metadata
          return Object.values(emojisByCategory).flat().filter(emoji => 
            emoji.includes(searchQuery.value)
          );
        }
        return emojisByCategory[activeCategory.value];
      });
  
      const selectEmoji = (emoji) => {
        console.log('Emoji selected:', emoji);
        emit('select-emoji', emoji);
      };
  
      return {
        categories,
        activeCategory,
        searchQuery,
        filteredEmojis,
        selectEmoji
      };
    }
  };
  </script>
  
  <style>
  .emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 0;
    width: 320px;
    max-width: 100%;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: 0 -4px 12px var(--shadow);
    margin-bottom: 8px;
    z-index: 10;
    animation: fadeIn 0.2s ease;
  }
  
  .emoji-categories {
    display: flex;
    overflow-x: auto;
    padding: 8px;
    border-bottom: 1px solid var(--border);
  }
  
  .category-button {
    flex: 0 0 auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .category-button:hover, .category-button.active {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .emoji-search {
    padding: 8px;
    border-bottom: 1px solid var(--border);
  }
  
  .emoji-search input {
    width: 100%;
    padding: 8px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border);
    border-radius: 4px;
    outline: none;
  }
  
  .emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
    padding: 8px;
    max-height: 200px;
    overflow-y: auto;
  }
  
  .emoji-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 20px;
    transition: var(--transition);
  }
  
  .emoji-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @media (max-width: 767px) {
    .emoji-picker {
      width: 100%;
    }
    
    .emoji-grid {
      grid-template-columns: repeat(7, 1fr);
    }
  }
  </style>
  
  
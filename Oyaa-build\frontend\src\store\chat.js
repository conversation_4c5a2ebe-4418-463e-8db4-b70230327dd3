export default {
  namespaced: true,
  state: {
    lastChats: {}, // Object with friendId as keys and full chat objects as values
    typingStatuses: {},
  },
  mutations: {
    // Set initial last chats from server fetch
    SET_LAST_CHATS(state, chats) {
      state.lastChats = chats.reduce((acc, chat) => {
        acc[chat.friend_id] = chat;
        return acc;
      }, {});
    },
    // Update last chat with full message object from WebSocket
    UPDATE_LAST_CHAT(state, { friendId, message }) {
      state.lastChats = {
        ...state.lastChats,
        [friendId]: message,
      };
    },
    CLEAR_UNREAD(state, friendId) {
      if (state.lastChats[friendId]) {
        state.lastChats = {
          ...state.lastChats,
          [friendId]: {
            ...state.lastChats[friendId],
            unreadCount: 0,
          },
        };
      }
    },
    SET_TYPING_STATUS(state, { friendId, status }) {
      state.typingStatuses = { ...state.typingStatuses, [friendId]: status };
    },
  },
  actions: {
    updateLastChat({ commit }, payload) {
      commit('UPDATE_LAST_CHAT', payload);
    },
    clearUnread({ commit }, friendId) {
      commit('CLEAR_UNREAD', friendId);
    },
    updateTypingStatus({ commit }, payload) {
      commit('SET_TYPING_STATUS', payload);
    },
  },
};
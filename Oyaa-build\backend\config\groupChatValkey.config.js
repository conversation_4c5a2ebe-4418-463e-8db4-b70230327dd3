/**
 * VALKEY SERVICE COMPLETELY DISABLED
 *
 * This file provides mock implementations of all Valkey functions.
 * No actual connection to Valkey is attempted.
 */

const logger = require('../utils/logger.js');

logger.info('⚠️ Group Chat Valkey service is COMPLETELY DISABLED - using mock implementations');

// Create mock clients that don't do anything
const groupChatClient = {
  on: () => {}
};

const groupChatPubSubClient = {
  on: () => {}
};

// Helper functions for common operations (all mocked)
const groupChatValkeyGet = async (key) => {
  logger.debug(`[MOCK] Getting key ${key} from Group Chat Valkey`);
  return null;
};

const groupChatValkeySet = async (key, value, expireSeconds = null) => {
  logger.debug(`[MOCK] Setting key ${key} in Group Chat Valkey`);
  return true;
};

const groupChatValkeyDel = async (key) => {
  logger.debug(`[MOCK] Deleting key ${key} from Group Chat Valkey`);
  return true;
};

// List operations for message caching (all mocked)
const groupChatValkeyLPush = async (key, value) => {
  logger.debug(`[MOCK] Pushing to list ${key} in Group Chat Valkey`);
  return true;
};

const groupChatValkeyLRange = async (key, start, stop) => {
  logger.debug(`[MOCK] Getting range from list ${key} in Group Chat Valkey`);
  return [];
};

const groupChatValkeyLTrim = async (key, start, stop) => {
  logger.debug(`[MOCK] Trimming list ${key} in Group Chat Valkey`);
  return true;
};

// Set operations for online users tracking (all mocked)
const groupChatValkeySAdd = async (key, member) => {
  logger.debug(`[MOCK] Adding to set ${key} in Group Chat Valkey`);
  return true;
};

const groupChatValkeySRem = async (key, member) => {
  logger.debug(`[MOCK] Removing from set ${key} in Group Chat Valkey`);
  return true;
};

const groupChatValkeySMembers = async (key) => {
  logger.debug(`[MOCK] Getting members from set ${key} in Group Chat Valkey`);
  return [];
};

// Publish and subscribe methods (all mocked)
const groupChatValkeyPublish = async (channel, message) => {
  logger.debug(`[MOCK] Publishing to channel ${channel} in Group Chat Valkey`);
  return true;
};

const groupChatValkeySubscribe = async (channel, callback) => {
  logger.debug(`[MOCK] Subscribing to channel ${channel} in Group Chat Valkey`);
  return true;
};

// Function to synchronize the cache with the database for a specific group (mocked)
const groupChatValkeySyncGroupCache = async (groupId, omniQuery, db) => {
  logger.info(`[MOCK] Synchronizing cache for group ${groupId} (Valkey service disabled)`);
  return true;
};

module.exports = {
  // Clients
  groupChatClient,
  groupChatPubSubClient,

  // Basic operations
  groupChatValkeyGet,
  groupChatValkeySet,
  groupChatValkeyDel,

  // List operations for message caching
  groupChatValkeyLPush,
  groupChatValkeyLRange,
  groupChatValkeyLTrim,

  // Set operations for online users tracking
  groupChatValkeySAdd,
  groupChatValkeySRem,
  groupChatValkeySMembers,

  // Pub/Sub operations
  groupChatValkeyPublish,
  groupChatValkeySubscribe,

  // Cache management
  groupChatValkeySyncGroupCache
};

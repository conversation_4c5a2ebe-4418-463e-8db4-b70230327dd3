<script lang="ts">
	import { onMount } from 'svelte';
	import type { MediaFile } from '../types/chat';

	export let media: MediaFile;

	let mediaElement: HTMLElement;
	let isVisible = false;
	let isLoaded = false;
	let isError = false;
	let userInteracted = false;
	let isLoading = false;

	onMount(() => {
		// Use Intersection Observer only for visibility detection
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						isVisible = true;
						// Don't auto-load media, wait for user interaction
						observer.unobserve(entry.target);
					}
				});
			},
			{
				rootMargin: '100px' // Detect visibility early but don't load
			}
		);

		if (mediaElement) {
			observer.observe(mediaElement);
		}

		return () => {
			observer.disconnect();
		};
	});

	function handleUserInteraction() {
		if (!userInteracted) {
			userInteracted = true;
			isLoading = true;
		}
	}

	function handleLoad() {
		isLoaded = true;
	}

	function handleError() {
		isError = true;
	}

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	function getFileIcon(type: string): string {
		switch (type) {
			case 'image': return '🖼️';
			case 'audio': return '🎵';
			case 'video': return '🎬';
			case 'file': return '📄';
			default: return '📎';
		}
	}
</script>

<div class="media-content" bind:this={mediaElement}>
	{#if media.type === 'image'}
		<div class="image-container">
			{#if userInteracted}
				<img
					src={media.url}
					alt={media.name}
					class="media-image"
					class:loaded={isLoaded}
					class:error={isError}
					on:load={handleLoad}
					on:error={handleError}
					loading="lazy"
				/>
				{#if isLoading && !isLoaded && !isError}
					<div class="loading-overlay">
						<div class="loading-spinner"></div>
						<span>Loading image...</span>
					</div>
				{/if}
				{#if isError}
					<div class="error-placeholder">
						<span>❌ Failed to load image</span>
					</div>
				{/if}
			{:else}
				<button class="media-placeholder image-placeholder" on:click={handleUserInteraction}>
					<div class="placeholder-content">
						<span class="placeholder-icon">🖼️</span>
						<span class="placeholder-text">Click to load image</span>
						<span class="placeholder-name">{media.name}</span>
						{#if media.size}
							<span class="placeholder-size">{formatFileSize(media.size)}</span>
						{/if}
					</div>
				</button>
			{/if}
		</div>
	{:else if media.type === 'video'}
		<div class="video-container">
			{#if userInteracted}
				<video
					src={media.url}
					controls
					preload="metadata"
					class="media-video"
					class:loaded={isLoaded}
					class:error={isError}
					on:loadeddata={handleLoad}
					on:error={handleError}
				>
					<track kind="captions" />
				</video>
				{#if isLoading && !isLoaded && !isError}
					<div class="loading-overlay">
						<div class="loading-spinner"></div>
						<span>Loading video...</span>
					</div>
				{/if}
			{:else}
				<button class="media-placeholder video-placeholder" on:click={handleUserInteraction}>
					<div class="placeholder-content">
						<span class="placeholder-icon">🎬</span>
						<span class="placeholder-text">Click to load video</span>
						<span class="placeholder-name">{media.name}</span>
						{#if media.size}
							<span class="placeholder-size">{formatFileSize(media.size)}</span>
						{/if}
					</div>
				</button>
			{/if}
		</div>
	{:else if media.type === 'audio'}
		<div class="audio-container">
			{#if userInteracted}
				<audio
					src={media.url}
					controls
					preload="metadata"
					class="media-audio"
					on:loadeddata={handleLoad}
					on:error={handleError}
				>
					Your browser does not support the audio element.
				</audio>
				{#if isLoading && !isLoaded && !isError}
					<div class="loading-overlay">
						<div class="loading-spinner"></div>
						<span>Loading audio...</span>
					</div>
				{/if}
			{:else}
				<button class="media-placeholder audio-placeholder" on:click={handleUserInteraction}>
					<div class="placeholder-content">
						<span class="placeholder-icon">🎵</span>
						<span class="placeholder-text">Click to load audio</span>
						<span class="placeholder-name">{media.name}</span>
						{#if media.duration}
							<span class="placeholder-duration">{Math.floor(media.duration / 60)}:{(media.duration % 60).toString().padStart(2, '0')}</span>
						{/if}
					</div>
				</button>
			{/if}
		</div>
	{:else if media.type === 'file'}
		<div class="file-container">
			<a href={media.url} download={media.name} class="file-link">
				<div class="file-info">
					<span class="file-icon">{getFileIcon(media.type)}</span>
					<div class="file-details">
						<span class="file-name">{media.name}</span>
						{#if media.size}
							<span class="file-size">{formatFileSize(media.size)}</span>
						{/if}
					</div>
				</div>
			</a>
		</div>
	{/if}
</div>

<style>
	.media-content {
		margin: 8px 0;
		border-radius: 12px;
		overflow: hidden;
		position: relative;
	}

	.image-container {
		position: relative;
		max-width: 300px;
		max-height: 300px;
	}

	.media-image {
		width: 100%;
		height: auto;
		max-width: 100%;
		max-height: 300px;
		object-fit: cover;
		border-radius: 8px;
		transition: opacity 0.3s ease;
		opacity: 0;
	}

	.media-image.loaded {
		opacity: 1;
	}

	.video-container {
		position: relative;
		max-width: 400px;
	}

	.media-video {
		width: 100%;
		max-height: 300px;
		border-radius: 8px;
	}

	.audio-container {
		min-width: 250px;
	}

	.media-audio {
		width: 100%;
		height: 40px;
	}

	.file-container {
		background: rgba(0, 0, 0, 0.05);
		border-radius: 8px;
		padding: 12px;
	}

	.file-link {
		text-decoration: none;
		color: inherit;
		display: block;
	}

	.file-info {
		display: flex;
		align-items: center;
		gap: 12px;
	}

	.file-icon {
		font-size: 24px;
	}

	.file-details {
		display: flex;
		flex-direction: column;
		gap: 2px;
	}

	.file-name {
		font-weight: 500;
		font-size: 0.9rem;
	}

	.file-size {
		font-size: 0.75rem;
		opacity: 0.7;
	}

	.media-placeholder {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		gap: 8px;
		padding: 20px;
		background: rgba(0, 123, 255, 0.05);
		border: 2px dashed rgba(0, 123, 255, 0.3);
		border-radius: 8px;
		min-height: 120px;
		cursor: pointer;
		transition: all 0.2s ease;
		font-family: inherit;
		width: 100%;
		max-width: 300px;
	}

	.media-placeholder:hover {
		background: rgba(0, 123, 255, 0.1);
		border-color: rgba(0, 123, 255, 0.5);
		transform: translateY(-2px);
	}

	.placeholder-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 6px;
		text-align: center;
	}

	.placeholder-icon {
		font-size: 32px;
		margin-bottom: 4px;
	}

	.placeholder-text {
		font-size: 0.9rem;
		font-weight: 500;
		color: #007bff;
	}

	.placeholder-name {
		font-size: 0.8rem;
		color: #666;
		font-weight: 400;
	}

	.placeholder-size,
	.placeholder-duration {
		font-size: 0.7rem;
		color: #999;
	}

	.loading-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.9);
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		gap: 8px;
		border-radius: 8px;
		z-index: 10;
	}

	.error-placeholder {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		gap: 8px;
		padding: 20px;
		background: rgba(220, 53, 69, 0.1);
		border-radius: 8px;
		min-height: 100px;
		color: #dc3545;
	}

	.loading-spinner {
		width: 20px;
		height: 20px;
		border: 2px solid #f3f3f3;
		border-top: 2px solid #007bff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}


</style>

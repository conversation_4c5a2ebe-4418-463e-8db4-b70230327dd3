<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import type { Message } from '../types/chat';
	import MediaContent from './MediaContent.svelte';

	export let message: Message;

	const dispatch = createEventDispatcher<{
		heightChange: { id: string; height: number };
	}>();

	let messageElement: HTMLDivElement;
	let previousHeight = 0;
	let resizeObserver: ResizeObserver;
	let heightReportTimeout: number;

	onMount(() => {
		// Use ResizeObserver to track height changes with debouncing
		resizeObserver = new ResizeObserver(entries => {
			for (const entry of entries) {
				const newHeight = entry.contentRect.height;
				if (Math.abs(newHeight - previousHeight) > 1) { // Only update if significant change
					previousHeight = newHeight;

					// Debounce height changes to reduce updates
					if (heightReportTimeout) clearTimeout(heightReportTimeout);
					heightReportTimeout = setTimeout(() => {
						dispatch('heightChange', {
							id: message.id,
							height: newHeight
						});
					}, 10);
				}
			}
		});

		if (messageElement) {
			resizeObserver.observe(messageElement);
		}

		return () => {
			resizeObserver?.disconnect();
			if (heightReportTimeout) clearTimeout(heightReportTimeout);
		};
	});

	function formatTime(timestamp: Date): string {
		return timestamp.toLocaleTimeString([], {
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	function formatDate(timestamp: Date): string {
		const today = new Date();
		const messageDate = new Date(timestamp);

		if (messageDate.toDateString() === today.toDateString()) {
			return 'Today';
		}

		const yesterday = new Date(today);
		yesterday.setDate(yesterday.getDate() - 1);

		if (messageDate.toDateString() === yesterday.toDateString()) {
			return 'Yesterday';
		}

		return messageDate.toLocaleDateString();
	}

	$: isCurrentUser = message.sender.id === 'current-user';
	$: hasMedia = message.media && message.type !== 'text';
</script>

<div
	class="message-item {isCurrentUser ? 'current-user' : 'other-user'}"
	bind:this={messageElement}
>
	<div class="message-content">
		{#if !isCurrentUser}
			<div class="avatar">
				<img
					src={message.sender.avatar || '/default-avatar.png'}
					alt={message.sender.name}
					loading="lazy"
				/>
			</div>
		{/if}

		<div class="message-bubble">
			{#if !isCurrentUser}
				<div class="sender-name">{message.sender.name}</div>
			{/if}

			{#if hasMedia && message.media}
				<MediaContent media={message.media} />
			{/if}

			{#if message.text.trim()}
				<div class="message-text">{message.text}</div>
			{/if}

			<div class="message-meta">
				<span class="timestamp">{formatTime(message.timestamp)}</span>
				{#if message.status}
					<span class="status status-{message.status}">
						{#if message.status === 'sent'}✓{/if}
						{#if message.status === 'delivered'}✓✓{/if}
						{#if message.status === 'read'}✓✓{/if}
						{#if message.status === 'failed'}⚠{/if}
					</span>
				{/if}
			</div>
		</div>

		{#if isCurrentUser}
			<div class="avatar">
				<img
					src={message.sender.avatar || '/default-avatar.png'}
					alt={message.sender.name}
					loading="lazy"
				/>
			</div>
		{/if}
	</div>
</div>

<style>
	.message-item {
		padding: 8px 16px;
		margin-bottom: 4px;
		will-change: transform;
	}

	.message-content {
		display: flex;
		align-items: flex-end;
		gap: 8px;
		max-width: 80%;
	}

	.current-user .message-content {
		margin-left: auto;
		flex-direction: row-reverse;
	}

	.avatar {
		width: 32px;
		height: 32px;
		border-radius: 50%;
		overflow: hidden;
		flex-shrink: 0;
	}

	.avatar img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.message-bubble {
		background: #f1f3f4;
		border-radius: 18px;
		padding: 12px 16px;
		position: relative;
		word-wrap: break-word;
		max-width: 100%;
	}

	.current-user .message-bubble {
		background: #007bff;
		color: white;
	}

	.sender-name {
		font-size: 0.75rem;
		font-weight: 600;
		color: #666;
		margin-bottom: 4px;
	}

	.current-user .sender-name {
		color: rgba(255, 255, 255, 0.8);
	}

	.message-text {
		line-height: 1.4;
		white-space: pre-wrap;
		word-break: break-word;
	}

	.message-meta {
		display: flex;
		align-items: center;
		gap: 4px;
		margin-top: 4px;
		font-size: 0.7rem;
		opacity: 0.7;
	}

	.timestamp {
		color: inherit;
	}

	.status {
		font-size: 0.6rem;
	}

	.status-sent {
		color: #28a745;
	}

	.status-delivered {
		color: #007bff;
	}

	.status-read {
		color: #007bff;
	}

	.status-failed {
		color: #dc3545;
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.message-content {
			max-width: 90%;
		}

		.message-item {
			padding: 6px 12px;
		}
	}
</style>

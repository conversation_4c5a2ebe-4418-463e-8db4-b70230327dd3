// backend/models/Groups/worldChatModel.js
const db = require('../../utils/db');
const GroupModel = require('./groupModel');

class WorldChatModel {
  // Fetch the world chat group based on is_world_chat flag
  async getWorldChat() {
    const query = `SELECT * FROM groups WHERE name = 'World Chat' LIMIT 1;`;
    const result = await db.query(query);

    if (!result.rows[0]) {
      // If World Chat doesn't exist, initialize it
      console.log('World Chat not found in getWorldChat, initializing it...');
      return await GroupModel.initializeWorldChat();
    }

    return result.rows[0]; // Returns the group object, including group_id
  }
  // Check if a group is the world chat
  async isWorldChat(groupId) {
    const query = `
      SELECT is_world_chat FROM groups WHERE id = $1;
    `;
    const result = await db.query(query, [groupId]);
    return result.rows[0]?.is_world_chat || false;
  }
}

module.exports = new WorldChatModel();
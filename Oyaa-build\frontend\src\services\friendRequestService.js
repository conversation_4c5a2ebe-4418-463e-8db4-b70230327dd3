// frontend/src/services/friendRequestService.js
import axios from 'axios';
import store from '@/store';

const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

/**
 * Accept a friend request
 * @param {number} requestId - The ID of the request to accept
 * @returns {Promise} - A promise that resolves with the response
 */
export async function acceptFriendRequest(requestId) {
  try {
    const response = await axios.post(
      `${API_URL}/api/friend-requests/accept/${requestId}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );

    // Refresh the friend requests lists
    store.dispatch('friendRequests/fetchReceivedRequests');
    store.dispatch('friendRequests/fetchSentRequests');

    return response.data;
  } catch (error) {
    console.error('Error accepting friend request:', error);
    throw new Error(error.response?.data?.message || 'Failed to accept friend request');
  }
}

/**
 * Reject a friend request
 * @param {number} requestId - The ID of the request to reject
 * @returns {Promise} - A promise that resolves with the response
 */
export async function rejectFriendRequest(requestId) {
  try {
    const response = await axios.post(
      `${API_URL}/api/friend-requests/reject/${requestId}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );

    // Refresh the friend requests lists
    store.dispatch('friendRequests/fetchReceivedRequests');
    store.dispatch('friendRequests/fetchSentRequests');

    return response.data;
  } catch (error) {
    console.error('Error rejecting friend request:', error);
    throw new Error(error.response?.data?.message || 'Failed to reject friend request');
  }
}

/**
 * Send a friend request
 * @param {number} receiverId - The ID of the user to send the request to
 * @returns {Promise} - A promise that resolves with the response
 */
export async function sendFriendRequest(receiverId) {
  try {
    const response = await axios.post(
      `${API_URL}/api/friend-requests/send`,
      { receiverId },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );

    // Refresh the sent requests list
    store.dispatch('friendRequests/fetchSentRequests');

    return response.data;
  } catch (error) {
    console.error('Error sending friend request:', error);
    throw new Error(error.response?.data?.message || 'Failed to send friend request');
  }
}

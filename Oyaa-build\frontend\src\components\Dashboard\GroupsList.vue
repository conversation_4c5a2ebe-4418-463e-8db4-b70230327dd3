<template>
  <div class="groups-container">
    <SearchBar @search="handleSearch" />

    <div class="list">
      <!-- Display any error message -->
      <div v-if="error" class="error-message">
        <div class="error-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
        </div>
        <p>{{ error }}</p>
      </div>

      <!-- Skeleton loading view -->
      <ul v-else-if="loading" class="groups-list">
        <li v-for="i in 5" :key="i" class="group-item skeleton">
          <div class="avatar skeleton-avatar"></div>
          <div class="group-info">
            <span class="group-name skeleton-text"></span>
            <span class="group-description skeleton-text"></span>
          </div>
          <div class="meta">
            <div class="member-count skeleton-text"></div>
          </div>
        </li>
      </ul>

      <!-- Actual groups list -->
      <div v-else>
        <div v-if="groups.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <p>You are not part of any group yet.</p>
          <p>Join or create a group to start collaborating!</p>
        </div>
        <div v-else-if="filteredGroups.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </div>
          <p>No groups match your search.</p>
        </div>
        <ul v-else class="groups-list">
          <li
            v-for="group in filteredGroups"
            :key="group.id"
            @click="navigateToGroupChat(group.id)"
            class="group-item"
          >
            <img :src="group.avatar || '/Avatar/default-group.svg'" alt="Group Avatar" class="avatar" />
            <div class="group-info">
              <span class="group-name">{{ group.name }}</span>
              <span class="group-description">
                {{ group.description || 'No description' }}
              </span>
            </div>
            <div class="meta">
              <div class="member-count">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                <span>{{ group.member_count || 0 }}</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { mapGetters } from 'vuex';
import SearchBar from './SearchBar.vue';

export default {
  name: 'GroupsList',
  components: {
    SearchBar
  },
  data() {
    return {
      groups: [],
      loading: false,
      error: '',
      searchQuery: ''
    };
  },
  computed: {
    ...mapGetters('auth', ['user']),
    filteredGroups() {
      if (!this.searchQuery) return this.groups;
      return this.groups.filter(group =>
        group.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    }
  },
  created() {
    if (this.user && this.user.id) {
      this.fetchGroups(this.user.id);
    }
  },
  methods: {
    async fetchGroups(userId) {
      this.loading = true;
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/groups/user/${userId}`,
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        );
        this.groups = response.data.groups;
        console.log('GroupsList - Fetched groups:', this.groups);
      } catch (error) {
        console.error('GroupsList - Error fetching groups:', error);
        this.error = error.response?.data?.message || 'Error fetching groups';
      } finally {
        this.loading = false;
      }
    },
    navigateToGroupChat(groupId) {
      console.log('GroupsList - Navigating to GroupChatPage with groupId:', groupId);
      this.$router.push({ name: 'GroupChatPage', params: { groupId } });
    },
    handleSearch(query) {
      this.searchQuery = query;
    }
  },
};
</script>

<style scoped>
.groups-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.list {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 15vh;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 100, 100, 0.2) transparent;
}

/* WebKit scrollbar styling (Chrome, Safari, Edge) */
.list::-webkit-scrollbar {
  width: 4px; /* Very slim scrollbar */
}

.list::-webkit-scrollbar-track {
  background: transparent; /* Transparent track */
  margin: 4px 0; /* Add some margin to top and bottom */
}

.list::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.2); /* Dark subtle color */
  border-radius: 4px; /* Rounded corners */
}

/* Only show scrollbar on hover for a cleaner look */
.list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(150, 150, 150, 0.3); /* Slightly lighter on hover */
}

.groups-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #a0a0a0;
  height: 50vh;
}

.empty-icon {
  margin-bottom: 16px;
  color: #6366f1;
  opacity: 0.7;
}

.empty-state p {
  margin: 4px 0;
  font-size: 15px;
}

.empty-state p:first-of-type {
  color: #e2e2e2;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #f87171;
  text-align: center;
  margin: 20px 0;
}

.error-icon {
  margin-bottom: 12px;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  margin: 4px 8px;
  touch-action: manipulation; /* Improve touch behavior */
}

.group-item:hover {
  background-color: rgba(26, 26, 34, 0.8);
  transform: translateY(-2px);
}

.group-item:active {
  transform: scale(0.98);
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 12px;
  flex-shrink: 0;
  object-fit: cover;
  border: 1px solid rgba(46, 46, 58, 0.8);
  background-color: #1a1a22;
}

.group-info {
  flex: 1;
  min-width: 0;
  margin-right: 12px;
}

.group-name {
  display: block;
  color: #e2e2e2;
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 4px;
}

.group-description {
  display: block;
  color: #a0a0a0;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.meta {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.member-count {
  background-color: rgba(26, 26, 34, 0.8);
  color: #a0a0a0;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  border: 1px solid rgba(46, 46, 58, 0.8);
}

/* Skeleton loading styles */
.skeleton {
  animation: pulse 1.5s infinite;
}

.skeleton-avatar {
  background-color: rgba(26, 26, 34, 0.8);
  border: 1px solid rgba(46, 46, 58, 0.8);
}

.skeleton-text {
  height: 1em;
  margin-bottom: 0.5em;
  background-color: rgba(26, 26, 34, 0.8);
  border-radius: 4px;
}

.group-item.skeleton .group-name {
  width: 60%;
}

.group-item.skeleton .group-description {
  width: 80%;
}

.group-item.skeleton .member-count {
  width: 40px;
  height: 24px;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .empty-state {
    padding: 20px 16px;
  }

  .empty-icon svg {
    width: 40px;
    height: 40px;
  }

  .empty-state p {
    font-size: 14px;
  }

  .empty-state p:first-of-type {
    font-size: 15px;
  }

  .group-item {
    padding: 10px 12px;
    margin: 3px 6px;
  }

  .avatar {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }

  .group-name {
    font-size: 14px;
  }

  .group-description {
    font-size: 12px;
  }

  .member-count {
    font-size: 11px;
    padding: 3px 6px;
  }

  .member-count svg {
    width: 12px;
    height: 12px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .avatar {
    width: 36px;
    height: 36px;
  }

  .group-name {
    font-size: 13px;
  }

  .group-description {
    font-size: 11px;
  }
}
</style>
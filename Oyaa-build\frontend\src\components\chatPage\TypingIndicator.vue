<template>
  <div v-if="isTyping" class="typing-indicator typing-indicator-active">
    <span class="typing-text">{{ friendName }} is typing...</span>
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
  </div>
</template>

<script setup>


const props = defineProps({
  isTyping: {
    type: Boolean,
    default: false,
  },
  friendName: {
    type: String,
    default: 'Friend',
  },
});
</script>

<style scoped>
.typing-text {
  font-size: 0.9rem;
  margin-right: 8px;
  color: rgb(97, 240, 97);
}

.typing-indicator {
  display: inline-flex;
  gap: 4px;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* When the indicator is active, set opacity to 1 */
.typing-indicator-active {
  opacity: 1;
}

/* Bouncing circle style */
.dot {
  height: 14px; /* Increased size */
  width: 14px;  /* Increased size */
  background-color: #30e630;
  border-radius: 50%;
  animation: bounce 1.2s infinite ease-in-out;
}

/* Stagger the animation delays for each circle */
.dot:nth-child(2) {
  animation-delay: 0.1s;
}

.dot:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes bounce {
  0%, 60%, 100% {
    transform: translate(0, 0);
  }
  30% {
    transform: translate(4px, -10px); /* Increased movement */
  }
}
</style>

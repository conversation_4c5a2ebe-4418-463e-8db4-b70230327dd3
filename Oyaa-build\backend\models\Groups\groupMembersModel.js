const db = require('../../utils/db');

class GroupMembersModel {
  async addMember(groupId, userId, role = 'member') {
    const query = `
      INSERT INTO group_members (group_id, user_id, role)
      VALUES ($1, $2, $3)
      RETURNING *;
    `;
    const result = await db.query(query, [groupId, userId, role]);
    return result.rows[0];
  }

  async removeMember(groupId, userId) {
    const query = `
      DELETE FROM group_members
      WHERE group_id = $1 AND user_id = $2
      RETURNING *;
    `;
    const result = await db.query(query, [groupId, userId]);
    return result.rows[0];
  }

  async getMembers(groupId) {
    const query = `
      SELECT gm.*, u.username, u.avatar
      FROM group_members gm
      JOIN users u ON gm.user_id = u.id
      WHERE gm.group_id = $1;
    `;
    const result = await db.query(query, [groupId]);
    return result.rows;
  }

  async updateMemberRole(groupId, userId, newRole) {
    const query = `
      UPDATE group_members
      SET role = $3
      WHERE group_id = $1 AND user_id = $2
      RETURNING *;
    `;
    const result = await db.query(query, [groupId, userId, newRole]);
    return result.rows[0];
  }

  async isMember(groupId, userId) {
    const query = `
      SELECT 1
      FROM group_members
      WHERE group_id = $1 AND user_id = $2;
    `;
    const result = await db.query(query, [groupId, userId]);
    return result.rows.length > 0;
  }
  // Add this method inside the GroupMembersModel class
async isAdmin(groupId, userId) {
  const query = `
    SELECT 1
    FROM group_members
    WHERE group_id = $1 AND user_id = $2 AND role = 'admin';
  `;
  const result = await db.query(query, [groupId, userId]);
  return result.rows.length > 0; // True if user is an admin, false otherwise
}
}

module.exports = new GroupMembersModel();
<template>
  <div class="page-container">
    <!-- Header with back button -->
    <div class="page-header">
      <button class="back-btn" @click="$router.push('/dashboard')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
        <span class="back-text">Back</span>
      </button>
      <h1>Group Invites</h1>
      <div class="header-spacer"></div>
    </div>

    <div class="content-area">
      <!-- Loading state -->
      <div v-if="loading && initialLoad" class="loading-state">
        <div class="spinner"></div>
        <p>Loading invites...</p>
      </div>

      <!-- Empty state -->
      <div v-else-if="requests.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            <line x1="22" y1="8" x2="17" y2="13"></line>
            <line x1="17" y1="8" x2="22" y2="13"></line>
          </svg>
        </div>
        <p class="empty-title">No group invites</p>
        <p class="empty-subtitle">When someone invites you to a group, it will appear here.</p>
      </div>

      <!-- Request list -->
      <transition-group 
        v-else 
        name="request-list" 
        tag="ul" 
        class="requests-list"
      >
        <GroupRequestItem
          v-for="request in requests"
          :key="request.id"
          :request="request"
          @accept="acceptRequest"
          @reject="rejectRequest"
        />
      </transition-group>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { mapGetters, mapActions } from 'vuex';
import GroupRequestItem from './GroupRequestItem.vue';
import useGroupNotifications from '@/composables/useGroupNotifications';

export default {
  components: { GroupRequestItem },

  mounted() {
    this.$store.commit('app/MARK_AS_SEEN');
    this.fetchGroupRequests();
  },

  data() {
    return {
      requests: [],
      loading: false,
      initialLoad: true,
      pollInterval: null,
      lastUpdated: Date.now(),
    };
  },

  computed: {
    ...mapGetters('auth', ['user']),
  },

  async created() {
    if (this.user) {
      this.loading = true;
      await this.fetchGroupRequests();
      this.loading = false;
      this.initialLoad = false;
      this.startPolling();

      // Update last seen time when the component mounts
      this.updateLastSeenGroupRequests(Date.now());

      // Clear notified requests since the user has seen them
      this.clearNotifiedGroupRequests();

      // Setup WebSocket notifications
      const { setupGroupWebSocket } = useGroupNotifications();
      this.unsubscribeWebSocket = setupGroupWebSocket((data) => {
        this.requests.unshift(data);
      });
    }
  },

  beforeUnmount() {
    this.stopPolling();
    if (this.unsubscribeWebSocket) {
      this.unsubscribeWebSocket();
    }
  },

  methods: {
    ...mapActions('auth', ['fetchGroupRequests']),
    ...mapActions('app', ['updateLastSeenGroupRequests', 'clearNotifiedGroupRequests']),

    startPolling() {
      this.pollInterval = setInterval(async () => {
        await this.fetchGroupRequests();
      }, 5000); // Reduced polling frequency to save battery
    },

    stopPolling() {
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
        this.pollInterval = null;
      }
    },

    async fetchGroupRequests() {
      this.loading = true;

      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/groups/group-requests/${this.user.id}`,
          {
            headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
            params: { lastUpdated: this.lastUpdated },
          }
        );

        if (response.data.lastUpdated > this.lastUpdated) {
          const currentIds = new Set(this.requests.map((r) => r.id));
          this.requests = [
            ...this.requests.filter((r) => response.data.requests.some((nr) => nr.id === r.id)),
            ...response.data.requests.filter((nr) => !currentIds.has(nr.id)),
          ];
          this.lastUpdated = response.data.lastUpdated;
        }
      } catch (error) {
        console.error('Error fetching group requests:', error);
      } finally {
        this.loading = false;
      }
    },

    async acceptRequest(request) {
      try {
        request.processing = true;

        await axios.post(
          `${import.meta.env.VITE_API_BASE_URL}/api/group-requests/accept/${request.id}`,
          {},
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        );

        request.status = 'accepted';
        setTimeout(() => {
          this.requests = this.requests.filter((req) => req.id !== request.id);
        }, 1000); // Wait for animation to complete
      } catch (error) {
        console.error('Error accepting group request:', error);
        request.processing = false;
      }
    },

    async rejectRequest(request) {
      try {
        request.processing = true;

        await axios.post(
          `${import.meta.env.VITE_API_BASE_URL}/api/group-requests/reject/${request.id}`,
          {},
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        );

        request.status = 'rejected';
        setTimeout(() => {
          this.requests = this.requests.filter((req) => req.id !== request.id);
        }, 1000); // Wait for animation to complete
      } catch (error) {
        console.error('Error rejecting group request:', error);
        request.processing = false;
      }
    },
  },
};
</script>

<style scoped>
.page-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

/* Header styles */
.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.page-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-btn:hover, .back-btn:focus {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
}

.back-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.header-spacer {
  width: 6rem; /* Match the width of the back button for balance */
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  gap: 1rem;
  color: var(--text-secondary);
}

.spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 0.25rem solid var(--accent-tertiary);
  border-radius: 50%;
  border-top-color: var(--accent-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 60vh;
  padding: 0 1.5rem;
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  margin-bottom: 1.5rem;
  color: var(--accent-tertiary);
}

.empty-icon svg {
  width: 100%;
  height: 100%;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.empty-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  max-width: 20rem;
  margin: 0;
}

/* Request list */
.requests-list {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
}

/* Animations */
.request-list-enter-active,
.request-list-leave-active {
  transition: all 0.3s ease;
}

.request-list-enter-from,
.request-list-leave-to {
  opacity: 0;
  transform: translateY(1rem);
}

.request-list-move {
  transition: transform 0.3s ease;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .page-header {
    padding: 0.75rem 1rem;
  }
  
  .back-text {
    display: none;
  }
  
  .back-btn {
    padding: 0.5rem;
  }
  
  .header-spacer {
    width: 2.5rem;
  }
  
  .content-area {
    padding: 0.75rem;
  }
}

/* Safe area insets for notched phones */
@supports (padding: max(0px)) {
  .page-header {
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-left: max(1.25rem, env(safe-area-inset-left));
    padding-right: max(1.25rem, env(safe-area-inset-right));
  }
  
  .content-area {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}
</style>
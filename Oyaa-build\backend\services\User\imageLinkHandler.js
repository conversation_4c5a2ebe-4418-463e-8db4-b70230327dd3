// backend/services/imageLinkHandler.js

// Utility function to extract cloud name from CLOUDINARY_URL
function getCloudName() {
  const url = process.env.CLOUDINARY_URL;
  if (url) {
    const match = url.match(/@([^/]+)/);
    if (match && match[1]) {
      return match[1];
    }
  }
  return 'your-cloud-name';
}

function processImageMessage(message) {
  if (message.media) {
    // If mediaUrl is already provided, do nothing.
    if (message.media.mediaUrl && message.media.mediaUrl.trim() !== '') {
      return message;
    }
    const publicId = message.media.publicId || message.media.public_id;
    if (publicId) {
      const cloudName = getCloudName();
      const mediaType = message.media.mediaType || message.media.media_type || 'image';
      message.media.mediaUrl = `https://res.cloudinary.com/${cloudName}/${mediaType}/upload/${publicId}`;
    }
  }
  return message;
}

module.exports = { processImageMessage };

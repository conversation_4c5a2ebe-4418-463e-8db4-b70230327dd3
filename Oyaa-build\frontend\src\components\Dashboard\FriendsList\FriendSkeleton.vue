<template>
  <li class="friend-item skeleton">
    <div class="avatar skeleton-avatar"></div>
    <div class="friend-info">
      <span class="friend-username skeleton-text"></span>
      <span class="last-message skeleton-text"></span>
    </div>
    <div class="meta">
      <div class="timestamp skeleton-text"></div>
    </div>
  </li>
</template>

<script>
export default {
  name: "FriendSkeleton",
};
</script>

<style scoped>
.friend-item {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 12px;
  margin: 4px 8px;
  width: 100%;
  box-sizing: border-box;
}

/* Skeleton animation */
.skeleton {
  animation: pulse 1.5s infinite;
}

/* Avatar styling */
.avatar,
.skeleton-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 12px;
  flex-shrink: 0;
}

.skeleton-avatar {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
}

/* Friend info container */
.friend-info {
  flex: 1;
  min-width: 0;
  margin-right: 12px;
}

/* Skeleton text placeholders */
.skeleton-text {
  background-color: var(--bg-tertiary);
  border-radius: 4px;
  margin-bottom: 0.5em;
}

/* Placeholder for friend username */
.friend-username.skeleton-text {
  width: 60%;
  height: 1em;
}

/* Placeholder for last message */
.last-message.skeleton-text {
  width: 80%;
  height: 0.9em;
}

/* Meta container */
.meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  flex-shrink: 0;
}

/* Placeholder for timestamp */
.timestamp.skeleton-text {
  width: 40px;
  height: 0.75em;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .friend-item {
    padding: 10px 12px;
    margin: 3px 6px;
  }
  
  .avatar,
  .skeleton-avatar {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }
  
  .friend-username.skeleton-text {
    height: 0.9em;
  }
  
  .last-message.skeleton-text {
    height: 0.8em;
  }
  
  .timestamp.skeleton-text {
    width: 36px;
    height: 0.7em;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .friend-item {
    padding: 8px 10px;
    margin: 2px 4px;
  }
  
  .avatar,
  .skeleton-avatar {
    width: 36px;
    height: 36px;
    margin-right: 8px;
  }
}
</style>
// frontend/src/store/modules/friendRequests.js
import axios from 'axios';

const state = {
  // Requests received by the current user
  receivedRequests: [],
  // Requests sent by the current user
  sentRequests: [],
  // Loading states
  loading: {
    received: false,
    sent: false
  },
  // Error states
  error: {
    received: null,
    sent: null
  },
  // Last updated timestamps
  lastUpdated: {
    received: null,
    sent: null
  }
};

const getters = {
  // Get all received requests
  receivedRequests: state => state.receivedRequests,

  // Get all sent requests
  sentRequests: state => state.sentRequests,

  // Get pending received requests
  pendingReceivedRequests: state => state.receivedRequests.filter(
    req => !req.status || req.status === null || req.status === '' || req.status.toLowerCase() === 'pending'
  ),

  // Get pending sent requests
  pendingSentRequests: state => state.sentRequests.filter(
    req => !req.status || req.status === null || req.status === '' || req.status.toLowerCase() === 'pending'
  ),

  // Get accepted requests (friends)
  acceptedRequests: state => [
    ...state.receivedRequests.filter(req => req.status && req.status.toLowerCase() === 'accepted'),
    ...state.sentRequests.filter(req => req.status && req.status.toLowerCase() === 'accepted')
  ],

  // Loading states
  isLoadingReceived: state => state.loading.received,
  isLoadingSent: state => state.loading.sent,

  // Error states
  receivedError: state => state.error.received,
  sentError: state => state.error.sent,

  // Check if a user has a pending request from current user
  hasPendingRequestTo: state => userId => {
    return state.sentRequests.some(
      req => req.receiver_id === userId &&
             req.status &&
             req.status.toLowerCase() === 'pending'
    );
  },

  // Check if a user has sent a pending request to current user
  hasPendingRequestFrom: state => userId => {
    return state.receivedRequests.some(
      req => req.sender_id === userId &&
             req.status &&
             req.status.toLowerCase() === 'pending'
    );
  },

  // Check if users are friends
  isFriendWith: state => userId => {
    return state.sentRequests.some(
      req => req.receiver_id === userId &&
             req.status &&
             req.status.toLowerCase() === 'accepted'
    ) || state.receivedRequests.some(
      req => req.sender_id === userId &&
             req.status &&
             req.status.toLowerCase() === 'accepted'
    );
  }
};

const actions = {
  // Fetch received friend requests
  async fetchReceivedRequests({ commit, rootGetters }) {

    commit('SET_LOADING', { type: 'received', value: true });
    commit('SET_ERROR', { type: 'received', value: null });

    try {
      const userId = rootGetters['auth/userId'];

      // Check if userId is valid
      if (!userId) {
        console.warn('Cannot fetch received friend requests: No valid user ID available');
        commit('SET_ERROR', {
          type: 'received',
          value: 'Cannot fetch friend requests: User not fully authenticated yet'
        });
        commit('SET_LOADING', { type: 'received', value: false });
        return;
      }

      // Log the URL we're fetching from
      const url = `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/received/${userId}`;

      const response = await axios.get(
        url,
        {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
          // Add a timestamp to prevent caching
          params: { _t: Date.now() }
        }
      );



      // Check if the response contains the requests array
      if (response.data && response.data.requests) {

        commit('SET_RECEIVED_REQUESTS', response.data.requests);

        // Log each request for debugging
        response.data.requests.forEach((req, index) => {

        });
      } else {
        console.warn('Unexpected API response format for received requests:', response.data);
        commit('SET_RECEIVED_REQUESTS', []);
      }
      commit('SET_LAST_UPDATED', { type: 'received', value: Date.now() });
    } catch (error) {
      console.error('Error fetching received friend requests:', error);
      console.error('Error details:', error.response?.data || error.message);
      commit('SET_ERROR', {
        type: 'received',
        value: error.response?.data?.message || 'Failed to fetch received friend requests'
      });
    } finally {
      commit('SET_LOADING', { type: 'received', value: false });
    }
  },

  // Fetch sent friend requests
  async fetchSentRequests({ commit, rootGetters }) {
    commit('SET_LOADING', { type: 'sent', value: true });
    commit('SET_ERROR', { type: 'sent', value: null });

    try {
      const userId = rootGetters['auth/userId'];

      // Check if userId is valid
      if (!userId) {
        console.warn('Cannot fetch sent friend requests: No valid user ID available');
        commit('SET_ERROR', {
          type: 'sent',
          value: 'Cannot fetch friend requests: User not fully authenticated yet'
        });
        commit('SET_LOADING', { type: 'sent', value: false });
        return;
      }

      const response = await axios.get(
        `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/sent/${userId}`,
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );



      // Check if the response contains the requests array
      if (response.data && response.data.requests) {
        commit('SET_SENT_REQUESTS', response.data.requests);
      } else {
        console.warn('Unexpected API response format for sent requests:', response.data);
        commit('SET_SENT_REQUESTS', []);
      }
      commit('SET_LAST_UPDATED', { type: 'sent', value: Date.now() });
    } catch (error) {
      console.error('Error fetching sent friend requests:', error);
      commit('SET_ERROR', {
        type: 'sent',
        value: error.response?.data?.message || 'Failed to fetch sent friend requests'
      });
    } finally {
      commit('SET_LOADING', { type: 'sent', value: false });
    }
  },

  // Send a friend request
  async sendFriendRequest({ commit, dispatch, rootGetters }, receiverId) {
    try {
      const senderId = rootGetters['auth/userId'];

      // Check if userId is valid
      if (!senderId) {
        console.warn('Cannot send friend request: No valid user ID available');
        dispatch('app/showToast', {
          message: 'Cannot send friend request: User not fully authenticated yet',
          type: 'error',
          duration: 5000
        }, { root: true });
        return;
      }

      const response = await axios.post(
        `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/send`,
        { senderId, receiverId },
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );

      // Optimistically add the request to the sent requests list
      if (response.data && response.data.request) {
        commit('ADD_SENT_REQUEST', response.data.request);
      }

      // No toast for successful actions - notifications will handle this

      return response.data;
    } catch (error) {
      console.error('Error sending friend request:', error);

      // Show error toast
      const errorMessage = error.response?.data?.message || 'Failed to send friend request';
      dispatch('app/showToast', {
        message: errorMessage,
        type: 'error',
        duration: 5000
      }, { root: true });

      throw error;
    }
  },

  // Accept a friend request
  async acceptFriendRequest({ commit, dispatch }, requestId) {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/accept/${requestId}`,
        {},
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );

      // Update the request status in the store
      commit('UPDATE_REQUEST_STATUS', {
        id: requestId,
        status: 'accepted',
        isReceived: true
      });

      // No toast for successful actions - notifications will handle this

      return response.data;
    } catch (error) {
      console.error('Error accepting friend request:', error);

      // Show error toast
      const errorMessage = error.response?.data?.message || 'Failed to accept friend request';
      dispatch('app/showToast', {
        message: errorMessage,
        type: 'error',
        duration: 5000
      }, { root: true });

      throw error;
    }
  },

  // Reject a friend request
  async rejectFriendRequest({ commit, dispatch }, requestId) {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/reject/${requestId}`,
        {},
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );

      // Update the request status in the store
      commit('UPDATE_REQUEST_STATUS', {
        id: requestId,
        status: 'rejected',
        isReceived: true
      });

      // No toast for successful actions - notifications will handle this

      return response.data;
    } catch (error) {
      console.error('Error rejecting friend request:', error);

      // Show error toast
      const errorMessage = error.response?.data?.message || 'Failed to reject friend request';
      dispatch('app/showToast', {
        message: errorMessage,
        type: 'error',
        duration: 5000
      }, { root: true });

      throw error;
    }
  },

  // Handle WebSocket updates
  handleWebSocketUpdate({ commit, dispatch }, data) {


    if (data.type === 'NEW_FRIEND_REQUEST') {

      const currentUserId = this.rootGetters['auth/userId'];

      // If this is a new request received by the current user
      if (data.receiverId === currentUserId) {

        commit('ADD_RECEIVED_REQUEST', data.request);

        // Also refresh the full list to ensure we have the latest data
        dispatch('fetchReceivedRequests');
      }
      // If this is a new request sent by the current user
      else if (data.senderId === currentUserId) {

        commit('ADD_SENT_REQUEST', data.request);

        // Also refresh the full list to ensure we have the latest data
        dispatch('fetchSentRequests');
      }
    }
    else if (data.type === 'FRIEND_REQUEST_UPDATE') {

      const currentUserId = this.rootGetters['auth/userId'];
      const isReceived = data.senderId !== currentUserId;



      // Update the request status in the store
      commit('UPDATE_REQUEST_STATUS', {
        id: data.request.id,
        status: data.request.status,
        isReceived
      });

      // Also refresh the appropriate list to ensure we have the latest data
      if (isReceived) {
        dispatch('fetchReceivedRequests');
      } else {
        dispatch('fetchSentRequests');
      }
    }
    else if (data.type === 'FRIEND_REQUESTS_LIST') {


      if (data.receivedRequests) {

        commit('SET_RECEIVED_REQUESTS', data.receivedRequests);
      }
      if (data.sentRequests) {

        commit('SET_SENT_REQUESTS', data.sentRequests);
      }

      // Log the update but don't trigger additional refreshes
      if ((!data.receivedRequests || data.receivedRequests.length === 0) &&
          (!data.sentRequests || data.sentRequests.length === 0)) {

      }
    }
  }
};

const mutations = {
  // Set received requests
  SET_RECEIVED_REQUESTS(state, requests) {
    state.receivedRequests = requests;
  },

  // Set sent requests
  SET_SENT_REQUESTS(state, requests) {
    state.sentRequests = requests;
  },

  // Add a new received request
  ADD_RECEIVED_REQUEST(state, request) {
    // Check if the request already exists
    const exists = state.receivedRequests.some(req => req.id === request.id);
    if (!exists) {
      state.receivedRequests.unshift(request);
    }
  },

  // Add a new sent request
  ADD_SENT_REQUEST(state, request) {
    // Check if the request already exists
    const exists = state.sentRequests.some(req => req.id === request.id);
    if (!exists) {
      state.sentRequests.unshift(request);
    }
  },

  // Update a request status
  UPDATE_REQUEST_STATUS(state, { id, status, isReceived }) {
    if (isReceived) {
      // Update received request
      const index = state.receivedRequests.findIndex(req => req.id === id);
      if (index !== -1) {
        state.receivedRequests[index].status = status;
      }
    } else {
      // Update sent request
      const index = state.sentRequests.findIndex(req => req.id === id);
      if (index !== -1) {
        state.sentRequests[index].status = status;
      }
    }
  },

  // Remove a request
  REMOVE_REQUEST(state, { id, isReceived }) {
    if (isReceived) {
      // Remove from received requests
      state.receivedRequests = state.receivedRequests.filter(req => req.id !== id);
    } else {
      // Remove from sent requests
      state.sentRequests = state.sentRequests.filter(req => req.id !== id);
    }
  },

  // Set loading state
  SET_LOADING(state, { type, value }) {
    state.loading[type] = value;
  },

  // Set error state
  SET_ERROR(state, { type, value }) {
    state.error[type] = value;
  },

  // Set last updated timestamp
  SET_LAST_UPDATED(state, { type, value }) {
    state.lastUpdated[type] = value;
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};

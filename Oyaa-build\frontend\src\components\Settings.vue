<template>
  <div class="settings">
    <div class="header">
      <button class="back-button" @click="goBack">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>Settings</h1>
    </div>

    <div class="menu-list">
      <!-- About Section Header -->
      <h2 class="section-header">ABOUT</h2>

      <!-- About Section Menu Items -->
      <router-link to="/community-guidelines" class="menu-item">
        <div class="menu-item-content">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          <span>Community guidelines</span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </router-link>

      <router-link to="/terms-of-service" class="menu-item">
        <div class="menu-item-content">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
            <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
            <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
          </svg>
          <span>Terms of service</span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </router-link>

      <router-link to="/privacy-policy" class="menu-item">
        <div class="menu-item-content">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
          <span>Privacy policy</span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </router-link>

      <router-link to="/intellectual-property-policy" class="menu-item">
        <div class="menu-item-content">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 8v4"></path>
            <path d="M12 16h.01"></path>
          </svg>
          <span>Intellectual property policy</span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </router-link>

      <!-- Account Section Header -->
      <h2 class="section-header">ACCOUNT</h2>

      <!-- Account Section Menu Items -->
      <router-link to="/manage-account" class="menu-item">
        <div class="menu-item-content">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          <span>Manage account</span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </router-link>

      <router-link to="/user-privacy" class="menu-item">
        <div class="menu-item-content">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
          </svg>
          <span>User privacy</span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </router-link>

      <router-link to="/customer-support" class="menu-item">
        <div class="menu-item-content">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          </svg>
          <span>Customer Support</span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </router-link>
      
      <a href="#" class="menu-item logout" @click.prevent="handleLogout">
        <div class="menu-item-content">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
            <polyline points="16 17 21 12 16 7"></polyline>
            <line x1="21" y1="12" x2="9" y2="12"></line>
          </svg>
          <span>Logout</span>
        </div>
      </a>
    </div>
  </div>
</template>

<script>
import { onMounted, ref, computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import axios from 'axios';
import BottomNavigation from './Dashboard/BottomNavigation.vue';

export default {
  name: 'Settings',
  components: {
    BottomNavigation,
  },
  setup() {
    const store = useStore();
    const router = useRouter();
    const user = ref({});

    const fetchUserDetails = async () => {
      try {
        const userId = store.getters['auth/userId'];
        const token = store.getters['auth/token'];
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/users/${userId}`,
          {
            headers: { Authorization: `Bearer ${token}` }
          }
        );
        console.log('Full user response:', response.data);
        user.value = response.data;
      } catch (error) {
        console.error('Error fetching user details:', error);
      }
    };

    onMounted(fetchUserDetails);

    const avatarSrc = computed(() => {
      return user.value && user.value.avatar ? user.value.avatar : '/Avatar/default.svg';
    });

    const handleLogout = () => {
      store.dispatch('auth/logout');
      router.push('/signin');
    };

    const goBack = () => {
  router.push('/dashboard');
};

    return {
      user,
      avatarSrc,
      handleLogout,
      goBack,
    };
  },
};
</script>

<style scoped>
.settings {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  padding: 20px;
  margin: 0 auto;
  background: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  animation: fadeIn 0.3s ease-out;
  padding-bottom: calc(80px + env(safe-area-inset-bottom, 0px)); /* Account for bottom nav and safe area */
  font-family: 'Inter', 'Segoe UI', sans-serif;
  max-width: 100%;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  position: relative;
}

.header h1 {
  flex: 1;
  text-align: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-button {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  cursor: pointer;
  outline: none;
  padding: 8px;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation; /* Improve touch behavior */
}

.back-button:hover {
  background: var(--accent-tertiary);
  color: var(--accent-primary);
  transform: translateX(-2px);
}

.back-button:active {
  transform: scale(0.95);
}

.menu-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.section-header {
  margin: 24px 0 12px;
  font-size: 13px;
  font-weight: 500;
  color: var(--accent-primary);
  letter-spacing: 1px;
  padding-left: 8px;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 16px;
  text-decoration: none;
  color: var(--text-primary);
  background: var(--bg-tertiary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  touch-action: manipulation; /* Improve touch behavior */
}

.menu-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
  border-color: var(--accent-tertiary);
}

.menu-item:active {
  transform: scale(0.98);
  background-color: var(--bg-secondary);
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 14px;
}

.icon {
  color: var(--text-secondary);
  transition: color var(--transition-fast);
}

.menu-item:hover .icon {
  color: var(--accent-primary);
}

.chevron {
  color: var(--text-secondary);
  transition: transform var(--transition-fast);
}

.menu-item:hover .chevron {
  transform: translateX(2px);
  color: var(--accent-primary);
}

.logout {
  margin-top: 16px;
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.logout .icon, 
.logout span {
  color: var(--danger-color);
}

.logout:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .settings {
    padding: 16px;
  }
  
  .header {
    margin-bottom: 24px;
  }
  
  .header h1 {
    font-size: 18px;
  }
  
  .back-button {
    padding: 6px;
  }
  
  .back-button svg {
    width: 22px;
    height: 22px;
  }
  
  .section-header {
    margin: 20px 0 8px;
    font-size: 12px;
  }
  
  .menu-item {
    padding: 12px 14px;
  }
  
  .menu-item-content {
    gap: 12px;
  }
  
  .icon {
    width: 18px;
    height: 18px;
  }
  
  .chevron {
    width: 14px;
    height: 14px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .settings {
    padding: 12px;
  }
  
  .header h1 {
    font-size: 16px;
  }
  
  .menu-item {
    padding: 10px 12px;
  }
  
  .menu-item-content {
    gap: 10px;
  }
  
  .menu-item-content span {
    font-size: 14px;
  }
  
  .icon {
    width: 16px;
    height: 16px;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .settings {
    animation: none;
  }
  
  .back-button,
  .menu-item,
  .icon,
  .chevron {
    transition: none;
  }
}
</style>
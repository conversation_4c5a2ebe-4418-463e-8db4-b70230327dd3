<template>
  <main class="dynamic-content">
    <FriendsList
      v-if="activeTab === 'friends'"
      :friends="friends"
      :friendRequestsCount="friendRequestsCount"
    />
    <GroupsList
      v-else-if="activeTab === 'groups'"
      :groups="groups"
      :groupRequestsCount="groupRequestsCount"
    />
    <LocalsList v-else-if="activeTab === 'locals'" />
    <Profile v-else-if="activeTab === 'profile'" />
    <div v-else class="invalid-tab">
      <p>Invalid tab selection.</p>
    </div>
  </main>
</template>

<script>
import FriendsList from './FriendsList.vue';
import GroupsList from './GroupsList.vue';
import LocalsList from './LocalsList.vue';
import Profile from './Profile.vue';

export default {
  props: {
    activeTab: { type: String, required: true },
    friends: { type: Array, required: true },
    groups: { type: Array, required: true },
    friendRequestsCount: { type: Number, default: 0 },
    groupRequestsCount: { type: Number, default: 0 },
  },
  components: {
    FriendsList,
    GroupsList,
    LocalsList,
    Profile,
  },
};
</script>

<style scoped>
.dynamic-content {
  height: 100%;
  background: transparent;
  color: #e2e2e2;
}

.invalid-tab {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  color: #a0a0a0;
  font-size: 1.1rem;
}
</style>
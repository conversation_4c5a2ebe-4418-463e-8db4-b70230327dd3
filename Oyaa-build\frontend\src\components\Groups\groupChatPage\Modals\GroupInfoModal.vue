<template>
  <div v-if="show" class="modal-backdrop" @click="close"></div>
  <div v-if="show" class="modal-content" @click.stop>
    <!-- Header with close button -->
    <div class="modal-header">
      <h2 v-if="!editingAvatar">Group Info</h2>
      <h2 v-else>Select Group Avatar</h2>
      <button @click="close" class="close-button">✕</button>
    </div>

    <div v-if="!editingAvatar">
      <div class="group-info">
        <div class="avatar-wrapper">
          <div class="avatar">
            <img :src="group.avatar || defaultAvatar" alt="Group avatar" />
          </div>
          <button v-if="isAdmin" @click="editingAvatar = true" class="edit-avatar-button">
            <i class="fas fa-pencil-alt"></i>
          </button>
        </div>
        <div class="group-name">
          <h1 v-if="!editingName">{{ group.name }}</h1>
          <input v-else v-model="editedName" class="edit-input" />
          <button v-if="isAdmin && !editingName" @click="startEditingName" class="edit-icon">✏️</button>
          <button v-if="editingName" @click="saveName" class="save-button">Save</button>
          <button v-if="editingName" @click="cancelEditingName" class="cancel-button">Cancel</button>
        </div>
        <p class="member-count">{{ members.length }} members</p>
      </div>

      <!-- Group description -->
      <div class="description">
        <p v-if="!editingDescription">{{ group.description }}</p>
        <textarea v-else v-model="editedDescription" class="edit-textarea"></textarea>
        <button v-if="isAdmin && !editingDescription" @click="startEditingDescription" class="edit-icon">✏️</button>
        <button v-if="editingDescription" @click="saveDescription" class="save-button">Save</button>
        <button v-if="editingDescription" @click="cancelEditingDescription" class="cancel-button">Cancel</button>
      </div>

      <!-- Tags section -->
      <div class="tags-section">
        <button @click="showTagsModal = true" class="view-tags-button">View Tags</button>
      </div>

      <div class="separator"></div>

      <!-- Members section -->
      <div class="members-section">
        <div class="members-header">
          <div class="members-title">
            <i class="fas fa-users"></i>
            <h3>Members</h3>
          </div>
          <span class="member-count">{{ members.length }}</span>
        </div>
        <div class="members-list">
          <div
            v-for="member in members"
            :key="member.id"
            class="member-item"
            @click="toggleMemberOptions(member.id)"
          >
            <div class="member-info">
              <div class="avatar small">
                <img :src="member.avatar" :alt="member.username" />
              </div>
              <div class="member-name">{{ member.username }}</div>
            </div>
            <span class="chevron" v-if="member.user_id !== currentUserId">›</span>
            <div v-if="activeMemberId === member.id" class="member-options">
              <div
                v-if="!friendsSet.has(member.user_id) && !pendingRequestsSet.has(member.user_id)"
                class="option"
                @click.stop="addFriend(member.user_id)"
              >
                <i class="fas fa-user-plus"></i> Add as friend
              </div>
              <div v-else-if="pendingRequestsSet.has(member.user_id)" class="option disabled">
                <i class="fas fa-user-plus"></i> Request sent
              </div>
              <div v-else class="option disabled">
                <i class="fas fa-user-plus"></i> Already friends
              </div>
              <div
                v-if="member.user_id !== currentUserId"
                class="option"
                @click.stop="blockUser(member)"
              >
                <i class="fas fa-ban"></i> Block this user
              </div>
              <div class="option danger" @click.stop="reportUser(member.user_id)">
                <i class="fas fa-flag"></i> Report this user
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Leave group button -->
      <button class="leave-button" @click="handleLeaveGroup">
        <i class="fas fa-sign-out-alt"></i> Leave Group
      </button>
    </div>

    <!-- Avatar selector when editing -->
    <div v-else>
      <PickGroupAvatar :selected-avatar="group.avatar" @avatar-selected="handleAvatarSelected" />
      <button @click="editingAvatar = false" class="cancel-button">Cancel</button>
    </div>
  </div>

  <!-- Tags Modal -->
  <GroupTagsModal
    v-if="showTagsModal"
    :groupId="group.id"
    :isAdmin="isAdmin"
    :currentTags="group.tags || []"
    @close="showTagsModal = false"
    @update-tags="handleUpdateTags"
  />
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, computed } from 'vue';
import { useStore } from 'vuex';
import axios from 'axios';
import PickGroupAvatar from '../../PickGroupAvatar.vue';
import GroupTagsModal from './GroupTagsModal.vue';

const props = defineProps({
  show: { type: Boolean, required: true },
  group: { type: Object, required: true },
});

const emit = defineEmits(['close', 'leave-group', 'update-avatar', 'update-name', 'update-description', 'update-tags']);
const store = useStore();
const currentUserId = store.state.auth.user.id;

const members = ref([]);
const activeMemberId = ref(null);
const defaultAvatar = '/default-group-avatar.png';
const friendsSet = ref(new Set());
const pendingRequestsSet = ref(new Set());
const editingAvatar = ref(false);
const editingName = ref(false);
const editedName = ref('');
const editingDescription = ref(false);
const editedDescription = ref('');
const showTagsModal = ref(false);

// Check if the current user is an admin
const isAdmin = computed(() => {
  const currentMember = members.value.find(m => Number(m.user_id) === Number(currentUserId));
  return currentMember && currentMember.role === 'admin';
});

// Fetch group data when modal is shown
watch(
  () => props.show,
  async (newVal) => {
    if (newVal) {
      try {
        const [membersResponse, friendsResponse, sentRequestsResponse] = await Promise.all([
          axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/group-members/${props.group.id}`),
          axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/friends/${currentUserId}`),
          axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/sent/${currentUserId}`),
        ]);
        members.value = membersResponse.data.members || [];
        friendsSet.value = new Set(friendsResponse.data.friends.map((friend) => friend.id));
        pendingRequestsSet.value = new Set(
          sentRequestsResponse.data.requests.map((request) => request.receiver_id)
        );
      } catch (error) {
        console.error('Error fetching data:', error);
        members.value = [];
      }
    }
  }
);

// Name editing
const startEditingName = () => {
  editedName.value = props.group.name;
  editingName.value = true;
};

const cancelEditingName = () => {
  editingName.value = false;
};

const saveName = async () => {
  try {
    await axios.put(
      `${import.meta.env.VITE_API_BASE_URL}/api/groups/${props.group.id}/name`,
      { name: editedName.value },
      { headers: { Authorization: `Bearer ${store.state.auth.token}` } }
    );
    emit('update-name', editedName.value);
    editingName.value = false;
  } catch (error) {
    console.error('Error updating name:', error);
    alert('Failed to update name');
  }
};

// Description editing
const startEditingDescription = () => {
  editedDescription.value = props.group.description;
  editingDescription.value = true;
};

const cancelEditingDescription = () => {
  editingDescription.value = false;
};

const saveDescription = async () => {
  try {
    await axios.put(
      `${import.meta.env.VITE_API_BASE_URL}/api/groups/${props.group.id}/description`,
      { description: editedDescription.value },
      { headers: { Authorization: `Bearer ${store.state.auth.token}` } }
    );
    emit('update-description', editedDescription.value);
    editingDescription.value = false;
  } catch (error) {
    console.error('Error updating description:', error);
    alert('Failed to update description');
  }
};

// Tags handling
const handleUpdateTags = (newTags) => {
  showTagsModal.value = false;
  emit('update-tags', newTags);
};

// Toggle member options dropdown
const toggleMemberOptions = (memberId) => {
  const member = members.value.find((m) => m.id === memberId);
  if (member && member.user_id === currentUserId) {
    activeMemberId.value = null;
    return;
  }
  activeMemberId.value = activeMemberId.value === memberId ? null : memberId;
};

// Send friend request
const addFriend = async (userId) => {
  try {
    await axios.post(`${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/send`, {
      senderId: currentUserId,
      receiverId: userId,
    });
    pendingRequestsSet.value.add(userId);
  } catch (error) {
    console.error('Error sending friend request:', error);
    alert(error.response?.data?.message || 'Failed to send friend request');
  }
  activeMemberId.value = null;
};

// Block user
const blockUser = async (member) => {
  try {
    await store.dispatch('app/blockUser', { id: member.user_id, username: member.username });
    activeMemberId.value = null;
  } catch (err) {
    console.error('Failed to block user:', err);
    alert('Failed to block user. Please try again.');
  }
};

// Report user
const reportUser = (userId) => {
  console.log(`Report user: ${userId}`);
  activeMemberId.value = null;
};

// Close modal
const close = () => emit('close');

// Handle leaving group
const handleLeaveGroup = () => {
  emit('leave-group');
  close();
};

// Handle avatar selection
const handleAvatarSelected = async (avatarPath) => {
  try {
    await axios.put(
      `${import.meta.env.VITE_API_BASE_URL}/api/groups/${props.group.id}/avatar`,
      { avatar: avatarPath },
      { headers: { Authorization: `Bearer ${store.state.auth.token}` } }
    );
    emit('update-avatar', avatarPath);
    editingAvatar.value = false;
  } catch (error) {
    console.error('Error updating avatar:', error);
    alert('Failed to update avatar');
  }
};

// Close dropdown when clicking outside
const closeDropdown = (event) => {
  if (!event.target.closest('.member-item')) {
    activeMemberId.value = null;
  }
};

onMounted(() => document.addEventListener('click', closeDropdown));
onUnmounted(() => document.removeEventListener('click', closeDropdown));
</script>

<style scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
}

.modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 425px;
  max-height: 90vh;
  overflow-y: scroll;
  z-index: 1001;
  border-radius: 8px;
  background: linear-gradient(337deg, #40444b30, #33333387);
  background-color: rgba(0, 0, 0, 0.751);
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  scrollbar-width: none;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.close-button {
  background: none;
  border: none;
  color: #a0a0a0;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: color 0.2s;
}

.close-button:hover {
  color: #ffffff;
}

.group-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.avatar-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.avatar {
  width: 96px;
  height: 96px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar.small {
  width: 40px;
  height: 40px;
}

.group-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.group-name h1 {
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  color: #ffffff;
  margin: 0;
}

.member-count {
  font-size: 14px;
  color: #a0a0a0;
}

.description {
  background-color: rgba(40, 43, 48, 0.5);
  border-radius: 8px;
  padding: 16px;
  position: relative;
}

.description p {
  font-size: 14px;
  color: #d0d0d0;
  line-height: 1.5;
  margin: 0;
}

.tags-section {
  margin-top: 10px;
}

.view-tags-button {
  background-color: #7289da;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.view-tags-button:hover {
  background-color: #677bc4;
}

.separator {
  height: 1px;
  background-color: #40444b;
  width: 100%;
}

.members-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.members-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.members-title h3 {
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
}

.members-title i {
  color: #ffffff;
  font-size: 18px;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 320px;
  overflow-y: auto;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s;
}

.member-item:hover {
  background-color: rgba(64, 68, 75, 0.5);
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-name {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
}

.chevron {
  font-size: 18px;
  color: #a0a0a0;
}

.member-options {
  position: absolute;
  right: 0;
  top: 100%;
  background-color: #2c2f33;
  border: 1px solid #40444b;
  border-radius: 4px;
  width: 200px;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.option {
  padding: 12px 16px;
  font-size: 14px;
  color: #ffffff;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.option:hover {
  background-color: #40444b;
}

.option.danger {
  color: #f04747;
}

.leave-button {
  background-color: rgba(240, 71, 71, 0.8);
  color: #ffffff;
  border: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.leave-button:hover {
  background-color: rgba(240, 71, 71, 1);
}

.edit-icon {
  background: none;
  border: none;
  color: #7289da;
  font-size: 18px;
  cursor: pointer;
}

.edit-input,
.edit-textarea {
  width: 100%;
  padding: 5px;
  border: 1px solid #40444b;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 16px;
}

.edit-textarea {
  min-height: 80px;
  resize: vertical;
}

.save-button,
.cancel-button {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
}

.save-button {
  background-color: #7289da;
  color: #fff;
}

.cancel-button {
  background-color: #4a4a4a;
  color: #fff;
}

.edit-avatar-button {
  background-color: #7289da;
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.edit-avatar-button:hover {
  background-color: #677bc4;
}

.cancel-button {
  background-color: rgba(64, 68, 75, 0.8);
  color: #ffffff;
  border: none;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: block;
  width: 100%;
  margin-top: 16px;
}

.cancel-button:hover {
  background-color: rgba(64, 68, 75, 1);
}

.edit-avatar-button {
  z-index: 10;
}

@media (max-width: 640px) {
  .modal-content {
    width: 100%;
    height: 90vh;
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    transform: none;
    border-radius: 16px 16px 0 0;
  }
}
</style>
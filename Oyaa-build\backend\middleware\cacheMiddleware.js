/**
 * CACHE MIDDLEWARE DISABLED
 *
 * This file provides a mock implementation of the cache middleware.
 * No actual caching is performed.
 */

const logger = require('../utils/logger');

/**
 * Cache middleware with TTL (Time to Live) - DISABLED
 * @param {number} ttl - Cache duration in seconds (ignored)
 * @returns {Function} Express middleware
 */
const cacheMiddleware = (ttl = 300) => {
  return async (req, res, next) => {
    // Log that caching is disabled
    logger.debug(`[MOCK] Cache middleware disabled for: ${req.originalUrl || req.url}`);

    // Continue without caching
    next();
  };
};

module.exports = cacheMiddleware;
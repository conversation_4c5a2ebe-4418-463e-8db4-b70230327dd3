<template>
  <div class="sidebar">
    <SearchBar />
    <div class="user-list">
      <div v-for="user in users" :key="user.id" class="user-item">
        <div class="user-info">
          <span>{{ user.username }}</span>
          <UserStatus :is-online="user.isOnline" />
        </div>
        <Notifications :count="user.unreadMessages" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import SearchBar from './SearchBar.vue';
import UserStatus from './UserStatus.vue';
import Notifications from './Notifications.vue';

const users = ref([]);

onMounted(async () => {
  try {
    const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/user/nearby-users/1`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    });
    users.value = response.data.nearbyUsers;
  } catch (error) {
    console.error('Failed to fetch users:', error);
  }
});
</script>

<style scoped>
.sidebar {
  width: 300px;
  background-color: #fff;
  border-right: 1px solid #ddd;
  padding: 20px;
  overflow-y: auto;
}

.user-list {
  margin-top: 20px;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.user-item:hover {
  background-color: #f9f9f9;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
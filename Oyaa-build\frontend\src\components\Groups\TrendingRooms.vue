<template>
  <div class="trending-rooms">
    <div class="header">
      <h2>Trending Rooms</h2>
      <button class="back-button" @click="$router.push('/dashboard')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
        Back
      </button>
    </div>
    
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading trending rooms...</p>
    </div>
    
    <div v-else-if="error" class="error-container">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
      <p>Failed to load trending rooms</p>
      <p class="error-details">{{ error }}</p>
      <button @click="fetchTrendingGroups" class="retry-button">Try Again</button>
    </div>
    
    <ul v-else-if="groups.length" class="group-list">
      <li v-for="(group, index) in groups" :key="group.id" class="group-item" tabindex="0">
        <div class="group-main">
          <div class="rank">{{ index + 1 }}</div>
          <div class="group-avatar">
            <img :src="group.avatar || '/Avatar/groups-Avatar/Basketball.svg'" alt="Group Avatar" />
          </div>
          <div class="group-info">
            <span class="group-name">{{ group.name }}</span>
            <span class="group-meta">{{ group.member_count }} members</span>
          </div>
          <button @click="joinGroup(group.id)" class="join-btn">Join</button>
        </div>
        <div class="group-details">
          <div class="details-section">
            <span class="details-label">Created by:</span>
            <span class="details-value">{{ group.creator_name }}</span>
          </div>
          <div class="details-section">
            <span class="details-label">Created on:</span>
            <span class="details-value">{{ formatDate(group.created_at) }}</span>
          </div>
          <div v-if="group.description" class="details-section description">
            <span class="details-label">Description:</span>
            <span class="details-value">{{ group.description }}</span>
          </div>
          <div v-if="group.tags && group.tags.length" class="tags-container">
            <span v-for="tag in group.tags" :key="tag" class="tag">{{ tag }}</span>
          </div>
        </div>
      </li>
    </ul>
    
    <div v-else class="empty-state">
      <div class="empty-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      </div>
      <p>No trending rooms found</p>
      <p>Be the first to create a popular group!</p>
      <button @click="$router.push('/groups/create')" class="create-button">Create a Group</button>
    </div>
    
    <button v-if="hasMore" @click="loadMore" class="load-more">
      <span>Load More</span>
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="6 9 12 15 18 9"></polyline>
      </svg>
    </button>
  </div>
</template>

<script>
import axios from '@/api/axios';
import { format } from 'date-fns';

export default {
  name: 'TrendingRooms',
  data() {
    return {
      groups: [],
      loading: false,
      error: null,
      page: 1,
      limit: 20,
      hasMore: true,
    };
  },
  methods: {
    async fetchTrendingGroups() {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await axios.get(`/api/groups/trending?page=${this.page}&limit=${this.limit}`);
        const { groups: newGroups, total } = response.data;
        this.groups = [...this.groups, ...newGroups];
        this.hasMore = this.groups.length < total;
      } catch (err) {
        this.error = err.response?.data?.message || 'An error occurred';
      } finally {
        this.loading = false;
      }
    },
    
    formatDate(dateString) {
      return format(new Date(dateString), 'PPP');
    },
    
    async joinGroup(groupId) {
      try {
        const userId = this.$store.state.auth.user.id;
        await axios.post('/api/group-members/add', { groupId, userId });
        this.$router.push({ name: 'GroupChatPage', params: { groupId } });
      } catch (err) {
        this.error = err.response?.data?.message || 'Failed to join group';
      }
    },
    
    loadMore() {
      this.page += 1;
      this.fetchTrendingGroups();
    },
  },
  mounted() {
    this.fetchTrendingGroups();
  },
};
</script>

<style scoped>
.trending-rooms {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  max-width: 800px;
  margin: 20px auto;
  padding: 24px;
  background: var(--bg-tertiary);
  border-radius: 16px;
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-button:hover {
  background: var(--accent-tertiary);
  color: var(--accent-primary);
  transform: translateX(-2px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px 20px;
  color: var(--text-secondary);
  min-height: 300px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--accent-tertiary);
  border-radius: 50%;
  border-top-color: var(--accent-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
  min-height: 300px;
}

.error-container svg {
  color: #ef4444;
  margin-bottom: 16px;
}

.error-container p {
  margin: 4px 0;
  font-size: 15px;
}

.error-container p:first-of-type {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
}

.error-details {
  font-size: 14px;
  color: #ef4444;
  max-width: 400px;
  margin-bottom: 16px;
}

.retry-button {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.retry-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
}

.group-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.group-item {
  position: relative;
  padding: 16px;
  margin-bottom: 16px;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.group-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-tertiary);
}

.group-main {
  display: flex;
  align-items: center;
  gap: 16px;
}

.rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: var(--accent-tertiary);
  color: var(--accent-primary);
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
}

.group-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  background: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
}

.group-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.group-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.group-name {
  font-weight: 500;
  font-size: 16px;
  color: var(--text-primary);
}

.group-meta {
  font-size: 14px;
  color: var(--text-secondary);
}

.join-btn {
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.join-btn:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-2px);
}

.join-btn:active {
  transform: scale(0.98);
}

.group-details {
  display: none;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.group-item:hover .group-details,
.group-item:focus .group-details {
  display: block;
}

.details-section {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.details-label {
  color: var(--text-secondary);
  font-size: 14px;
  min-width: 100px;
}

.details-value {
  color: var(--text-primary);
  font-size: 14px;
  flex: 1;
}

.description {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.description .details-value {
  line-height: 1.5;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.tag {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  padding: 4px 10px;
  border-radius: 8px;
  font-size: 12px;
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
  min-height: 300px;
}

.empty-icon {
  margin-bottom: 16px;
  color: var(--accent-primary);
  opacity: 0.7;
}

.empty-state p {
  margin: 4px 0;
  font-size: 15px;
}

.empty-state p:first-of-type {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
}

.create-button {
  margin-top: 16px;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.create-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-2px);
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 24px auto 0;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.load-more:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  transform: translateY(-2px);
}

.load-more svg {
  transition: transform 0.3s ease;
}

.load-more:hover svg {
  transform: translateY(2px);
}
</style>
.message-item {
  padding: 8px 16px;
  margin-bottom: 4px;
}

.message-content {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 80%;
}

.current-user .message-content {
  margin-left: auto;
  flex-direction: row-reverse;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-bubble {
  background: #f1f3f4;
  border-radius: 18px;
  padding: 12px 16px;
  position: relative;
  word-wrap: break-word;
  max-width: 100%;
}

.current-user .message-bubble {
  background: #007bff;
  color: white;
}

.sender-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: #666;
  margin-bottom: 4px;
}

.current-user .sender-name {
  color: rgba(255, 255, 255, 0.8);
}

.message-text {
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 0.7rem;
  opacity: 0.7;
}

.timestamp {
  color: inherit;
}

.status {
  font-size: 0.6rem;
}

.status-sent {
  color: #28a745;
}

.status-delivered {
  color: #007bff;
}

.status-read {
  color: #007bff;
}

.status-failed {
  color: #dc3545;
}

/* Responsive design */
@media (max-width: 768px) {
  .message-content {
    max-width: 90%;
  }
  
  .message-item {
    padding: 6px 12px;
  }
}

* {
  font-family: 'Rubik', sans-serif;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
  touch-action: manipulation; /* Improve touch behavior */
}

html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: #1E1F22;
  height: 100%;
  width: 100%;
  position: fixed; /* Prevent bounce on iOS */
}

/* Prevent text size adjustment on orientation change */
html {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* Improve scrolling on iOS */
* {
  -webkit-overflow-scrolling: touch;
}

/* Fix for iOS input zoom */
input, textarea, select {
  font-size: 16px !important; /* Prevents zoom on focus in iOS */
  -webkit-appearance: none; /* Remove iOS default styling */
  appearance: none;
  border-radius: 0; /* Remove iOS default styling */
}

/* Safe area insets for notched phones */
@supports (padding: max(0px)) {
  body {
    padding-left: env(safe-area-inset-left, 0px);
    padding-right: env(safe-area-inset-right, 0px);
    padding-top: env(safe-area-inset-top, 0px);
    padding-bottom: env(safe-area-inset-bottom, 0px);
  }
}

/* Fix for bottom navigation on notched phones */
.bottom-navigation {
  padding-bottom: max(8px, env(safe-area-inset-bottom, 0px));
}
/* Improve touch feedback */
button, 
[role="button"],
.clickable {
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Improve text selection on mobile */
::selection {
  background-color: rgba(52, 152, 219, 0.4);
  color: inherit;
}
/* Optimize for different screen sizes */
@media (max-width: 768px) {
  html {
    font-size: 14px; /* Slightly smaller base font size on mobile */
  }
}

@media (max-width: 480px) {
  html {
    font-size: 13px;
  }
}

/* Fix for Safari bottom bar */
@supports (-webkit-touch-callout: none) {
  .content-wrapper {
    /* Adjust for Safari's bottom bar */
    padding-bottom: 70px;
  }
}
/* Fix for iOS keyboard appearance */
@supports (-webkit-touch-callout: none) {
  body.keyboard-open {
    height: -webkit-fill-available;
    position: fixed;
    width: 100%;
    overflow: hidden;
  }
  
  .input-focused {
    position: relative;
    z-index: 1001;
  }
}

/* Improve scrolling performance */
* {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
<template>
    <div class="gif-section">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <div class="spinner"></div>
        <div class="loading-text">Loading GIFs...</div>
      </div>
      
      <!-- Empty State -->
      <div v-else-if="gifs.length === 0" class="empty-state">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
          <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
          <circle cx="9" cy="9" r="2"/>
          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
        </svg>
        <div class="empty-text">No GIFs found</div>
        <div class="empty-subtext">Try a different search term</div>
      </div>
      
      <!-- GIF Grid -->
      <div v-else class="gif-grid">
        <div 
          v-for="gif in gifs" 
          :key="gif.id" 
          class="gif-item"
          @click="selectGif(gif)"
        >
          <div class="gif-wrapper">
            <img 
              :src="gif.images.fixed_height.url" 
              :alt="gif.title || 'GIF'"
              loading="lazy"
              class="gif-image"
            />
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import { ref, watch, onMounted } from 'vue';
  import { GiphyFetch } from '@giphy/js-fetch-api';
  import debounce from 'lodash.debounce';
  
  export default {
    name: 'GifSection',
    props: {
      searchQuery: {
        type: String,
        default: ''
      }
    },
    emits: ['select'],
    setup(props, { emit }) {
      const gifs = ref([]);
      const isLoading = ref(true);
      
      // Initialize Giphy API client
      const gf = new GiphyFetch('o4Jhck5Plb4kryLjWXh1D9BJY7QW1CBW');
      
      // Fetch trending GIFs
      const fetchTrendingGifs = async () => {
        try {
          isLoading.value = true;
          const { data } = await gf.trending({ limit: 30 });
          gifs.value = data;
        } catch (error) {
          console.error('Error fetching trending GIFs:', error);
        } finally {
          isLoading.value = false;
        }
      };
      
      // Search GIFs
      const searchGifs = async (query) => {
        try {
          isLoading.value = true;
          if (query) {
            const { data } = await gf.search(query, { limit: 30 });
            gifs.value = data;
          } else {
            fetchTrendingGifs();
          }
        } catch (error) {
          console.error('Error searching GIFs:', error);
        } finally {
          isLoading.value = false;
        }
      };
      
      // Debounced search function
      const debouncedSearch = debounce((query) => {
        searchGifs(query);
      }, 300);
      
      // Select a GIF
      const selectGif = (gif) => {
        emit('select', gif);
      };
      
      // Watch for search query changes
      watch(() => props.searchQuery, (newQuery) => {
        debouncedSearch(newQuery);
      });
      
      onMounted(() => {
        fetchTrendingGifs();
      });
      
      return {
        gifs,
        isLoading,
        selectGif
      };
    }
  };
  </script>
  
  <style scoped>
  .gif-section {
    height: 100%;
    min-height: 200px;
    position: relative;
  }
  
  .gif-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    padding: 12px;
  }
  
  .gif-item {
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s;
    background: #1f2c33;
  }
  
  .gif-item:hover {
    transform: scale(1.03);
  }
  
  .gif-wrapper {
    position: relative;
    width: 100%;
    padding-top: 100%; /* 1:1 Aspect Ratio */
    overflow: hidden;
  }
  
  .gif-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  /* Loading state */
  .loading-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(34, 46, 53, 0.8);
    z-index: 2;
  }
  
  .spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(0, 168, 132, 0.3);
    border-radius: 50%;
    border-top-color: #00a884;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }
  
  .loading-text {
    color: #e0e0e0;
    font-size: 14px;
  }
  
  /* Empty state */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200px;
    color: #8a9aa4;
    padding: 20px;
    text-align: center;
  }
  
  .empty-state svg {
    margin-bottom: 16px;
    opacity: 0.6;
  }
  
  .empty-text {
    font-size: 16px;
    margin-bottom: 8px;
    color: #e0e0e0;
  }
  
  .empty-subtext {
    font-size: 14px;
    color: #8a9aa4;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  /* Responsive adjustments */
  @media (min-width: 480px) {
    .gif-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  @media (min-width: 768px) {
    .gif-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  </style>
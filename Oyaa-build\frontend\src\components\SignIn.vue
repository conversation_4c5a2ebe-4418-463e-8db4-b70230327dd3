<!-- frontend/src/components/SignIn.vue -->
<template>
  <div class="sign-in-container" :class="{ 'dark-mode': isDarkMode }">
    <ParticleBackground :theme="isDarkMode" />
    <ThemeToggle @toggle="toggleTheme" :isDarkMode="isDarkMode" />
    <main class="main-content">
      <div class="content-wrapper">
        <form class="form" @submit.prevent="handleSubmit">
          <div class="header">
            <img :src="isDarkMode ? '/Oyaalogo-DB.svg' : '/Oyaa-logo-LB.svg'" alt="Oyaa Logo" class="logo-img" />
            <h1 class="app-name">Oyaa</h1>
          </div>

          <div class="flex-column">
            <label>Email</label>
          </div>
          <div class="inputForm">
            <svg class="theme-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <input
              placeholder="Enter your Email"
              class="input"
              type="email"
              v-model="email"
              required
              @input="clearError"
            />
          </div>

          <div class="flex-column">
            <label>Password</label>
          </div>
          <div class="inputForm">
            <svg class="theme-icon" xmlns="http://www.w3.org/2000/svg" width="20" viewBox="-64 0 512 512" height="20">
              <path fill="currentColor" d="m336 512h-288c-26.453125 0-48-21.523438-48-48v-224c0-26.476562 21.546875-48 48-48h288c26.453125 0 48 21.523438 48 48v224c0 26.476562-21.546875 48-48 48zm-288-288c-8.8125 0-16 7.167969-16 16v224c0 8.832031 7.1875 16 16 16h288c8.8125 0 16-7.167969 16-16v-224c0-8.832031-7.1875-16-16-16zm0 0"></path>
              <path fill="currentColor" d="m304 224c-8.832031 0-16-7.167969-16-16v-80c0-52.929688-43.070312-96-96-96s-96 43.070312-96 96v80c0 8.832031-7.167969 16-16 16s-16-7.167969-16-16v-80c0-70.59375 57.40625-128 128-128s128 57.40625 128 128v80c0 8.832031-7.167969 16-16 16zm0 0"></path>
            </svg>
            <input
              placeholder="Enter your Password"
              class="input"
              :type="showPassword ? 'text' : 'password'"
              v-model="password"
              required
              @input="clearError"
            />
            <button type="button" class="eye-icon" @click="togglePasswordVisibility">
              <svg
                class="theme-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path v-if="showPassword" d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                <path v-else d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                <circle cx="12" cy="12" r="3" />
              </svg>
            </button>
          </div>

          <div class="flex-row">
            <div class="remember">
              <input type="checkbox" id="remember" v-model="remember" />
              <label for="remember">Remember me</label>
            </div>
            <span class="span" @click="showForgotPassword = true">Forgot password?</span>
          </div>

          <div v-if="error || localError" class="error-message">{{ localError || error }}</div>

          <button type="submit" class="button-submit" :disabled="loading || authLoading">
            <span v-if="loading || authLoading">Signing in...</span>
            <span v-else>Sign In</span>
          </button>

          <div class="divider">
            <span>OR</span>
          </div>

          <button type="button" class="google-button" @click="handleGoogleSignIn" :disabled="loading || authLoading">
            <img src="/google-icon.svg" alt="Google" class="google-icon" />
            <span v-if="loading || authLoading">Signing in...</span>
            <span v-else>Sign in with Google</span>
          </button>

          <p class="p">
            Don't have an account? <router-link to="/signup" class="span">Sign Up</router-link>
          </p>
        </form>

        <!-- Forgot Password Modal -->
        <div v-if="showForgotPassword" class="modal-overlay" @click.self="showForgotPassword = false">
          <div class="modal">
            <h2>Reset Password</h2>
            <p>Enter your email address and we'll send you a link to reset your password.</p>
            <div class="inputForm">
              <svg class="theme-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
              <input
                placeholder="Enter your Email"
                class="input"
                type="email"
                v-model="resetEmail"
                required
              />
            </div>
            <div v-if="resetError" class="error-message">{{ resetError }}</div>
            <div v-if="resetSuccess" class="success-message">{{ resetSuccess }}</div>
            <div class="modal-buttons">
              <button type="button" class="cancel-button" @click="showForgotPassword = false">Cancel</button>
              <button type="button" class="reset-button" @click="handleResetPassword" :disabled="resetLoading">
                <span v-if="resetLoading">Sending...</span>
                <span v-else>Send Reset Link</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Username Selection Modal for Google Sign-in -->
        <div v-if="needsUsername" class="modal-overlay">
          <div class="modal">
            <h2>{{ isNewUser ? 'Choose a Username' : 'Complete Your Sign In' }}</h2>
            <p v-if="isNewUser">Please choose a unique username for your Oyaa account. This username will be visible to other users and cannot be changed later.</p>
            <p v-else>You need to set a username to complete your sign in. This username will be visible to other users and cannot be changed later.</p>
            <p class="note">Note: We don't use your Google name automatically to ensure you have a unique identity in our application.</p>
            <div
              class="inputForm"
              :class="{ 'input-valid': usernameValid, 'input-error': usernameError && usernameError.length > 0 }"
            >
              <span>@</span>
              <input
                placeholder="Enter your Username"
                class="input"
                type="text"
                v-model="newUsername"
                required
                maxlength="20"
                @input="validateUsername"
                ref="usernameInput"
                autofocus
              />
              <span v-if="usernameValid" class="validation-icon valid-icon">✓</span>
              <span v-if="usernameError && usernameError.length > 0" class="validation-icon error-icon">✗</span>
            </div>
            <div v-if="usernameError" class="error-message">{{ usernameError }}</div>
            <div v-if="usernameValid" class="success-message">
              Username is available!
              {{ isNewUser ? 'Click "Save Username" to continue.' : 'Click "Complete Sign In" to continue.' }}
            </div>
            <div class="username-requirements">
              <p>Username requirements:</p>
              <ul>
                <li>3-20 characters long</li>
                <li>Letters, numbers, underscores only</li>
                <li>Must be unique</li>
                <li>This will be your display name in the app</li>
              </ul>
            </div>
            <div class="modal-buttons">
              <button
                type="button"
                class="reset-button"
                @click="handleUsernameSubmit"
                :disabled="usernameLoading || !newUsername || (usernameError && usernameError.length > 0) || !usernameValid"
              >
                <span v-if="usernameLoading">Saving...</span>
                <span v-else>{{ isNewUser ? 'Save Username' : 'Complete Sign In' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import ParticleBackground from '@/components/ParticleBackground.vue';
import ThemeToggle from '@/components/ThemeToggle.vue';
import { forceDashboardRedirect } from '../utils/authHandler';

const store = useStore();
const router = useRouter();

const isDarkMode = ref(localStorage.getItem('theme') === 'dark' || true);
const email = ref('');
const password = ref('');
const remember = ref(false);
const showPassword = ref(false);

// Forgot password
const showForgotPassword = ref(false);
const resetEmail = ref('');
const resetLoading = ref(false);
const resetError = ref('');
const resetSuccess = ref('');

// Google sign-in with username selection
const needsUsername = computed(() => store.getters['auth/needsUsername']);
const isNewUser = computed(() => store.getters['auth/isNewUser']);
const newUsername = ref('');
const usernameLoading = ref(false);
const usernameError = ref('');
const usernameValid = ref(false);
const usernameInput = ref(null);

// Debounce function for username validation
const debounce = (fn, delay) => {
  let timeoutId;
  return function(...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), delay);
  };
};

// Validate username format and availability
const validateUsername = debounce(async () => {
  // Reset validation state
  usernameValid.value = false;
  usernameError.value = '';

  // Check if username is empty
  if (!newUsername.value) {
    console.log('Username is empty, validation skipped');
    return;
  }

  // Check username format
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  if (!usernameRegex.test(newUsername.value)) {
    console.log('Username format invalid:', newUsername.value);
    usernameError.value = 'Username must be 3-20 characters and contain only letters, numbers, and underscores';
    return;
  }

  // Check username availability
  try {
    console.log('Checking username availability for:', newUsername.value);

    // Call the store action to check username availability
    const isAvailable = await store.dispatch('auth/checkUsernameAvailability', newUsername.value);
    console.log('Username availability result:', isAvailable);

    if (!isAvailable) {
      usernameError.value = 'This username is already taken';
      usernameValid.value = false;
    } else {
      // Username is available
      console.log('Username is valid and available:', newUsername.value);
      usernameValid.value = true;
      usernameError.value = ''; // Ensure error is cleared
    }
  } catch (err) {
    console.error('Username validation error:', err);

    // Provide a more specific error message if possible
    if (err.response && err.response.data && err.response.data.message) {
      usernameError.value = err.response.data.message;
    } else if (err.message) {
      usernameError.value = err.message;
    } else {
      usernameError.value = 'Error checking username availability';
    }

    // If we can't check availability due to server issues, assume it's valid
    // The backend will do a final check during registration
    if (err.message && err.message.includes('Network Error')) {
      console.log('Network error during validation, assuming username is valid');
      usernameValid.value = true;
      usernameError.value = ''; // Clear error message
    }
  }

  // Log final validation state
  console.log('Final validation state:', {
    username: newUsername.value,
    valid: usernameValid.value,
    error: usernameError.value
  });
}, 500);

// Use ref for local loading state instead of computed
const loading = ref(false);
// Use computed for auth store loading state
const authLoading = computed(() => store.state.auth.loading);
const error = computed(() => store.state.auth.error);
// Local error for Google sign-in errors
const localError = ref('');

// Watch for needsUsername changes
watch(needsUsername, (newValue) => {
  if (newValue) {
    // Reset the username field when the modal appears
    newUsername.value = '';
    usernameError.value = '';
    usernameValid.value = false;

    // Focus the username input field after a short delay to ensure the modal is rendered
    setTimeout(() => {
      if (usernameInput.value) {
        usernameInput.value.focus();
      }
    }, 100);
  }
});

onMounted(() => {
  document.title = 'Oyaa - Sign In';
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme) isDarkMode.value = savedTheme === 'dark';

  // Add viewport meta tag for better mobile display
  const viewportMeta = document.createElement('meta');
  viewportMeta.name = 'viewport';
  viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
  document.head.appendChild(viewportMeta);
});

const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value;
  localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light');
};

const handleSubmit = async () => {
  try {
    // Show loading state
    loading.value = true;

    // Clear any previous errors
    clearError();

    await store.dispatch('auth/login', {
      email: email.value,
      password: password.value,
      remember: remember.value,
    });

    // Force redirect to dashboard after a short delay
    setTimeout(() => {
      if (!forceDashboardRedirect()) {
        router.push('/dashboard');
      }
    }, 500);
  } catch (err) {
    // Error is already handled in the store
    if (err.message) {
      localError.value = err.message;
    }
  } finally {
    // Hide loading state
    loading.value = false;
  }
};

const handleGoogleSignIn = async () => {
  try {
    // Show loading state
    loading.value = true;

    // Clear any previous errors
    clearError();

    console.log('Starting Google sign-in from SignIn component');

    const result = await store.dispatch('auth/signInWithGoogle');

    console.log('Google sign-in result:', result);

    // Check if the user needs to select a username
    if (result?.needsUsername) {
      console.log('User needs to select a username');
      console.log('Is new user:', result.isNewUser);

      // The store should already be updated by the signInWithGoogle action
      // Just verify that needsUsername is set correctly
      if (!store.getters['auth/needsUsername']) {
        console.warn('WARNING: needsUsername is false but should be true!');

        // Force needsUsername to true with the correct isNewUser flag
        store.commit('auth/SET_NEEDS_USERNAME', {
          firebaseUser: result.user,
          isNewUser: result.isNewUser || false
        });
      }
    } else {
      // User doesn't need to select a username, redirect to dashboard
      console.log('User already has a username, redirecting to dashboard');
      router.push('/dashboard');
    }
  } catch (err) {
    console.error('Google sign-in error in component:', err);

    // Display a user-friendly error message
    if (err.message) {
      // Set a local error message for specific Google sign-in errors
      if (err.message.includes('popup') || err.message.includes('network')) {
        localError.value = err.message;
      } else {
        // Let the store handle other errors
        console.warn('Google sign-in error:', err.message);
      }
    }
  } finally {
    // Hide loading state
    loading.value = false;
  }
};

const handleResetPassword = async () => {
  if (!resetEmail.value) {
    resetError.value = 'Please enter your email address';
    return;
  }

  resetLoading.value = true;
  resetError.value = '';
  resetSuccess.value = '';

  try {
    await store.dispatch('auth/resetPassword', resetEmail.value);
    resetSuccess.value = 'Password reset link sent to your email';
    // Close the modal after 3 seconds
    setTimeout(() => {
      showForgotPassword.value = false;
      resetEmail.value = '';
      resetSuccess.value = '';
    }, 3000);
  } catch (err) {
    resetError.value = err.message || 'Failed to send reset link';
  } finally {
    resetLoading.value = false;
  }
};

const handleUsernameSubmit = async () => {
  if (!newUsername.value) {
    usernameError.value = 'Please enter a username';
    return;
  }

  // Validate username format
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  if (!usernameRegex.test(newUsername.value)) {
    usernameError.value = 'Username must be 3-20 characters and contain only letters, numbers, and underscores';
    return;
  }

  usernameLoading.value = true;
  usernameError.value = '';
  usernameValid.value = false;

  try {
    console.log('Submitting username:', newUsername.value);

    // Make sure we have a Firebase user
    const firebaseUser = store.getters['auth/firebaseUser'];
    if (!firebaseUser) {
      throw new Error('No Firebase user found. Please sign in again.');
    }

    console.log('Firebase user found:', firebaseUser.uid);

    // Check username availability one more time before submission
    console.log('Checking username availability one more time');

    let isAvailable = true; // Default to true if we can't check

    try {
      isAvailable = await store.dispatch('auth/checkUsernameAvailability', newUsername.value);
      console.log('Username availability result:', isAvailable);

      if (!isAvailable) {
        usernameError.value = 'This username is already taken';
        usernameLoading.value = false;
        return;
      }
    } catch (availabilityError) {
      console.warn('Error checking username availability, proceeding anyway:', availabilityError);
      // If we can't check availability due to server issues, we'll proceed anyway
      // The backend will do a final check during registration
    }

    console.log('Username is available, completing Google sign-up');

    // Try to complete the Google sign-up process with the chosen username
    try {
      const result = await store.dispatch('auth/completeGoogleSignUp', {
        username: newUsername.value,
        handle: newUsername.value, // Use the same value for handle
        avatar: firebaseUser.photoURL || null // Use the Google profile picture if available
      });

      console.log('Google sign-up completed successfully with username:', newUsername.value);
      console.log('Result:', result);

      // Show success message
      usernameValid.value = true;

      // Store the token in localStorage
      if (result && result.token) {
        console.log('Storing token in localStorage');
        localStorage.setItem('token', result.token);
      } else {
        console.warn('No token received from completeGoogleSignUp');

        // If no token but we have a Firebase user, get a token directly
        try {
          console.log('Getting token directly from Firebase');
          const idToken = await firebaseUser.getIdToken(true);
          localStorage.setItem('token', idToken);
          console.log('Token obtained directly from Firebase');
        } catch (tokenError) {
          console.error('Failed to get token from Firebase:', tokenError);
        }
      }

      // Ensure the auth state is refreshed before redirecting
      console.log('Refreshing auth state before redirect');
      store.commit('auth/REFRESH_AUTH_STATE');

      // Trigger the auth state change handler to update the user state
      try {
        console.log('Triggering auth state change handler');
        const idToken = await firebaseUser.getIdToken(true);
        localStorage.setItem('token', idToken);

        // Force a login success with the new username and handle
        store.commit('auth/LOGIN_SUCCESS', {
          user: {
            id: result.user.id,
            firebaseUid: firebaseUser.uid,
            username: newUsername.value,
            handle: newUsername.value, // Use the same value for handle
            email: firebaseUser.email,
            avatar: firebaseUser.photoURL
          },
          token: idToken,
          firebaseUser: firebaseUser
        });

        // Clear the needs username flag
        store.commit('auth/CLEAR_NEEDS_USERNAME');

        // Force another refresh
        store.commit('auth/REFRESH_AUTH_STATE');
      } catch (authError) {
        console.error('Error triggering auth state change:', authError);
      }

      // Redirect to dashboard after a short delay to show the success message
      console.log('Redirecting to dashboard in 1 second');
      setTimeout(() => {
        // Force another refresh right before redirect
        store.commit('auth/REFRESH_AUTH_STATE');

        // Ensure token is in localStorage
        if (!localStorage.getItem('token') && result && result.token) {
          console.log('Setting token in localStorage before redirect');
          localStorage.setItem('token', result.token);
        }

        router.push('/dashboard');
      }, 1000);
    } catch (registrationError) {
      console.error('Registration error:', registrationError);

      // If the error is due to the username already existing, show that message
      if (registrationError.message && registrationError.message.includes('already exists')) {
        usernameError.value = 'This username is already taken. Please choose another.';
      } else if (registrationError.response && registrationError.response.data && registrationError.response.data.message) {
        usernameError.value = registrationError.response.data.message;
      } else if (registrationError.message) {
        usernameError.value = registrationError.message;
      } else {
        usernameError.value = 'Failed to register with the server. Please try again.';
      }

      usernameLoading.value = false;
    }
  } catch (err) {
    console.error('Username submission error:', err);

    // Provide a more specific error message if possible
    if (err.response && err.response.data && err.response.data.message) {
      usernameError.value = err.response.data.message;
    } else if (err.message) {
      usernameError.value = err.message;
    } else {
      usernameError.value = 'Failed to set username';
    }

    usernameLoading.value = false;
  }
};

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

const clearError = () => {
  localError.value = '';
  store.commit('auth/CLEAR_ERROR');
};
</script>

<style scoped>
.sign-in-container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  transition: background-color 0.3s ease;
  background: linear-gradient(to bottom right, #f8fafc, #e2e8f0);
  color: #1e293b;
}

.sign-in-container.dark-mode {
  background: linear-gradient(to bottom right, #18181b, #09090b);
  color: #ffffff;
}

.main-content {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
}

.content-wrapper {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 28rem;
  padding: 0 0.5rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #ffffff;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  font-family: 'Rubik', sans-serif;
  animation: fadeIn 0.8s forwards;

}

.dark-mode .form {
  background-color: #18181b;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.logo-img {
  width: 60px;
  height: auto;
  margin-bottom: 8px;
}

.app-name {
  font-size: 20px;
  color: #151717;
  margin: 0;
}

.dark-mode .app-name {
  color: #ffffff;
}

.flex-column > label {
  color: #151717;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.dark-mode .flex-column > label {
  color: #ffffff;
}

.inputForm {
  border: 1.5px solid #ecedec;
  border-radius: 8px;
  height: 42px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  transition: border 0.2s ease-in-out;
  position: relative;
}

.dark-mode .inputForm {
  border: 1.5px solid #444;
}

.inputForm.input-valid {
  border-color: #10b981; /* Green border for valid input */
}

.inputForm.input-error {
  border-color: #ef4444; /* Red border for invalid input */
}

.validation-icon {
  position: absolute;
  right: 12px;
  font-size: 18px;
  pointer-events: none;
}

.valid-icon {
  color: #10b981; /* Green */
}

.error-icon {
  color: #ef4444; /* Red */
}

.input {
  margin-left: 8px;
  border: none;
  width: 100%;
  height: 100%;
  font-size: 14px;
  background: transparent;
  color: #151717;
}

.dark-mode .input {
  color: #ffffff;
}

.input:focus {
  outline: none;
}

.input::placeholder {
  color: #888;
  font-size: 13px;
}

.dark-mode .input::placeholder {
  color: #aaa;
}

.inputForm:focus-within {
  border: 1.5px solid #2d79f3;
}

.theme-icon {
  color: #151717;
  width: 16px;
  height: 16px;
}

.dark-mode .theme-icon {
  color: #ffffff;
}

.eye-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-row {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: space-between;
  font-size: 12px;
}

.remember {
  display: flex;
  align-items: center;
  gap: 4px;
}

.remember label {
  color: #151717;
  font-size: 12px;
}

.dark-mode .remember label {
  color: #ffffff;
}

.span {
  font-size: 12px;
  color: #2d79f3;
  font-weight: 500;
  cursor: pointer;
}

.button-submit {
  background-color: #151717;
  border: none;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  height: 42px;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 4px;
}

.dark-mode .button-submit {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
}

.button-submit:hover:not(:disabled) {
  background-color: #292929;
}

.dark-mode .button-submit:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.2);
}

.button-submit:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.p {
  text-align: center;
  color: #151717;
  font-size: 12px;
  margin: 4px 0;
}

.dark-mode .p {
  color: #ffffff;
}

.error-message {
  color: #ef4444;
  margin: 8px 0;
  padding: 8px 12px;
  text-align: left;
  font-size: 13px;
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: 6px;
  border-left: 3px solid #ef4444;
}

.success-message {
  color: #10b981;
  margin: 8px 0;
  padding: 8px 12px;
  text-align: left;
  font-size: 13px;
  background-color: rgba(16, 185, 129, 0.1);
  border-radius: 6px;
  border-left: 3px solid #10b981;
  font-weight: 500;
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 10px 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #e2e8f0;
}

.dark-mode .divider::before,
.dark-mode .divider::after {
  border-bottom: 1px solid #444;
}

.divider span {
  padding: 0 10px;
  font-size: 12px;
  color: #888;
}

.google-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: white;
  border: 1px solid #e2e8f0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  height: 42px;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .google-button {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid #444;
  color: #fff;
}

.google-button:hover:not(:disabled) {
  background-color: #f8f9fa;
  border-color: #d2d6dc;
}

.dark-mode .google-button:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}

.google-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.google-icon {
  width: 18px;
  height: 18px;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  animation: fadeIn 0.3s forwards;
}

.modal {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s forwards;
}

.dark-mode .modal {
  background-color: #18181b;
  color: white;
}

.modal h2 {
  margin-top: 0;
  font-size: 18px;
  margin-bottom: 10px;
}

.modal p {
  font-size: 14px;
  margin-bottom: 15px;
  color: #666;
}

.dark-mode .modal p {
  color: #aaa;
}

.note {
  font-size: 13px;
  color: #666;
  font-style: italic;
  margin-top: 8px;
}

.dark-mode .note {
  color: #aaa;
}

.username-requirements {
  margin-top: 16px;
  font-size: 13px;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #2d79f3;
}

.dark-mode .username-requirements {
  background-color: #27272a;
  border-left: 3px solid #3b82f6;
}

.username-requirements p {
  margin: 0 0 8px 0;
  font-weight: 600;
}

.username-requirements ul {
  margin: 0;
  padding-left: 20px;
}

.username-requirements li {
  margin-bottom: 4px;
}

/* This style is now defined above */

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid #e2e8f0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  height: 36px;
  padding: 0 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .cancel-button {
  border: 1px solid #444;
  color: #aaa;
}

.cancel-button:hover {
  background-color: #f8f9fa;
}

.dark-mode .cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.reset-button {
  background-color: #2d79f3;
  border: none;
  color: white;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  height: 36px;
  padding: 0 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-button:hover:not(:disabled) {
  background-color: #1a65e0;
}

.reset-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Mobile-specific styles */
@media (max-width: 480px) {
  .content-wrapper {
    padding: 0;
    max-width: 100%;
  }

  .form {
    padding: 16px;
    border-radius: 12px;
    gap: 10px;
  }

  .logo-img {
    width: 50px;
  }

  .app-name {
    font-size: 18px;
  }

  .inputForm {
    height: 38px;
  }

  .button-submit, .google-button {
    height: 38px;
    font-size: 13px;
  }

  .flex-column > label {
    font-size: 13px;
  }

  .input::placeholder {
    font-size: 12px;
  }

  .remember label, .span, .p {
    font-size: 11px;
  }

  .modal {
    width: 95%;
    padding: 15px;
  }

  .modal h2 {
    font-size: 16px;
  }

  .modal p {
    font-size: 12px;
  }

  .modal-buttons {
    margin-top: 15px;
  }

  .cancel-button, .reset-button {
    height: 32px;
    font-size: 12px;
    padding: 0 12px;
  }
}

/* Tablet and desktop styles */
@media (min-width: 640px) {
  .content-wrapper {
    padding: 0 1rem;
  }

  .form {
    padding: 30px;
    gap: 15px;
  }

  .logo-img {
    width: 80px;
  }

  .app-name {
    font-size: 24px;
  }

  .inputForm {
    height: 50px;
  }

  .button-submit, .google-button {
    height: 50px;
    font-size: 15px;
  }

  .flex-column > label {
    font-size: 16px;
  }

  .input {
    font-size: 16px;
  }

  .input::placeholder {
    font-size: 14px;
  }

  .remember label, .span, .p {
    font-size: 14px;
  }

  .theme-icon {
    width: 20px;
    height: 20px;
  }

  .modal {
    padding: 25px;
  }

  .modal h2 {
    font-size: 20px;
  }

  .modal p {
    font-size: 15px;
  }

  .modal-buttons {
    margin-top: 25px;
  }

  .cancel-button, .reset-button {
    height: 40px;
    font-size: 15px;
    padding: 0 20px;
  }
}

/* Fix for iOS viewport height issue */
@supports (-webkit-touch-callout: none) {
  .main-content {
    min-height: -webkit-fill-available;
  }
}
</style>
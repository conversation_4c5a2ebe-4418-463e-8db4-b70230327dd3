const GroupService = require('../../services/Groups/groupService.js');
const { valkeyClient } = require('../../config/valkey.config.js');
const GroupLocationService = require('../../services/Groups/groupLocationService.js');

class GroupController {
// backend/controllers/Group/groupController.js
async createGroup(req, res) {
  try {
    const { name, creatorId, description = '', avatar, tags = [], latitude, longitude } = req.body;
    const isWorldChat = false; // Explicitly set for regular group creation
    const group = await GroupService.createGroup(
      name,
      creatorId,
      description,
      avatar,
      tags,          // Pass tags explicitly as the fifth argument
      isWorldChat,
      latitude,
      longitude
    );
    console.log('[createGroup] Group created:', group);
    res.status(200).json({ message: 'Group created', group });
  } catch (err) {
    console.error('[createGroup] Error:', err);
    res.status(500).json({ message: err.message });
  }
}

  async getGroups(req, res) {
    try {
      const { userId } = req.params;
      const groups = await GroupService.getGroups(userId);
      console.log('[getGroups] Groups for user', userId, groups);
      res.status(200).json({ groups });
    } catch (err) {
      console.error('[getGroups] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async getGroupRequests(req, res) {
    try {
      const { userId } = req.params;
      const { lastUpdated } = req.query;
      const groupRequests = await GroupService.getGroupRequests(userId, lastUpdated);
      res.status(200).json({
        requests: groupRequests,
        lastUpdated: Date.now(),
      });
    } catch (err) {
      console.error('[getGroupRequests] Error:', err);
      res.status(500).json({ message: err.message, stack: err.stack });
    }
  }

  async searchGroups(req, res) {
    try {
      const { name } = req.query;
      console.log('[searchGroups] Query parameter:', name);
      const groups = await GroupService.searchGroups(name);
      console.log('[searchGroups] Found groups:', groups);
      res.status(200).json({ groups });
    } catch (err) {
      console.error('[searchGroups] Error:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async getGroupById(req, res) {
    try {
        const groupId = req.params.groupId;
        if (!/^\d+$/.test(groupId)) {
            return res.status(400).json({ message: 'Group ID must be a number' });
        }
        const group = await GroupService.getGroupById(groupId);
        if (group) {
            console.log('[getGroupById] Group found:', group);
            res.status(200).json({ group });
        } else {
            res.status(404).json({ message: 'Group not found' });
        }
    } catch (err) {
        console.error('[getGroupById] Error:', err);
        res.status(500).json({ message: err.message });
    }
}
// groupController.js
async getTrendingGroups(req, res) {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const data = await GroupService.getTrendingGroups(page, limit);
  res.status(200).json(data);
}
async getNearbyGroups(req, res) {
  try {
    const { lat, lng, radius = 10000, limit = 10, offset = 0 } = req.query;
    if (!lat || !lng) {
      return res.status(400).json({ message: 'Latitude and longitude are required' });
    }
    const groups = await GroupLocationService.getNearbyGroups(
      parseFloat(lat),
      parseFloat(lng),
      parseFloat(radius),
      parseInt(limit),
      parseInt(offset)
    );
    console.log('[getNearbyGroups] Found groups:', groups);
    res.status(200).json({ groups });
  } catch (err) {
    console.error('[getNearbyGroups] Error:', err);
    res.status(500).json({ message: err.message });
  }
}
async searchGroupsByTags(req, res) {
  try {
    let { tags, limit = 10, offset = 0 } = req.query;
    if (!tags || !Array.isArray(tags)) {
      return res.status(400).json({ message: 'Tags must be an array' });
    }

    // Convert all tags to lowercase for case-insensitive matching
    tags = tags.map(tag => tag.toLowerCase());
    console.log('[searchGroupsByTags] Searching for tags (lowercase):', tags);

    const groups = await GroupService.searchGroupsByTags(tags, parseInt(limit), parseInt(offset));
    res.status(200).json({ groups });
  } catch (err) {
    console.error('[searchGroupsByTags] Error:', err);
    res.status(500).json({ message: err.message });
  }
}

async getTagSuggestions(req, res) {
  try {
    const { query } = req.query;
    if (!query) {
      return res.status(400).json({ message: 'Query parameter is required' });
    }
    const suggestions = await GroupService.getTagSuggestions(query);
    res.status(200).json({ tags: suggestions });
  } catch (err) {
    console.error('[getTagSuggestions] Error:', err);
    res.status(500).json({ message: err.message });
  }
}
// Add this method inside the GroupController class
async updateGroupAvatar(req, res) {
  try {
    const { groupId } = req.params; // Extract groupId from URL
    const { avatar } = req.body;     // Extract new avatar from request body
    const userId = req.user.userId;  // Changed from req.user.id to req.user.userId

    const updatedGroup = await GroupService.updateGroupAvatar(groupId, userId, avatar);
    console.log('[updateGroupAvatar] Group avatar updated:', updatedGroup);
    res.status(200).json({ message: 'Group avatar updated successfully', group: updatedGroup });
  } catch (err) {
    console.error('[updateGroupAvatar] Error:', err);
    const status = err.status || 500;
    res.status(status).json({ message: err.message });
  }
}
async updateGroupName(req, res) {
  try {
    const { groupId } = req.params;
    const { name } = req.body;
    const userId = req.user.userId;
    const updatedGroup = await GroupService.updateGroupName(groupId, userId, name);
    res.status(200).json({ message: 'Group name updated successfully', group: updatedGroup });
  } catch (err) {
    console.error('[updateGroupName] Error:', err);
    const status = err.status || 500;
    res.status(status).json({ message: err.message });
  }
}

async updateGroupDescription(req, res) {
  try {
    const { groupId } = req.params;
    const { description } = req.body;
    const userId = req.user.userId;
    const updatedGroup = await GroupService.updateGroupDescription(groupId, userId, description);
    res.status(200).json({ message: 'Group description updated successfully', group: updatedGroup });
  } catch (err) {
    console.error('[updateGroupDescription] Error:', err);
    const status = err.status || 500;
    res.status(status).json({ message: err.message });
  }
}

async updateGroupTags(req, res) {
  try {
    const { groupId } = req.params;
    const { tags } = req.body;
    const userId = req.user.userId;
    const updatedGroup = await GroupService.updateGroupTags(groupId, userId, tags);
    res.status(200).json({ message: 'Group tags updated successfully', group: updatedGroup });
  } catch (err) {
    console.error('[updateGroupTags] Error:', err);
    const status = err.status || 500;
    res.status(status).json({ message: err.message });
  }
}
}

module.exports = new GroupController();
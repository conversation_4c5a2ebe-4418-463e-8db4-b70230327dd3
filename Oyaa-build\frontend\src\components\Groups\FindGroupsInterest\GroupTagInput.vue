<template>
  <div class="tag-input-container" ref="tagInputContainer" :class="{ 'is-focused': inputFocused }">
    <div class="tag-input-wrapper">
      <transition-group name="tag" tag="div" class="tags-group">
        <div v-for="tag in selectedTags" :key="tag" class="tag">
          <span class="tag-text">{{ tag }}</span>
          <button type="button" @click="removeTag(tag)" class="tag-remove" aria-label="Remove tag">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </transition-group>
      <input
        ref="tagInput"
        type="text"
        v-model="inputValue"
        @keydown="handleKeyDown"
        @focus="handleInputFocus"
        @blur="handleInputBlur"
        :placeholder="selectedTags.length === 0 ? 'Type your interests...' : ''"
        class="tag-input"
      />
    </div>
    <transition name="fade">
      <div v-if="showSuggestions && suggestions.length > 0" class="suggestions">
        <ul>
          <li
            v-for="(suggestion, index) in suggestions"
            :key="suggestion"
            @click="addTag(suggestion)"
            @mouseenter="highlightedIndex = index"
            class="suggestion-item"
            :class="{ 'is-highlighted': highlightedIndex === index }"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="suggestion-icon">
              <path d="M12 5v14M5 12h14"></path>
            </svg>
            {{ suggestion }}
          </li>
        </ul>
      </div>
    </transition>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  props: {
    selectedTags: {
      type: Array,
      default: () => [],
    },
    type: {  // New prop to determine endpoint
      type: String,
      default: 'users',
    },
  },
  data() {
    return {
      inputValue: '',
      suggestions: [],
      showSuggestions: false,
      inputFocused: false,
      highlightedIndex: -1,
    };
  },
  watch: {
    inputValue() {
      this.updateSuggestions();
    },
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    async fetchTagSuggestions(query) {
      if (query.trim().length < 2) {
        this.suggestions = [];
        this.showSuggestions = false;
        return;
      }
      try {
        const endpoint = this.type === 'groups' ? '/api/groups/tags/suggestions' : '/api/users/tags/suggestions';
        const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}${endpoint}`, {
          params: { query },
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
        });
        this.suggestions = response.data.tags.filter(tag => !this.selectedTags.includes(tag));
        this.showSuggestions = true;
        this.highlightedIndex = this.suggestions.length > 0 ? 0 : -1;
      } catch (error) {
        console.error('Error fetching tag suggestions:', error);
        this.suggestions = [];
        this.showSuggestions = false;
      }
    },
    updateSuggestions() {
      if (this.inputValue.trim().length > 1) {
        this.fetchTagSuggestions(this.inputValue);
      } else {
        this.suggestions = [];
        this.showSuggestions = false;
        this.highlightedIndex = -1;
      }
    },
    addTag(tag) {
      const trimmedTag = tag.trim().toLowerCase();
      if (trimmedTag && !this.selectedTags.includes(trimmedTag)) {
        this.$emit('add-tag', trimmedTag);
        this.inputValue = '';
        this.suggestions = [];
        this.showSuggestions = false;
        this.$nextTick(() => this.$refs.tagInput.focus());
      }
    },
    removeTag(tag) {
      this.$emit('remove-tag', tag);
    },
    handleKeyDown(e) {
      if (e.key === 'Enter' && this.inputValue && this.highlightedIndex === -1) {
        e.preventDefault();
        this.addTag(this.inputValue);
      } else if (e.key === 'Backspace' && !this.inputValue && this.selectedTags.length > 0) {
        this.removeTag(this.selectedTags[this.selectedTags.length - 1]);
      } else if (e.key === 'ArrowDown' && this.showSuggestions) {
        e.preventDefault();
        this.highlightedIndex = (this.highlightedIndex + 1) % this.suggestions.length;
      } else if (e.key === 'ArrowUp' && this.showSuggestions) {
        e.preventDefault();
        this.highlightedIndex = (this.highlightedIndex - 1 + this.suggestions.length) % this.suggestions.length;
      } else if (e.key === 'Enter' && this.highlightedIndex >= 0) {
        e.preventDefault();
        this.addTag(this.suggestions[this.highlightedIndex]);
      }
    },
    handleInputFocus() {
      this.inputFocused = true;
      if (this.inputValue) this.updateSuggestions();
    },
    handleInputBlur() {
      this.inputFocused = false;
      setTimeout(() => {
        if (!this.inputFocused) this.showSuggestions = false;
      }, 200);
    },
    handleClickOutside(event) {
      if (this.$refs.tagInputContainer && !this.$refs.tagInputContainer.contains(event.target)) {
        this.showSuggestions = false;
      }
    },
  },
};
</script>

<style scoped>
.tag-input-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  position: relative;
  margin-bottom: 1.25rem;
  transition: all var(--transition-fast);
}

.tag-input-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.625rem 0.75rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  min-height: 2.75rem;
  align-items: center;
  transition: all var(--transition-fast);
}

.tag-input-container.is-focused .tag-input-wrapper {
  border-color: var(--accent-primary);
  background-color: var(--bg-secondary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}

.tags-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  padding: 0.25rem 0.5rem 0.25rem 0.625rem;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.tag:hover {
  background-color: var(--accent-tertiary);
  border-color: var(--accent-primary);
}

.tag-text {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tag-remove {
  background: none;
  border: none;
  color: var(--accent-primary);
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.125rem;
  height: 1.125rem;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.tag-remove:hover {
  color: white;
  background-color: var(--accent-primary);
  transform: scale(1.1);
}

.tag-input {
  flex: 1;
  min-width: 120px;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 0.9375rem;
  padding: 0.25rem 0.375rem;
  outline: none;
  caret-color: var(--accent-primary);
}

.tag-input::placeholder {
  color: var(--text-secondary);
}

.suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.375rem;
  background-color: var(--bg-secondary);
  border-radius: 10px;
  box-shadow: var(--shadow-lg);
  z-index: 10;
  max-height: 15rem;
  overflow-y: auto;
  border: 1px solid var(--border-color);
}

.suggestions ul {
  list-style: none;
  padding: 0.375rem;
  margin: 0;
}

.suggestion-item {
  padding: 0.625rem 0.75rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-primary);
}

.suggestion-icon {
  color: var(--accent-primary);
  transition: all var(--transition-fast);
}

.suggestion-item:hover,
.suggestion-item.is-highlighted {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
}

.suggestion-item:hover .suggestion-icon,
.suggestion-item.is-highlighted .suggestion-icon {
  color: var(--accent-primary);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.tag-enter-active,
.tag-leave-active {
  transition: all 0.3s ease;
}

.tag-enter-from,
.tag-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
</style>
<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import type { Message } from '../types/chat';

	export let messages: Message[] = [];
	export let visibleItems: any[] = [];
	export let totalHeight: number = 0;
	export let containerHeight: number = 0;
	export let scrollTop: number = 0;
	export let virtualScrollManager: any = null;

	const dispatch = createEventDispatcher<{
		generateMessages: { count: number };
		clearMessages: void;
		togglePerformanceMode: { mode: string };
	}>();

	let isOpen = false;
	let fps = 0;
	let frameCount = 0;
	let lastTime = performance.now();
	let memoryUsage = { used: 0, total: 0 };
	let renderTime = 0;
	let scrollPerformance = { smooth: true, jank: 0 };
	let lastScrollTime = 0;
	let scrollJankCount = 0;
	let domNodes = 0;
	let updateCount = 0;
	let lastUpdateTime = 0;

	// FPS Monitoring
	onMount(() => {
		let animationId: number;

		function updateFPS() {
			frameCount++;
			const currentTime = performance.now();

			if (currentTime - lastTime >= 1000) {
				fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
				frameCount = 0;
				lastTime = currentTime;
			}

			animationId = requestAnimationFrame(updateFPS);
		}

		updateFPS();

		// Memory monitoring and DOM node counting
		const memoryInterval = setInterval(() => {
			if ('memory' in performance) {
				const memory = (performance as any).memory;
				memoryUsage = {
					used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
					total: Math.round(memory.totalJSHeapSize / 1024 / 1024)
				};
			}

			// Count DOM nodes
			domNodes = document.querySelectorAll('*').length;
			updateCount++;
			lastUpdateTime = performance.now();
		}, 1000);

		return () => {
			cancelAnimationFrame(animationId);
			clearInterval(memoryInterval);
		};
	});

	// Performance metrics calculations
	$: visibleRange = virtualScrollManager ? virtualScrollManager.getVisibleRange() : { start: 0, end: 0, offset: 0 };
	$: renderedItems = visibleItems.length;
	$: totalItems = messages.length;
	$: virtualizationRatio = totalItems > 0 ? ((totalItems - renderedItems) / totalItems * 100).toFixed(1) : '0';
	$: scrollPercentage = totalHeight > 0 ? ((scrollTop / (totalHeight - containerHeight)) * 100).toFixed(1) : '0';
	$: averageItemHeight = totalItems > 0 ? (totalHeight / totalItems).toFixed(1) : '0';
	$: expectedItemsInViewport = containerHeight > 0 ? Math.ceil(containerHeight / 80) : 0;
	$: actualVsExpected = `${renderedItems} / ${expectedItemsInViewport + 20}`;  // +20 for buffer
	$: mediaMessages = messages.filter(m => m.media && m.type !== 'text');
	$: totalMediaCount = mediaMessages.length;
	$: mediaLoadingStatus = `${totalMediaCount} media items (lazy loaded)`;

	function generateMessages(count: number) {
		const startTime = performance.now();
		dispatch('generateMessages', { count });
		renderTime = performance.now() - startTime;
	}

	function clearAllMessages() {
		dispatch('clearMessages');
	}

	function copyMetrics() {
		const metrics = {
			timestamp: new Date().toISOString(),
			performance: {
				fps,
				renderTime: `${renderTime.toFixed(2)}ms`,
				memoryUsage: `${memoryUsage.used}MB / ${memoryUsage.total}MB`,
				scrollJank: scrollJankCount
			},
			virtualization: {
				totalMessages: totalItems,
				renderedMessages: renderedItems,
				virtualizationRatio: `${virtualizationRatio}%`,
				visibleRange: `${visibleRange.start} - ${visibleRange.end}`,
				totalHeight: `${totalHeight}px`,
				averageItemHeight: `${averageItemHeight}px`
			},
			scroll: {
				scrollTop: `${scrollTop}px`,
				containerHeight: `${containerHeight}px`,
				scrollPercentage: `${scrollPercentage}%`,
				isSmooth: scrollPerformance.smooth
			},
			browser: {
				userAgent: navigator.userAgent,
				viewport: `${window.innerWidth}x${window.innerHeight}`,
				devicePixelRatio: window.devicePixelRatio
			}
		};

		navigator.clipboard.writeText(JSON.stringify(metrics, null, 2)).then(() => {
			alert('Metrics copied to clipboard!');
		}).catch(() => {
			console.log('Metrics:', metrics);
			alert('Metrics logged to console (clipboard not available)');
		});
	}

	// Monitor scroll performance with better tracking
	function trackScrollPerformance() {
		const currentTime = performance.now();
		if (lastScrollTime > 0) {
			const timeDiff = currentTime - lastScrollTime;
			// Track different levels of jank
			if (timeDiff > 33.33) { // Worse than 30fps
				scrollJankCount += 2; // Heavy jank
				scrollPerformance.smooth = false;
			} else if (timeDiff > 16.67) { // Worse than 60fps
				scrollJankCount++;
				scrollPerformance.smooth = false;
			}
		}
		lastScrollTime = currentTime;
	}

	// Reset jank counter periodically
	function resetJankCounter() {
		scrollJankCount = Math.max(0, scrollJankCount - 1);
	}

	// Reset jank counter every 5 seconds to show recent performance
	setInterval(resetJankCounter, 5000);

	$: if (scrollTop !== undefined) trackScrollPerformance();
</script>

<div class="debug-menu" class:open={isOpen}>
	<button
		class="debug-toggle"
		on:click={() => isOpen = !isOpen}
		aria-label="Toggle debug menu"
	>
		🐛
	</button>

	{#if isOpen}
		<div class="debug-panel">
			<div class="debug-header">
				<h3>Performance Debug</h3>
				<button class="copy-btn" on:click={copyMetrics}>📋 Copy All</button>
			</div>

			<div class="debug-section">
				<h4>🚀 Performance</h4>
				<div class="metric">
					<span class="label">FPS:</span>
					<span class="value" class:warning={fps < 30} class:critical={fps < 15}>{fps}</span>
				</div>
				<div class="metric">
					<span class="label">Render Time:</span>
					<span class="value">{renderTime.toFixed(2)}ms</span>
				</div>
				<div class="metric">
					<span class="label">Memory:</span>
					<span class="value">{memoryUsage.used}MB / {memoryUsage.total}MB</span>
				</div>
				<div class="metric">
					<span class="label">Scroll Jank:</span>
					<span class="value" class:warning={scrollJankCount > 10}>{scrollJankCount}</span>
				</div>
				<div class="metric">
					<span class="label">DOM Nodes:</span>
					<span class="value" class:warning={domNodes > 1000}>{domNodes.toLocaleString()}</span>
				</div>
			</div>

			<div class="debug-section">
				<h4>📊 Virtualization</h4>
				<div class="metric">
					<span class="label">Total Messages:</span>
					<span class="value">{totalItems.toLocaleString()}</span>
				</div>
				<div class="metric">
					<span class="label">Rendered:</span>
					<span class="value">{renderedItems}</span>
				</div>
				<div class="metric">
					<span class="label">Virtualized:</span>
					<span class="value">{virtualizationRatio}%</span>
				</div>
				<div class="metric">
					<span class="label">Visible Range:</span>
					<span class="value">{visibleRange.start} - {visibleRange.end}</span>
				</div>
				<div class="metric">
					<span class="label">Avg Height:</span>
					<span class="value">{averageItemHeight}px</span>
				</div>
				<div class="metric">
					<span class="label">Expected in Viewport:</span>
					<span class="value">{expectedItemsInViewport}</span>
				</div>
				<div class="metric">
					<span class="label">Actual vs Expected:</span>
					<span class="value" class:warning={renderedItems < expectedItemsInViewport}>{actualVsExpected}</span>
				</div>
				<div class="metric">
					<span class="label">Media Items:</span>
					<span class="value">{mediaLoadingStatus}</span>
				</div>
			</div>

			<div class="debug-section">
				<h4>📏 Scroll Metrics</h4>
				<div class="metric">
					<span class="label">Scroll Top:</span>
					<span class="value">{scrollTop.toFixed(0)}px</span>
				</div>
				<div class="metric">
					<span class="label">Total Height:</span>
					<span class="value">{totalHeight.toLocaleString()}px</span>
				</div>
				<div class="metric">
					<span class="label">Progress:</span>
					<span class="value">{scrollPercentage}%</span>
				</div>
			</div>

			<div class="debug-section">
				<h4>⚡ Performance Status</h4>
				{#if fps < 15}
					<div class="alert critical">🔴 Critical: FPS too low</div>
				{:else if fps < 30}
					<div class="alert warning">🟡 Warning: FPS below optimal</div>
				{:else}
					<div class="alert good">🟢 Good: FPS optimal</div>
				{/if}

				{#if scrollJankCount > 50}
					<div class="alert warning">🟡 High scroll jank detected</div>
				{/if}

				{#if domNodes > 2000}
					<div class="alert warning">🟡 High DOM node count</div>
				{/if}

				{#if totalMediaCount > 0}
					<div class="alert good">🟢 Media lazy loading active</div>
				{/if}
			</div>

			<div class="debug-section">
				<h4>⚙️ Performance Mode</h4>
				<div class="button-grid">
					<button class="test-btn" on:click={() => dispatch('togglePerformanceMode', { mode: 'smooth' })}>
						Smooth Mode
					</button>
					<button class="test-btn" on:click={() => dispatch('togglePerformanceMode', { mode: 'performance' })}>
						Performance Mode
					</button>
				</div>
			</div>

			<div class="debug-section">
				<h4>🧪 Test Data</h4>
				<div class="button-grid">
					<button class="test-btn" on:click={() => generateMessages(1000)}>
						+1K Messages
					</button>
					<button class="test-btn" on:click={() => generateMessages(10000)}>
						+10K Messages
					</button>
					<button class="test-btn" on:click={() => generateMessages(50000)}>
						+50K Messages
					</button>
					<button class="test-btn" on:click={() => generateMessages(100000)}>
						+100K Messages
					</button>
					<button class="test-btn danger" on:click={clearAllMessages}>
						Clear All
					</button>
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.debug-menu {
		position: fixed;
		top: 20px;
		left: 20px;
		z-index: 1000;
		font-family: 'Courier New', monospace;
		font-size: 12px;
	}

	.debug-toggle {
		background: #333;
		color: white;
		border: none;
		border-radius: 50%;
		width: 40px;
		height: 40px;
		cursor: pointer;
		font-size: 16px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
		transition: all 0.2s ease;
	}

	.debug-toggle:hover {
		background: #555;
		transform: scale(1.1);
	}

	.debug-panel {
		position: absolute;
		top: 50px;
		left: 0;
		background: rgba(0, 0, 0, 0.95);
		color: #00ff00;
		border: 1px solid #333;
		border-radius: 8px;
		padding: 16px;
		min-width: 300px;
		max-height: 80vh;
		overflow-y: auto;
		backdrop-filter: blur(10px);
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
	}

	.debug-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;
		border-bottom: 1px solid #333;
		padding-bottom: 8px;
	}

	.debug-header h3 {
		margin: 0;
		color: #00ff00;
		font-size: 14px;
	}

	.copy-btn {
		background: #007bff;
		color: white;
		border: none;
		border-radius: 4px;
		padding: 4px 8px;
		cursor: pointer;
		font-size: 10px;
		transition: background 0.2s;
	}

	.copy-btn:hover {
		background: #0056b3;
	}

	.debug-section {
		margin-bottom: 16px;
	}

	.debug-section h4 {
		margin: 0 0 8px 0;
		color: #ffff00;
		font-size: 12px;
		border-bottom: 1px solid #444;
		padding-bottom: 4px;
	}

	.metric {
		display: flex;
		justify-content: space-between;
		margin-bottom: 4px;
		padding: 2px 0;
	}

	.label {
		color: #ccc;
	}

	.value {
		color: #00ff00;
		font-weight: bold;
	}

	.value.warning {
		color: #ffaa00;
	}

	.value.critical {
		color: #ff0000;
	}

	.button-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 8px;
	}

	.test-btn {
		background: #28a745;
		color: white;
		border: none;
		border-radius: 4px;
		padding: 8px 12px;
		cursor: pointer;
		font-size: 10px;
		transition: all 0.2s;
	}

	.test-btn:hover {
		background: #218838;
		transform: translateY(-1px);
	}

	.test-btn.danger {
		background: #dc3545;
		grid-column: 1 / -1;
	}

	.test-btn.danger:hover {
		background: #c82333;
	}

	.alert {
		padding: 6px 8px;
		border-radius: 4px;
		font-size: 10px;
		margin-bottom: 4px;
		border-left: 3px solid;
	}

	.alert.good {
		background: rgba(40, 167, 69, 0.1);
		border-color: #28a745;
		color: #28a745;
	}

	.alert.warning {
		background: rgba(255, 193, 7, 0.1);
		border-color: #ffc107;
		color: #ffc107;
	}

	.alert.critical {
		background: rgba(220, 53, 69, 0.1);
		border-color: #dc3545;
		color: #dc3545;
	}

	/* Scrollbar for debug panel */
	.debug-panel::-webkit-scrollbar {
		width: 4px;
	}

	.debug-panel::-webkit-scrollbar-track {
		background: #333;
	}

	.debug-panel::-webkit-scrollbar-thumb {
		background: #666;
		border-radius: 2px;
	}
</style>

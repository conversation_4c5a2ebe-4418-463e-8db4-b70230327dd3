const UserModel = require('../../models/User/userModel');
const userModel = new UserModel(); // Instantiate the class
const bcrypt = require('bcrypt');
class UserService {
  async updateLocation(userId, latitude, longitude) {
    return await UserModel.updateUserLocation(userId, latitude, longitude);
  }

  async getNearbyUsers(userId, limit = 10, offset = 0) {
    return await UserModel.findNearbyUsers(userId, limit, offset);
  }

  async searchUserByUsername(username) {
    const user = await UserModel.searchUserByUsername(username);

    return user;
  }

  async getUserById(userId) {
    const user = await UserModel.getUserById(userId);

    return user;
  }
  /**
   * Update a user's avatar.
   */
  async updateAvatar(userId, avatarUrl) {
    return await UserModel.updateAvatar(userId, avatarUrl);
  }

  async getUserById(userId) {
    try {

      const user = await UserModel.getUserById(userId);

      return user;
    } catch (error) {
      console.error('userService: Error in getUserById:', {
        message: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  // Update user profile with improved logging
  async updateProfile(userId, updates) {
    console.log('UserService: Updating profile for user ID:', userId);
    console.log('UserService: Update data received:', updates);

    try {
      // Handle password hashing if provided
      if (updates.password) {
        console.log('UserService: Hashing password');
        updates.password_hash = await bcrypt.hash(updates.password, 10);
        delete updates.password; // Remove plain password from updates
      }

      // Ensure handle is set if username is being updated
      if (updates.username && !updates.handle) {
        console.log('UserService: Setting handle to match username');
        updates.handle = updates.username;
      }

      const result = await userModel.updateProfile(userId, updates);
      console.log('UserService: Profile updated successfully:', result);
      return result;
    } catch (error) {
      console.error('UserService: Error updating profile:', error);
      throw error;
    }
  }
}

module.exports = new UserService();
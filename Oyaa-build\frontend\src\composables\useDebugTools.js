// frontend/src/composables/useDebugTools.js
import { ref, onMounted, onUnmounted } from 'vue';

export default function useDebugTools() {
  // Start with debug menu hidden
  const showDebugMenu = ref(false);

  // Toggle debug menu visibility
  const toggleDebugMenu = () => {
    showDebugMenu.value = !showDebugMenu.value;
    // Save preference to localStorage
    localStorage.setItem('debug_menu_visible', showDebugMenu.value);
    console.log(`Debug menu ${showDebugMenu.value ? 'shown' : 'hidden'}. Press Ctrl+Shift+D to toggle.`);
  };

  // Handle keyboard shortcut
  const handleKeyDown = (event) => {
    // Ctrl+Shift+D to toggle debug menu
    if (event.ctrlKey && event.shiftKey && event.key.toLowerCase() === 'd') {
      event.preventDefault();
      toggleDebugMenu();
    }
  };

  onMounted(() => {
    // Check if debug menu was previously visible
    const savedVisibility = localStorage.getItem('debug_menu_visible');
    if (savedVisibility === 'true') {
      showDebugMenu.value = true;
    }

    // Add keyboard shortcut listener
    window.addEventListener('keydown', handleKeyDown);

    // Log instructions if debug menu is visible
    if (showDebugMenu.value) {
      console.log('Debug menu is visible. Press Ctrl+Shift+D to toggle.');
    }
  });

  onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown);
  });

  return {
    showDebugMenu,
    toggleDebugMenu
  };
}

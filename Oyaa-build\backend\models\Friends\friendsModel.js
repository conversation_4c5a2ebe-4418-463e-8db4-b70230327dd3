const db = require('../../utils/db');

class FriendsModel {
  async addFriend(userId, friendId) {
    const query = `
      INSERT INTO friends (user_id, friend_id)
      VALUES ($1, $2)
      RETURNING *;
    `;
    const result = await db.query(query, [userId, friendId]);
    return result.rows[0];
  }

  async removeFriend(userId, friendId) {
    const query = `
      DELETE FROM friends
      WHERE user_id = $1 AND friend_id = $2
      RETURNING *;
    `;
    const result = await db.query(query, [userId, friendId]);
    return result.rows[0];
  }

  async getFriends(userId) {
    const query = `
      SELECT u.id, u.username, u.avatar
      FROM friends f
      JOIN users u ON f.friend_id = u.id
      WHERE f.user_id = $1;
    `;
    const result = await db.query(query, [userId]);
    return result.rows;
  }
  async areFriends(userId, friendId) {
    const query = `
      SELECT EXISTS (
        SELECT 1 FROM friends
        WHERE (user_id = $1 AND friend_id = $2) OR (user_id = $2 AND friend_id = $1)
      );
    `;
    const result = await db.query(query, [userId, friendId]);
    return result.rows[0].exists;
  }
  
}

module.exports = new FriendsModel();
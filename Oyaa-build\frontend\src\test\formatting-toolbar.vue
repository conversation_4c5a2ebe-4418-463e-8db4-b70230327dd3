<template>
    <div class="formatting-toolbar">
      <button 
        v-for="tool in formattingTools" 
        :key="tool.command"
        class="formatting-button"
        @click="applyFormatting(tool.command)"
        :aria-label="`Format text as ${tool.label}`"
        :title="tool.label"
      >
        <component :is="tool.icon" class="icon" />
      </button>
    </div>
  </template>
  
  <script setup>
  import { BoldIcon, ItalicIcon, UnderlineIcon, LinkIcon, ListIcon, CodeIcon } from 'lucide-vue-next';
  
  const formattingTools = [
    { command: 'bold', label: 'Bold', icon: BoldIcon },
    { command: 'italic', label: 'Italic', icon: ItalicIcon },
    { command: 'underline', label: 'Underline', icon: UnderlineIcon },
    { command: 'link', label: 'Insert Link', icon: LinkIcon },
    { command: 'list', label: 'Bullet List', icon: ListIcon },
    { command: 'code', label: 'Code Block', icon: CodeIcon }
  ];
  
  const applyFormatting = (command) => {
    // In a real implementation, this would apply the formatting to the selected text
    document.execCommand(command);
  };
  </script>
  
  <style>
  .formatting-toolbar {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px;
    border-radius: var(--radius);
    background-color: var(--bg-primary);
    margin-bottom: 8px;
  }
  
  .formatting-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: none;
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .formatting-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
  }
  
  .icon {
    width: 16px;
    height: 16px;
  }
  
  @media (max-width: 767px) {
    .formatting-toolbar {
      width: 100%;
      overflow-x: auto;
      padding: 4px 0;
    }
  }
  </style>
  
  
<template>
  <Teleport to="body">
    <div 
      class="media-gallery" 
      v-if="isOpen" 
      @click.self="closeGallery"
      @keydown="handleKeyDown"
      tabindex="0"
      ref="galleryRef"
    >
      <div class="gallery-container">
        <!-- Header with controls -->
        <div class="gallery-header">
          <div class="media-info">
            <span class="media-count">{{ currentIndex + 1 }} / {{ mediaItems.length }}</span>
            <span class="media-date" v-if="currentMedia">{{ formatDate(currentMedia.createdAt) }}</span>
          </div>
          
          <div class="gallery-actions">
            <button 
              class="action-button download-button" 
              @click="downloadMedia" 
              v-if="canDownload"
              title="Download media"
            >
              <i class="fas fa-download"></i>
            </button>
            <button 
              class="action-button close-button" 
              @click="closeGallery"
              title="Close gallery"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        
        <!-- Main content area -->
        <div 
          class="gallery-content" 
          @touchstart="handleTouchStart" 
          @touchmove="handleTouchMove" 
          @touchend="handleTouchEnd"
        >
          <!-- Loading indicator -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <span>Loading media...</span>
          </div>
          
          <!-- Error state -->
          <div v-else-if="hasError" class="error-container">
            <i class="fas fa-exclamation-circle"></i>
            <p>Failed to load media</p>
            <button class="retry-button" @click="retryLoading">Retry</button>
          </div>
          
          <!-- Media display -->
          <div v-else class="media-viewer" :class="{ 'is-zoomed': isZoomed }">
            <!-- Image viewer -->
            <div v-if="isImage(currentMedia)" class="image-viewer">
              <img 
                :src="currentMedia.mediaUrl" 
                :alt="`Media shared by ${currentMedia.senderName || 'user'}`"
                @click="toggleZoom"
                ref="currentImage"
                :class="{ 'zoomed': isZoomed }"
              />
            </div>
            
            <!-- Video player -->
            <div v-else-if="isVideo(currentMedia)" class="video-viewer">
              <video 
                ref="videoPlayer" 
                controls 
                autoplay 
                :src="currentMedia.mediaUrl"
                @loadeddata="handleMediaLoaded"
                @error="handleMediaError"
              >
                Your browser does not support the video tag.
              </video>
            </div>
            
            <!-- Audio player -->
            <div v-else-if="isAudio(currentMedia)" class="audio-viewer">
              <div class="audio-player-container">
                <div class="audio-waveform">
                  <div v-for="n in 20" :key="n" class="waveform-bar" :style="{ height: `${Math.random() * 30 + 10}px` }"></div>
                </div>
                <audio 
                  ref="audioPlayer" 
                  controls 
                  autoplay 
                  :src="currentMedia.mediaUrl"
                  @loadeddata="handleMediaLoaded"
                  @error="handleMediaError"
                >
                  Your browser does not support the audio tag.
                </audio>
              </div>
            </div>
            
            <!-- Unsupported media type -->
            <div v-else class="unsupported-media">
              <i class="fas fa-file-alt"></i>
              <p>Unsupported media type</p>
            </div>
          </div>
          
          <!-- Navigation buttons -->
          <button 
            v-if="mediaItems.length > 1 && !isZoomed" 
            class="nav-button prev-button" 
            @click="prevMedia"
            aria-label="Previous media"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <button 
            v-if="mediaItems.length > 1 && !isZoomed" 
            class="nav-button next-button" 
            @click="nextMedia"
            aria-label="Next media"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
        
        <!-- Media info footer -->
        <div class="gallery-footer">
          <div class="sender-info" v-if="currentMedia && currentMedia.senderName">
            <span>Shared by: {{ currentMedia.senderName }}</span>
          </div>
          
          <!-- Thumbnails carousel -->
          <div class="thumbnails-container" v-if="mediaItems.length > 1" ref="thumbnailsContainer">
            <div 
              v-for="(item, index) in mediaItems" 
              :key="index"
              class="thumbnail"
              :class="{ 'active': index === currentIndex }"
              @click="goToMedia(index)"
              :ref="el => { if (el) thumbnailRefs[index] = el }"
            >
              <!-- Image thumbnail -->
              <img 
                v-if="isImage(item) && shouldLoadThumbnail(index)" 
                :src="item.thumbnailUrl || getThumbnailUrl(item.mediaUrl)" 
                :alt="`Thumbnail ${index + 1}`"
                loading="lazy"
              />
              
              <!-- Video thumbnail -->
              <div v-else-if="isVideo(item) && shouldLoadThumbnail(index)" class="video-thumbnail">
                <img 
                  :src="item.thumbnailUrl || getVideoThumbnail(item.mediaUrl)" 
                  :alt="`Video thumbnail ${index + 1}`"
                  loading="lazy"
                />
                <div class="thumbnail-play-icon">
                  <i class="fas fa-play"></i>
                </div>
              </div>
              
              <!-- Audio thumbnail -->
              <div v-else-if="isAudio(item)" class="audio-thumbnail">
                <i class="fas fa-music"></i>
              </div>
              
              <!-- Placeholder for not-yet-loaded thumbnails -->
              <div v-else class="thumbnail-placeholder">
                <div class="thumbnail-loading"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { format } from 'date-fns';

export default {
  name: 'GroupMediaGallery',
  props: {
    isOpen: {
      type: Boolean,
      default: false
    },
    mediaItems: {
      type: Array,
      default: () => []
    },
    initialIndex: {
      type: Number,
      default: 0
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const currentIndex = ref(props.initialIndex);
    const isLocalLoading = ref(props.isLoading);
    const hasError = ref(false);
    const isZoomed = ref(false);
    const galleryRef = ref(null);
    const videoPlayer = ref(null);
    const audioPlayer = ref(null);
    const thumbnailsContainer = ref(null);
    const thumbnailRefs = ref({});
    
    // Touch handling for swipe
    const touchStartX = ref(0);
    const touchEndX = ref(0);
    const minSwipeDistance = 50;
    
    // Computed properties
    const currentMedia = computed(() => {
      if (!props.mediaItems.length) return null;
      return props.mediaItems[currentIndex.value];
    });
    
    const canDownload = computed(() => {
      if (!currentMedia.value) return false;
      return isImage(currentMedia.value) || isVideo(currentMedia.value) || isAudio(currentMedia.value);
    });
    
    // Watch for changes in props
    watch(() => props.isLoading, (newVal) => {
      isLocalLoading.value = newVal;
    });
    
    watch(() => props.initialIndex, (newVal) => {
      currentIndex.value = newVal;
    });
    
    watch(() => props.isOpen, (isOpen) => {
      if (isOpen) {
        isLocalLoading.value = props.isLoading;
        hasError.value = false;
        isZoomed.value = false;
        currentIndex.value = props.initialIndex;
        
        // Focus the gallery for keyboard navigation
        nextTick(() => {
          if (galleryRef.value) galleryRef.value.focus();
          scrollToActiveThumbnail();
        });
      } else {
        // Stop media playback when gallery closes
        if (videoPlayer.value) videoPlayer.value.pause();
        if (audioPlayer.value) audioPlayer.value.pause();
      }
    });
    
    // Watch for current index changes
    watch(() => currentIndex.value, () => {
      isLocalLoading.value = true;
      hasError.value = false;
      isZoomed.value = false;
      
      // Stop previous media playback
      if (videoPlayer.value) videoPlayer.value.pause();
      if (audioPlayer.value) audioPlayer.value.pause();
      
      // Scroll thumbnails to keep active one in view
      nextTick(() => {
        scrollToActiveThumbnail();
      });
    });
    
    // Media type checks
    const isImage = (media) => {
      return media && media.mediaType && media.mediaType.startsWith('image');
    };
    
    const isVideo = (media) => {
      return media && media.mediaType && media.mediaType.startsWith('video');
    };
    
    const isAudio = (media) => {
      return media && media.mediaType && media.mediaType.startsWith('audio');
    };
    
    // Navigation methods
    const closeGallery = () => {
      if (isZoomed.value) {
        isZoomed.value = false;
        return;
      }
      emit('close');
    };
    
    const nextMedia = () => {
      if (isZoomed.value) return;
      if (currentIndex.value < props.mediaItems.length - 1) {
        currentIndex.value++;
      } else {
        currentIndex.value = 0; // Loop back to first
      }
    };
    
    const prevMedia = () => {
      if (isZoomed.value) return;
      if (currentIndex.value > 0) {
        currentIndex.value--;
      } else {
        currentIndex.value = props.mediaItems.length - 1; // Loop to last
      }
    };
    
    const goToMedia = (index) => {
      if (isZoomed.value) return;
      currentIndex.value = index;
    };
    
    // Media loading handlers
    const handleMediaLoaded = () => {
      isLocalLoading.value = false;
    };
    
    const handleMediaError = () => {
      isLocalLoading.value = false;
      hasError.value = true;
    };
    
    const retryLoading = () => {
      isLocalLoading.value = true;
      hasError.value = false;
      
      // Force reload the current media
      const currentMediaItem = props.mediaItems[currentIndex.value];
      if (isVideo(currentMediaItem) && videoPlayer.value) {
        videoPlayer.value.load();
      } else if (isAudio(currentMediaItem) && audioPlayer.value) {
        audioPlayer.value.load();
      } else {
        // For images, we'll just reset the loading state and let the img tag retry
        nextTick(() => {
          isLocalLoading.value = false;
        });
      }
    };
    
    // Thumbnail handling
    const shouldLoadThumbnail = (index) => {
      // Always load thumbnails near the current index
      return Math.abs(index - currentIndex.value) <= 5;
    };
    
    const getThumbnailUrl = (url) => {
      if (!url) return '';
      
      // Generate thumbnail URL for Cloudinary images
      if (url.includes('cloudinary.com')) {
        const urlParts = url.split('/upload/');
        if (urlParts.length === 2) {
          return `${urlParts[0]}/upload/c_thumb,w_150,h_150,g_auto/${urlParts[1]}`;
        }
      }
      
      return url;
    };
    
    const getVideoThumbnail = (url) => {
      if (!url) return '';
      
      // Generate thumbnail URL for Cloudinary videos
      if (url.includes('cloudinary.com')) {
        const urlParts = url.split('/upload/');
        if (urlParts.length === 2) {
          return `${urlParts[0]}/upload/c_thumb,w_150,h_100,g_auto/${urlParts[1].replace(/\.\w+$/, '.jpg')}`;
        }
      }
      
      return url;
    };
    
    const scrollToActiveThumbnail = () => {
      if (!thumbnailsContainer.value || !thumbnailRefs.value[currentIndex.value]) return;
      
      const container = thumbnailsContainer.value;
      const thumbnail = thumbnailRefs.value[currentIndex.value];
      
      // Calculate the scroll position to center the active thumbnail
      const scrollLeft = thumbnail.offsetLeft - (container.clientWidth / 2) + (thumbnail.clientWidth / 2);
      container.scrollTo({ left: scrollLeft, behavior: 'smooth' });
    };
    
    // Format date for display
    const formatDate = (dateString) => {
      if (!dateString) return '';
      try {
        return format(new Date(dateString), 'MMM d, yyyy h:mm a');
      } catch (e) {
        return dateString;
      }
    };
    
    // Image zoom toggle
    const toggleZoom = () => {
      isZoomed.value = !isZoomed.value;
    };
    
    // Download current media
    const downloadMedia = async () => {
      if (!currentMedia.value || !currentMedia.value.mediaUrl) return;
      
      try {
        const response = await fetch(currentMedia.value.mediaUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        
        // Generate filename from URL or use a default
        const urlParts = currentMedia.value.mediaUrl.split('/');
        const fileName = urlParts[urlParts.length - 1].split('?')[0] || 'download';
        a.download = fileName;
        
        document.body.appendChild(a);
        a.click();
        
        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } catch (error) {
        console.error('Download failed:', error);
        alert('Failed to download media');
      }
    };
    
    // Touch event handlers for swipe
    const handleTouchStart = (e) => {
      touchStartX.value = e.changedTouches[0].screenX;
    };
    
    const handleTouchMove = (e) => {
      if (isZoomed.value) return; // Don't handle swipe when zoomed
      touchEndX.value = e.changedTouches[0].screenX;
    };
    
    const handleTouchEnd = () => {
      if (isZoomed.value) return;
      
      const distance = touchEndX.value - touchStartX.value;
      if (Math.abs(distance) > minSwipeDistance) {
        if (distance > 0) {
          // Swipe right, go to previous
          prevMedia();
        } else {
          // Swipe left, go to next
          nextMedia();
        }
      }
    };
    
    // Keyboard navigation
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        closeGallery();
      } else if (e.key === 'ArrowRight') {
        nextMedia();
      } else if (e.key === 'ArrowLeft') {
        prevMedia();
      } else if (e.key === 'Home') {
        goToMedia(0);
      } else if (e.key === 'End') {
        goToMedia(props.mediaItems.length - 1);
      }
    };
    
    // Preload adjacent images for smoother navigation
    const preloadAdjacentMedia = () => {
      if (!props.mediaItems.length) return;
      
      const preloadIndex = (index) => {
        if (index >= 0 && index < props.mediaItems.length) {
          const media = props.mediaItems[index];
          if (isImage(media)) {
            const img = new Image();
            img.src = media.mediaUrl;
          }
        }
      };
      
      // Preload next and previous
      preloadIndex(currentIndex.value + 1);
      preloadIndex(currentIndex.value - 1);
    };
    
    // Lifecycle hooks
    onMounted(() => {
      if (props.isOpen) {
        if (galleryRef.value) galleryRef.value.focus();
        preloadAdjacentMedia();
      }
    });
    
    onBeforeUnmount(() => {
      // Clean up any resources
    });
    
    return {
      currentIndex,
      currentMedia,
      isLocalLoading,
      hasError,
      isZoomed,
      galleryRef,
      videoPlayer,
      audioPlayer,
      thumbnailsContainer,
      thumbnailRefs,
      canDownload,
      closeGallery,
      nextMedia,
      prevMedia,
      goToMedia,
      isImage,
      isVideo,
      isAudio,
      handleMediaLoaded,
      handleMediaError,
      retryLoading,
      shouldLoadThumbnail,
      getThumbnailUrl,
      getVideoThumbnail,
      formatDate,
      toggleZoom,
      downloadMedia,
      handleTouchStart,
      handleTouchMove,
      handleTouchEnd,
      handleKeyDown,
      scrollToActiveThumbnail
    };
  }
};
</script>

<style scoped>
.media-gallery {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.gallery-container {
  width: 100%;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Gallery header */
.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  z-index: 10;
}

.media-info {
  display: flex;
  gap: 16px;
  align-items: center;
}

.media-count {
  font-weight: 600;
  font-size: 0.9rem;
}

.media-date {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
}

.gallery-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  background: transparent;
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.action-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.close-button {
  font-size: 1.2rem;
}

.download-button {
  font-size: 1rem;
}

/* Gallery content */
.gallery-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* Loading state */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: white;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error state */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: #e74c3c;
}

.error-container i {
  font-size: 3rem;
}

.retry-button {
  background-color: rgba(231, 76, 60, 0.2);
  border: 1px solid rgba(231, 76, 60, 0.5);
  color: #e74c3c;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: rgba(231, 76, 60, 0.3);
}

/* Media viewer */
.media-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.media-viewer.is-zoomed {
  cursor: zoom-out;
}

/* Image viewer */
.image-viewer {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-viewer img {
  max-width: 90%;
  max-height: 80vh;
  object-fit: contain;
  cursor: zoom-in;
  transition: transform 0.3s ease;
}

.image-viewer img.zoomed {
  max-width: none;
  max-height: none;
  transform: scale(1.5);
}

/* Video viewer */
.video-viewer {
  max-width: 90%;
  max-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-viewer video {
  max-width: 100%;
  max-height: 100%;
  background-color: black;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
}

/* Audio viewer */
.audio-viewer {
  width: 90%;
  max-width: 600px;
  padding: 24px;
  background-color: rgba(26, 26, 26, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.audio-player-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audio-waveform {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 60px;
  gap: 3px;
  margin-bottom: 16px;
}

.waveform-bar {
  width: 4px;
  background: linear-gradient(to top, #3498db, #2980b9);
  border-radius: 2px;
}

.audio-viewer audio {
  width: 100%;
  border-radius: 24px;
}

/* Unsupported media */
.unsupported-media {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: white;
  padding: 32px;
  background-color: rgba(26, 26, 26, 0.8);
  border-radius: 12px;
}

.unsupported-media i {
  font-size: 3rem;
}

/* Navigation buttons */
.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 5;
  font-size: 1.2rem;
}

.nav-button:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.05);
}

.prev-button {
  left: 24px;
}

.next-button {
  right: 24px;
}

/* Gallery footer */
.gallery-footer {
  padding: 16px 24px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  z-index: 10;
}

.sender-info {
  margin-bottom: 12px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Thumbnails */
.thumbnails-container {
  display: flex;
  overflow-x: auto;
  gap: 12px;
  padding: 8px 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.thumbnails-container::-webkit-scrollbar {
  height: 6px;
}

.thumbnails-container::-webkit-scrollbar-track {
  background: transparent;
}

.thumbnails-container::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.thumbnail {
  width: 80px;
  height: 60px;
  flex-shrink: 0;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  opacity: 0.6;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
  background-color: #1a1a1a;
}

.thumbnail:hover {
  opacity: 0.9;
}

.thumbnail.active {
  opacity: 1;
  border-color: #3498db;
  transform: scale(1.05);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
}

.thumbnail-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.7rem;
}

.audio-thumbnail {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2c3e50;
  color: #3498db;
  font-size: 1.5rem;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a1a1a;
}

.thumbnail-loading {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gallery-header, .gallery-footer {
    padding: 12px 16px;
  }
  
  .nav-button {
    width: 48px;
    height: 48px;
    font-size: 1rem;
  }
  
  .prev-button {
    left: 16px;
  }
  
  .next-button {
    right: 16px;
  }
  
  .thumbnail {
    width: 70px;
    height: 52px;
  }
  
  .audio-viewer {
    width: 95%;
  }
}

@media (max-width: 480px) {
  .gallery-header, .gallery-footer {
    padding: 8px 12px;
  }
  
  .media-info {
    gap: 8px;
  }
  
  .media-count, .media-date {
    font-size: 0.8rem;
  }
  
  .action-button {
    width: 36px;
    height: 36px;
  }
  
  .nav-button {
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }
  
  .prev-button {
    left: 8px;
  }
  
  .next-button {
    right: 8px;
  }
  
  .thumbnail {
    width: 60px;
    height: 45px;
  }
}

/* Landscape mode on mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .gallery-container {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .gallery-header {
    width: 100%;
    order: 1;
  }
  
  .gallery-content {
    width: 80%;
    height: calc(100% - 120px);
    order: 2;
  }
  
  .gallery-footer {
    width: 20%;
    height: calc(100% - 120px);
    order: 3;
    display: flex;
    flex-direction: column;
  }
  
  .thumbnails-container {
    flex-direction: column;
    height: calc(100% - 40px);
    overflow-y: auto;
    overflow-x: hidden;
  }
  
  .thumbnail {
    width: 100%;
    height: 60px;
  }
}
</style>
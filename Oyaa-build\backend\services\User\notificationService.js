// backend/services/notificationService.js
const { getIO } = require('../../WebSocket/socketInstance');

class NotificationService {
  notifyUser(userId, type, payload) {
    const io = getIO();
    console.log(
      `NotificationService: Emitting notification to room user_${userId} in '/notifications' namespace. Type: ${type}, Payload: `,
      payload
    );
    io.of('/notifications').to(`user_${userId}`).emit('notification', { type, ...payload });
  }
}

module.exports = new NotificationService();

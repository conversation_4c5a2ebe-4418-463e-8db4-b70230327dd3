import { Component, createMemo, Show } from 'solid-js';
import { Message } from '../../types/chat';
import '../../styles/telegram-bubbles.css';

interface TelegramBubbleProps {
  message: Message;
  isOut: boolean;
  groupPosition?: 'first' | 'middle' | 'last' | 'single';
  showAvatar?: boolean;
  showTime?: boolean;
  onReply?: (message: Message) => void;
  onReact?: (message: Message, emoji: string) => void;
}

const TelegramBubble: Component<TelegramBubbleProps> = (props) => {
  // Telegram's message grouping logic
  const bubbleClasses = createMemo(() => {
    const classes = ['bubble'];
    
    if (props.isOut) {
      classes.push('is-out');
    } else {
      classes.push('is-in');
    }
    
    // Telegram's grouping classes
    if (props.groupPosition) {
      classes.push(`is-group-${props.groupPosition}`);
    }
    
    return classes.join(' ');
  });

  // Format time like Telegram (HH:MM)
  const formatTime = (timestamp: string | Date) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // Message status for outgoing messages
  const getMessageStatus = () => {
    if (!props.isOut) return null;
    
    // This would come from your message status in real implementation
    const status = props.message.status || 'sent';
    return status;
  };

  return (
    <div class={bubbleClasses()}>
      {/* Reply preview (if message is a reply) */}
      <Show when={props.message.replyTo}>
        <div class="reply-preview" onClick={() => {
          // Handle jump to replied message
          console.log('Jump to message:', props.message.replyTo);
        }}>
          <div class="reply-author">
            {props.message.replyTo?.senderName || 'Unknown'}
          </div>
          <div class="reply-text">
            {props.message.replyTo?.text || 'Media message'}
          </div>
        </div>
      </Show>

      {/* Media content (if any) */}
      <Show when={props.message.media}>
        <div class="media-container">
          <Show when={props.message.media?.type === 'image'}>
            <img 
              src={props.message.media?.url} 
              alt="Image"
              loading="lazy"
            />
          </Show>
          <Show when={props.message.media?.type === 'video'}>
            <video 
              src={props.message.media?.url}
              controls
              preload="metadata"
            />
          </Show>
          <Show when={props.message.media?.type === 'file'}>
            <div class="file-preview">
              <div class="file-icon">📄</div>
              <div class="file-info">
                <div class="file-name">{props.message.media?.fileName}</div>
                <div class="file-size">{props.message.media?.fileSize}</div>
              </div>
            </div>
          </Show>
        </div>
      </Show>

      {/* Message text content */}
      <Show when={props.message.text}>
        <div class="message">
          {props.message.text}
          
          {/* Time and status (Telegram style - inline with text) */}
          <Show when={props.showTime !== false}>
            <span class="time">
              {formatTime(props.message.timestamp)}
              <Show when={props.isOut}>
                <span class={`message-status ${getMessageStatus()}`}></span>
              </Show>
            </span>
          </Show>
        </div>
      </Show>

      {/* Reactions (if any) */}
      <Show when={props.message.reactions && props.message.reactions.length > 0}>
        <div class="reactions">
          {props.message.reactions?.map((reaction) => (
            <div 
              class={`reaction ${reaction.isChosen ? 'is-chosen' : ''}`}
              onClick={() => props.onReact?.(props.message, reaction.emoji)}
            >
              <span class="reaction-emoji">{reaction.emoji}</span>
              <span class="reaction-count">{reaction.count}</span>
            </div>
          ))}
        </div>
      </Show>
    </div>
  );
};

export default TelegramBubble;

<template>
  <div class="add-friends-container">
    <!-- Added header with back button -->
    <div class="page-header">
      <button class="back-button" @click="$router.push('/dashboard')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>Find Friends</h1>
      <div class="header-spacer"></div>
    </div>

    <div class="card">
      <div class="card-header">
        <button class="info-button" @click="showInfo = true" aria-label="Show information">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
        </button>
        <h2 class="title">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          Find Friends by Interest
        </h2>
        <p class="subtitle">Discover people who share your interests</p>
      </div>

      <div class="card-content">
        <TagInput :selectedTags="selectedTags" @add-tag="addTag" @remove-tag="removeTag" />
        <div class="input-footer">
          <div class="tag-count">
            {{ selectedTags.length === 0 ? "Add interests to find friends" : `${selectedTags.length} interest${selectedTags.length !== 1 ? 's' : ''} selected` }}
          </div>
          <SearchButton :loading="loading" :selectedTags="selectedTags" @search="searchUsers" />
        </div>

        <transition name="fade">
          <div v-if="loading && selectedTags.length > 0" class="loading-container">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loader">
              <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
            </svg>
          </div>
        </transition>

        <transition name="fade">
          <div v-if="!loading && searchResults.length > 0" class="results-container">
            <transition-group name="list" tag="div" class="results-list">
              <UserResult v-for="user in searchResults" :key="user.id" :user="user" @send-friend-request="sendFriendRequest" @start-chat="startChat" />
            </transition-group>
            <button v-if="hasMore" @click="loadMore" :disabled="loading" class="load-more-button">
              <svg v-if="loading" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loader">
                <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
              </svg>
              <span>Load More</span>
            </button>
          </div>
        </transition>

        <transition name="fade">
          <div v-if="!loading && searched && searchResults.length === 0" class="empty-state">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
            <p>No users found with these interests</p>
            <p class="empty-state-hint">Try adding different interests or removing some filters</p>
          </div>
        </transition>
      </div>
    </div>
    <InfoDialog :show="showInfo" @close="showInfo = false" />
  </div>
</template>

<script>
import axios from 'axios';
import { mapActions } from 'vuex';
import TagInput from './TagInput.vue';
import SearchButton from './SearchButton.vue';
import UserResult from './UserResult.vue';
import InfoDialog from './InfoDialog.vue';

export default {
  components: {
    TagInput,
    SearchButton,
    UserResult,
    InfoDialog,
  },
  data() {
    return {
      selectedTags: [],
      searchResults: [],
      loading: false,
      hasMore: false,
      currentOffset: 0,
      limit: 10,
      searched: false,
      showInfo: false,
    };
  },
  methods: {
    ...mapActions('app', ['openTempChat']),
    addTag(tag) {
      this.selectedTags.push(tag);
    },
    removeTag(tag) {
      this.selectedTags = this.selectedTags.filter(t => t !== tag);
    },
    async searchUsers() {
      if (this.selectedTags.length === 0) {
        alert('Please select at least one tag');
        return;
      }
      this.loading = true;
      this.currentOffset = 0;
      this.searched = true;
      try {
        const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/users/search-by-tags`, {
          params: { tags: this.selectedTags, limit: this.limit, offset: this.currentOffset },
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
        });
        this.searchResults = response.data.users;
        this.hasMore = response.data.users.length === this.limit;
      } catch (error) {
        console.error('Error searching users:', error);
        this.searchResults = [];
      } finally {
        this.loading = false;
      }
    },
    async loadMore() {
      this.loading = true;
      this.currentOffset += this.limit;
      try {
        const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/users/search-by-tags`, {
          params: { tags: this.selectedTags, limit: this.limit, offset: this.currentOffset },
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
        });
        this.searchResults = [...this.searchResults, ...response.data.users];
        this.hasMore = response.data.users.length === this.limit;
      } catch (error) {
        console.error('Error loading more users:', error);
      } finally {
        this.loading = false;
      }
    },
    async sendFriendRequest(userId) {
      try {
        await axios.post(
          `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/send`,
          { receiverId: userId },
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        );
        this.searchResults = this.searchResults.map(user =>
          user.id === userId ? { ...user, has_sent_request: true } : user
        );
        // No toast for successful actions - notifications will handle this
      } catch (error) {
        console.error('Error sending friend request:', error);
        alert('Failed to send friend request.');
      }
    },
    async startChat(userId) {
      try {
        const currentUserId = this.$store.getters['auth/userId'];
        await axios.post(
          `${import.meta.env.VITE_API_BASE_URL}/api/temp-chat/initiate`,
          { userId: currentUserId, targetUserId: userId },
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        );
        const chatFriend = { id: userId, username: this.searchResults.find(u => u.id === userId).username };
        await this.openTempChat(chatFriend);
        // Include the friendId parameter in the route
        this.$router.push({ name: 'TempChat', params: { friendId: userId.toString() } });
      } catch (error) {
        console.error('Error initiating temporary chat:', error);
        alert('Failed to start chat.');
      }
    },
  },
};
</script>

<style scoped>
.add-friends-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
  padding-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Added header styles */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  background-color: var(--bg-primary);
  z-index: 20;
  width: 100%;
  height: 56px;
  box-sizing: border-box;
}

.page-header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
}

.back-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.back-button:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
}

.header-spacer {
  width: 36px; /* Same width as back button for balanced layout */
}

.card {
  background-color: var(--bg-tertiary);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  width: 100%;
  max-width: 600px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin-top: 1rem;
}

.card-header {
  padding: 1.5rem 1.5rem 1rem;
  position: relative;
  border-bottom: 1px solid var(--border-color);
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.625rem;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.title svg {
  color: var(--accent-primary);
}

.subtitle {
  margin: 0.5rem 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.info-button {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.info-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  transform: scale(1.05);
}

.card-content {
  padding: 1.25rem 1.5rem;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.75rem;
}

.tag-count {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 3rem 0;
}

.loader {
  animation: spin 1.2s linear infinite;
  color: var(--accent-primary);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.results-container {
  margin-top: 1.5rem;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.load-more-button {
  width: 100%;
  margin-top: 1.25rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.load-more-button:hover:not(:disabled) {
  background-color: var(--accent-tertiary);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
  transform: translateY(-2px);
}

.load-more-button:active:not(:disabled) {
  transform: scale(0.98);
}

.load-more-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.empty-state {
  text-align: center;
  padding: 3rem 0;
  color: var(--text-secondary);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.empty-state svg {
  color: var(--accent-tertiary);
}

.empty-state p {
  margin: 0;
  font-size: 0.9375rem;
  color: var(--text-primary);
}

.empty-state-hint {
  font-size: 0.8125rem !important;
  color: var(--text-secondary) !important;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.list-enter-active, .list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.list-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .page-header {
    padding: 10px 12px;
    height: 50px;
  }

  .page-header h1 {
    font-size: 16px;
  }

  .back-button {
    padding: 6px;
  }

  .back-button svg {
    width: 18px;
    height: 18px;
  }

  .card {
    margin-top: 0.75rem;
    border-radius: 12px;
  }

  .card-header {
    padding: 1.25rem 1.25rem 0.75rem;
  }

  .title {
    font-size: 1.125rem;
  }

  .info-button {
    top: 1.25rem;
    right: 1.25rem;
    width: 1.75rem;
    height: 1.75rem;
  }

  .card-content {
    padding: 1rem 1.25rem;
  }

  .empty-state {
    padding: 2rem 0;
  }

  .empty-state svg {
    width: 40px;
    height: 40px;
  }
}

/* Small phones */
@media (max-width: 375px) {
  .page-header {
    padding: 8px 10px;
    height: 46px;
  }

  .page-header h1 {
    font-size: 15px;
  }

  .back-button svg {
    width: 16px;
    height: 16px;
  }

  .card {
    margin-top: 0.5rem;
    border-radius: 10px;
  }

  .card-header {
    padding: 1rem 1rem 0.75rem;
  }

  .title {
    font-size: 1rem;
    gap: 0.5rem;
  }

  .title svg {
    width: 18px;
    height: 18px;
  }

  .subtitle {
    font-size: 0.75rem;
  }

  .info-button {
    top: 1rem;
    right: 1rem;
    width: 1.5rem;
    height: 1.5rem;
  }

  .info-button svg {
    width: 16px;
    height: 16px;
  }

  .card-content {
    padding: 0.75rem 1rem;
  }
}

/* Fix for iOS viewport height issues */
@supports (-webkit-touch-callout: none) {
  .add-friends-container {
    min-height: -webkit-fill-available;
  }
}
</style>
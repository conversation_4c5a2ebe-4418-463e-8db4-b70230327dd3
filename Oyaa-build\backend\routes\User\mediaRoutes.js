// backend/routes/mediaRoutes.js
const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const mediaController = require('../../controllers/User/mediaController');

const upload = multer({
  dest: 'uploads/',
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB, matching group chat
  fileFilter: (req, file, cb) => {
    const allowedExtensions = /\.(jpeg|jpg|png|gif|mp4|mov|avi|mp3|m4a|wav|ogg|aac|webm)$/i;
    const allowedMimeTypes = /^(image\/(jpeg|png|gif)|video\/(mp4|quicktime|x-msvideo)|audio\/(mpeg|mp4|x-wav|ogg|x-m4a|webm))$/;
    const extname = allowedExtensions.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedMimeTypes.test(file.mimetype);
    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Unsupported file type'));
    }
  },
});

router.post('/upload', upload.single('file'), mediaController.uploadMedia);
router.get('/chat/:chatId', mediaController.getMediaByChat);
router.get('/user/:userId', mediaController.getMediaByUser);
router.delete('/:id', mediaController.deleteMedia);

module.exports = router;
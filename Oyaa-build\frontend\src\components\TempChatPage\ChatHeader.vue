<!-- frontend/src/components/TempChatPage/ChatHeader.vue -->
<template>
    <header class="chat-header">
      <h2>Chat with {{ chatFriend.username }}</h2>
      <button class="menu-icon" @click.stop="toggleDropdown" aria-label="More options">
        ⋮
      </button>
      <div v-if="isDropdownOpen" class="dropdown-menu" @click.stop>
        <button @click="addAsFriend">Add as Friend</button>
        <button @click="blockUser">Block User</button>
      </div>
    </header>
  </template>
  
  <script>
  export default {
    name: "ChatHeader",
    props: {
      chatFriend: {
        type: Object,
        required: true
      }
    },
    data() {
      return {
        isDropdownOpen: false
      };
    },
    watch: {
      chatFriend() {
        this.isDropdownOpen = false;
      }
    },
    methods: {
      toggleDropdown() {
        this.isDropdownOpen = !this.isDropdownOpen;
      },
      blockUser() {
        this.$emit('block-user', this.chatFriend.id);
        this.isDropdownOpen = false;
      },
      addAsFriend() {
        this.$emit('send-friend-request', this.chatFriend.id);
        this.isDropdownOpen = false;
      },
      handleClickOutside() {
        if (this.isDropdownOpen) {
          this.isDropdownOpen = false;
        }
      }
    },
    mounted() {
      document.addEventListener('click', this.handleClickOutside);
    },
    beforeDestroy() {
      document.removeEventListener('click', this.handleClickOutside);
    }
  };
  </script>
  
  <style scoped>
  .chat-header {
    background-color: #007acc;
    color: #fff;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
  }
  
  .menu-icon {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5em;
    cursor: pointer;
    padding: 0 10px;
  }
  
  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border: 1px solid #ccc;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 150px;
  }
  
  .dropdown-menu button {
    display: block;
    width: 100%;
    padding: 10px;
    text-align: left;
    border: none;
    background: none;
    cursor: pointer;
    color: #333;
  }
  
  .dropdown-menu button:hover {
    background-color: #f0f0f0;
  }
  </style>
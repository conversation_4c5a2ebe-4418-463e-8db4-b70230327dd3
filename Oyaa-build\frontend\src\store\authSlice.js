import axios from 'axios';
import {
  registerWithEmailPassword,
  loginWithEmailPassword,
  signInWithGoogle,
  completeGoogleSignUp,
  logout as firebaseLogout,
  resetPassword,
  checkUsernameAvailability,
  getCurrentUser,
  getIdToken
} from '../services/firebaseService';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

const state = {
  user: null,
  token: null,
  loading: false,
  error: null,
  firebaseUser: null,
  needsUsername: false,
  isNewUser: false,
  lastRefresh: Date.now() // Add a timestamp for forcing reactivity
};

const mutations = {
  LOGIN_START(state) {
    state.loading = true;
    state.error = null;
  },
  LOGIN_SUCCESS(state, payload) {
    // Only set user data if we're not in the middle of a Google sign-in flow
    // that requires username selection
    if (!state.needsUsername) {
      state.user = payload.user;
      state.token = payload.token;
      state.firebaseUser = payload.firebaseUser || null;
      state.loading = false;
      state.isNewUser = false;
      console.log('LOGIN_SUCCESS: Normal login flow');

      // Store the token in localStorage
      if (payload.token) {
        localStorage.setItem('token', payload.token);
        console.log('Token stored in localStorage during LOGIN_SUCCESS');
      }

      // Force a refresh of the auth state
      state.lastRefresh = Date.now();
    } else {
      // If we need a username, just update the loading state
      // but keep needsUsername true
      state.loading = false;
      console.log('LOGIN_SUCCESS: Keeping needsUsername true');
    }
  },
  LOGIN_FAILURE(state, error) {
    state.error = error;
    state.loading = false;
  },
  REGISTER_START(state) {
    state.loading = true;
    state.error = null;
  },
  REGISTER_SUCCESS(state, payload) {
    // Only set user data if we're not in the middle of a Google sign-in flow
    // that requires username selection
    if (!state.needsUsername) {
      state.user = payload.user;
      state.token = payload.token || null;
      state.firebaseUser = payload.firebaseUser || null;
      state.loading = false;
      console.log('REGISTER_SUCCESS: Normal registration flow');
    } else {
      // If we need a username, just update the loading state
      // but keep needsUsername true
      state.loading = false;
      console.log('REGISTER_SUCCESS: Keeping needsUsername true');
    }
  },
  REGISTER_FAILURE(state, error) {
    state.error = error;
    state.loading = false;
  },
  LOGOUT(state) {
    state.user = null;
    state.token = null;
    state.firebaseUser = null;
    state.error = null;
    state.needsUsername = false;
    state.isNewUser = false;
  },
  SET_TOKEN(state, token) {
    state.token = token;
  },
  UPDATE_USER(state, updatedUser) {
    state.user = { ...state.user, ...updatedUser };
  },
  CLEAR_ERROR(state) {
    state.error = null;
  },
  SET_NEEDS_USERNAME(state, { firebaseUser, isNewUser }) {
    console.log('SET_NEEDS_USERNAME mutation called');
    state.needsUsername = true;
    state.firebaseUser = firebaseUser;
    state.isNewUser = isNewUser || false;

    // Remove any token to prevent automatic redirection
    localStorage.removeItem('token');
  },

  CLEAR_NEEDS_USERNAME(state) {
    console.log('CLEAR_NEEDS_USERNAME mutation called');
    state.needsUsername = false;
    state.isNewUser = false;
  },

  REFRESH_AUTH_STATE(state) {
    // This mutation doesn't change any state directly
    // It's used to trigger a state refresh in components that are watching the auth state
    console.log('Refreshing auth state');

    // We can add a timestamp to force reactivity
    state.lastRefresh = Date.now();
  },

  SET_FIREBASE_USER(state, firebaseUser) {
    console.log('SET_FIREBASE_USER mutation called');
    state.firebaseUser = firebaseUser;
  },
};

const actions = {
  /**
   * Legacy login method - kept for backward compatibility
   */
  async legacyLogin({ commit }, credentials) {
    commit('LOGIN_START');
    try {
      const response = await axios.post(`${API_BASE_URL}/api/auth/login`, credentials);
      commit('LOGIN_SUCCESS', {
        user: response.data.user,
        token: response.data.token,
      });
      localStorage.setItem('token', response.data.token);
      return response.data;
    } catch (error) {
      commit('LOGIN_FAILURE', error.response?.data?.message || 'Login failed');
      throw error;
    }
  },

  /**
   * Legacy register method - kept for backward compatibility
   */
  async legacyRegister({ commit }, userData) {
    commit('REGISTER_START');
    try {
      const response = await axios.post(`${API_BASE_URL}/api/auth/register`, userData);
      commit('REGISTER_SUCCESS', {
        user: response.data.user,
        token: response.data.token,
      });
      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
      }
      return response.data;
    } catch (error) {
      commit('REGISTER_FAILURE', error.response?.data?.message || 'Registration failed');
      throw error;
    }
  },

  /**
   * Login with email and password using Firebase
   */
  async login({ commit }, credentials) {
    commit('LOGIN_START');
    try {
      // Login with Firebase
      const result = await loginWithEmailPassword(credentials.email, credentials.password);

      // Store the token
      localStorage.setItem('token', result.token);

      // Update the store
      commit('LOGIN_SUCCESS', {
        user: result.user,
        token: result.token,
        firebaseUser: result.user
      });

      return result;
    } catch (error) {
      let errorMessage = 'Login failed';

      // Handle Firebase error codes
      if (error.code) {
        switch (error.code) {
          case 'auth/user-not-found':
          case 'auth/wrong-password':
            errorMessage = 'Invalid email or password';
            break;
          case 'auth/too-many-requests':
            errorMessage = 'Too many failed login attempts. Please try again later.';
            break;
          case 'auth/user-disabled':
            errorMessage = 'This account has been disabled.';
            break;
          default:
            errorMessage = error.message || 'Login failed';
        }
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      commit('LOGIN_FAILURE', errorMessage);
      throw error;
    }
  },

  /**
   * Register with email and password using Firebase
   */
  async register({ commit }, userData) {
    commit('REGISTER_START');
    try {
      // Check if username is available
      const isAvailable = await checkUsernameAvailability(userData.username);
      if (!isAvailable) {
        throw new Error('Username already exists');
      }

      // Register with Firebase
      const result = await registerWithEmailPassword(
        userData.email,
        userData.password,
        userData.username,
        userData.avatar
      );

      // Store the token
      localStorage.setItem('token', result.token);

      // Update the store
      commit('LOGIN_SUCCESS', {  // Changed from REGISTER_SUCCESS to LOGIN_SUCCESS
        user: result.user,
        token: result.token,
        firebaseUser: result.user
      });

      return result;
    } catch (error) {
      let errorMessage = 'Registration failed';

      // Handle Firebase error codes
      if (error.code) {
        switch (error.code) {
          case 'auth/email-already-in-use':
            errorMessage = 'Email already in use';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Invalid email address';
            break;
          case 'auth/weak-password':
            errorMessage = 'Password is too weak';
            break;
          default:
            errorMessage = error.message || 'Registration failed';
        }
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      commit('REGISTER_FAILURE', errorMessage);
      throw error;
    }
  },

  /**
   * Sign in with Google
   */
  async signInWithGoogle({ commit }) {
    commit('LOGIN_START');
    try {
      console.log('Starting Google sign-in process');

      // Sign in with Google
      const result = await signInWithGoogle();

      // Get the Firebase user
      const firebaseUser = result.user;

      if (!firebaseUser) {
        throw new Error('Google sign-in failed: No user returned');
      }

      console.log('Google sign-in successful, user:', firebaseUser.uid);
      console.log('Google display name:', firebaseUser.displayName);

      // Get the Firebase ID token
      const idToken = await firebaseUser.getIdToken();

      // We'll try to authenticate with the backend first
      // If the user exists, they'll be logged in directly
      // If not, they'll be prompted to choose a username
      console.log('Attempting to authenticate with the backend first');

      try {

        // Try to login with the backend
        const response = await axios.post(
          `${API_BASE_URL}/api/auth/firebase-login`,
          {},
          { headers: { Authorization: `Bearer ${idToken}` } }
        );

        // If we get here, the user exists in our backend and is logged in
        console.log('User exists in backend, login successful');

        // Update the store with the user data
        commit('LOGIN_SUCCESS', {
          user: response.data.user,
          token: idToken,
          firebaseUser
        });

        // Return success
        return {
          needsUsername: false,
          user: response.data.user,
          token: idToken
        };
      } catch (error) {
        // If the error is 403 and needsRegistration is true, the user needs to select a username
        if (error.response && error.response.status === 403 && error.response.data.needsRegistration) {
          console.log('User needs to select a username');

          // Store the Firebase user for later use in the username selection process
          commit('SET_NEEDS_USERNAME', {
            firebaseUser: firebaseUser,
            isNewUser: error.response.data.isNewUser || false
          });

          // Return with needsUsername flag
          return {
            needsUsername: true,
            isNewUser: error.response.data.isNewUser || false,
            user: firebaseUser,
            token: idToken
          };
        }

        // For other errors, rethrow
        throw error;
      }

      // Clear any existing token to prevent automatic redirection
      localStorage.removeItem('token');

      // Return with needsUsername flag to trigger the username selection modal
      return {
        needsUsername: true,
        user: firebaseUser,
        token: idToken
      };

      // None of the code below will execute because we're always returning above
      // This is intentional - we want to force the username selection flow
    } catch (error) {
      console.warn('Google sign-in error in store:', error);

      // Handle specific error cases
      let errorMessage = 'Google sign-in failed';

      // Don't show error for cancelled popups
      if (error.code === 'auth/popup-closed-by-user' || error.code === 'auth/cancelled-popup-request') {
        commit('LOGIN_FAILURE', null);
        return;
      }

      // Network errors
      if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }
      // Popup blocked
      else if (error.code === 'auth/popup-blocked') {
        errorMessage = 'Sign-in popup was blocked. Please allow popups for this site.';
      }
      // Timeout
      else if (error.code === 'auth/timeout') {
        errorMessage = 'The sign-in request timed out. Please try again.';
      }
      // Use custom error message if available
      else if (error.message) {
        errorMessage = error.message;
      }

      commit('LOGIN_FAILURE', errorMessage);
      throw error;
    }
  },

  /**
   * Complete Google sign-up by setting a username and handle
   */
  async completeGoogleSignUp({ commit, state }, { username, handle, avatar }) {
    commit('REGISTER_START');
    try {
      // Make sure we have a Firebase user
      if (!state.firebaseUser) {
        throw new Error('No Firebase user found. Please sign in again.');
      }

      console.log('Starting completeGoogleSignUp with username:', username);
      console.log('Firebase user:', state.firebaseUser.uid);

      // Validate username format
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
      if (!usernameRegex.test(username)) {
        throw new Error('Username must be 3-20 characters and contain only letters, numbers, and underscores');
      }

      // Check if username is available
      console.log('Checking if username is available:', username);

      let isAvailable = true; // Default to true if we can't check

      try {
        isAvailable = await checkUsernameAvailability(username);
        if (!isAvailable) {
          throw new Error('Username already exists. Please choose a different username.');
        }
      } catch (availabilityError) {
        console.warn('Error checking username availability, proceeding anyway:', availabilityError);
        // If we can't check availability due to server issues, we'll proceed anyway
        // The backend will do a final check during registration
      }

      console.log('Username is valid, proceeding with registration');

      // Try to complete the sign-up with our custom username
      let result;
      try {
        result = await completeGoogleSignUp(state.firebaseUser, username, handle || username, avatar);

        if (!result || !result.user) {
          throw new Error('Failed to register user with the backend');
        }

        console.log('User registered successfully with username:', username);
      } catch (registrationError) {
        console.error('Error registering with backend:', registrationError);

        // If we can't reach the backend, create a local user object
        if (registrationError.message && registrationError.message.includes('Network Error')) {
          console.log('Network error, creating local user object');

          // Create a minimal user object from Firebase data
          result = {
            user: {
              id: state.firebaseUser.uid,
              username: username,
              handle: handle || username,
              email: state.firebaseUser.email,
              avatar: avatar || state.firebaseUser.photoURL,
              displayName: state.firebaseUser.displayName || username
            }
          };

          console.log('Created local user object:', result.user);
        } else {
          // For other errors, rethrow
          throw registrationError;
        }
      }

      // Store the token
      try {
        if (result && result.token) {
          console.log('Storing token from result');
          localStorage.setItem('token', result.token);
        } else {
          // If no token in result, get a fresh one from Firebase
          console.log('Getting fresh token from Firebase');
          const idToken = await state.firebaseUser.getIdToken(true);
          localStorage.setItem('token', idToken);
        }
      } catch (tokenError) {
        console.error('Error getting/storing token:', tokenError);
      }

      // Update the store with the custom username
      try {
        // Prepare user data
        const userData = {
          user: {
            ...result.user,
            username: username, // Ensure the username is set correctly
            handle: handle || username // Ensure the handle is set correctly
          },
          token: result.token || await state.firebaseUser.getIdToken(),
          firebaseUser: state.firebaseUser
        };

        console.log('Updating store with user data:', userData);

        // Update the store
        commit('LOGIN_SUCCESS', userData);

        // Clear the needs username flag
        commit('CLEAR_NEEDS_USERNAME');

        // Store the token in localStorage
        if (userData.token) {
          localStorage.setItem('token', userData.token);
          console.log('Token stored in localStorage during completeGoogleSignUp');
        }

        // Force a state refresh to ensure all components recognize the user
        setTimeout(() => {
          commit('REFRESH_AUTH_STATE');
        }, 100);
      } catch (storeError) {
        console.error('Error updating store:', storeError);
      }

      return result;
    } catch (error) {
      console.error('Complete Google sign-up error:', error);

      let errorMessage = 'Failed to complete sign-up';

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // If it's a network error, provide a more user-friendly message
      if (error.message && error.message.includes('Network Error')) {
        errorMessage = 'Cannot connect to the server. Please check your internet connection and try again.';
      }

      commit('REGISTER_FAILURE', errorMessage);
      throw error;
    }
  },

  /**
   * Check if a username is available
   * @param {Object} context - Vuex context
   * @param {string} username - Username to check
   * @returns {Promise<boolean>} - Whether the username is available
   */
  async checkUsernameAvailability(context, username) {
    try {
      console.log('Checking username availability in store:', username);

      // Basic validation
      if (!username || username.length < 3) {
        console.log('Username too short, returning false');
        return false;
      }

      // Validate username format
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
      if (!usernameRegex.test(username)) {
        console.log('Username format invalid, returning false');
        return false;
      }

      // Call the service function to check availability
      try {
        const isAvailable = await checkUsernameAvailability(username);
        console.log('Username availability result:', isAvailable);
        return isAvailable;
      } catch (serviceError) {
        console.error('Service error checking username availability:', serviceError);

        // If the service fails, fall back to client-side validation only
        console.log('Falling back to client-side validation only');

        // For service errors, we'll assume the username is valid if it passes regex
        // This allows users to continue even if the backend is temporarily unavailable
        return true;
      }
    } catch (error) {
      console.error('Error checking username availability in store:', error);
      return false;
    }
  },

  /**
   * Reset password
   */
  async resetPassword({ commit }, email) {
    try {
      await resetPassword(email);
      return { success: true };
    } catch (error) {
      let errorMessage = 'Failed to send password reset email';
      if (error.code === 'auth/user-not-found') {
        errorMessage = 'No account found with this email address';
      } else if (error.message) {
        errorMessage = error.message;
      }

      commit('LOGIN_FAILURE', errorMessage);
      throw error;
    }
  },

  /**
   * Logout
   */
  async logout({ commit }) {
    try {
      await firebaseLogout();
      commit('LOGOUT');
      localStorage.removeItem('token');
      window.location.href = '/signin';
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local state even if Firebase logout fails
      commit('LOGOUT');
      localStorage.removeItem('token');
      window.location.href = '/signin';
    }
  },

  /**
   * Fetch group requests
   */
  async fetchGroupRequests({ state }) {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/groups/group-requests/${state.user.id}`,
        {
          headers: { Authorization: `Bearer ${state.token}` },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching group requests:', error);
      throw error;
    }
  },

  /**
   * Initialize authentication state
   */
  async initializeAuth({ commit, dispatch }) {
    try {
      console.log('Initializing authentication state');

      // Check if there's a Firebase user
      const firebaseUser = getCurrentUser();

      // Get the token from localStorage
      let token = localStorage.getItem('token');
      console.log('Token from localStorage:', token ? 'Found' : 'Not found');

      if (firebaseUser) {
        console.log('Firebase user found during initialization:', firebaseUser.uid);

        // Set the Firebase user in the store
        commit('SET_FIREBASE_USER', firebaseUser);

        // Get a fresh token if needed
        if (!token) {
          try {
            token = await firebaseUser.getIdToken(true);
            console.log('Got fresh token from Firebase');
          } catch (tokenError) {
            console.error('Error getting fresh token:', tokenError);
          }
        }

        // For Google sign-ins, check if this is a Google sign-in
        const isGoogleUser = firebaseUser.providerData.some(
          provider => provider.providerId === 'google.com'
        );

        try {
          // Try to login with the Firebase user
          console.log('Attempting Firebase login with token');

          const response = await axios.post(
            `${API_BASE_URL}/api/auth/firebase-login`,
            {},
            { headers: { Authorization: `Bearer ${token}` } }
          );

          // If we get here, the user exists in our backend
          console.log('User exists in backend with username:', response.data.user.username);

          // Update the store with the user data
          commit('LOGIN_SUCCESS', {
            user: response.data.user,
            token: token,
            firebaseUser
          });

          // Update the token in localStorage
          if (token) {
            localStorage.setItem('token', token);
            console.log('Token stored in localStorage');
          }

          // Force a refresh to ensure all components recognize the user
          setTimeout(() => {
            commit('REFRESH_AUTH_STATE');
            console.log('Auth state refreshed after successful login');
          }, 100);
        } catch (error) {
          console.error('Firebase login error:', error);

          // If the user needs to select a username
          if (error.response && error.response.status === 403 && error.response.data.needsRegistration) {
            console.log('User needs to select a username');

            commit('SET_NEEDS_USERNAME', {
              firebaseUser,
              isNewUser: true
            });

            // Don't remove the token here, as we'll need it for the username selection
          } else if (error.response && error.response.status === 404) {
            // If user not found in our database, they need to create a username
            console.log('User not found in backend, needs username selection');

            commit('SET_NEEDS_USERNAME', {
              firebaseUser,
              isNewUser: true
            });
          } else {
            // For other errors, try one more approach - check if the user exists by email
            try {
              if (firebaseUser.email) {
                console.log('Checking if user exists by email:', firebaseUser.email);

                // Get a fresh token
                const freshToken = await firebaseUser.getIdToken(true);

                // Try to login again with the fresh token
                const loginResponse = await axios.post(
                  `${API_BASE_URL}/api/auth/firebase-login`,
                  {},
                  { headers: { Authorization: `Bearer ${freshToken}` } }
                );

                console.log('Login successful with fresh token');

                // Update the store
                commit('LOGIN_SUCCESS', {
                  user: loginResponse.data.user,
                  token: freshToken,
                  firebaseUser
                });

                // Store the token
                localStorage.setItem('token', freshToken);

                // Force a refresh
                setTimeout(() => {
                  commit('REFRESH_AUTH_STATE');
                }, 100);
              } else {
                // If no email, the user needs to select a username
                console.log('No email available, user needs to select a username');

                commit('SET_NEEDS_USERNAME', {
                  firebaseUser,
                  isNewUser: true
                });
              }
            } catch (finalError) {
              console.error('Final login attempt failed:', finalError);

              // If all attempts fail, set needs username
              commit('SET_NEEDS_USERNAME', {
                firebaseUser,
                isNewUser: true
              });
            }
          }
        }
      } else if (token) {
        // Try legacy token authentication
        console.log('No Firebase user but token exists, trying legacy authentication');

        commit('SET_TOKEN', token);

        try {
          const response = await axios.get(`${API_BASE_URL}/api/users/me`, {
            headers: { Authorization: `Bearer ${token}` },
          });

          console.log('Legacy authentication successful');

          commit('LOGIN_SUCCESS', {
            user: response.data,
            token
          });

          // Force a refresh
          setTimeout(() => {
            commit('REFRESH_AUTH_STATE');
          }, 100);
        } catch (error) {
          console.error('Failed to initialize auth with legacy token:', error);
          localStorage.removeItem('token');
          commit('SET_TOKEN', null);
          commit('LOGOUT');
        }
      } else {
        // No Firebase user and no token, user is logged out
        console.log('No Firebase user and no token, user is logged out');
        commit('LOGOUT');
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      localStorage.removeItem('token');
      commit('LOGOUT');
    }
  },
};

const getters = {
  user: (state) => state.user,
  token: (state) => state.token,
  loading: (state) => state.loading,
  error: (state) => state.error,
  isAuthenticated: (state) => !!state.token,
  userId: (state) => {
    // First try to get the numeric ID
    if (state.user?.id) {
      return state.user.id;
    }
    // If no numeric ID is available, try to use the Firebase UID
    if (state.user?.firebaseUid) {
      return state.user.firebaseUid;
    }
    // If that's not available, try to get it from the firebaseUser
    if (state.firebaseUser?.uid) {
      return state.firebaseUser.uid;
    }
    // If no ID is available, return null
    return null;
  },
  username: (state) => state.user?.username || null,
  handle: (state) => state.user?.handle || state.user?.username || null,
  firebaseUser: (state) => state.firebaseUser,
  firebaseUid: (state) => state.firebaseUser?.uid || state.user?.firebaseUid || null,
  needsUsername: (state) => state.needsUsername,
  isNewUser: (state) => state.isNewUser,
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
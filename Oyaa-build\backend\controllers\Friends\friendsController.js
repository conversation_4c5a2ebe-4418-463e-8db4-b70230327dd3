const FriendsService = require('../../services/Friends/friendsService');

class FriendsController {
  async addFriend(req, res) {
    try {
      const { userId, friendId } = req.body;
      const friend = await FriendsService.addFriend(userId, friendId);
      res.status(200).json({ message: 'Friend added', friend });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async removeFriend(req, res) {
    try {
      const { userId, friendId } = req.body;
      const friend = await FriendsService.removeFriend(userId, friendId);
      res.status(200).json({ message: 'Friend removed', friend });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async getFriends(req, res) {
    try {
      const userId = parseInt(req.params.userId, 10);
      if (isNaN(userId)) {
        return res.status(400).json({ message: 'Invalid userId: must be a number' });
      }
      const friends = await FriendsService.getFriends(userId);
      res.status(200).json({ friends });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }
  async checkFriendship(req, res) {
    try {
      const userId = parseInt(req.query.userId, 10);
      const friendId = parseInt(req.query.friendId, 10);
      if (isNaN(userId) || isNaN(friendId)) {
        return res.status(400).json({ message: 'Invalid userId or friendId: must be numbers' });
      }
      const areFriends = await FriendsService.areFriends(userId, friendId);
      res.status(200).json({ areFriends });
    } catch (err) {
      console.error('Error in checkFriendship:', err.stack);
      res.status(500).json({ message: err.message });
    }
  }
}

module.exports = new FriendsController();
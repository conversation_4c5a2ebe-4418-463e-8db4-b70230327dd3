// backend/services/locationService.js
const User = require('../../models/User/userModel');

class LocationService {
  async updateUserLocation(userId, latitude, longitude) {
    const user = new User(); // Instantiate the class
    await user.connect();
    // Use the updateLocation method from the user model
    await user.updateLocation(userId, latitude, longitude);
    await user.close();
    return { success: true };
  }
  
  async getNearbyUsers(userId, limit, offset) {
    const user = new User();
    await user.connect();
    const nearbyUsers = await user.findNearbyUsers(userId, limit, offset);
    await user.close();
    return nearbyUsers;
  }
}

module.exports = new LocationService();

<template>
  <div v-if="count > 0" class="notification-badge">
    {{ count }}
  </div>
</template>

<script setup>
const props = defineProps({
  count: {
    type: Number,
    default: 0,
  },
});
</script>

<style scoped>
.notification-badge {
  background-color: #ff4757;
  color: white;
  font-size: 0.8rem;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
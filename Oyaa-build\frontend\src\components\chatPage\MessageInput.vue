<template>
  <div>
    <Attachments ref="attachmentsRef" @file-changed="handleFileChanged" />
    <ReplyPreview
      v-if="replyingMessage"
      :replyingMessage="replyingMessage"
      @cancel="cancelReply"
    />
    <div class="chat-input-container" :class="{ 'is-expanded': isExpanded }">
      <div class="emoji-picker-wrapper">
        <button class="emoji-toggle-button" @click="toggleEmojiPicker">
          <SmileIcon class="icon" />
        </button>
        <EmojiPicker v-if="showEmojiPicker" @emojiSelected="addEmoji" @closePicker="showEmojiPicker = false" />
      </div>
      <ChatTextArea
        ref="chatTextAreaRef"
        v-model="message"
        :placeholder="`Message ${friend?.username}`"
        @input="handleTyping"
      />
      <ChatVoice @sendVoiceNote="handleSendVoiceNote" />
      <ActionButtons
        :message="message"
        :hasAttachment="hasAttachment"
        :isOverCharLimit="isOverCharLimit"
        @triggerAttachmentInput="triggerAttachmentInput"
        @sendMessage="sendMessage"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import useDebounce from '@/composables/useDebounce';
import ChatTextArea from './MessageInputComponents/ChatTextArea.vue';
import ActionButtons from './MessageInputComponents/ActionButtons.vue';
import EmojiPicker from './MessageInputComponents/EmojiPicker.vue';
import Attachments from './Attachments.vue';
import ReplyPreview from './MessageInputComponents/ReplyPreview.vue';
import ChatVoice from './ChatVoice.vue';
import { uploadMedia } from '@/services/mediaService';
import { Smile as SmileIcon } from 'lucide-vue-next';

const store = useStore();
const currentUser = computed(() => store.getters['auth/user']);

const props = defineProps({
  friend: { type: Object, required: false },
  replyingMessage: { type: Object, default: null },
});

const emit = defineEmits(['send', 'typing', 'stopTyping', 'clearReply']);

const message = ref('');
const attachmentsRef = ref(null);
const showEmojiPicker = ref(false);
const chatTextAreaRef = ref(null);
const attachedFile = ref(null);

const handleFileChanged = (file) => {
  console.log('File attached:', file);
  attachedFile.value = file;
};

const hasAttachment = computed(() => {
  return !!attachedFile.value;
});

const isExpanded = computed(() => {
  return message.value.length > 30;
});

const isOverCharLimit = computed(() => {
  return message.value.length > 2000;
});

const debouncedTyping = useDebounce(() => {
  if (props.friend) {
    emit('typing');
  }
}, 500);

const handleTyping = () => {
  if (!currentUser.value || !props.friend || !props.friend.id) return;
  if (message.value.trim() === '') {
    emit('stopTyping', {
      senderId: currentUser.value.id,
      receiverId: props.friend.id,
    });
  } else {
    debouncedTyping();
  }
};

const sendMessage = async () => {
  console.log('Sending message from input component');
  
  if (!message.value.trim() && !attachedFile.value) return;
  
  const payload = {
    text: message.value.trim(),
    reply: props.replyingMessage,
    media: attachedFile.value
  };
  
  console.log('Emitting send event with payload:', payload);
  emit('send', payload);
  
  message.value = '';
  attachedFile.value = null;
  
  if (chatTextAreaRef.value && chatTextAreaRef.value.focus) {
    chatTextAreaRef.value.focus();
  }
};

const handleSendVoiceNote = (audioBlob) => {
  if (!audioBlob) return;
  attachedFile.value = {
    file: audioBlob,
    mediaType: 'audio/webm',
    isVoiceNote: true
  };
  sendMessage();
};

const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value;
};

const triggerAttachmentInput = () => {
  if (attachmentsRef.value && attachmentsRef.value.triggerFileInput) {
    attachmentsRef.value.triggerFileInput();
  } else {
    console.error('triggerFileInput method not found on attachmentsRef');
  }
};

const cancelReply = () => {
  emit('clearReply');
};

const addEmoji = (emoji) => {
  message.value += emoji;
  showEmojiPicker.value = false;
};

watch(message, (newVal) => {
  if (newVal.length > 2000) message.value = newVal.slice(0, 2000);
});

const handleKeyDown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

onBeforeUnmount(() => {
  emit('clearReply');
});
</script>

<style scoped>
.chat-input-container {
  background-color: #2b2d31;
  padding: 0 16px;
  min-height: 44px;
  display: flex;
  align-items: flex-end;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
}
.is-expanded {
  padding-top: 8px;
  padding-bottom: 8px;
}
.emoji-toggle-button {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #b9bbbe;
  transition: color 0.2s ease;
}
.emoji-toggle-button:hover {
  color: #dcddde;
}
.emoji-picker-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}
</style>
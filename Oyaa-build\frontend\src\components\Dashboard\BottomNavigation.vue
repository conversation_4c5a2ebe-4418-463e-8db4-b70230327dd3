<template>
  <nav class="bottom-navigation">
    <button
      v-for="item in navItems"
      :key="item.tab"
      :class="{ active: activeTab === item.tab }"
      @click="setActiveTab(item.tab)"
    >
      <component :is="item.icon" />
      <span>{{ item.title }}</span>
    </button>
  </nav>
</template>

<script setup>
const props = defineProps({
  navItems: { type: Array, required: true },
  activeTab: { type: String, required: true },
});

const emit = defineEmits(['update:activeTab']);

const setActiveTab = (tab) => {
  emit('update:activeTab', tab);
};
</script>

<style scoped>
.bottom-navigation {
  display: none; /* Hidden by default */
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 65px;
  background: rgba(15, 15, 19, 0.95); /* Slightly transparent */
  justify-content: space-around;
  padding: 8px 0;
  padding-bottom: calc(8px + env(safe-area-inset-bottom, 0px)); /* Add safe area padding */
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
  z-index: 10;
  backdrop-filter: blur(10px);

  box-sizing: border-box;
}

@media (max-width: 768px) {
  .bottom-navigation {
    display: flex;
  }
}

.bottom-navigation button {
  background: none;
  border: none;
  color: #a0a0a0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  flex: 1;
  transition: all 0.3s ease;
  position: relative;
  padding: 8px 0;
  min-width: 0; /* Allow shrinking below content size */
}

.bottom-navigation button::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: #6366f1;
  transition: width 0.3s ease;
  border-radius: 3px 3px 0 0;
}

.bottom-navigation button.active {
  color: #e2e2e2;
}

.bottom-navigation button.active::after {
  width: 40%;
}

.bottom-navigation button:active {
  transform: scale(0.95);
}

.bottom-navigation svg {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
}

.bottom-navigation button.active svg {
  color: #6366f1;
  transform: translateY(-2px);
}

/* Extra small screens */
@media (max-width: 320px) {
  .bottom-navigation button span {
    font-size: 10px;
  }
  
  .bottom-navigation svg {
    width: 18px;
    height: 18px;
  }
}
</style>
<template>
  <div class="chat-page">
    <div class="chat-header">
      <h2>{{ groupName }}</h2>
      <p class="description">{{ description || 'No description available' }}</p>
      <div v-if="!isConnected" class="connection-status">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="status-icon">
          <line x1="1" y1="1" x2="23" y2="23"></line>
          <path d="M16.72 11.06A10.94 10.94 0 0 1 19 12.55"></path>
          <path d="M5 12.55a10.94 10.94 0 0 1 5.17-2.39"></path>
          <path d="M10.71 5.05A16 16 0 0 1 22.58 9"></path>
          <path d="M1.42 9a15.91 15.91 0 0 1 4.7-2.88"></path>
          <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
          <line x1="12" y1="20" x2="12.01" y2="20"></line>
        </svg>
        Disconnected. Reconnecting...
      </div>
    </div>
    
    <div class="messages" ref="messagesContainer">
      <div v-if="messages.length === 0" class="empty-state">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
        <p>No messages yet. Start the conversation!</p>
      </div>
      
      <div v-for="message in messages" :key="message.id" class="message">
        <div class="message-header">
          <span class="sender">{{ message.sender_username }}</span>
          <span class="timestamp">{{ formatDate(message.sent_at) }}</span>
        </div>
        <div class="message-content">{{ message.message }}</div>
      </div>
    </div>
    
    <form @submit.prevent="sendMessage" class="message-form">
      <div class="input-wrapper">
        <input 
          v-model="newMessage" 
          placeholder="Type your message..." 
          required 
          :disabled="!isConnected"
        />
        <button 
          type="submit" 
          class="send-btn" 
          :disabled="!isConnected || !newMessage.trim()"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="22" y1="2" x2="11" y2="13"></line>
            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
          </svg>
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { io } from 'socket.io-client';
import { format } from 'date-fns';
import { jwtDecode } from 'jwt-decode';

export default {
  data() {
    return {
      socket: null,
      messages: [],
      newMessage: '',
      isConnected: false,
      groupName: '',
      description: '',
      reconnectAttempts: 0,
      maxReconnectAttempts: 5,
      reconnectTimer: null,
    };
  },
  methods: {
    async sendMessage() {
      if (!this.newMessage.trim() || !this.socket || !this.isConnected) {
        console.warn('Cannot send message: disconnected or empty message');
        return;
      }

      try {
        console.log('Sending message:', this.newMessage);
        this.socket.emit('sendMessage', { message: this.newMessage });
        
        // Clear the input field immediately for better UX
        const sentMessage = this.newMessage;
        this.newMessage = '';
        
        // Handle potential failure
        const messageTimeout = setTimeout(() => {
          console.warn('Message send confirmation timed out');
          this.$toast.error('Message may not have been delivered', {
            position: 'bottom-center',
            duration: 3000
          });
        }, 5000);
        
        // Store timeout to clear it if messageSent is received
        this._messageTimeouts = this._messageTimeouts || new Map();
        this._messageTimeouts.set(sentMessage, messageTimeout);
      } catch (error) {
        console.error('Error sending message:', error);
        this.$toast.error('Failed to send message', {
          position: 'bottom-center',
          duration: 3000
        });
      }
    },
    formatDate(dateString) {
      if (!dateString) return '';
      try {
        return format(new Date(dateString), 'p');
      } catch (error) {
        console.error('Date formatting error:', error);
        return '';
      }
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer;
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },
    setupSocketListeners() {
      // Connection events
      this.socket.on('connect', () => {
        console.log('Socket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        if (this.reconnectTimer) {
          clearTimeout(this.reconnectTimer);
          this.reconnectTimer = null;
        }
      });

      this.socket.on('connected', (data) => {
        console.log('Connected to anonymous group chat:', data);
        // Any additional setup after connection confirmation
      });
      
      // Message events
      this.socket.on('initialMessages', (messages) => {
        console.log('Received initial messages:', messages.length);
        this.messages = messages || [];
        this.scrollToBottom();
      });
      
      this.socket.on('newMessage', (message) => {
        console.log('New message received:', message);
        this.messages.push(message);
        this.scrollToBottom();
      });
      
      this.socket.on('messageSent', ({ id }) => {
        console.log('Message sent confirmation received for ID:', id);
        // Clear any pending timeout for this message
        if (this._messageTimeouts) {
          for (const [message, timeout] of this._messageTimeouts.entries()) {
            clearTimeout(timeout);
            this._messageTimeouts.delete(message);
          }
        }
      });
      
      // Error handling
      this.socket.on('error', (error) => {
        console.error('Socket error:', error);
        this.$toast.error(error.message || 'Connection error', {
          position: 'bottom-center',
          duration: 5000
        });
      });
      
      this.socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        this.isConnected = false;
        this.handleReconnect();
      });
      
      this.socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        this.isConnected = false;
        this.handleReconnect();
      });
    },
    handleReconnect() {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }
      
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(1.5, this.reconnectAttempts), 10000);
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
        
        this.reconnectTimer = setTimeout(() => {
          if (!this.isConnected) {
            console.log('Attempting reconnection...');
            this.socket.connect();
          }
        }, delay);
      } else {
        console.error('Max reconnection attempts reached');
        this.$toast.error('Failed to reconnect. Please refresh the page.', {
          position: 'bottom-center',
          duration: 0 // Don't auto-dismiss
        });
      }
    },
    initializeSocket() {
      if (this.socket) {
        // Clean up existing socket
        this.socket.disconnect();
        this.socket.offAny();
      }
      
      console.log('Initializing socket connection to temp-groups namespace');
      this.socket = io(`${import.meta.env.VITE_API_BASE_URL}/temp-groups`, {
        autoConnect: false,
        auth: { token: localStorage.getItem('tempGroupSessionToken') }
      });
      
      this.setupSocketListeners();
      this.socket.connect();
    }
  },
  mounted() {
    const sessionToken = localStorage.getItem('tempGroupSessionToken');
    if (!sessionToken) {
      console.error('No session token found');
      this.$router.push('/anonymous');
      return;
    }
    
    try {
      const decoded = jwtDecode(sessionToken);
      const now = Date.now() / 1000;
      
      if (decoded.exp < now) {
        console.error('Session token expired');
        localStorage.removeItem('tempGroupSessionToken');
        this.$router.push('/anonymous');
        return;
      }
      
      this.groupName = decoded.groupName;
      this.description = decoded.description;
      
      this.initializeSocket();
    } catch (error) {
      console.error('Invalid token format:', error);
      localStorage.removeItem('tempGroupSessionToken');
      this.$router.push('/anonymous');
    }
  },
  beforeUnmount() {
    if (this.socket) {
      console.log('Disconnecting socket');
      this.socket.disconnect();
      this.socket = null;
    }
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }
    
    // Clear any message timeouts
    if (this._messageTimeouts) {
      for (const timeout of this._messageTimeouts.values()) {
        clearTimeout(timeout);
      }
    }
  }
};
</script>
  
<style scoped>
.chat-page {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --warning-color: #fbbf24;
  
  max-width: 800px;
  margin: 1.5rem auto;
  padding: 1.5rem;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 3rem);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

.chat-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--warning-color);
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(251, 191, 36, 0.1);
  border-radius: 6px;
  margin-top: 0.5rem;
}

.status-icon {
  color: var(--warning-color);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
  gap: 1rem;
}

.empty-state svg {
  color: var(--accent-tertiary);
}

.message {
  background-color: var(--bg-secondary);
  padding: 0.75rem 1rem;
  border-radius: 8px;
  max-width: 85%;
  align-self: flex-start;
  box-shadow: var(--shadow-sm);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
}

.sender {
  font-weight: 600;
  color: var(--accent-primary);
}

.timestamp {
  color: var(--text-secondary);
}

.message-content {
  word-break: break-word;
  line-height: 1.4;
}

.message-form {
  margin-top: auto;
}

.input-wrapper {
  display: flex;
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: border-color var(--transition-fast);
}

.input-wrapper:focus-within {
  border-color: var(--accent-primary);
}

input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  font-family: inherit;
  outline: none;
}

input::placeholder {
  color: var(--text-secondary);
}

.send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.send-btn:hover:not(:disabled) {
  background-color: var(--accent-secondary);
}

.send-btn:disabled {
  background-color: #3a3a4c;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .chat-page {
    margin: 0;
    height: 100vh;
    border-radius: 0;
  }
  
  .message {
    max-width: 90%;
  }
}
</style>
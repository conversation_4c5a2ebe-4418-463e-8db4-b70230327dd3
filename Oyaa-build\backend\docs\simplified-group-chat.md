# Simplified Group Chat Implementation

This document outlines the simplified group chat implementation that removes message status tracking (sent/delivered counts).

## Overview

The simplified group chat implementation focuses on the core functionality of sending and receiving messages without tracking delivery status. This simplification:

1. Reduces database queries
2. Simplifies the codebase
3. Improves performance
4. Removes potential sources of inconsistency

## Database Schema

The simplified schema includes:

- `group_messages` - Stores all group chat messages
- `group_message_media` - Stores media attachments for group chat messages

The `message_status` table has been removed.

## API Endpoints

### Send Message
- `POST /api/enhanced-group-chat/send`
- Sends a new message to a group chat
- No longer updates message status

### Get Messages
- `GET /api/enhanced-group-chat/:groupId/messages`
- Retrieves messages for a group chat with pagination
- No longer includes sent/delivered counts

### Get Last Chats
- `GET /api/enhanced-group-chat/last-chats`
- Retrieves the last chat message for each group a user is in

### Send System Message
- `POST /api/enhanced-group-chat/system-message`
- Sends a system message to a group chat (admin only)

### Sync Group Cache
- `POST /api/enhanced-group-chat/:groupId/sync-cache`
- Synchronizes the cache with the database for a group chat (admin only)

## WebSocket Events

### Sending Messages
- `sendGroupMessage` - Client event to send a message
- `newGroupMessage` - Server event when a new message is received
- No longer emits message status updates

### Group Management
- `joinGroup` - Join a group chat room
- `leaveGroup` - Leave a group chat room
- `addMember` - Add a member to a group
- `removeMember` - Remove a member from a group
- `createGroup` - Create a new group

### Typing Indicators
- `groupTyping` - User is typing in a group
- `groupStopTyping` - User stopped typing in a group

### Message Management
- `messageHidden` - Message is hidden
- `deleteMessage` - Message is deleted
- `muteUser` - User is muted

## Migration

To migrate from the previous implementation with message status tracking to the simplified version:

1. Run the migration script:
   - If you're in the project root directory: `node backend/run-remove-message-status.js`
   - If you're already in the backend directory: `node run-remove-message-status.js`
2. This script will:
   - Create a backup of the message_status table
   - Drop the message_status table
   - Remove related indexes

## Benefits

1. **Improved Performance**: Fewer database queries and less data to transfer
2. **Simplified Code**: Easier to maintain and understand
3. **Reduced Database Load**: No need to track and update message status
4. **Consistent User Experience**: No inconsistencies in message status counts

## Limitations

1. **No Delivery Confirmation**: Cannot confirm if messages were delivered to recipients
2. **No Read Receipts**: Cannot track if messages were read by recipients

## Future Considerations

If delivery confirmation becomes necessary in the future, consider implementing a lightweight solution that:

1. Only tracks essential information (e.g., delivered yes/no)
2. Uses a more efficient storage mechanism (e.g., Redis/Valkey sets)
3. Updates status asynchronously to avoid blocking the main message flow

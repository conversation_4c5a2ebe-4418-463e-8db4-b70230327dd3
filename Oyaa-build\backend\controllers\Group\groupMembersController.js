const groupMembersService = require('../../services/Groups/groupMembersService');
const EnhancedGroupChatService = require('../../services/Groups/enhancedGroupChatService');
const db = require('../../utils/db');

class GroupMembersController {
  async addMember(req, res) {
    try {
      const { groupId, userId, role } = req.body;
      const member = await groupMembersService.addMember(groupId, userId, role || 'member');
      const userRes = await db.query('SELECT username FROM users WHERE id = $1', [userId]);
      const username = userRes.rows[0].username;
      const message = `${username} has joined the group`;
      const systemMessage = await EnhancedGroupChatService.sendSystemMessage(groupId, message);
      const io = req.app.get('io');
      if (io) {
        io.of('/groups').to(`group_${groupId}`).emit('newGroupMessage', systemMessage);
      } else {
        console.warn('Socket.io instance not found');
      }
      res.status(200).json({ message: 'Member added successfully', member });
    } catch (err) {
      if (err.message === 'User is already a member of this group.') {
        return res.status(400).json({ message: err.message });
      }
      res.status(500).json({ message: err.message });
    }
  }

  async removeMember(req, res) {
    try {
      const { groupId, userId } = req.body;
      // Prevent removal from World Chat (groupId = 1)
      if (parseInt(groupId) === 1) {
        return res.status(403).json({ message: 'Cannot leave World Chat' });
      }
      // Proceed with removal
      const member = await groupMembersService.removeMember(groupId, userId);
      const userRes = await db.query('SELECT username FROM users WHERE id = $1', [userId]);
      const username = userRes.rows[0].username;
      const message = `${username} has left the group`;
      const systemMessage = await EnhancedGroupChatService.sendSystemMessage(groupId, message);
      const io = req.app.get('io');
      if (io) {
        io.of('/groups').to(`group_${groupId}`).emit('newGroupMessage', systemMessage);
      } else {
        console.warn('Socket.io instance not found');
      }
      res.status(200).json({ message: 'Member removed successfully', member });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async updateMemberRole(req, res) {
    try {
      const { groupId, userId, newRole } = req.body;
      const member = await groupMembersService.updateMemberRole(groupId, userId, newRole);
      if (newRole === 'admin') {
        const userRes = await db.query('SELECT username FROM users WHERE id = $1', [userId]);
        const username = userRes.rows[0].username;
        const message = `${username} has become an admin`;
        const systemMessage = await EnhancedGroupChatService.sendSystemMessage(groupId, message);
        const io = req.app.get('io');
        if (io) {
          io.of('/groups').to(`group_${groupId}`).emit('newGroupMessage', systemMessage);
        } else {
          console.warn('Socket.io instance not found');
        }
      }
      res.status(200).json({ message: 'Member role updated', member });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async getMembers(req, res) {
    try {
      const { groupId } = req.params;
      const members = await groupMembersService.getMembers(groupId);
      res.status(200).json({ members });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }
}

module.exports = new GroupMembersController();
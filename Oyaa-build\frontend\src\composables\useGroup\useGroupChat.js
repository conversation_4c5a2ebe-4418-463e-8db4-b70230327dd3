// frontend/src/composables/useGroup/useGroupChat.js
import { ref, computed, watch } from 'vue';
import useGroupChatState from './useGroupChatState';
import useGroupChatActions from './useGroupChatActions'; // Ensure this path is correct
import useGroupChatApi from './useGroupChatApi';
import useGroupChatSocket from './useGroupChatSocket';
import usePagination from './GroupUtils/useGroupPagination';

export default function useGroupChat(initialGroup, initialCurrentUser, searchQuery) {
    // Use initial values directly, state composable handles reactivity
    const state = useGroupChatState(initialGroup, initialCurrentUser, searchQuery);
    const api = useGroupChatApi();
    const socket = useGroupChatSocket();
    const pagination = usePagination();
    // Pass the state *refs* correctly to actions
    const actions = useGroupChatActions(state, api, socket, pagination);

    const isLoadingOlder = ref(false);
    const reachedEnd = ref(false);
    const loadAttempts = ref(0);
    const maxLoadAttempts = 3;

    const loadOlderMessages = async () => {
        if (isLoadingOlder.value || reachedEnd.value || !pagination.hasMore.value) {
            console.log('Skipping loadOlderMessages: Already loading, reached end, or no more messages.');
            return;
        }

        console.log(`Attempting to load older messages for group ${state.group.value?.id}, page ${pagination.currentPage.value + 1}`);
        isLoadingOlder.value = true;
        state.error.value = null; // Clear previous errors on new attempt
        loadAttempts.value++;

        try {
            pagination.currentPage.value += 1;
            const response = await api.fetchGroupMessages(
                state.group.value.id,
                pagination.currentPage.value,
                pagination.pageSize,
                state.isWorldChat.value // Pass computed value
            );

            console.log('API response for older messages:', response);
            const olderMessages = response?.messages || []; // Safely access messages

             if (!Array.isArray(olderMessages)) {
                 throw new Error("Invalid response format: messages array not found.");
             }


            if (olderMessages.length > 0) {
                // Process messages (add uniqueId, etc. if needed, but VirtualList handles basic ID)
                const uniqueNewMessages = olderMessages.filter(
                    newMsg => !state.messages.value.some(existing => existing.id === newMsg.id)
                );

                if (uniqueNewMessages.length > 0) {
                    // *** CRITICAL: Prepend older messages ***
                    state.messages.value = [...uniqueNewMessages, ...state.messages.value];
                    console.log(`Prepended ${uniqueNewMessages.length} older messages.`);
                } else {
                    console.log("No new unique older messages received.");
                    // Potentially indicates an issue or end of messages if page size wasn't met before
                }

                pagination.hasMore.value = olderMessages.length === pagination.pageSize;
                 if (!pagination.hasMore.value) {
                    reachedEnd.value = true;
                    console.log("Reached end of message history (fewer messages received than page size).");
                 }
                loadAttempts.value = 0; // Reset attempts on success
            } else {
                console.log("No more older messages available.");
                pagination.hasMore.value = false;
                reachedEnd.value = true;
                 // No need to revert page number if API correctly returned empty
            }

        } catch (err) {
            console.error('Error loading older messages:', err);
            state.error.value = err.message || 'Failed to load older messages. Please try again.';
            pagination.currentPage.value -= 1; // Revert page number on error

            if (loadAttempts.value >= maxLoadAttempts) {
                console.warn(`Max load attempts (${maxLoadAttempts}) reached. Stopping automatic retries.`);
                pagination.hasMore.value = false; // Stop trying
            }
        } finally {
            isLoadingOlder.value = false;
             // Removed state.loading.value = false; -> isLoadingOlder serves this purpose
        }
    };

    const retryLoadMore = () => {
        if (!isLoadingOlder.value) {
            console.log("Retrying to load older messages...");
            loadAttempts.value = 0; // Reset attempts for manual retry
            pagination.hasMore.value = true; // Assume there might be more on retry
            reachedEnd.value = false;
            state.error.value = null; // Clear error
            loadOlderMessages();
        }
    };

    // Watch for group changes to reset state
    watch(() => state.group.value?.id, (newGroupId, oldGroupId) => {
        if (newGroupId !== undefined && newGroupId !== oldGroupId) {
            console.log(`useGroupChat: Group changed to ${newGroupId}. Resetting chat state.`);
            pagination.reset();
            reachedEnd.value = false;
            loadAttempts.value = 0;
            state.error.value = null;
            state.messages.value = []; // Clear messages for the new group
            isLoadingOlder.value = false;
            // Initialization (fetching first page) should be triggered by the component (e.g., GroupChatWindow's watcher/onMounted)
             actions.initializeChat(); // Ensure actions include initializeChat
        }
    }, { immediate: false }); // Don't run on initial setup

    return {
        // State refs (directly from state composable)
        messages: state.messages,
        loading: state.loading, // General loading (e.g., initial load)
        error: state.error,
        typingUsers: state.typingUsers,
        replyMessage: state.replyMessage,
        filteredMessages: state.filteredMessages, // Computed based on messages and searchQuery
        currentAdminUsername: state.currentAdminUsername,
        isAdmin: state.isAdmin,
        isMuted: state.isMuted,
        isWorldChat: state.isWorldChat, // Computed from state.group
        isUploading: state.isUploading, // From state

        // Actions (from actions composable)
        initializeChat: actions.initializeChat,
        sendMessage: actions.sendMessage,
        emitTyping: actions.emitTyping,
        emitStopTyping: actions.emitStopTyping,
        handleReply: actions.handleReply,
        cancelReply: actions.cancelReply,
        handleStar: actions.handleStar,
        handleDelete: actions.handleDelete,
        handleMute: actions.handleMute,

        // Pagination/Loading Older specific state and actions
        loadOlderMessages,
        retryLoadMore,
        isLoadingOlder, // Specific state for loading older messages
        hasMoreMessages: pagination.hasMore, // Reactive ref from pagination composable
        reachedEnd, // Ref indicating end of history
    };
}
<template>
  <div class="chat-page">
    <div class="chat-container">
      <div class="chat-window">
        <html-chat-window
          v-if="group && group.id"
          :messages="messages"
          :current-user="currentUser"
          :group="group"
          :loading="loading"
          :show-debug-menu="showDebugMenu"
          @send-message="sendMessage"
          @load-more-messages="loadMoreMessages"
          @cached-messages-loaded="handleCachedMessages"
          @search="handleSearch"
          @leave-group="handleLeaveGroup"
        />
        <div v-else class="loading-container">
          <div class="loading-spinner"></div>
          <p>{{ loading ? 'Loading messages...' : 'Loading group info...' }}</p>
        </div>
        <p v-if="errorMessage" class="error">{{ errorMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import axiosInstance from "@/api/axios"
import { ref, computed, onMounted, onUnmounted, watch } from "vue"
import { useStore } from "vuex"
import HtmlChatWindow from "./HtmlChatWindow.vue"
import groupSocket from "@/groupSocket"

export default {
  name: "GroupChatPage",
  components: { HtmlChatWindow },
  setup() {
    // Initialize Vuex store
    const store = useStore()

    // State
    const errorMessage = ref("")
    const loading = ref(true)
    // Always show debug menu by default, but allow toggling with keyboard shortcut
    const showDebugMenu = ref(localStorage.getItem('showDebugMenu') !== 'false') // Default to true unless explicitly set to false
    const messagesLoaded = ref(false)

    // Computed properties from Vuex store
    const group = computed(() => store.state.groupChat.group || {})
    const messages = computed(() => store.state.groupChat.messages || [])
    const currentUser = ref({})

    // Fetch group info and messages
    const fetchGroupInfo = async (groupId) => {
      try {
        loading.value = true
        console.log("GroupChatPage - Fetching group:", groupId)
        const response = await axiosInstance.get(`/api/groups/${groupId}`)
        const groupData = response.data.group || response.data

        // Store group in Vuex
        store.dispatch("groupChat/setGroup", groupData)
        console.log("GroupChatPage - Fetched group:", groupData)

        // Make sure we have current user info before joining the group room
        if (!currentUser.value || !currentUser.value.id) {
          await fetchCurrentUser()
        }

        // Join the group socket room with user ID
        joinGroupRoom(groupId)

        // Fetch messages
        await fetchMessages(groupId)
      } catch (error) {
        errorMessage.value = error.response?.data?.message || "Error fetching group info"
        console.error("GroupChatPage - Error fetching group info:", errorMessage.value)
      } finally {
        loading.value = false
      }
    }

    // Clear message load attempts
    const clearMessageLoadAttempts = () => {
      messageLoadAttempts.clear()
      console.log("GroupChatPage - Cleared message load attempts")
    }

    // Fetch messages for the group
    const fetchMessages = async (groupId) => {
      try {
        loading.value = true
        console.log("GroupChatPage - Fetching messages for group:", groupId)

        // Clear message load attempts when fetching messages for a new group
        clearMessageLoadAttempts()

        try {
          // Use the correct API endpoint for group chat messages
          const response = await axiosInstance.get(`/api/group-chat/${groupId}/messages`)

          const messagesData = response.data.messages || []

          // Get hasMoreMessages directly from the API response
          const hasMoreMessages = response.data.hasMoreMessages === true

          // Get additional information if available
          const totalCount = response.data.totalCount || 0
          const remainingCount = response.data.remainingCount || 0

          // Store messages in Vuex
          store.dispatch("groupChat/setMessages", messagesData)
          // Set hasMoreMessages based on API response
          store.dispatch("groupChat/setHasMoreMessages", hasMoreMessages)
          console.log(`GroupChatPage - Fetched ${messagesData.length} messages, hasMoreMessages: ${hasMoreMessages}, totalCount: ${totalCount}, remainingCount: ${remainingCount}`)
          messagesLoaded.value = true

          // If no messages were fetched, just log it
          if (messagesData.length === 0) {
            console.log("GroupChatPage - No messages fetched for this group")
          }
        } catch (apiError) {
          console.error("GroupChatPage - API error when fetching messages:", apiError)
          errorMessage.value = "Failed to load messages. Please try again later."
          messagesLoaded.value = true
        }
      } catch (error) {
        console.error("GroupChatPage - Error in fetchMessages:", error)
        errorMessage.value = "Failed to load messages. Please try again later."
      } finally {
        loading.value = false
      }
    }

    // Keep track of message IDs we've tried to load and how many times
    const messageLoadAttempts = new Map()

    // Load more messages (for pagination/infinite scroll)
    const loadMoreMessages = async (groupId, beforeMessageId) => {
      try {
        console.log(`GroupChatPage - Loading more messages before ID: ${beforeMessageId}`)

        // Set loading state
        loading.value = true

        // Use the correct API endpoint for group chat messages with the 'before' parameter
        const response = await axiosInstance.get(`/api/group-chat/${groupId}/messages`, {
          params: { before: beforeMessageId, limit: 50 },
        })

        const olderMessages = response.data.messages || []

        // Get hasMoreMessages directly from the API response
        const hasMoreMessages = response.data.hasMoreMessages === true

        // Get additional information if available
        const totalCount = response.data.totalCount || 0
        const remainingCount = response.data.remainingCount || 0

        console.log(`GroupChatPage - Received ${olderMessages.length} older messages from API, hasMoreMessages: ${hasMoreMessages}, totalCount: ${totalCount}, remainingCount: ${remainingCount}`)

        // Update the store with the hasMoreMessages flag from the API
        store.dispatch("groupChat/setHasMoreMessages", hasMoreMessages)

        // If we received 0 messages, it means we've reached the end
        if (olderMessages.length === 0) {
          console.log("GroupChatPage - No more older messages available, reached the beginning of history")
          return 0
        }

        if (olderMessages.length > 0) {
          // Check for duplicate messages
          const existingIds = new Set(messages.value.map((m) => m.id))
          const uniqueOlderMessages = olderMessages.filter((m) => !existingIds.has(m.id))

          console.log(`GroupChatPage - Found ${uniqueOlderMessages.length} unique older messages`)

          if (uniqueOlderMessages.length > 0) {
            // Prepend unique older messages to the existing messages
            // Backend now returns messages in ASC order (oldest first) for column-reverse layout,
            // so we prepend them to maintain the correct order
            const updatedMessages = [...uniqueOlderMessages, ...messages.value]
            store.dispatch("groupChat/setMessages", updatedMessages)
            console.log(`GroupChatPage - Loaded ${uniqueOlderMessages.length} more messages`)
            return uniqueOlderMessages.length
          } else {
            // Even if all messages are duplicates, we trust the API's hasMoreMessages flag
            console.warn("GroupChatPage - All older messages were duplicates, but trusting API's hasMoreMessages flag:", hasMoreMessages)
            return 0
          }
        }

        return 0
      } catch (error) {
        console.error("GroupChatPage - Error loading more messages:", error)
        errorMessage.value = "Failed to load older messages. Please try again later."
        return 0
      } finally {
        // Reset loading state
        loading.value = false
      }
    }

    // Fetch current user info
    const fetchCurrentUser = async () => {
      try {
        const response = await axiosInstance.get(`/api/users/me`)
        currentUser.value = response.data.user || response.data
        console.log("GroupChatPage - Current user:", currentUser.value)
      } catch (error) {
        console.error("GroupChatPage - Error fetching current user:", error)
        errorMessage.value = "Failed to load user information. Please try again later."
      }
    }

    // Send a new message
    const sendMessage = async (content, media = null) => {
      // Generate temp ID outside try/catch so it's available in the catch block
      const tempId = `temp-${Date.now()}`

      try {
        if (!group.value.id) {
          console.error("Cannot send message: No active group")
          return
        }

        const messageData = {
          groupId: group.value.id,
          message: content,
          media: media
        }

        // Optimistic update - add message to UI immediately
        const optimisticMessage = {
          id: tempId,
          group_id: group.value.id,
          sender_id: currentUser.value.id,
          message: content,
          media: media ? [media] : null, // Ensure media is in array format for UI rendering
          sent_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          sender_name: currentUser.value.username,
          sender_avatar: currentUser.value.avatar,
          sender: currentUser.value,
          status: "sending",
        }

        // Add to Vuex store
        store.dispatch("groupChat/addMessage", optimisticMessage)

        // Add the temp ID to the processed set to prevent duplicates
        processedMessageIds.add(tempId)

        // Send via API
        const response = await axiosInstance.post(`/api/group-chat/send`, messageData)

        // Get the real message data
        const realMessage = response.data.chatMessage

        // Add the real message ID to the processed set to prevent duplicates
        // when it comes back through the socket
        processedMessageIds.add(realMessage.id)

        console.log(`GroupChatPage - Message sent successfully, real ID: ${realMessage.id}`)

        // We don't need to update the message here anymore, as the socket will handle it
        // The handleNewMessage function will replace the temp message with the real one
        // when it comes back through the socket

        return realMessage
      } catch (error) {
        console.error("GroupChatPage - Error sending message:", error)
        errorMessage.value = "Failed to send message. Please try again."

        // If there was an error, we should update the UI to show the error state
        // Find the temporary message and mark it as failed
        const tempMessageIndex = messages.value.findIndex(m =>
          m.id.toString() === tempId
        )

        if (tempMessageIndex !== -1) {
          const updatedMessages = [...messages.value]
          updatedMessages[tempMessageIndex] = {
            ...updatedMessages[tempMessageIndex],
            status: "failed"
          }
          store.dispatch("groupChat/setMessages", updatedMessages)
        }

        return null
      }
    }

    // Join the group socket room
    const joinGroupRoom = (groupId) => {
      if (!groupId || !currentUser.value || !currentUser.value.id) return

      console.log("GroupChatPage - Joining group room:", groupId, "with user ID:", currentUser.value.id)
      groupSocket.emit("joinGroup", { groupId, userId: currentUser.value.id })
    }

    // Leave the group socket room
    const leaveGroupRoom = (groupId) => {
      if (!groupId || !currentUser.value || !currentUser.value.id) return

      console.log("GroupChatPage - Leaving group room:", groupId, "with user ID:", currentUser.value.id)
      groupSocket.emit("leaveGroup", { groupId, userId: currentUser.value.id })
    }

    // Track processed message IDs to prevent duplicates
    const processedMessageIds = new Set()

    // Handle new message from socket
    const handleNewMessage = (message) => {
      console.log("GroupChatPage - New message received:", message)

      // Check if we've already processed this message ID
      if (processedMessageIds.has(message.id)) {
        console.log(`GroupChatPage - Skipping duplicate message with ID: ${message.id}`)
        return
      }

      // Check if this is a real message that corresponds to a temporary message
      // Look for a temporary message from the same sender with the same content
      const tempMessageIndex = messages.value.findIndex(m =>
        m.id.toString().startsWith('temp-') &&
        m.sender_id === message.sender_id &&
        m.message === message.message
      )

      if (tempMessageIndex !== -1) {
        // Found a matching temporary message, replace it with the real one
        const tempMessage = messages.value[tempMessageIndex]
        console.log(`GroupChatPage - Replacing temporary message ${tempMessage.id} with real message ${message.id}`)

        // Ensure the message has media in the correct format for UI rendering
        const processedMessage = { ...message };
        if (processedMessage.media && !Array.isArray(processedMessage.media)) {
          processedMessage.media = [processedMessage.media];
        }

        // Create a new array with the temporary message replaced by the real one
        const updatedMessages = [...messages.value]
        updatedMessages[tempMessageIndex] = processedMessage

        // Update the store with the new array
        store.dispatch("groupChat/setMessages", updatedMessages)

        // Add to processed IDs set to prevent future duplicates
        processedMessageIds.add(message.id)
        // Also add the temp ID to prevent any issues
        processedMessageIds.add(tempMessage.id)
      }
      // If no temporary message was found, add the new message normally
      else if (!messages.value.some((m) => m.id === message.id)) {
        console.log(`GroupChatPage - Adding new message with ID: ${message.id}`)

        // Ensure the message has media in the correct format for UI rendering
        const processedMessage = { ...message };
        if (processedMessage.media && !Array.isArray(processedMessage.media)) {
          processedMessage.media = [processedMessage.media];
        }

        // Add the new message to the end (newest messages at the bottom)
        store.dispatch("groupChat/addMessage", processedMessage)

        // Add to processed IDs set to prevent future duplicates
        processedMessageIds.add(message.id)

        // Limit the size of the set to prevent memory leaks
        if (processedMessageIds.size > 1000) {
          // Remove the oldest entries (convert to array, slice, and convert back to set)
          const idsArray = Array.from(processedMessageIds)
          processedMessageIds.clear()
          idsArray.slice(-500).forEach(id => processedMessageIds.add(id))
        }
      } else {
        console.log(`GroupChatPage - Message with ID ${message.id} already exists in the store`)
        // Still add to processed IDs to prevent future duplicates
        processedMessageIds.add(message.id)
      }
    }

    // Handle typing status updates
    const handleTypingStatus = ({ userId, status }) => {
      store.dispatch("groupChat/updateTypingStatus", { userId, status })
    }

    // Handle cached messages
    const handleCachedMessages = (cachedMessages) => {
      if (cachedMessages && cachedMessages.length > 0) {
        console.log(`GroupChatPage - Received ${cachedMessages.length} cached messages`)
        store.dispatch("groupChat/setMessages", cachedMessages)
      }
    }

    // Handle search
    const handleSearch = (query) => {
      console.log("GroupChatPage - Search query:", query)
      // Implement search functionality here
      // For now, just log the search query
      alert(`Search not yet implemented. Query: ${query}`)
    }

    // Handle leave group
    const handleLeaveGroup = async (groupId) => {
      console.log("GroupChatPage - Leave group:", groupId)

      if (confirm("Are you sure you want to leave this group?")) {
        try {
          // Leave the group socket room
          leaveGroupRoom(groupId)

          // Call the API to leave the group
          await axiosInstance.post(`/api/groups/${groupId}/leave`)

          // Redirect to the groups list page
          window.location.href = "/groups"
        } catch (error) {
          console.error("GroupChatPage - Error leaving group:", error)
          errorMessage.value = "Failed to leave group. Please try again later."
        }
      }
    }

    // Toggle debug menu
    const toggleDebugMenu = () => {
      showDebugMenu.value = !showDebugMenu.value
      console.log("Debug menu:", showDebugMenu.value ? "shown" : "hidden")

      // Alert for debugging
      alert(`Debug menu ${showDebugMenu.value ? 'enabled' : 'disabled'}. Current value: ${showDebugMenu.value}`)

      // Store preference in localStorage
      try {
        localStorage.setItem('showDebugMenu', showDebugMenu.value ? 'true' : 'false')
      } catch (error) {
        console.error("Error saving debug menu preference:", error)
      }
    }

    // Setup keyboard shortcuts
    const handleKeyDown = (e) => {
      // Ctrl+Shift+D to toggle debug menu
      if (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === "d") {
        toggleDebugMenu()
      }
    }

    // Handle message updates
    const handleMessageUpdated = (updatedMessage) => {
      console.log("GroupChatPage - Message updated:", updatedMessage)

      // Find the message in the current messages array
      const messageIndex = messages.value.findIndex(m => m.id === updatedMessage.id)

      if (messageIndex !== -1) {
        // Create a new array with the updated message
        const updatedMessages = [...messages.value]

        // Preserve any properties that might not be in the updated message
        updatedMessages[messageIndex] = {
          ...updatedMessages[messageIndex],
          ...updatedMessage,
          message: updatedMessage.message,
          updated_at: updatedMessage.updated_at
        }

        // Update the store
        store.dispatch("groupChat/setMessages", updatedMessages)
        console.log(`GroupChatPage - Updated message with ID: ${updatedMessage.id}`)
      } else {
        console.log(`GroupChatPage - Message with ID ${updatedMessage.id} not found in the store`)
      }
    }

    // Handle message deletions
    const handleMessageDeleted = (data) => {
      console.log("GroupChatPage - Message deleted:", data)

      // Find the message in the current messages array
      const messageIndex = messages.value.findIndex(m => m.id === data.messageId)

      if (messageIndex !== -1) {
        // Create a new array with the message marked as deleted
        const updatedMessages = [...messages.value]
        updatedMessages[messageIndex] = {
          ...updatedMessages[messageIndex],
          deleted: true,
          message: "This message has been deleted"
        }

        // Update the store
        store.dispatch("groupChat/setMessages", updatedMessages)
        console.log(`GroupChatPage - Marked message with ID: ${data.messageId} as deleted`)
      } else {
        console.log(`GroupChatPage - Message with ID ${data.messageId} not found in the store`)
      }
    }

    // Setup socket listeners - use a more robust approach to prevent duplicates
    const setupSocketListeners = () => {
      // First, remove all existing listeners for these events
      groupSocket.off("newGroupMessage")
      groupSocket.off("typingStatus")
      groupSocket.off("messageUpdated")
      groupSocket.off("messageDeleted")

      // Then add our listeners with wrapper functions that include additional logging
      groupSocket.on("newGroupMessage", (message) => {
        console.log("GroupChatPage - New group message received via socket:", message)
        // Add a timestamp to track when we received this message
        message._receivedAt = Date.now()
        handleNewMessage(message)
      })

      groupSocket.on("typingStatus", (data) => {
        console.log("GroupChatPage - Typing status received:", data)
        handleTypingStatus(data)
      })

      groupSocket.on("messageUpdated", (updatedMessage) => {
        console.log("GroupChatPage - Message update received via socket:", updatedMessage)
        handleMessageUpdated(updatedMessage)
      })

      groupSocket.on("messageDeleted", (data) => {
        console.log("GroupChatPage - Message deletion received via socket:", data)
        handleMessageDeleted(data)
      })

      console.log("GroupChatPage - Socket listeners setup complete")
    };

    // Handle socket reconnection
    const handleSocketReconnection = (event) => {
      const { groupId } = event.detail;
      console.log("GroupChatPage - Socket reconnected event received for group:", groupId);

      // Setup socket listeners again to ensure we're receiving events
      console.log("GroupChatPage - Reconnected, setting up socket listeners again");
      setupSocketListeners();

      // Rejoin the group room with the current user ID
      if (currentUser.value && currentUser.value.id) {
        console.log("GroupChatPage - Reconnected, rejoining group room:", groupId);
        joinGroupRoom(groupId);
      }
    };

    // Lifecycle hooks
    onMounted(async () => {
      try {
        // Clear previous messages and message load attempts
        store.dispatch("groupChat/clearMessages")
        clearMessageLoadAttempts()

        // Get route params
        const groupId = window.location.pathname.split("/").pop()

        // Setup keyboard shortcuts
        window.addEventListener("keydown", handleKeyDown)

        // Setup socket reconnection listener
        window.addEventListener("socket-reconnected", handleSocketReconnection)

        // Check socket connection status
        console.log("GroupChatPage - Socket connection status:", groupSocket.connected ? "Connected" : "Disconnected")

        // Force reconnect if not connected
        if (!groupSocket.connected) {
          console.log("GroupChatPage - Socket not connected on mount, connecting...")
          groupSocket.connect()
        }

        // Setup the socket listeners
        setupSocketListeners()

        // Fetch current user first
        await fetchCurrentUser()

        // Then fetch group info and messages
        await fetchGroupInfo(groupId)
      } catch (error) {
        console.error("GroupChatPage - Error in onMounted:", error)
        errorMessage.value = "An error occurred while loading the chat. Please try again."
      }
    })

    onUnmounted(() => {
      // Clean up
      window.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("socket-reconnected", handleSocketReconnection)

      // Remove socket listeners
      console.log("GroupChatPage - Removing socket listeners on unmount")
      groupSocket.off("newGroupMessage")
      groupSocket.off("typingStatus")
      groupSocket.off("messageUpdated")
      groupSocket.off("messageDeleted")

      // Leave group room
      if (group.value && group.value.id) {
        console.log("GroupChatPage - Leaving group room on unmount:", group.value.id)
        leaveGroupRoom(group.value.id)
      }
    })

    // Watch for changes in the group ID to reload messages
    watch(
      () => group.value?.id,
      async (newGroupId, oldGroupId) => {
        if (newGroupId && newGroupId !== oldGroupId) {
          // Leave old group room if applicable
          if (oldGroupId) {
            leaveGroupRoom(oldGroupId)
          }

          // Make sure we have current user info before joining the group room
          if (!currentUser.value || !currentUser.value.id) {
            await fetchCurrentUser()
          }

          // Join new group room and fetch messages
          joinGroupRoom(newGroupId)
          // Clear message load attempts when changing groups
          clearMessageLoadAttempts()
          fetchMessages(newGroupId)
        }
      },
    )

    return {
      group,
      messages,
      currentUser,
      errorMessage,
      loading,
      showDebugMenu,
      loadMoreMessages,
      sendMessage,
      handleCachedMessages,
      handleSearch,
      handleLeaveGroup,
    }
  },
}

</script>

<style scoped>
.chat-page {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
 
  background-image: url('./background-pattern.svg');
  background-size: 300px;
  background-repeat: repeat;
  background-position: center;
  background-blend-mode: overlay;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: rgba(18, 18, 18, 0.359);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ECEFF4;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #FFFFFF;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

.error {
  color: #e74c3c;
  padding: 10px;
  text-align: center;
  background-color: rgba(231, 76, 60, 0.1);
  border-radius: 4px;
  margin: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chat-window {
    border-radius: 0;
  }
}
</style>
<template>
    <div class="sticker-section">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <div class="spinner"></div>
        <div class="loading-text">Loading Stickers...</div>
      </div>
      
      <!-- Empty State -->
      <div v-else-if="stickers.length === 0" class="empty-state">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
          <path d="M15.5 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3Z"/>
          <path d="M15 3v6h6"/>
        </svg>
        <div class="empty-text">No Stickers found</div>
        <div class="empty-subtext">Try a different search term</div>
      </div>
      
      <!-- Sticker Grid -->
      <div v-else class="sticker-grid">
        <div 
          v-for="sticker in stickers" 
          :key="sticker.id" 
          class="sticker-item"
          @click="selectSticker(sticker)"
        >
          <div class="sticker-wrapper">
            <img 
              :src="sticker.images.fixed_height.url" 
              :alt="sticker.title || 'Sticker'"
              loading="lazy"
              class="sticker-image"
            />
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import { ref, watch, onMounted } from 'vue';
  import { GiphyFetch } from '@giphy/js-fetch-api';
  import debounce from 'lodash.debounce';
  
  export default {
    name: 'StickerSection',
    props: {
      searchQuery: {
        type: String,
        default: ''
      }
    },
    emits: ['select'],
    setup(props, { emit }) {
      const stickers = ref([]);
      const isLoading = ref(true);
      
      // Initialize Giphy API client
      const gf = new GiphyFetch('o4Jhck5Plb4kryLjWXh1D9BJY7QW1CBW');
      
      // Fetch trending stickers
      const fetchTrendingStickers = async () => {
        try {
          isLoading.value = true;
          const { data } = await gf.trending({ limit: 30, type: 'stickers' });
          stickers.value = data;
        } catch (error) {
          console.error('Error fetching trending stickers:', error);
        } finally {
          isLoading.value = false;
        }
      };
      
      // Search stickers
      const searchStickers = async (query) => {
        try {
          isLoading.value = true;
          if (query) {
            const { data } = await gf.search(query, { limit: 30, type: 'stickers' });
            stickers.value = data;
          } else {
            fetchTrendingStickers();
          }
        } catch (error) {
          console.error('Error searching stickers:', error);
        } finally {
          isLoading.value = false;
        }
      };
      
      // Debounced search function
      const debouncedSearch = debounce((query) => {
        searchStickers(query);
      }, 300);
      
      // Select a sticker
      const selectSticker = (sticker) => {
        emit('select', sticker);
      };
      
      // Watch for search query changes
      watch(() => props.searchQuery, (newQuery) => {
        debouncedSearch(newQuery);
      });
      
      onMounted(() => {
        fetchTrendingStickers();
      });
      
      return {
        stickers,
        isLoading,
        selectSticker
      };
    }
  };
  </script>
  
  <style scoped>
  .sticker-section {
    height: 100%;
    min-height: 200px;
    position: relative;
  }
  
  .sticker-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 12px;
  }
  
  .sticker-item {
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s;
    background: transparent;
  }
  
  .sticker-item:hover {
    transform: scale(1.05);
  }
  
  .sticker-wrapper {
    position: relative;
    width: 100%;
    padding-top: 100%; /* 1:1 Aspect Ratio */
    overflow: hidden;
  }
  
  .sticker-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  
  /* Loading state */
  .loading-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(34, 46, 53, 0.8);
    z-index: 2;
  }
  
  .spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(0, 168, 132, 0.3);
    border-radius: 50%;
    border-top-color: #00a884;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }
  
  .loading-text {
    color: #e0e0e0;
    font-size: 14px;
  }
  
  /* Empty state */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200px;
    color: #8a9aa4;
    padding: 20px;
    text-align: center;
  }
  
  .empty-state svg {
    margin-bottom: 16px;
    opacity: 0.6;
  }
  
  .empty-text {
    font-size: 16px;
    margin-bottom: 8px;
    color: #e0e0e0;
  }
  
  .empty-subtext {
    font-size: 14px;
    color: #8a9aa4;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  /* Responsive adjustments */
  @media (min-width: 480px) {
    .sticker-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  
  @media (min-width: 768px) {
    .sticker-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  </style>
// backend/routes/Groups/worldChatRoutes.js
const express = require('express');
const WorldChatController = require('../../controllers/Group/worldChatController');
const authMiddleware = require('../../middleware/authMiddleware');
const router = express.Router();

router.post('/send', authMiddleware, WorldChatController.sendMessage);
router.get('/messages', authMiddleware, WorldChatController.getMessages);

router.delete('/messages/:messageId', authMiddleware, WorldChatController.deleteMessage);
router.post('/mute/:userId', authMiddleware, WorldChatController.muteUser);
router.post('/messages/:messageId/report', authMiddleware, WorldChatController.reportMessage);

router.get('/reported-messages', authMiddleware, WorldChatController.getReportedMessages);
router.post('/ban/:userId', authMiddleware, WorldChatController.banUser);

module.exports = router;
<template>
    <div class="modal-backdrop" @click="close"></div>
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Group Tags</h2>
        <button @click="close" class="close-button">✕</button>
      </div>
      <div v-if="!isEditing">
        <div class="tags-list">
          <span v-for="tag in currentTags" :key="tag" class="tag">{{ tag }}</span>
          <span v-if="!currentTags.length" class="no-tags">No tags available</span>
        </div>
        <button v-if="isAdmin" @click="startEditing" class="edit-button">Edit Tags</button>
      </div>
      <div v-else>
        <div class="tag-input-wrapper">
          <div class="tags-group">
            <div v-for="tag in selectedTags" :key="tag" class="tag editable">
              <span class="tag-text">{{ tag }}</span>
              <button
                type="button"
                @click="removeTag(tag)"
                class="tag-remove"
                aria-label="Remove tag"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          </div>
          <input
            ref="tagInput"
            type="text"
            v-model="inputValue"
            @keydown="handleKeyDown"
            :placeholder="selectedTags.length === 0 ? 'Add group tags...' : ''"
            class="tag-input"
          />
        </div>
        <div class="button-group">
          <button @click="saveTags" class="save-button">Save</button>
          <button @click="cancelEditing" class="cancel-button">Cancel</button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  import axios from 'axios';
  
  const props = defineProps({
    groupId: { type: Number, required: true },
    isAdmin: { type: Boolean, required: true },
    currentTags: { type: Array, required: true },
  });
  
  const emit = defineEmits(['close', 'update-tags']);
  
  const isEditing = ref(false);
  const selectedTags = ref([...props.currentTags]);
  const inputValue = ref('');
  
  const startEditing = () => {
    isEditing.value = true;
  };
  
  const cancelEditing = () => {
    isEditing.value = false;
    selectedTags.value = [...props.currentTags];
  };
  
  const addTag = (tag) => {
    const trimmedTag = tag.trim().toLowerCase();
    if (trimmedTag && !selectedTags.value.includes(trimmedTag)) {
      selectedTags.value.push(trimmedTag);
      inputValue.value = '';
    }
  };
  
  const removeTag = (tag) => {
    const index = selectedTags.value.indexOf(tag);
    if (index > -1) {
      selectedTags.value.splice(index, 1);
    }
  };
  
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && inputValue.value) {
      e.preventDefault();
      addTag(inputValue.value);
    } else if (e.key === 'Backspace' && !inputValue.value && selectedTags.value.length > 0) {
      removeTag(selectedTags.value[selectedTags.value.length - 1]);
    }
  };
  
  const saveTags = async () => {
    try {
      await axios.put(
        `${import.meta.env.VITE_API_BASE_URL}/api/groups/${props.groupId}/tags`,
        { tags: selectedTags.value },
        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
      );
      emit('update-tags', selectedTags.value);
      isEditing.value = false;
    } catch (error) {
      console.error('Error updating tags:', error);
      alert('Failed to update tags');
    }
  };
  
  const close = () => emit('close');
  </script>
  
  <style scoped>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
  }
  
  .modal-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1001;
    border-radius: 8px;
    background: linear-gradient(337deg, #40444b30, #33333387);
    background-color: rgba(0, 0, 0, 0.751);
    padding: 20px;
    color: #ffffff;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .modal-header h2 {
    font-size: 20px;
    font-weight: 600;
  }
  
  .close-button {
    background: none;
    border: none;
    color: #a0a0a0;
    font-size: 18px;
    cursor: pointer;
  }
  
  .close-button:hover {
    color: #ffffff;
  }
  
  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .tag {
    background-color: rgba(255, 255, 255, 0.07);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .no-tags {
    color: #a0a0a0;
    font-style: italic;
  }
  
  .tag-input-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.625rem 0.75rem;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 4px;
    min-height: 2.75rem;
    align-items: center;
  }
  
  .tags-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .tag.editable {
    display: flex;
    align-items: center;
    gap: 0.375rem;
  }
  
  .tag-text {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .tag-remove {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    padding: 0;
    width: 1.125rem;
    height: 1.125rem;
  }
  
  .tag-remove:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .tag-input {
    flex: 1;
    min-width: 120px;
    background: transparent;
    border: none;
    color: #ffffff;
    font-size: 14px;
    padding: 0.25rem 0.375rem;
    outline: none;
  }
  
  .tag-input::placeholder {
    color: #aaa;
  }
  
  .button-group {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1rem;
  }
  
  .save-button,
  .cancel-button,
  .edit-button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .save-button {
    background-color: #7289da;
    color: #fff;
  }
  
  .cancel-button {
    background-color: #4a4a4a;
    color: #fff;
  }
  
  .edit-button {
    background-color: #7289da;
    color: #fff;
    margin-top: 1rem;
  }
  </style>
<template>
  <div>
    <!-- System Notifications -->
    <div class="notification-center">
      <transition-group name="slide-fade">
        <div
          v-for="notif in notifications"
          :key="notif.id"
          class="notification"
          :class="notif.type"
        >
          <!-- Left area: clicking here opens the notification -->
          <div class="notification-body"
               @click="handleClick(notif)"
               @mouseenter="pauseAutoDismiss(notif)"
               @mouseleave="resumeAutoDismiss(notif)">
            <!-- Show icon or avatar if provided -->
            <div class="notification-icon" v-if="!notif.avatar">
              <component :is="getIconComponent(notif.type)" size="20" />
            </div>
            <div v-else class="notification-avatar">
              <img :src="notif.avatar" :alt="notif.sender?.name || 'Notification'" />
            </div>
            <div class="notification-message">
              <h4>{{ formatTitle(notif) }}</h4>
              <p>{{ notif.message }}</p>
              <div v-if="notif.sender?.name" class="notification-sender">
                From: {{ notif.sender.name }}
              </div>
              <div v-if="notif.timestamp" class="notification-timestamp">
                {{ formatTimestamp(notif.timestamp) }}
              </div>
            </div>
          </div>
          <!-- Right area: clicking here will dismiss the notification -->
          <div class="notification-close-area" @click.stop="dismiss(notif)">
            <button class="notification-close" aria-label="Close notification">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
      </transition-group>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container">
      <transition-group name="toast">
        <div
          v-for="toast in toasts"
          :key="toast.id"
          class="toast"
          :class="toast.type"
        >
          <div class="toast-content">
            <div v-if="toast.type === 'success'" class="toast-icon success">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </div>
            <div v-else-if="toast.type === 'error'" class="toast-icon error">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="15" y1="9" x2="9" y2="15"></line>
                <line x1="9" y1="9" x2="15" y2="15"></line>
              </svg>
            </div>
            <div v-else class="toast-icon info">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
              </svg>
            </div>
            <div class="toast-message">{{ toast.message }}</div>
            <button class="toast-close" @click="removeToast(toast.id)">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script>
import { io } from 'socket.io-client';
import { CheckCircle, AlertCircle, Info, Bell } from 'lucide-vue-next';

export default {
  name: 'GlobalNotificationCenter',
  computed: {
    notifications() {
      return this.$store.state.app.pendingNotifications;
    },
    toasts() {
      return this.$store.state.app.toasts;
    },
  },
  methods: {
    // Toast methods
    removeToast(id) {
      this.$store.dispatch('app/removeToast', id);
    },

    // Notification methods
    formatTitle(notif) {
      switch (notif.type) {
        case 'friendRequest':
          return 'Friend Request';
        case 'chat':
          return 'New Message';
        case 'system':
          return 'System Notification';
        default:
          return 'Notification';
      }
    },
    formatTimestamp(timestamp) {
      return new Intl.DateTimeFormat('en-US', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: true,
      }).format(new Date(timestamp));
    },
    getIconComponent(type) {
      switch (type) {
        case 'friendRequest':
          return Bell;
        case 'chat':
          return CheckCircle;
        case 'system':
          return AlertCircle;
        default:
          return Info;
      }
    },
    handleClick(notification) {
      if (notification.type === 'friendRequest') {
        this.$router.push('/friend-requests');
      } else if (notification.type === 'chat') {
        this.$router.push(`/chat/${notification.chatId}`);
      }
      this.$store.commit('app/MARK_AS_SEEN', notification.id);
      this.$store.commit('app/REMOVE_NOTIFICATION', notification.id);
    },
    dismiss(notification) {
      this.$store.commit('app/REMOVE_NOTIFICATION', notification.id);
    },
    playSound() {
      const audio = new Audio('/sounds/mixkit-software-interface-start-2574.mp3');
      audio.preload = 'auto';
      audio.load();
      audio.play().catch((error) => {
        console.error('Error playing sound:', error);
      });
    },
    setupAutoDismiss(notification) {
      if (notification.timer) {
        clearTimeout(notification.timer);
      }
      notification.timer = setTimeout(() => {
        this.dismiss(notification);
      }, 5000);
    },
    pauseAutoDismiss(notification) {
      if (notification.timer) {
        clearTimeout(notification.timer);
        notification.timer = null;
      }
    },
    resumeAutoDismiss(notification) {
      this.setupAutoDismiss(notification);
    },
  },
  watch: {
    notifications: {
      handler(newNotifications) {
        newNotifications.forEach((notif) => {
          if (!notif.timer) {
            this.setupAutoDismiss(notif);
          }
        });
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    const socket = io(`${import.meta.env.VITE_API_BASE_URL}/notifications`, {
      query: { token: localStorage.getItem('token') },
    });
    socket.on('connect', () => {
     
    });
    socket.on('disconnect', () => {
     
    });
    const user = this.$store.getters['auth/user'];
    if (user && user.id) {
      const room = `user_${user.id}`;
      socket.emit('joinRoom', room, (ack) => {
        console.log('joinRoom acknowledgment:', ack);
      });
    }
    socket.on('notification', (notif) => {
      this.$store.commit('app/ADD_NOTIFICATION', notif);
      this.playSound();
    });
    this.$store.dispatch('app/fetchRequests')
      .then(() => {
      
      })
      .catch(err => {
        console.error('Error fetching initial notifications:', err);
      });
  },
};
</script>

<style scoped>
/* Common variables */
:root {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --error-color: #f87171;
  --success-color: #34d399;
  --warning-color: #fbbf24;
}

/* System Notifications */
.notification-center {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  width: 350px;
  max-width: calc(100vw - 40px);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 80px; /* Position below the notification center */
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 350px;
  max-width: calc(100vw - 40px);
}

.notification {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  margin-bottom: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  backdrop-filter: blur(8px);
}

/* Left area: 85% width */
.notification-body {
  flex: 5;
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.notification-body:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Right area: 15% width for closing */
.notification-close-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.notification-close-area:hover {
  background-color: rgba(248, 113, 113, 0.2);
}

.notification-icon,
.notification-avatar {
  flex-shrink: 0;
  margin-right: 0.75rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-tertiary);
  border-radius: 10px;
  color: var(--accent-primary);
}

.notification-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

.notification-message {
  flex-grow: 1;
  min-width: 0;
}

.notification-message h4 {
  margin: 0 0 0.25rem;
  font-size: 0.9375rem;
  font-weight: 600;
  color: var(--text-primary);
}

.notification-message p {
  margin: 0 0 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-sender,
.notification-timestamp {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* The close button itself */
.notification-close {
  background: none;
  border: none;
  color: var(--text-primary);
  opacity: 0.8;
  transition: opacity var(--transition-fast);
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  opacity: 1;
  color: var(--error-color);
}

/* Notification types */
.notification.friendRequest {
  border-left: 3px solid var(--accent-primary);
}

.notification.chat {
  border-left: 3px solid var(--success-color);
}

.notification.system {
  border-left: 3px solid var(--warning-color);
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.5s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from {
  transform: translateX(20px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(100px);
  opacity: 0;
}

.notification-icon,
.notification-avatar {
  animation: subtle-bounce 2s ease-in-out infinite;
}

@keyframes subtle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

/* Toast styles */
.toast {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  width: 100%;
  border-left: 4px solid var(--accent-primary);
  backdrop-filter: blur(8px);
  margin-bottom: 10px;
  animation: toast-slide-in 0.3s ease forwards;
}

.toast.success {
  border-left-color: var(--success-color);
  background-color: rgba(52, 211, 153, 0.1);
}

.toast.error {
  border-left-color: var(--error-color);
  background-color: rgba(248, 113, 113, 0.1);
}

.toast.info {
  border-left-color: var(--accent-primary);
  background-color: rgba(99, 102, 241, 0.1);
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
}

.toast-icon {
  margin-right: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.toast-icon.success {
  color: var(--success-color);
  background-color: rgba(52, 211, 153, 0.15);
}

.toast-icon.error {
  color: var(--error-color);
  background-color: rgba(248, 113, 113, 0.15);
}

.toast-icon.info {
  color: var(--accent-primary);
  background-color: rgba(99, 102, 241, 0.15);
}

.toast-message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-fast);
  margin-left: 8px;
}

.toast-close:hover {
  color: var(--text-primary);
  background-color: rgba(255, 255, 255, 0.1);
}

@keyframes toast-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Toast animations */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(50px);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100px);
}

/* Responsive adjustments for small devices */
@media (max-width: 480px) {
  .notification-center,
  .toast-container {
    width: calc(100% - 40px);
    max-width: 350px;
  }

  .notification-body {
    padding: 0.75rem;
  }

  .notification-icon,
  .notification-avatar {
    width: 2rem;
    height: 2rem;
    margin-right: 0.5rem;
  }

  .notification-message h4 {
    font-size: 0.875rem;
  }

  .notification-message p {
    font-size: 0.8125rem;
  }
}
</style>
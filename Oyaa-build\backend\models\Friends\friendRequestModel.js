// backend/models/friendRequestModel.js
const db = require('../../utils/db');

class FriendRequestModel {
  async createRequest(senderId, receiverId) {
   
    // Validate input parameters
    if (!senderId || isNaN(parseInt(senderId))) {
      console.error('Invalid senderId:', senderId);
      throw new Error('Invalid sender ID');
    }

    if (!receiverId || isNaN(parseInt(receiverId))) {
      console.error('Invalid receiverId:', receiverId);
      throw new Error('Invalid receiver ID');
    }

    // Convert to integers to ensure proper database handling
    const senderIdInt = parseInt(senderId);
    const receiverIdInt = parseInt(receiverId);

   

    const query = `
      INSERT INTO friend_requests (sender_id, receiver_id, status, created_at)
      VALUES ($1, $2, 'pending', CURRENT_TIMESTAMP)
      RETURNING *;
    `;

    try {
      const result = await db.query(query, [senderIdInt, receiverIdInt]);
     
      return result.rows[0];
    } catch (error) {
      console.error('Database error in createRequest:', error);
      console.error('Error code:', error.code);
      console.error('Error detail:', error.detail);
      console.error('Error stack:', error.stack);
      throw error;
    }
  }

  async getRequestById(requestId) {
    const query = 'SELECT * FROM friend_requests WHERE id = $1';
    const result = await db.query(query, [requestId]);
    return result.rows[0];
  }

  async getRequests(receiverId) {
    

    // Validate receiverId
    if (!receiverId || isNaN(parseInt(receiverId))) {
      console.error('Invalid receiverId in getRequests:', receiverId);
      throw new Error('Invalid receiver ID');
    }

    const receiverIdInt = parseInt(receiverId);
   

    // Query to get pending friend requests with proper status
    const query = `
      SELECT fr.id, fr.sender_id, u.username AS sender_username,
             u.avatar AS sender_avatar, u.description AS sender_description,
             fr.created_at,
             fr.status
      FROM friend_requests fr
      JOIN users u ON fr.sender_id = u.id
      WHERE fr.receiver_id = $1 AND (fr.status IS NULL OR fr.status = 'Pending' OR fr.status = 'pending')
      ORDER BY fr.created_at DESC;
    `;

    try {
      const result = await db.query(query, [receiverIdInt]);
    

      // Log each row to debug
      result.rows.forEach(row => {
      
      });

      return result.rows;
    } catch (error) {
      console.error('Database error in getRequests:', error);
      throw error;
    }
  }

  async acceptRequest(requestId) {
   
    try {
      // Start a transaction
     
      await db.query('BEGIN');

      // Update the friend request status
      const query = `
        UPDATE friend_requests
        SET status = 'Accepted'
        WHERE id = $1
        RETURNING *;
      `;
     
      const result = await db.query(query, [requestId]);

      if (result.rows.length === 0) {
        console.error(`No friend request found with ID ${requestId}`);
        await db.query('ROLLBACK');
        throw new Error(`Friend request with ID ${requestId} not found`);
      }

      

      // Also add to the friends table if not already friends
      const { sender_id, receiver_id } = result.rows[0];
     
      // Check if they are already friends
      const checkQuery = `
        SELECT * FROM friends
        WHERE (user_id = $1 AND friend_id = $2)
        OR (user_id = $2 AND friend_id = $1)
        LIMIT 1;
      `;
      
      const checkResult = await db.query(checkQuery, [sender_id, receiver_id]);

      // Only add friendship if it doesn't exist
      if (checkResult.rows.length === 0) {
       
        const insertQuery = `
          INSERT INTO friends (user_id, friend_id) VALUES ($1, $2), ($2, $1);
        `;
       
        await db.query(insertQuery, [sender_id, receiver_id]);
        
      } else {
       
      }

      // Commit the transaction
     
      await db.query('COMMIT');
    
      return result.rows[0];
    } catch (error) {
      // Rollback in case of error
      console.error(`Error in acceptRequest for ID ${requestId}:`, error);
      console.error('Error stack:', error.stack);
      try {
        
        await db.query('ROLLBACK');
        
      } catch (rollbackError) {
        console.error('Error during rollback:', rollbackError);
      }
      throw error;
    }
  }

  async rejectRequest(requestId) {
  
    try {
      const query = `
        UPDATE friend_requests
        SET status = 'Rejected'
        WHERE id = $1
        RETURNING *;
      `;
     
      const result = await db.query(query, [requestId]);

      if (result.rows.length === 0) {
        console.error(`No friend request found with ID ${requestId}`);
        throw new Error(`Friend request with ID ${requestId} not found`);
      }

    
      return result.rows[0];
    } catch (error) {
      console.error(`Error in rejectRequest for ID ${requestId}:`, error);
      console.error('Error stack:', error.stack);
      throw error;
    }
  }

  async getAllSentRequests(senderId) {


    // This query gets ALL sent requests regardless of status
    const query = `
      SELECT
        fr.id,
        fr.receiver_id,
        fr.status,
        fr.created_at,
        u.username AS receiver_username,
        u.avatar AS receiver_avatar,
        u.description AS receiver_description
      FROM friend_requests fr
      JOIN users u ON fr.receiver_id = u.id
      WHERE fr.sender_id = $1
      ORDER BY fr.created_at DESC
    `;

    try {
      const result = await db.query(query, [senderId]);
     

      // Log each row for debugging
      result.rows.forEach(row => {
       
      });

      return result.rows;
    } catch (error) {
      console.error('Error in getAllSentRequests:', error);
      throw error;
    }
  }

  async getSentRequests(senderId, seenRejections = []) {
   

    let query = `
      SELECT fr.id, fr.receiver_id, u.username AS receiver_username,
             u.avatar AS receiver_avatar, u.description AS receiver_description,
             fr.status, fr.created_at
      FROM friend_requests fr
      LEFT JOIN users u ON fr.receiver_id = u.id
      WHERE fr.sender_id = $1
        AND (
          LOWER(fr.status) = 'pending'
          OR (
            LOWER(fr.status) = 'rejected'
    `;
    const params = [senderId];

    if (seenRejections.length > 0) {
      const placeholders = seenRejections.map((_, index) => `$${index + 2}`).join(', ');
      query += ` AND fr.id NOT IN (${placeholders}) `;
      params.push(...seenRejections);
    }

    query += `
          )
        )
      ORDER BY fr.created_at DESC;
    `;

    try {
      const result = await db.query(query, params);
     

      // Log each row for debugging
      result.rows.forEach(row => {
       
      });

      return result.rows;
    } catch (error) {
      console.error('Error in getSentRequests:', error);
      throw error;
    }
  }

  async getRequestsByStatus(receiverId, status) {
    const query = `
      SELECT fr.id, fr.sender_id, u.username AS sender_username, fr.status, fr.created_at
      FROM friend_requests fr
      JOIN users u ON fr.sender_id = u.id
      WHERE fr.receiver_id = $1 AND fr.status = $2;
    `;
    const result = await db.query(query, [receiverId, status]);
    return result.rows;
  }

  async getSentRequestsByStatus(senderId, status) {
    const query = `
      SELECT fr.id, fr.receiver_id, u.username AS receiver_username, fr.status, fr.created_at
      FROM friend_requests fr
      JOIN users u ON fr.receiver_id = u.id
      WHERE fr.sender_id = $1 AND fr.status = $2;
    `;
    const result = await db.query(query, [senderId, status]);
    return result.rows;
  }
  async getRequestsSince(receiverId, since) {
    const query = `
      SELECT fr.id, fr.sender_id, u.username AS sender_username, fr.created_at
      FROM friend_requests fr
      JOIN users u ON fr.sender_id = u.id
      WHERE fr.receiver_id = $1
        AND fr.status = 'Pending'
        AND fr.created_at > to_timestamp($2 / 1000.0)
      ORDER BY fr.created_at DESC;
    `;
    const result = await db.query(query, [receiverId, since]);
    return result.rows;
  }

  async getRequestById(requestId) {
    const query = `
      SELECT fr.id, fr.sender_id, fr.receiver_id, fr.status, fr.created_at,
             s.username AS sender_username, s.avatar AS sender_avatar,
             r.username AS receiver_username, r.avatar AS receiver_avatar
      FROM friend_requests fr
      JOIN users s ON fr.sender_id = s.id
      JOIN users r ON fr.receiver_id = r.id
      WHERE fr.id = $1;
    `;
    const result = await db.query(query, [requestId]);
    return result.rows[0];
  }
}

module.exports = new FriendRequestModel();

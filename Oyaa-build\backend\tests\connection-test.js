/**
 * Test script to verify connections to AlloyDB Omni and Group Chat Valkey
 * Run with: node tests/connection-test.js
 */

const { omniPool, omniQuery } = require('../config/alloydb.config');
const {
  groupChatClient,
  groupChatValkeySet,
  groupChatValkeyGet,
  groupChatValkeyLPush,
  groupChatValkeyLRange
} = require('../config/groupChatValkey.config');
const logger = require('../utils/logger');

// Test AlloyDB Omni connection
async function testAlloyDBConnection() {
  try {
    logger.info('Testing AlloyDB Omni connection...');
    const result = await omniQuery('SELECT version()');
    logger.info(`AlloyDB Omni connection successful. Version: ${result.rows[0].version}`);
    return true;
  } catch (error) {
    logger.error(`AlloyDB Omni connection test failed: ${error.message}`);
    return false;
  }
}

// Test Group Chat Valkey connection
async function testGroupChatValkeyConnection() {
  try {
    logger.info('Testing Group Chat Valkey connection...');
    
    // Test basic set/get operations
    const testKey = 'connection_test';
    const testValue = { timestamp: new Date().toISOString(), message: 'Connection test successful' };
    
    await groupChatValkeySet(testKey, testValue, 60); // Expire in 60 seconds
    const retrievedValue = await groupChatValkeyGet(testKey);
    
    if (retrievedValue && retrievedValue.timestamp === testValue.timestamp) {
      logger.info('Group Chat Valkey basic operations test passed');
    } else {
      throw new Error('Retrieved value does not match set value');
    }
    
    // Test list operations
    const listKey = 'connection_test_list';
    await groupChatValkeyLPush(listKey, { id: 1, message: 'Test message 1' });
    await groupChatValkeyLPush(listKey, { id: 2, message: 'Test message 2' });
    
    const listValues = await groupChatValkeyLRange(listKey, 0, -1);
    
    if (listValues.length === 2 && listValues[0].id === 2) {
      logger.info('Group Chat Valkey list operations test passed');
    } else {
      throw new Error('List operations test failed');
    }
    
    logger.info('Group Chat Valkey connection test successful');
    return true;
  } catch (error) {
    logger.error(`Group Chat Valkey connection test failed: ${error.message}`);
    return false;
  }
}

// Run tests
async function runTests() {
  try {
    const alloyDBResult = await testAlloyDBConnection();
    const valkeyResult = await testGroupChatValkeyConnection();
    
    if (alloyDBResult && valkeyResult) {
      logger.info('All connection tests passed successfully!');
    } else {
      logger.error('Some connection tests failed. Check the logs for details.');
    }
    
    // Close connections
    await omniPool.end();
    await groupChatClient.quit();
    
    process.exit(alloyDBResult && valkeyResult ? 0 : 1);
  } catch (error) {
    logger.error(`Error running tests: ${error.message}`);
    process.exit(1);
  }
}

// Execute tests
runTests();

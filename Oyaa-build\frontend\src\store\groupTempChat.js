import axios from '@/api/axios';

const state = {
  // List of temporary chat groups
  tempChats: [],
  // Currently active temp chat
  currentTempChat: null,
  // Messages for the current anonymous group chat
  messages: []
};

const mutations = {
  SET_TEMP_CHATS(state, chats) {
    state.tempChats = chats;
  },
  SET_CURRENT_TEMP_CHAT(state, chat) {
    state.currentTempChat = chat;
  },
  CLEAR_CURRENT_TEMP_CHAT(state) {
    state.currentTempChat = null;
  },
  SET_MESSAGES(state, messages) {
    state.messages = messages;
  },
  ADD_MESSAGE(state, message) {
    state.messages.push(message);
  },
  CLEAR_MESSAGES(state) {
    state.messages = [];
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions: {
    // Fetch all temporary chats created by the user
    async fetchTempChats({ commit, rootState }) {
      try {
        const response = await axios.get('/api/temp-groups/created', {
          headers: { Authorization: `Bearer ${rootState.auth.token}` },
        });
        commit('SET_TEMP_CHATS', response.data.groups || []);
      } catch (error) {
        console.error('Error fetching temp chats:', error);
      }
    },
    // Join a temporary chat group
    async joinTempChat({ commit }, { linkToken, tempUsername }) {
      try {
        const response = await axios.post('/api/temp-groups/join', {
          linkToken,
          tempUsername,
        });
        const { sessionToken, groupId } = response.data;
        localStorage.setItem('tempGroupSessionToken', sessionToken);
        commit('SET_CURRENT_TEMP_CHAT', { id: groupId });
        // Messages will be loaded via WebSocket in the component
        return { sessionToken, groupId };
      } catch (error) {
        console.error('Error joining temp chat:', error);
        throw error;
      }
    },
    // Send a message (used if not relying solely on WebSocket)
    async sendMessage({ state }, message) {
      try {
        const sessionToken = localStorage.getItem('tempGroupSessionToken');
        await axios.post(
          '/api/temp-groups/send', // Adjust endpoint if needed
          { groupId: state.currentTempChat.id, message },
          { headers: { Authorization: `Bearer ${sessionToken}` } }
        );
        // Note: Real-time updates will come via WebSocket, so no commit here
      } catch (error) {
        console.error('Error sending message:', error);
      }
    },
    // Leave the current temp chat
    leaveTempChat({ commit }) {
      localStorage.removeItem('tempGroupSessionToken');
      commit('CLEAR_CURRENT_TEMP_CHAT');
    },
  },
};
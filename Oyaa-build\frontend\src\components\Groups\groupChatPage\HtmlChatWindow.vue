<template>
  <div class="chat-window">
    <!-- Chat header -->
    <group-header
      :group="group"
      :typing-users="typingUsers"
      @search="handleSearch"
      @leave-group="handleLeaveGroup"
    />

    <!-- Chat body -->
    <div class="chat-body">
      <!-- HTML-based chat list -->
      <html-chat-list
        ref="chatList"
        :messages="messages"
        :current-user="currentUser"
        :group="group"
        :loading="loading"
        :has-more-messages="hasMoreMessages"
        :load-error="loadError"
        @load-more="handleLoadMore"
        @scroll-state-change="handleScrollStateChange"
        @cached-messages-loaded="handleCachedMessagesLoaded"
        @debug-metrics="handleDebugMetrics"
        @no-more-messages="handleNoMoreMessages"
        @reply="handleReply"
        @copy="handleCopy"
        @edit="handleEdit"
        @delete="handleDelete"
        @report="handleReport"
        @open-gallery="handleOpenGallery"
        @open-user-profile="handleOpenUserProfile"
      />

      <!-- Jump to bottom button (to latest messages) - only shown when not at bottom -->
      <div v-if="!isAtBottom" class="jump-to-bottom-button" @click="jumpToLatestMessages">
        <svg class="jump-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 17L5 10H19L12 17Z" fill="currentColor"/>
        </svg>
        <!--
          TODO: Future implementation - Two-way scroll to bottom:
          1. First click: Scroll to first unread message
          2. Second click: Scroll to very bottom (latest message)

          Will need to:
          - Track unread messages
          - Add badge to show count of new messages
          - Implement two-stage scrolling logic

          Example badge implementation:
          <div v-if="newMessagesCount > 0" class="new-messages-badge">
            {{ newMessagesCount > 99 ? '99+' : newMessagesCount }}
          </div>
        -->
      </div>
    </div>

    <!-- Chat input -->
    <group-message-input
      ref="chatInputArea"
      :group="group"
      :reply-message="replyMessage"
      :is-muted="false"
      :is-uploading="false"
      @send-message="sendMessage"
      @cancel-reply="cancelReply"
    />

    <!-- Debug panel -->
    <fps-counter
      :message-count="messages.length || 0"
      :visible-count="visibleMessagesCount"
      :scroll-percentage="scrollPercentage"
      :scroll-direction="scrollDirection"
      :is-at-bottom="isAtBottom"
      :visible-range-start="visibleRangeStart"
      :visible-range-end="visibleRangeEnd"
      :top-spacer-height="topSpacerHeight"
      :bottom-spacer-height="bottomSpacerHeight"
      :html-chat-fps="htmlChatFps"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from "vue"
import { useStore } from "vuex"
import HtmlChatList from "./HtmlChatList.vue"
import FpsCounter from "../../Debug/FpsCounter.vue"
import GroupMessageInput from "./GroupMessageInput.vue"
import GroupHeader from "./GroupHeader.vue"
import useGroupChatApi from "../../../composables/useGroup/useGroupChatApi"

export default {
  name: "HtmlChatWindow",

  components: {
    HtmlChatList,
    FpsCounter,
    GroupMessageInput,
    GroupHeader,
  },

  props: {
    messages: {
      type: Array,
      default: () => [],
    },
    currentUser: {
      type: Object,
      required: true,
    },
    group: {
      type: Object,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    showDebugMenu: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["send-message", "load-more-messages", "cached-messages-loaded", "reply", "copy", "edit", "delete", "report", "open-gallery", "open-user-profile", "search", "leave-group"],

  setup(props, { emit }) {
    // References
    const chatList = ref(null)
    const chatInputArea = ref(null)

    // State
    const loadError = ref(null)
    const replyMessage = ref(null)
    const typingUsers = ref([])

    // Handle search from header
    const handleSearch = (query) => {
      console.log("HtmlChatWindow: Search query:", query)
      emit("search", query)
    }

    // Handle leave group
    const handleLeaveGroup = () => {
      console.log("HtmlChatWindow: Leave group")
      emit("leave-group", props.group.id)
    }

    // Debug state
    const visibleMessagesCount = ref(0)
    const scrollPercentage = ref(0)
    const scrollDirection = ref("none")
    const isAtBottom = ref(true)
    const visibleRangeStart = ref(0)
    const visibleRangeEnd = ref(0)
    const topSpacerHeight = ref(0)
    const bottomSpacerHeight = ref(0)
    const htmlChatFps = ref(0)

    // Get store
    const store = useStore()

    // Computed properties
    const hasMoreMessages = computed(() => {
      // Use the hasMoreMessages state from the store
      return store.state.groupChat.hasMoreMessages
    })

    // Check if current user is an admin of the group
    const isAdmin = computed(() => {
      if (!props.group || !props.currentUser) return false

      // Check if the user is the creator of the group
      if (props.group.creator_id === props.currentUser.id) return true

      // Check if the user is in the admins array
      if (props.group.admins && Array.isArray(props.group.admins)) {
        return props.group.admins.includes(props.currentUser.id)
      }

      return false
    })

    // Get initials from name
    const getInitials = (name) => {
      if (!name) return "?"
      return name
        .split(" ")
        .map((word) => word[0])
        .join("")
        .toUpperCase()
        .substring(0, 2)
    }

    // Send message (forwarded from GroupMessageInput)
    const sendMessage = (payload) => {
      // Extract content from payload
      const content = payload.message || '';
      const media = payload.media || null;

      // Emit send message event to parent with content and media
      emit("send-message", content, media);

      // Clear reply message
      replyMessage.value = null;

      // Jump to latest messages after sending
      nextTick(() => {
        jumpToLatestMessages();
      });
    }

    // Cancel reply
    const cancelReply = () => {
      replyMessage.value = null;
    }

    // Handle load more
    const handleLoadMore = (messageId) => {
      // Reset any previous error
      loadError.value = null

      try {
        // If a specific message ID is provided, use it
        if (messageId) {
          console.log(`HtmlChatWindow: Loading more messages before ID: ${messageId}`)
          emit("load-more-messages", props.group.id, messageId)
          return
        }

        // Otherwise use the first message
        if (props.messages.length > 0) {
          const oldestMessage = props.messages[0]
          console.log(`HtmlChatWindow: Loading more messages before ID: ${oldestMessage.id}`)
          emit("load-more-messages", props.group.id, oldestMessage.id)
        } else {
          console.warn("HtmlChatWindow: Cannot load more messages - no messages available")
          loadError.value = "No messages available to load more"
        }
      } catch (error) {
        console.error("HtmlChatWindow: Error in handleLoadMore:", error)
        loadError.value = error.message || "Failed to load more messages"
      }
    }

    // Handle scroll state change
    const handleScrollStateChange = (state) => {
      visibleMessagesCount.value = state.visibleCount
      scrollPercentage.value = state.scrollPercentage
      scrollDirection.value = state.scrollDirection || "unknown"
      isAtBottom.value = state.isAtBottom
      visibleRangeStart.value = state.visibleRangeStart || 0
      visibleRangeEnd.value = state.visibleRangeEnd || state.visibleCount - 1
      topSpacerHeight.value = state.topSpacerHeight || 0
      bottomSpacerHeight.value = state.bottomSpacerHeight || 0
    }

    // Handle cached messages loaded
    const handleCachedMessagesLoaded = (cachedMessages) => {
      if (cachedMessages && cachedMessages.length > 0) {
        console.log(`HtmlChatWindow: Received ${cachedMessages.length} cached messages`)
        emit("cached-messages-loaded", cachedMessages)
      }
    }

    // Handle debug metrics
    const handleDebugMetrics = (metrics) => {
      if (metrics) {
        htmlChatFps.value = metrics.fps || 0
        // Update other metrics if needed
      }
    }

    // Handle no more messages event
    const handleNoMoreMessages = () => {
      console.log("HtmlChatWindow: No more messages available")
      // Update store to reflect that there are no more messages
      store.dispatch("groupChat/setHasMoreMessages", false)
    }

    // Context menu handlers
    const handleReply = (message) => {
      console.log("HtmlChatWindow: Reply to message", message)
      // Set the reply message
      replyMessage.value = message;

      // Focus the input
      if (chatInputArea.value) {
        chatInputArea.value.$el.querySelector('textarea')?.focus();
      }
    }

    const handleCopy = (message) => {
      console.log("HtmlChatWindow: Copy message", message)
      // Copying is handled in the context menu component
    }

    const handleEdit = (message) => {
      console.log("HtmlChatWindow: Edit message", message)

      // Prompt the user for the new message content
      const newContent = prompt("Edit your message:", message.message || message.content)

      // If the user cancels or enters an empty message, do nothing
      if (!newContent || newContent.trim() === "") {
        return
      }

      // Call the API to update the message
      try {
        const groupChatApi = useGroupChatApi()
        groupChatApi.editMessage(message.id, newContent)
          .then(updatedMessage => {
            console.log("Message updated successfully:", updatedMessage)
            // The socket will handle updating the UI
          })
          .catch(error => {
            console.error("Error updating message:", error)
            alert("Failed to update message. Please try again.")
          })
      } catch (error) {
        console.error("Error in handleEdit:", error)
        alert("An error occurred while editing the message.")
      }
    }

    const handleDelete = (message) => {
      console.log("HtmlChatWindow: Delete message", message)

      // Show a confirmation dialog
      if (confirm(`Are you sure you want to delete this message?\n\n"${message.message || message.content}"`)) {
        // Call the API to delete the message
        try {
          const groupChatApi = useGroupChatApi()
          groupChatApi.deleteMessage(message.id)
            .then(response => {
              console.log("Message deleted successfully:", response)
              // The socket will handle updating the UI
            })
            .catch(error => {
              console.error("Error deleting message:", error)
              alert("Failed to delete message. Please try again.")
            })
        } catch (error) {
          console.error("Error in handleDelete:", error)
          alert("An error occurred while deleting the message.")
        }
      }
    }

    const handleReport = (message) => {
      console.log("HtmlChatWindow: Report message", message)
      // TODO: Implement report functionality
      // For now, just show a notification
      alert(`Report message from ${message.sender_name || 'Unknown user'}: ${message.message || message.content}`)
    }

    const handleOpenGallery = (data) => {
      console.log("HtmlChatWindow: Open gallery", data)
      // TODO: Implement gallery functionality
      // For now, just open the media URL if available
      if (data.media && data.media.mediaUrl) {
        window.open(data.media.mediaUrl, "_blank")
      }
    }

    const handleOpenUserProfile = (userId) => {
      console.log("HtmlChatWindow: Open user profile", userId)
      // TODO: Implement user profile functionality
      // For now, just show a notification
      alert(`Open user profile: ${userId}`)
    }


    // Scroll to bottom
    const scrollToBottom = () => {
      if (chatList.value) {
        chatList.value.scrollToBottom()
      }
    }

    // Jump to latest messages (for button click)
    const jumpToLatestMessages = () => {
      if (chatList.value) {
        chatList.value.jumpToLatestMessages()
      }
    }

    // Lifecycle hooks
    onMounted(() => {
      console.log("HtmlChatWindow: Component mounted")
      console.log("Debug state:", {
        htmlChatFps: htmlChatFps.value,
        visibleMessagesCount: visibleMessagesCount.value,
        scrollPercentage: scrollPercentage.value,
        scrollDirection: scrollDirection.value,
        isAtBottom: isAtBottom.value
      })

      // Focus input in the ChatInputArea component
      nextTick(() => {
        if (chatInputArea.value) {
          chatInputArea.value.focusInput()
        }
      })
    })

    onUnmounted(() => {
      // Cleanup if needed
    })

    // Watch for new messages
    watch(
      () => props.messages.length,
      (newLength, oldLength) => {
        // If new messages were added and we're at the bottom, jump to latest messages
        if (newLength > oldLength && isAtBottom.value) {
          nextTick(() => {
            jumpToLatestMessages()
          })
        }
      },
    )

    // Expose methods to parent component
    return {
      chatList,
      chatInputArea,
      hasMoreMessages,
      loadError,
      replyMessage,
      typingUsers,
      visibleMessagesCount,
      scrollPercentage,
      scrollDirection,
      isAtBottom,
      visibleRangeStart,
      visibleRangeEnd,
      topSpacerHeight,
      bottomSpacerHeight,
      htmlChatFps,
      isAdmin,
      getInitials,
      sendMessage,
      cancelReply,
      handleLoadMore,
      handleScrollStateChange,
      handleCachedMessagesLoaded,
      handleDebugMetrics,
      handleNoMoreMessages,
      handleReply,
      handleCopy,
      handleEdit,
      handleDelete,
      handleReport,
      handleOpenGallery,
      handleOpenUserProfile,
      handleSearch,
      handleLeaveGroup,
      scrollToBottom,
      jumpToLatestMessages,
    }
  },
}

</script>

<style scoped>
.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #12121215;
  color: white;
}

/* Header styles are now in GroupHeader.vue */

.chat-body {
  flex-grow: 1;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%; /* Ensure it takes full height */
}



.jump-to-bottom-button {
  position: absolute;
  bottom: 80px;
  right: 20px;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background-color: #262626;
  color: #f1f1f1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  z-index: 100;
  transition: all 0.2s ease;
  border: none;
  opacity: 0.9;
}

.jump-to-bottom-button:hover {
  background-color: #333333;
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.5);
}

.jump-to-bottom-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.jump-icon {
  color: #f1f1f1;
  width: 20px;
  height: 20px;
}

/*
  CSS for future badge implementation
.new-messages-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #25D366;
  color: white;
  font-size: 12px;
  font-weight: bold;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
*/


</style>

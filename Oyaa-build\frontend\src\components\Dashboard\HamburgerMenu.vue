<template>
  <div class="hamburger-menu" ref="menu">
    <!-- Hamburger Icon -->
    <div class="hamburger-icon" @click="toggleMenu">
      <span></span>
      <span></span>
      <span></span>
    </div>

    <!-- Menu Dropdown -->
    <div v-if="isOpen" class="menu-dropdown">
      <ul>
        <li @click="navigateTo('profile')">User Bio</li>
        <li @click="navigateTo('settings')">Settings</li>
        <li @click="handleLogout">Logout</li> <!-- Logout button -->
      </ul>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  data() {
    return {
      isOpen: false, // Controls menu visibility
    };
  },
  methods: {
    toggleMenu(event) {
      this.isOpen = !this.isOpen; // Toggle menu visibility
    },
    navigateTo(route) {
      this.$router.push(`/${route}`); // Navigate to the selected route
      this.isOpen = false; // Close the menu after navigation
    },
    async handleLogout() {
      try {
        await this.logout(); // Dispatch the logout action from Vuex
        this.$router.push('/signin'); // Redirect to the login page after logout
      } catch (error) {
        console.error('Logout failed:', error);
      }
    },
    ...mapActions('auth', ['logout']),
    handleClickOutside(event) {
      // Check if the click was outside the component
      if (this.isOpen && !this.$refs.menu.contains(event.target)) {
        this.isOpen = false;
      }
    },
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {  // Use "beforeUnmount" if you are using Vue 3
    document.removeEventListener('click', this.handleClickOutside);
  },
};
</script>

<style scoped>
.hamburger-menu {
  position: relative;
}

.hamburger-icon {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.hamburger-icon span {
  width: 100%;
  height: 3px;
  background-color: white;
}

.menu-dropdown {
  position: absolute;
  top: 30px;
  left: 0;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.menu-dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-dropdown li {
  padding: 10px 20px;
  cursor: pointer;
  color: #333;
}

.menu-dropdown li:hover {
  background-color: #f5f5f5;
}
</style>

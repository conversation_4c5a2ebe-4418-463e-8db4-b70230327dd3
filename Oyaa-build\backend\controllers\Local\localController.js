// backend/controllers/localController.js
const localService = require('../../services/Local/localService');

class LocalController {
  async add(req, res) {
    const { userId, local } = req.body;
    if (!userId || !local) {
      return res.status(400).json({ error: 'Missing userId or local data' });
    }
    try {
      const result = await localService.addOrUpdateLocal(userId, local);
      res.status(200).json({
        message: 'Local contact added/updated successfully',
        data: result,
      });
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: err.message });
    }
  }

  async get(req, res) {
    const { userId } = req.params;
    try {
      const locals = await localService.getLocals(userId);
      res.status(200).json({ locals });
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: err.message });
    }
  }
}

module.exports = new LocalController();

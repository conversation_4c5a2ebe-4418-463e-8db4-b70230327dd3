// run-migration.js
require('dotenv').config();
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const client = new Client({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  ssl: {
    rejectUnauthorized: false, // Allow self-signed certificates
  },
});

async function runMigration() {
  try {
    // Connect to the database
    await client.connect();
    console.log('✅ Database connected successfully!');

    // Create message_delivery_status table
    console.log('Creating message_delivery_status table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS message_delivery_status (
        id SERIAL PRIMARY KEY,
        message_id INTEGER NOT NULL,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        status VARCHAR(20) NOT NULL, -- 'sent', 'delivered'
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(message_id, user_id)
      );
    `);
    console.log('✅ message_delivery_status table created');

    // Create indexes for faster queries
    console.log('Creating indexes...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_message_delivery_status_message_id ON message_delivery_status(message_id);
      CREATE INDEX IF NOT EXISTS idx_message_delivery_status_user_id ON message_delivery_status(user_id);
    `);
    console.log('✅ Indexes created');

    // Add deleted column to group_chats table if it doesn't exist
    console.log('Adding deleted column to group_chats table...');
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                      WHERE table_name = 'group_chats' AND column_name = 'deleted') THEN
          ALTER TABLE group_chats ADD COLUMN deleted BOOLEAN DEFAULT FALSE;
        END IF;
      END
      $$;
    `);
    console.log('✅ Added deleted column to group_chats table');

    // Create blocked_users table if it doesn't exist
    console.log('Creating blocked_users table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS blocked_users (
        id SERIAL PRIMARY KEY,
        blocker_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        blocked_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(blocker_id, blocked_id)
      );
    `);
    console.log('✅ Created blocked_users table');

    // Add muted_until column to users table if it doesn't exist
    console.log('Adding muted_until column to users table...');
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                      WHERE table_name = 'users' AND column_name = 'muted_until') THEN
          ALTER TABLE users ADD COLUMN muted_until TIMESTAMP;
        END IF;
      END
      $$;
    `);
    console.log('✅ Added muted_until column to users table');

    console.log('✅ Migration completed successfully!');
  } catch (err) {
    console.error('❌ Migration error:', err);
  } finally {
    await client.end();
  }
}

runMigration();

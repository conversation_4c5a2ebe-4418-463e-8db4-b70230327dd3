// backend/routes/groupMediaRoutes.js
const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const groupMediaController = require('../../controllers/Group/groupMediaController');

// Configure multer
const upload = multer({
  dest: 'uploads/',
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB
  fileFilter: (req, file, cb) => {
    // Updated to include .opus
    const allowedExtensions = /\.(jpeg|jpg|png|gif|webp|mp4|mov|avi|mp3|m4a|wav|ogg|aac|webm|opus)$/i;
    // Updated to include audio/opus
    const allowedMimeTypes = /^(image\/(jpeg|png|gif|webp)|video\/(mp4|quicktime|x-msvideo)|audio\/(mpeg|mp4|x-wav|ogg|x-m4a|webm|opus))$/;
    
    const extname = allowedExtensions.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedMimeTypes.test(file.mimetype);
  
    if (!extname) {
      console.log(`Rejected - Extension: ${path.extname(file.originalname)}`);
    }
    if (!mimetype) {
      console.log(`Rejected - MIME type: ${file.mimetype}`);
    }
  
    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Unsupported file type'));
    }
  },
});

// Upload group media (POST: /api/group-media/upload)
router.post('/upload', upload.single('file'), groupMediaController.uploadGroupMedia);

// Other routes remain unchanged
router.get('/group-chat/:groupChatId', groupMediaController.getMediaByGroupChat);
router.get('/group/:groupId', groupMediaController.getAllMediaByGroup);
router.post('/messages', groupMediaController.getMediaByMessageIds);
router.get('/user/:userId', groupMediaController.getMediaByUser);
router.delete('/:id', groupMediaController.deleteGroupMedia);

module.exports = router;
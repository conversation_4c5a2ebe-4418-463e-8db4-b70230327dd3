<template>
  <div class="nearby-users-map">
    <div class="header">
      <h2>Nearby Users Map</h2>
      <button @click="goBack" class="back-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="m15 18-6-6 6-6"></path>
        </svg>
        Back to List
      </button>
    </div>

    <div v-if="loading && !map" class="loading-state">
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loader">
        <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
      </svg>
      <p>Loading map...</p>
    </div>

    <div id="map" class="map-container"></div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

export default {
  name: "NearbyUsersMap",
  data() {
    return {
      map: null,
      markers: [],
      users: [],
      loading: true,
      error: null,
      limit: 50, // adjust as needed
      offset: 0,
      radius: 1000, // in meters
      coordinates: {
        lat: null,
        lng: null
      }
    };
  },
  created() {
    // Get current location and then fetch users.
    this.getCurrentLocation()
      .catch(err => {
        console.warn("Geolocation failed:", err);
        this.error = "Unable to get your location. Please allow location access.";
      })
      .finally(() => {
        this.fetchNearbyUsers();
      });
  },
  mounted() {
    // Initialize the map once the component is mounted.
    this.initializeMap();
  },
  methods: {
    /**
     * Initialize Leaflet map.
     */
    initializeMap() {
      const defaultCenter = [51.505, -0.09];

      this.map = L.map('map', {
        center: this.coordinates.lat && this.coordinates.lng
          ? [this.coordinates.lat, this.coordinates.lng]
          : defaultCenter,
        zoom: 13, // Initial zoom
        minZoom: 3,  // Allow zooming far out
        maxZoom: 20, // Allow deeper zoom levels
        zoomControl: true, // Ensure the user can zoom in/out
      });

      // Use a dark theme map tile layer
      L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
        subdomains: 'abcd',
        maxZoom: 20
      }).addTo(this.map);

      // Add user location marker if coordinates are available
      if (this.coordinates.lat && this.coordinates.lng) {
        this.addUserLocationMarker();
      }
    },

    /**
     * Get the user's current location.
     */
    async getCurrentLocation() {
      return new Promise((resolve, reject) => {
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              this.coordinates.lat = position.coords.latitude;
              this.coordinates.lng = position.coords.longitude;
              console.log("Map geolocation success:", this.coordinates);
              if (this.map) {
                this.map.setView([this.coordinates.lat, this.coordinates.lng], 13);
                this.addUserLocationMarker(); // Add user location marker after coordinates are set
              }
              resolve();
            },
            (error) => {
              console.error("Map geolocation error:", error);
              reject(error);
            }
          );
        } else {
          reject(new Error("Geolocation not supported"));
        }
      });
    },

    /**
     * Add a marker for the user's current location.
     */
    addUserLocationMarker() {
      if (this.coordinates.lat && this.coordinates.lng) {
        // Create a custom icon for the current user
        const currentMarkerIcon = L.divIcon({
          className: 'custom-div-icon',
          html: `<div class="marker-pin current-user-pin">
                  <span>You</span>
                </div>`,
          iconSize: [40, 40],
          iconAnchor: [20, 40],
          popupAnchor: [0, -40]
        });

        const currentMarker = L.marker([this.coordinates.lat, this.coordinates.lng], { icon: currentMarkerIcon })
          .addTo(this.map)
          .bindPopup("<div class='custom-popup'>Your current location</div>");

        this.markers.push(currentMarker);
      }
    },

    /**
     * Fetch nearby users from the backend.
     */
    async fetchNearbyUsers() {
      this.loading = true;
      this.error = null;

      try {
        const params = {
          limit: this.limit,
          offset: this.offset,
          radius: this.radius,
          ...(this.coordinates.lat && this.coordinates.lng && {
            lat: this.coordinates.lat,
            lng: this.coordinates.lng
          })
        };

        const token = this.$store.getters['auth/token'];
        if (!token) {
          console.error("No token found, user might not be authenticated.");
          this.error = "Authentication required. Please log in.";
          return;
        }

        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/proximity/nearby`,
          {
            params,
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );

        const { results } = response.data;
        this.users = results;
        this.addMarkers();
      } catch (error) {
        console.error("Error fetching nearby users:", error);
        this.error =
          (error.response && error.response.data && error.response.data.error) ||
          error.message ||
          "An error occurred while fetching nearby users.";
      } finally {
        this.loading = false;
      }
    },

    /**
     * Add markers to the map for each user.
     */
    addMarkers() {
      // Remove existing markers except the user's location marker.
      if (this.markers.length) {
        this.markers.forEach(marker => marker.remove());
        this.markers = [];
      }

      const currentUserId = this.$store.getters['auth/userId'];

      this.users.forEach(user => {
        const isCurrentUser = currentUserId === user.id;

        // Skip adding a marker for the current user as we already have one
        if (isCurrentUser) return;

        // Create a custom icon for other users
        const markerIcon = L.divIcon({
          className: 'custom-div-icon',
          html: `<div class="marker-pin other-user-pin">
                  <span>${this.getInitials(user.username)}</span>
                </div>`,
          iconSize: [36, 36],
          iconAnchor: [18, 36],
          popupAnchor: [0, -36]
        });

        // Ensure that user coordinates are valid.
        if (user.latitude && user.longitude) {
          const marker = L.marker([user.latitude, user.longitude], { icon: markerIcon }).addTo(this.map);

          // Create a custom popup with the username and distance
          const distance = this.calculateDistance(
            this.coordinates.lat,
            this.coordinates.lng,
            user.latitude,
            user.longitude
          );

          const formattedDistance = distance >= 1000
            ? `${(distance / 1000).toFixed(2)} km`
            : `${distance.toFixed(0)} m`;

          const popupContent = `
            <div class="custom-popup">
              <strong>${user.username}</strong>
              <p>Distance: ${formattedDistance}</p>
              <button class="popup-chat-btn" onclick="window.startChatWithUser('${user.id}')">Chat</button>
            </div>`;

          // Bind the popup to the marker.
          marker.bindPopup(popupContent);

          this.markers.push(marker);
        }
      });

      // Add global function to handle chat button clicks from popups
      window.startChatWithUser = (userId) => {
        const user = this.users.find(u => u.id === userId);
        if (user) {
          this.startChat(user);
        }
      };
    },

    /**
     * Calculate distance between two coordinates in meters
     */
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371e3; // Earth's radius in meters
      const φ1 = lat1 * Math.PI/180;
      const φ2 = lat2 * Math.PI/180;
      const Δφ = (lat2-lat1) * Math.PI/180;
      const Δλ = (lon2-lon1) * Math.PI/180;

      const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                Math.cos(φ1) * Math.cos(φ2) *
                Math.sin(Δλ/2) * Math.sin(Δλ/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

      return R * c; // Distance in meters
    },

    /**
     * Get initials from username
     */
    getInitials(name) {
      if (!name) return '';
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    },

    /**
     * Start a chat with a user
     */
    async startChat(user) {
      try {
        await this.$store.dispatch('app/openTempChat', user);
        this.$router.push({ name: 'TempChat', params: { friendId: user.id } });
      } catch (error) {
        console.error('Error starting chat:', error);
      }
    },

    /**
     * Navigate back to the list view.
     */
    goBack() {
      this.$router.push({ name: 'NearYou' });
    }
  }
};
</script>

<style scoped>
.nearby-users-map {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  padding: 1.5rem;
  background-color: var(--bg-primary);
  min-height: 100vh;
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
  transform: translateY(-1px);
}

.back-button:active {
  transform: translateY(0);
}

.map-container {
  height: calc(100vh - 150px);
  width: 100%;
  margin-bottom: 1.5rem;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.loader {
  animation: spin 1.2s linear infinite;
  color: var(--accent-primary);
  margin-bottom: 1rem;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  padding: 1rem;
  background-color: rgba(248, 113, 113, 0.1);
  color: #f87171;
  border-radius: 8px;
  margin-top: 1rem;
  text-align: center;
}
</style>

<style>
/* Global styles for map elements */
.custom-div-icon {
  background: none;
  border: none;
}

.marker-pin {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 100%;
  height: 100%;
  font-weight: 500;
  font-size: 0.875rem;
}

.current-user-pin {
  background-color: #6366f1;
  color: white;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.3);
}

.other-user-pin {
  background-color: rgba(99, 102, 241, 0.2);
  color: #6366f1;
  border: 2px solid #6366f1;
}

.custom-popup {
  text-align: center;
  padding: 0.5rem;
}

.custom-popup strong {
  display: block;
  margin-bottom: 0.25rem;
  color: #6366f1;
}

.custom-popup p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: #a0a0b0;
}

.popup-chat-btn {
  margin-top: 0.5rem;
  padding: 0.375rem 0.75rem;
  background-color: #6366f1;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
}

.popup-chat-btn:hover {
  background-color: #4f46e5;
}

/* Override leaflet default styles for dark theme */
.leaflet-container {
  background-color: #13131a;
}

.leaflet-popup-content-wrapper,
.leaflet-popup-tip {
  background-color: #1c1c26;
  color: #f2f2f2;
  border: 1px solid rgba(40, 40, 60, 0.8);
}

.leaflet-bar a {
  background-color: #1c1c26;
  color: #f2f2f2;
  border: 1px solid rgba(40, 40, 60, 0.8);
}

.leaflet-bar a:hover {
  background-color: #13131a;
}

.leaflet-control-attribution {
  background-color: rgba(19, 19, 26, 0.8) !important;
  color: #a0a0b0 !important;
}

.leaflet-control-attribution a {
  color: #6366f1 !important;
}
</style>
<template>
    <div class="container">
      <!-- Hero Section -->
      <section class="hero">
        <div class="hero-content">
          <h1> Privacy Policy</h1>
          <p>At <strong>Oyaa</strong>, we prioritize your privacy while connecting you with your intermediate society and the world. This Privacy Policy outlines how we collect, use, and protect your information.</p>
        </div>
        <div class="hero-background"></div>
      </section>
  
      <!-- Main Content -->
      <main class="main-content">
        <!-- Privacy Policy Content -->
        <div class="privacy-content">
          <section id="introduction" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Shield class="icon purple" />
                  <h2 class="card-title">Introduction</h2>
                </div>
                <p class="card-description">Our commitment to your privacy</p>
              </div>
              <div class="card-content">
                <p>
                  Welcome to Oyaa's Privacy Policy. We are committed to protecting your personal information and your right to privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application and services.
                </p>
                <p>
                  By using Oyaa, you agree to the collection and use of information in accordance with this policy. We will not use or share your information with anyone except as described in this Privacy Policy.
                </p>
              </div>
            </div>
          </section>
  
          <section id="information-collection" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Database class="icon blue" />
                  <h2 class="card-title">Information Collection</h2>
                </div>
                <p class="card-description">What information we collect and how</p>
              </div>
              <div class="card-content">
                <div class="accordion">
                  <div class="accordion-item">
                    <div class="accordion-header" @click="toggleAccordion('personal-info')">
                      <span>Personal Information</span>
                      <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === 'personal-info' }]" />
                    </div>
                    <div class="accordion-content" v-show="openAccordion === 'personal-info'">
                      <p>We may collect personal information that you provide directly to us, such as:</p>
                      <ul>
                        <li>Name</li>
                        <li>Email address</li>
                        <li>Profile information</li>
                        <li>Content you share on the platform</li>
                      </ul>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <div class="accordion-header" @click="toggleAccordion('location-data')">
                      <span>Location Data</span>
                      <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === 'location-data' }]" />
                    </div>
                    <div class="accordion-content" v-show="openAccordion === 'location-data'">
                      <p>We use location data to enable proximity chats and geographical connections. This may include:</p>
                      <ul>
                        <li>GPS location (when permitted)</li>
                        <li>IP address-based location</li>
                        <li>Wi-Fi and Bluetooth signals for precise indoor location (when applicable)</li>
                      </ul>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <div class="accordion-header" @click="toggleAccordion('usage-data')">
                      <span>Usage Data</span>
                      <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === 'usage-data' }]" />
                    </div>
                    <div class="accordion-content" v-show="openAccordion === 'usage-data'">
                      <p>We collect information on how you use our app, including:</p>
                      <ul>
                        <li>Interactions with other users</li>
                        <li>Content you view or engage with</li>
                        <li>Features you use</li>
                        <li>Time spent on the app</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
  
          <section id="use-of-information" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <FileSearch class="icon green" />
                  <h2 class="card-title">Use of Information</h2>
                </div>
                <p class="card-description">How we use your information</p>
              </div>
              <div class="card-content">
                <p>We use the information we collect for various purposes, including:</p>
                <ul class="success-list">
                  <li>Providing and maintaining our services</li>
                  <li>Improving and personalizing user experience</li>
                  <li>Enabling proximity chats and geographical connections</li>
                  <li>Analyzing usage patterns to enhance our platform</li>
                  <li>Communicating with you about updates, offers, and support</li>
                  <li>Ensuring compliance with our terms and policies</li>
                </ul>
                <div class="alert alert-blue">
                  <h3>Location Data Usage</h3>
                  <p>
                    We use location data specifically to enable proximity chats and geographical connections. This allows you to discover and interact with users and communities in your vicinity, enhancing your local social experience.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="data-sharing" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Share2 class="icon yellow" />
                  <h2 class="card-title">Data Sharing and Disclosure</h2>
                </div>
                <p class="card-description">When and how we share your information</p>
              </div>
              <div class="card-content">
                <p>
                  Your privacy is important to us. We are committed to sharing your personal information only in specific circumstances:
                </p>
                <div class="two-column">
                  <div class="column">
                    <h3>With Your Consent</h3>
                    <p>
                      Your personal details are only shared with your consent (e.g., when befriending someone). We will always ask for your permission before sharing your information with other users or third parties.
                    </p>
                  </div>
                  <div class="column">
                    <h3>Public Activities</h3>
                    <p>
                      Public group activities may be visible to all users. This includes posts, comments, and interactions in public groups or forums within the app.
                    </p>
                  </div>
                </div>
                <div class="alert alert-green">
                  <h3>Private Communications</h3>
                  <p>
                    Private chats remain confidential. We do not access, share, or disclose the content of your private communications unless required by law or to protect our rights and the safety of our users.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="data-security" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Lock class="icon red" />
                  <h2 class="card-title">Data Security</h2>
                </div>
                <p class="card-description">How we protect your information</p>
              </div>
              <div class="card-content">
                <p>
                  We prioritize the security of your personal information and employ industry-standard measures to protect it from unauthorized access, disclosure, alteration, and destruction. These measures include:
                </p>
                <ul class="success-list">
                  <li>Encryption of data in transit and at rest</li>
                  <li>Regular security audits and vulnerability assessments</li>
                  <li>Strict access controls for our employees and contractors</li>
                  <li>Continuous monitoring for potential security threats</li>
                </ul>
                <p>
                  However, please note that no method of transmission over the Internet or electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your personal information, we cannot guarantee its absolute security.
                </p>
              </div>
            </div>
          </section>
  
          <section id="your-rights" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <UserCog class="icon purple" />
                  <h2 class="card-title">Your Rights and Choices</h2>
                </div>
                <p class="card-description">Control over your information</p>
              </div>
              <div class="card-content">
                <p>You have certain rights regarding your personal information:</p>
                <ul class="success-list">
                  <li>Access and update your personal information</li>
                  <li>Request deletion of your data</li>
                  <li>Opt-out of marketing communications</li>
                  <li>Control location sharing settings</li>
                  <li>Manage privacy settings for your profile and content</li>
                </ul>
                <div class="alert alert-blue">
                  <h3>Exercising Your Rights</h3>
                  <p>
                    To exercise these rights or if you have any questions about your personal information, please contact us using the information provided in the "Contact Us" section below.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="policy-changes" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <RefreshCw class="icon green" />
                  <h2 class="card-title">Changes to Privacy Policy</h2>
                </div>
                <p class="card-description">How we update our privacy practices</p>
              </div>
              <div class="card-content">
                <p>
                  We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.
                </p>
                <p>
                  You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page.
                </p>
                <div class="alert alert-yellow">
                  <h3>Stay Informed</h3>
                  <p>
                    By continuing to use our Service after any revisions become effective, you agree to be bound by the updated Privacy Policy. If you do not agree to the new terms, please stop using the Service.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="contact-us" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Mail class="icon blue" />
                  <h2 class="card-title">Contact Us</h2>
                </div>
                <p class="card-description">How to reach us with privacy concerns</p>
              </div>
              <div class="card-content">
                <p>
                  If you have any questions about this Privacy Policy or our privacy practices, please contact us at:
                </p>
                <div class="contact-info">
                  <p><strong>Email:</strong> <EMAIL></p>
                 
                </div>
                <p>
                  We will do our best to address your concerns and resolve any issues in a timely manner.
                </p>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  import { 
    Shield,
    Database,
    FileSearch,
    Share2,
    Lock,
    UserCog,
    RefreshCw,
    Mail,
    ChevronDown
  } from 'lucide-vue-next';
  
  const openAccordion = ref(null);
  
  const toggleAccordion = (id) => {
    if (openAccordion.value === id) {
      openAccordion.value = null;
    } else {
      openAccordion.value = id;
    }
  };
  </script>
  
  <style>
  /* Reset and Base Styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    
  }
  
  :root {
    /* Colors */
    --color-black: #000000;
    --color-white: #ffffff;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    --color-gray-950: #030712;
    
    --color-purple-300: #c4b5fd;
    --color-purple-400: #a78bfa;
    --color-purple-500: #8b5cf6;
    --color-purple-600: #7c3aed;
    --color-purple-700: #6d28d9;
    --color-purple-800: #5b21b6;
    --color-purple-900: #4c1d95;
    --color-purple-950: #2e1065;
    
    --color-blue-500: #3b82f6;
    --color-blue-600: #2563eb;
    --color-blue-700: #1d4ed8;
    --color-blue-800: #1e40af;
    --color-blue-900: #1e3a8a;
    --color-blue-950: #172554;
    
    --color-green-500: #22c55e;
    --color-green-600: #16a34a;
    --color-green-700: #15803d;
    --color-green-800: #166534;
    --color-green-900: #14532d;
    --color-green-950: #052e16;
    
    --color-yellow-500: #eab308;
    --color-yellow-600: #ca8a04;
    --color-yellow-700: #a16207;
    --color-yellow-800: #854d0e;
    --color-yellow-900: #713f12;
    --color-yellow-950: #422006;
    
    --color-red-500: #ef4444;
    --color-red-600: #dc2626;
    --color-red-700: #b91c1c;
    --color-red-800: #991b1b;
    --color-red-900: #7f1d1d;
    --color-red-950: #450a0a;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    
    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  body {
    font-family: 'Rubik', sans-serif;
    background-color: var(--color-black);
    color: var(--color-white);
    line-height: 1.5;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
  }
  
  a {
    color: var(--color-gray-400);
    text-decoration: none;
    transition: color 0.2s ease;
  }
  
  a:hover {
    color: var(--color-white);
  }
  
  ul, ol {
    padding-left: var(--space-5);
  }
  
  /* Container */
  .container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
  
  /* Hero Section */
  .hero {
    position: relative;
    padding: var(--space-20) var(--space-4);
    border-bottom: 1px solid var(--color-gray-800);
    background: linear-gradient(to bottom right, var(--color-black), var(--color-gray-900), var(--color-purple-950));
    overflow: hidden;
  }
  
  .hero-content {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
  }
  
  .hero h1 {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-4);
    font-weight: 700;
    letter-spacing: -0.025em;
  }
  
  .hero p {
    max-width: 36rem;
    margin: 0 auto;
    color: var(--color-gray-400);
    font-size: var(--text-lg);
  }
  
  .hero-background {
    position: absolute;
    inset: 0;
    z-index: 0;
    opacity: 0.2;
    background: radial-gradient(circle at center, var(--color-purple-500), transparent);
  }
  
  /* Main Content */
  .main-content {
    flex: 1;
    padding: var(--space-12) var(--space-4);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
  }
  
  .privacy-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-10);
  }
  
  /* Section */
  .section {
    scroll-margin-top: var(--space-20);
  }
  
  /* Card */
  .card {
    background-color: var(--color-gray-900);
    border: 1px solid var(--color-gray-800);
    border-radius: var(--radius-lg);
    overflow: hidden;
  }
  
  .card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-gray-800);
  }
  
  .card-title-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-1);
  }
  
  .card-title {
    font-size: var(--text-xl);
    font-weight: 600;
  }
  
  .card-description {
    color: var(--color-gray-400);
    font-size: var(--text-sm);
  }
  
  .card-content {
    padding: var(--space-6);
  }
  
  .card-content > p {
    margin-bottom: var(--space-4);
  }
  
  /* Accordion */
  .accordion {
    border: 1px solid var(--color-gray-800);
    border-radius: var(--radius-md);
    overflow: hidden;
  }
  
  .accordion-item {
    border-bottom: 1px solid var(--color-gray-800);
  }
  
  .accordion-item:last-child {
    border-bottom: none;
  }
  
  .accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    cursor: pointer;
    font-weight: 500;
    transition: color 0.2s ease;
  }
  
  .accordion-header:hover {
    color: var(--color-white);
  }
  
  .accordion-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.2s ease;
  }
  
  .accordion-icon.rotated {
    transform: rotate(180deg);
  }
  
  .accordion-content {
    padding: var(--space-4) var(--space-6);
    color: var(--color-gray-400);
  }
  
  .accordion-content p {
    margin-bottom: var(--space-2);
  }
  
  /* Alerts */
  .alert {
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }
  
  .alert h3 {
    margin-bottom: var(--space-2);
    font-weight: 500;
    font-size: var(--text-base);
  }
  
  .alert ul {
    list-style: none;
    padding-left: 0;
  }
  
  .alert li {
    display: flex;
    align-items: flex-start;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
  }
  
  .alert-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    margin-top: 4px;
  }
  
  .alert-blue {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid var(--color-blue-900);
  }
  
  .alert-blue h3 {
    color: var(--color-blue-400);
  }
  
  .alert-green {
    background-color: rgba(34, 197, 94, 0.1);
    border: 1px solid var(--color-green-900);
  }
  
  .alert-green h3 {
    color: var(--color-green-400);
  }
  
  .alert-yellow {
    background-color: rgba(234, 179, 8, 0.1);
    border: 1px solid var(--color-yellow-900);
  }
  
  .alert-yellow h3 {
    color: var(--color-yellow-400);
  }
  
  /* Two Column Layout */
  .two-column {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  @media (min-width: 768px) {
    .two-column {
      grid-template-columns: 1fr 1fr;
    }
  }
  
  .column h3 {
    margin-bottom: var(--space-3);
    font-size: var(--text-lg);
  }
  
  .column p {
    color: var(--color-gray-400);
    margin-bottom: var(--space-3);
  }
  
  /* Lists */
  .success-list {
    list-style: none;
    padding-left: 0;
  }
  
  .success-list li {
    position: relative;
    padding-left: var(--space-6);
    margin-bottom: var(--space-2);
  }
  
  .success-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--color-green-500);
    font-weight: bold;
  }
  
  /* Contact Info */
  .contact-info {
    background-color: var(--color-gray-800);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }
  
  .contact-info p {
    margin-bottom: var(--space-2);
  }
  
  /* Icons */
  .icon {
    width: 20px;
    height: 20px;
  }
  
  .icon.purple {
    color: var(--color-purple-500);
  }
  
  .icon.blue {
    color: var(--color-blue-500);
  }
  
  .icon.green {
    color: var(--color-green-500);
  }
  
  .icon.yellow {
    color: var(--color-yellow-500);
  }
  
  .icon.red {
    color: var(--color-red-500);
  }
  
  /* Responsive Adjustments */
  @media (min-width: 768px) {
    .hero h1 {
      font-size: var(--text-5xl);
    }
    
    .main-content {
      padding: var(--space-12) var(--space-8);
    }
  }
  
  @media (min-width: 1024px) {
    .hero h1 {
      font-size: var(--text-6xl);
    }
  }
  </style>
  
  
const { Pool } = require('pg');
const dotenv = require('dotenv');
const logger = require('../utils/logger.js');

dotenv.config();

// Check for required environment variables
if (!process.env.OMNI_DB_USER || !process.env.OMNI_DB_PASSWORD || !process.env.OMNI_DB_HOST) {
  logger.error('AlloyDB Omni environment variables are not set properly');
  process.exit(1);
}

// Create connection pool for AlloyDB Omni
const omniPool = new Pool({
  user: process.env.OMNI_DB_USER,
  password: process.env.OMNI_DB_PASSWORD,
  host: process.env.OMNI_DB_HOST,
  port: process.env.OMNI_DB_PORT || 13427,
  database: process.env.OMNI_DB_NAME || 'defaultdb',
  ssl: {
    rejectUnauthorized: false, // Allow self-signed certificates
  },
  // Connection timeout
  connectionTimeoutMillis: 10000,
  // Idle timeout
  idleTimeoutMillis: 30000,
  // Maximum number of clients the pool should contain
  max: 20,
});

// Test the connection
omniPool.on('connect', (client) => {
  logger.info('✅ AlloyDB Omni connected successfully');
  console.log('✅ AlloyDB Omni connected successfully');
});

omniPool.on('error', (err, client) => {
  logger.error(`AlloyDB Omni connection error: ${err.message}`);
  console.error(`AlloyDB Omni connection error: ${err.message}`);
});

// Explicitly test the connection on module load
(async () => {
  try {
    const client = await omniPool.connect();
    logger.info('✅ AlloyDB Omni client acquired from pool');
    console.log('✅ AlloyDB Omni client acquired from pool');
    client.release();
  } catch (err) {
    logger.error(`Failed to connect to AlloyDB Omni: ${err.message}`);
    console.error(`Failed to connect to AlloyDB Omni: ${err.message}`);
  }
})();

// Helper function for queries with error handling
const omniQuery = async (text, params = []) => {
  try {
    const start = Date.now();
    const result = await omniPool.query(text, params);
    const duration = Date.now() - start;

    logger.debug(`AlloyDB Omni query executed in ${duration}ms: ${text}`);

    return result;
  } catch (error) {
    logger.error(`AlloyDB Omni query error: ${error.message}`, { query: text, params });
    throw error;
  }
};

// Helper function for transactions
const omniTransaction = async (callback) => {
  const client = await omniPool.connect();

  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error(`AlloyDB Omni transaction error: ${error.message}`);
    throw error;
  } finally {
    client.release();
  }
};

module.exports = {
  omniPool,
  omniQuery,
  omniTransaction
};

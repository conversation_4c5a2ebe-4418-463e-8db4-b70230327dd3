<template>
  <div class="chat-page">
    <div class="chat-container">
      <Sidebar @select-user="handleUserSelect" />
      <ChatWindow v-if="selectedFriendId" :friend-id="selectedFriendId" />
      <div v-else class="no-chat-selected">
        <div class="welcome-message">
          <h2>Welcome to Chat!</h2>
          <p>Select a conversation to start messaging</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import Sidebar from "./Sidebar.vue";
import ChatWindow from "./ChatWindow.vue";

const selectedFriendId = ref(null);

const handleUserSelect = (userId) => {
  selectedFriendId.value = userId;
};
</script>

<style scoped>
.chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #313338;
 
  color: #f3f4f5;
}

.chat-container {
  display: flex;
  flex: 1;
}

.no-chat-selected {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #313338;
}

.welcome-message {
  text-align: center;
}

.welcome-message h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.welcome-message p {
  color: #949ba4;
  font-size: 16px;
}
</style>
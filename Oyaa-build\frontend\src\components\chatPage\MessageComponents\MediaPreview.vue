<template>
  <div class="media-preview-container">
    <img
      v-if="media.mediaType === 'image'"
      :src="media.mediaUrl"
      alt="Image attachment"
      class="media-preview"
    />
    <ChatVideoPlayer
      v-else-if="media.mediaType === 'video'"
      :media-url="media.mediaUrl"
      :media-type="media.mediaType"
      :video-id="messageId.toString()"
    />
  </div>
</template>

<script setup>
import ChatVideoPlayer from './ChatVideoPlayer.vue';

defineProps({
  media: { type: Object, required: true },
  messageId: { type: [String, Number], required: true },
});
</script>

<style scoped>
.media-preview-container {
  width: 100%;
  aspect-ratio: 16 / 9;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: #000;
}

.media-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  display: block;
}
</style>
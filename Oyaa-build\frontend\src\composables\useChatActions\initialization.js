// frontend/src/composables/useChatActions/initialization.js
export function useChatInitialization(state, api, socket, pagination, store, router, handlers) {
  const { handleNewMessage, handleTypingEvent, handleStopTypingEvent } = handlers;

  const initializeChat = async () => {
    try {
      console.log('Initializing chat with friendId:', state.friendId.value);
      
      // Ensure socket connection is active
      if (!socket.isConnected()) {
        console.log('Socket not connected, reconnecting...');
        socket.reconnect();
      }
      
      // Fetch friend details
      state.friend.value = await api.fetchFriend(state.friendId.value, localStorage.getItem('token'));
      if (!state.friend.value) {
        console.error('Friend not found');
        throw new Error('Friend not found');
      }
      
      console.log('Friend details loaded:', state.friend.value);

      // Fetch initial messages
      console.log('Fetching messages between', state.currentUser.value.id, 'and', state.friendId.value);
      const fetchedMessages = await api.fetchMessages(
        state.currentUser.value.id,
        state.friendId.value,
        pagination.currentPage.value,
        pagination.pageSize,
        localStorage.getItem('token')
      );
      
      console.log('Fetched messages:', fetchedMessages.length);

      // Map fetched messages, preserving the media object
      state.messages.value = fetchedMessages.map((message) => {
        const formattedMessage = { ...message };
        formattedMessage.rawSentAt = message.sent_at;
        formattedMessage.sent_at = new Date(message.sent_at).toLocaleString(); // Or use a custom formatTimestamp function
        if (message.reply_id) {
          formattedMessage.reply = {
            id: message.reply_id,
            message: message.reply_message,
            sender_name: message.reply_sender_name,
          };
        }
        // Do not overwrite media; it's already correctly structured by the backend
        return formattedMessage;
      });

      state.messagesLoaded.value = true;

      if (fetchedMessages.length < pagination.pageSize) {
        pagination.hasMoreMessages.value = false;
      }
      
      if (state.messages.value.length) {
        const lastMessage = state.messages.value[state.messages.value.length - 1];
        store.dispatch('chat/updateLastChat', {
          friendId: state.friendId.value,
          message: lastMessage.message || (lastMessage.media ? '[Media]' : ''),
          timestamp: lastMessage.sent_at,
        });
      }

      // Set up socket connections and listeners
      console.log('Setting up socket for chat between', state.currentUser.value.id, 'and', state.friendId.value);
      socket.joinChat(state.currentUser.value.id, state.friendId.value);
      
      // Set up listeners with proper handling
      socket.onNewMessage(handleNewMessage);
      socket.onTyping(handleTypingEvent);
      socket.onStopTyping(handleStopTypingEvent);
      
      console.log('Chat initialization complete');
    } catch (error) {
      console.error('Chat initialization error:', error);
      
      // Safely handle navigation
      try {
        router.push('/dashboard');
      } catch (routeError) {
        console.error('Navigation error:', routeError);
      }
    }
  };

  return { initializeChat };
}
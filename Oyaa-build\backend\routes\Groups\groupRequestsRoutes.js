const express = require('express');
const router = express.Router();
const groupRequestsController = require('../../controllers/Group/groupRequestsController');

// Create a new group request
router.post('/create', groupRequestsController.createRequest);

// Update the status of a group request (e.g., approve/reject)
router.post('/update-status', groupRequestsController.updateRequestStatus);

// Get all requests by a user
router.get('/user/:userId', groupRequestsController.getRequestsByUser);

// Get all requests for a specific group
router.get('/group/:groupId', groupRequestsController.getRequestsByGroup);

module.exports = router;

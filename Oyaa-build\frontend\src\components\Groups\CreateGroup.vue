<template>
  <div class="create-group-container">
    <!-- Added header with back button -->
    <div class="page-header">
      <button class="back-button" @click="$router.push('/dashboard')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>Create Group</h1>
      <div class="header-spacer"></div>
    </div>

    <div class="content-wrapper">
      <div class="card">
        <h2>Create a New Group</h2>
        <form @submit.prevent="createGroup" class="form">
          <div class="form-group">
            <input
              v-model="groupName"
              placeholder="Group Name"
              required
              class="form-input"
            />
          </div>
          <div class="form-group">
            <textarea
              v-model="description"
              placeholder="Group Description (optional)"
              class="form-textarea"
            ></textarea>
          </div>
          <div class="form-group">
            <div class="tag-input-wrapper">
              <div class="tags-group">
                <div v-for="tag in selectedTags" :key="tag" class="tag">
                  <span class="tag-text">{{ tag }}</span>
                  <button
                    type="button"
                    @click="removeTag(tag)"
                    class="tag-remove"
                    aria-label="Remove tag"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>
              </div>
              <input
                ref="tagInput"
                type="text"
                v-model="inputValue"
                @keydown="handleKeyDown"
                @focus="handleInputFocus"
                @blur="handleInputBlur"
                :placeholder="selectedTags.length === 0 ? 'Type group tags...' : ''"
                class="tag-input"
              />
            </div>
          </div>
          <div class="form-group">
            <button type="button" @click="openAvatarSelector" class="btn avatar-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              Select Avatar
            </button>
          </div>
          <div class="form-group checkbox-group">
            <input
              type="checkbox"
              id="public-group"
              v-model="isPublic"
              class="form-checkbox"
            />
            <label for="public-group">
              Make this group publicly available (attach location)
            </label>
          </div>
          <button type="submit" class="btn submit-btn">Create Group</button>
        </form>
        <p v-if="errorMessage" class="error">{{ errorMessage }}</p>
      </div>
    </div>

    <!-- Modal for Avatar Selection -->
    <div v-if="isAvatarSelectorOpen" class="modal">
      <div class="modal-content">
        <PickGroupAvatar
          :selected-avatar="selectedAvatar"
          @avatar-selected="handleAvatarSelected"
        />
        <button @click="closeAvatarSelector" class="close-btn">Close</button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import PickGroupAvatar from './PickGroupAvatar.vue';

export default {
  name: 'CreateGroup',
  components: {
    PickGroupAvatar,
  },
  data() {
    return {
      groupName: '',
      description: '',
      selectedTags: [],
      inputValue: '',
      isPublic: false,
      latitude: null,
      longitude: null,
      errorMessage: '',
      selectedAvatar: '/Avatar/groups-Avatar/Basketball.svg', // Default avatar
      isAvatarSelectorOpen: false,
    };
  },
  methods: {
    handleAvatarSelected(avatar) {
      this.selectedAvatar = avatar;
      this.closeAvatarSelector();
    },
    openAvatarSelector() {
      this.isAvatarSelectorOpen = true;
    },
    closeAvatarSelector() {
      this.isAvatarSelectorOpen = false;
    },
    addTag(tag) {
      const trimmedTag = tag.trim().toLowerCase();
      if (trimmedTag && !this.selectedTags.includes(trimmedTag)) {
        this.selectedTags.push(trimmedTag);
        this.inputValue = '';
        this.$nextTick(() => this.$refs.tagInput.focus());
      }
    },
    removeTag(tag) {
      const index = this.selectedTags.indexOf(tag);
      if (index > -1) {
        this.selectedTags.splice(index, 1);
      }
    },
    handleKeyDown(e) {
      if (e.key === 'Enter' && this.inputValue) {
        e.preventDefault();
        this.addTag(this.inputValue);
      } else if (e.key === 'Backspace' && !this.inputValue && this.selectedTags.length > 0) {
        this.removeTag(this.selectedTags[this.selectedTags.length - 1]);
      }
    },
    handleInputFocus() {},
    handleInputBlur() {},
    async createGroup() {
      try {
        const creatorId = this.$store.state.auth.user.id;
        let locationData = {};
        if (this.isPublic) {
          await this.getUserLocation();
          if (!this.latitude || !this.longitude) {
            this.errorMessage = 'Failed to get location. Please allow location access.';
            return;
          }
          locationData = { latitude: this.latitude, longitude: this.longitude };
        }
        const response = await axios.post(
          `${import.meta.env.VITE_API_BASE_URL}/api/groups/create`,
          {
            name: this.groupName,
            creatorId,
            description: this.description,
            avatar: this.selectedAvatar,
            tags: this.selectedTags,
            ...locationData,
          }
        );
        const groupId = response.data.group.id;
        this.$router.push({ path: `/groups/${groupId}` });
      } catch (error) {
        this.errorMessage = error.response?.data?.message || 'Error creating group';
      }
    },
    getUserLocation() {
      return new Promise((resolve, reject) => {
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              this.latitude = position.coords.latitude;
              this.longitude = position.coords.longitude;
              resolve();
            },
            (error) => {
              this.errorMessage = 'Location access denied. Cannot make group public.';
              reject(error);
            }
          );
        } else {
          this.errorMessage = 'Geolocation is not supported by this browser.';
          reject(new Error('Geolocation not supported'));
        }
      });
    },
  },
};
</script>

<style scoped>
.create-group-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0102,241,0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

/* Added header styles */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  background-color: var(--bg-primary);
  z-index: 20;
  width: 100%;
  height: 56px;
  box-sizing: border-box;
}

.page-header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
}

.back-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.back-button:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
}

.header-spacer {
  width: 36px; /* Same width as back button for balanced layout */
}

.content-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.card {
  background-color: var(--bg-tertiary);
  padding: 30px;
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 500px;
  border: 1px solid var(--border-color);
}

h2 {
  margin-bottom: 24px;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.form-group {
  margin-bottom: 20px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 15px;
  transition: all var(--transition-fast);
}

.form-input:focus,
.form-textarea:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
  outline: none;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-secondary);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.form-checkbox {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-secondary);
  cursor: pointer;
  position: relative;
  transition: all var(--transition-fast);
}

.form-checkbox:checked {
  background-color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.form-checkbox:checked::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 7px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form-checkbox:focus {
  box-shadow: 0 0 0 2px var(--accent-tertiary);
  outline: none;
}

.btn {
  padding: 12px 20px;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: var(--shadow-sm);
}

.btn:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: scale(0.98);
}

.avatar-btn {
  width: auto;
  padding: 10px 16px;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.avatar-btn:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
}

.submit-btn {
  width: 100%;
  margin-top: 10px;
}

.error {
  margin-top: 15px;
  color: #ef4444;
  text-align: center;
  font-size: 14px;
}

.tag-input-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 16px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  min-height: 48px;
  align-items: center;
  transition: all var(--transition-fast);
}

.tag-input-wrapper:focus-within {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}

.tags-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  padding: 4px 8px 4px 10px;
  border-radius: 8px;
  font-size: 14px;
  border: 1px solid rgba(99, 102, 241, 0.3);
  transition: all var(--transition-fast);
}

.tag:hover {
  background-color: rgba(99, 102, 241, 0.25);
}

.tag-text {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tag-remove {
  background: none;
  border: none;
  color: var(--accent-primary);
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.tag-remove:hover {
  background-color: rgba(99, 102, 241, 0.3);
}

.tag-input {
  flex: 1;
  min-width: 120px;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 15px;
  padding: 4px 6px;
  outline: none;
}

.tag-input::placeholder {
  color: var(--text-secondary);
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: var(--bg-tertiary);
  padding: 24px;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  position: relative;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
}

.close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all var(--transition-fast);
}

.close-btn:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .page-header {
    padding: 10px 12px;
    height: 50px;
  }
  
  .page-header h1 {
    font-size: 16px;
  }
  
  .back-button {
    padding: 6px;
  }
  
  .back-button svg {
    width: 18px;
    height: 18px;
  }
  
  .content-wrapper {
    padding: 16px 12px;
  }
  
  .card {
    padding: 20px;
  }
  
  h2 {
    font-size: 20px;
    margin-bottom: 20px;
  }
  
  .form-input, .form-textarea {
    padding: 10px 14px;
    font-size: 14px;
  }
  
  .btn {
    padding: 10px 16px;
    font-size: 14px;
  }
}

/* Small phones */
@media (max-width: 375px) {
  .page-header {
    padding: 8px 10px;
    height: 46px;
  }
  
  .page-header h1 {
    font-size: 15px;
  }
  
  .back-button svg {
    width: 16px;
    height: 16px;
  }
  
  .content-wrapper {
    padding: 12px 8px;
  }
  
  .card {
    padding: 16px;
  }
  
  h2 {
    font-size: 18px;
  }
  
  .form-group {
    margin-bottom: 16px;
  }
  
  .checkbox-group {
    font-size: 13px;
  }
}

/* Fix for iOS viewport height issues */
@supports (-webkit-touch-callout: none) {
  .create-group-container {
    min-height: -webkit-fill-available;
  }
}
</style>
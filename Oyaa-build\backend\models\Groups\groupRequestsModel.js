const db = require('../../utils/db');

class GroupRequestsModel {
  async createRequest(groupId, userId) {
    const query = `
      INSERT INTO group_requests (group_id, user_id)
      VALUES ($1, $2)
      RETURNING *;
    `;
    const result = await db.query(query, [groupId, userId]);
    return result.rows[0];
  }

  async updateRequestStatus(groupId, userId, status) {
    const query = `
      UPDATE group_requests
      SET status = $3
      WHERE group_id = $1 AND user_id = $2
      RETURNING *;
    `;
    const result = await db.query(query, [groupId, userId, status]);
    return result.rows[0];
  }

  async getRequestsByUser(userId) {
    const query = `
      SELECT gr.*, g.name AS group_name
      FROM group_requests gr
      JOIN groups g ON gr.group_id = g.id
      WHERE gr.user_id = $1;
    `;
    const result = await db.query(query, [userId]);
    return result.rows;
  }

  async getRequestsByGroup(groupId) {
    const query = `
      SELECT gr.*, u.username, u.avatar
      FROM group_requests gr
      JOIN users u ON gr.user_id = u.id
      WHERE gr.group_id = $1;
    `;
    const result = await db.query(query, [groupId]);
    return result.rows;
  }
}

module.exports = new GroupRequestsModel();

<template>
  <div class="chat-header">
    <div class="user-info">
      <img v-if="friend?.avatar" :src="friend.avatar" class="avatar" alt="" />
      <div v-else class="avatar-placeholder"></div>
      <div class="user-details">
        <h2>{{ friend?.username }}</h2>
        <!-- Show the typing indicator when the friend is typing -->
        <div class="typing-indicator-container">
      <TypingIndicator :is-typing="isTyping" :friend-name="friend?.username" />
    </div>
      </div>
    </div>
    <div class="header-actions">
      <SearchBar @update-search="handleUpdateSearch" />
      <button class="action-button add-friend">Add Friend</button>
      <button class="action-button block">Block</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import SearchBar from './SearchBar.vue';
import TypingIndicator from './TypingIndicator.vue';
import useChatSocket from '@/composables/useChatSocket';

const props = defineProps({
  friend: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['update-search']);
const handleUpdateSearch = (query) => {
  emit('update-search', query);
};

// Manage the typing indicator state here:
const isTyping = ref(false);
const { onTyping } = useChatSocket();
let typingTimeout = null;

onMounted(() => {
  onTyping((data) => {
    // Check if the typing event is coming from the friend.
    if (data.userId === props.friend.id) {
      isTyping.value = true;
      if (typingTimeout) clearTimeout(typingTimeout);
      // Hide the typing indicator after 2 seconds.
      typingTimeout = setTimeout(() => {
        isTyping.value = false;
      }, 2000);
    }
  });
});

onBeforeUnmount(() => {
  if (typingTimeout) clearTimeout(typingTimeout);
});
</script>

<style scoped>
.chat-header {
  padding: 12px 16px;
  background-color: #313338;
  border-bottom: 1px solid #1e1f22;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px; /* Slightly taller to accommodate the indicator */
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #4e5058;
}

.user-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

h2 {
  color: #f3f4f5;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-friend {
  background-color: #5865f2;
  color: white;
}

.add-friend:hover {
  background-color: #4752c4;
}

.block {
  background-color: #4e5058;
  color: white;
}

.block:hover {
  background-color: #6d6f78;
}
</style>

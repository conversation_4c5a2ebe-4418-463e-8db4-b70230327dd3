// frontend/src/chatSocket.js
import { io } from 'socket.io-client';

const chatSocket = io(`${import.meta.env.VITE_API_BASE_URL}/chat`, {
  autoConnect: true,
  withCredentials: true,
  transports: ['websocket'], // Force WebSocket instead of polling
  reconnection: true,        // Enable reconnection
  reconnectionAttempts: 5,   // Number of reconnection attempts
  reconnectionDelay: 1000,   // Initial delay before reconnection (1 second)
  timeout: 10000            // Connection timeout (10 seconds)
});

chatSocket.on('connect', () => {
  console.log('Connected to chat namespace, socket ID:', chatSocket.id);
});

chatSocket.on('connect_error', (error) => {
  console.error('Connection error:', error.message);
});

chatSocket.on('disconnect', (reason) => {
  console.log('Disconnected from chat namespace, reason:', reason);
});

chatSocket.on('error', (error) => {
  console.error('Socket error:', error);
});

// Debug incoming events
chatSocket.onAny((event, ...args) => {
  console.log(`[SOCKET DEBUG] Received event: ${event}`, args);
});

// Export the socket instance
export default chatSocket;
// backend/controllers/mediaController.js
const cloudinary = require('../../config/cloudinary');
const fs = require('fs');

class MediaController {
  async uploadMedia(req, res) {
    try {
      if (!req.file) return res.status(400).json({ error: 'No file uploaded' });
      if (!req.body.userId) return res.status(400).json({ error: 'userId is required' });

      const filePath = req.file.path;
      const result = await cloudinary.uploader.upload(filePath, { resource_type: 'auto' });
      console.log('Cloudinary result:', result);

      const audioFormats = ['mp3', 'm4a', 'wav', 'ogg', 'aac', 'webm'];
      let mediaType;
      if (result.resource_type === 'image') {
        mediaType = 'image';
      } else if (result.resource_type === 'video') {
        mediaType = audioFormats.includes(result.format) ? 'audio' : 'video';
      } else {
        mediaType = result.resource_type;
      }

      let mediaDetails = {
        mediaUrl: result.secure_url,
        publicId: result.public_id,
        mediaType: mediaType,
      };

      if (mediaType === 'audio' || mediaType === 'video') {
        mediaDetails.duration = result.duration;
      }

      if (mediaType === 'video') {
        const thumbnailUrl = cloudinary.url(`${result.public_id}.jpg`, {
          resource_type: 'video',
          transformation: [{ width: 300, height: 200, crop: 'fill' }],
        });
        mediaDetails.thumbnailUrl = thumbnailUrl;
      }

      fs.unlink(filePath, (err) => {
        if (err) console.error('Failed to delete temporary file:', err);
      });

      res.status(201).json({ message: 'Media uploaded successfully', media: mediaDetails });
    } catch (err) {
      console.error('Error in media upload endpoint:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async getMediaByChat(req, res) {
    try {
      const { chatId } = req.params;
      const mediaItems = await mediaService.getMediaByChatId(chatId);
      res.status(200).json(mediaItems);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async getMediaByUser(req, res) {
    try {
      const { userId } = req.params;
      const mediaItems = await mediaService.getMediaByUserId(userId);
      res.status(200).json(mediaItems);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async deleteMedia(req, res) {
    try {
      const { id } = req.params;
      const deletedMedia = await mediaService.deleteMedia(id);
      res.status(200).json({ message: 'Media deleted successfully', media: deletedMedia });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }
}

module.exports = new MediaController();
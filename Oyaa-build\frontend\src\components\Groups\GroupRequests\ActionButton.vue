<template>
  <button
    :class="['action-btn', type]"
    :disabled="disabled"
    @click="$emit('click')"
    type="button"
  >
    <span v-if="!loading" class="btn-text">{{ buttonText }}</span>
    <div v-else class="button-spinner"></div>
  </button>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: 'accept',
      validator: (value) => ['accept', 'reject'].includes(value),
    },
    loading: <PERSON>olean,
    disabled: Boolean,
  },
  computed: {
    buttonText() {
      return this.type === 'accept' ? 'Accept' : 'Reject';
    },
  },
};
</script>

<style scoped>
.action-btn {
  --success: #10b981;
  --success-dark: #059669;
  --danger: #ef4444;
  --danger-dark: #dc2626;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 5rem;
  height: 2.25rem;
  padding: 0 0.875rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  white-space: nowrap;
}

.action-btn.accept {
  background-color: var(--success);
  color: white;
}

.action-btn.accept:hover:not(:disabled),
.action-btn.accept:focus:not(:disabled) {
  background-color: var(--success-dark);
  transform: translateY(-1px);
}

.action-btn.reject {
  background-color: var(--danger);
  color: white;
}

.action-btn.reject:hover:not(:disabled),
.action-btn.reject:focus:not(:disabled) {
  background-color: var(--danger-dark);
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn:active:not(:disabled) {
  transform: translateY(1px);
}

.button-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Touch-friendly adjustments for mobile */
@media (max-width: 640px) {
  .action-btn {
    height: 2.5rem;
    min-width: 5.5rem;
    padding: 0 1rem;
  }
}

/* Ensure buttons are properly sized on small screens */
@media (max-width: 360px) {
  .action-btn {
    width: 100%;
  }
}
</style>
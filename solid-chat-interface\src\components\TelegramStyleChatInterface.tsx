import { Component, createSignal, onMount, onCleanup, createEffect, For } from 'solid-js';
import { Message, PerformanceMetrics } from '../types/chat';
import { ViewportSlicer } from '../utils/viewportSlicing';
import MessageItem from './MessageItem';
import MessageInput from './MessageInput';
import './ChatInterface.css';

interface TelegramStyleChatInterfaceProps {
  messages: Message[];
  onSendMessage: (text: string, media?: any) => void;
  onMetricsUpdate: (metrics: PerformanceMetrics) => void;
}

const TelegramStyleChatInterface: Component<TelegramStyleChatInterfaceProps> = (props) => {
  let scrollElement: HTMLDivElement | undefined;
  let messagesContainer: HTMLDivElement | undefined;
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = createSignal(true);
  const [isScrolledToBottom, setIsScrolledToBottom] = createSignal(true);
  
  // Performance tracking
  const [fps, setFps] = createSignal(0);
  const [scrollJank, setScrollJank] = createSignal(0);
  let frameCount = 0;
  let lastTime = performance.now();
  let lastScrollTime = 0;
  let animationId: number;
  let scrollTimeout: number;

  // Telegram's viewport slicer
  let viewportSlicer: ViewportSlicer;

  // FPS monitoring
  onMount(() => {
    const updateFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(updateFPS);
    };
    
    updateFPS();

    // Initialize viewport slicer (Telegram's approach)
    if (messagesContainer) {
      viewportSlicer = new ViewportSlicer({
        container: messagesContainer,
        selector: '.message-item',
        extraSize: window.innerHeight * 2, // 2x viewport height buffer
        extraMinLength: 10 // Keep 10 extra messages for smooth scrolling
      });
    }
    
    // Initial scroll to bottom
    setTimeout(() => scrollToBottom(), 100);
  });

  onCleanup(() => {
    if (animationId) cancelAnimationFrame(animationId);
    if (scrollTimeout) clearTimeout(scrollTimeout);
    viewportSlicer?.destroy();
  });

  // Update metrics - Telegram style
  createEffect(() => {
    const stats = viewportSlicer?.getStats() || {
      total: props.messages.length,
      visible: props.messages.length,
      removed: 0,
      efficiency: 0
    };

    props.onMetricsUpdate({
      fps: fps(),
      renderTime: 0,
      memoryUsage: getMemoryUsage(),
      scrollJank: scrollJank(),
      domNodes: document.querySelectorAll('*').length,
      virtualization: {
        totalMessages: stats.total,
        renderedMessages: stats.visible,
        virtualizationRatio: stats.efficiency,
        visibleRange: { 
          start: 0, 
          end: stats.visible - 1 
        }
      }
    });
  });

  const getMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024)
      };
    }
    return { used: 0, total: 0 };
  };

  const handleScroll = () => {
    if (!scrollElement) return;
    
    // Clear any pending timeout
    if (scrollTimeout) clearTimeout(scrollTimeout);

    // Immediate scroll position tracking
    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    setIsScrolledToBottom(scrollHeight - scrollTop - clientHeight < 10);
    
    // Minimal jank detection (like Telegram)
    const currentTime = performance.now();
    if (lastScrollTime > 0) {
      const timeDiff = currentTime - lastScrollTime;
      if (timeDiff > 50) { // Only count severe jank
        setScrollJank(prev => prev + 1);
      }
    }
    lastScrollTime = currentTime;

    // Telegram's approach: Schedule viewport slicing after scroll stops
    if (viewportSlicer) {
      viewportSlicer.cancelSlice();
      viewportSlicer.scheduleSlice(3000); // 3 seconds delay like Telegram
    }
  };

  const scrollToBottom = () => {
    if (scrollElement) {
      scrollElement.scrollTop = scrollElement.scrollHeight;
    }
  };

  const handleSendMessage = (text: string, media?: any) => {
    props.onSendMessage(text, media);
    
    // Auto-scroll to bottom when sending a message
    setTimeout(() => {
      if (isAutoScrollEnabled()) {
        scrollToBottom();
      }
    }, 50);
  };

  // Auto-scroll when new messages arrive (only if already at bottom)
  createEffect(() => {
    if (props.messages.length > 0 && isScrolledToBottom() && isAutoScrollEnabled()) {
      // Restore any sliced elements before scrolling
      viewportSlicer?.restoreElements();
      setTimeout(() => scrollToBottom(), 50);
    }
  });

  return (
    <div class="chat-interface">
      <div 
        ref={scrollElement}
        class="messages-container"
        onScroll={handleScroll}
        style={{
          height: '500px',
          overflow: 'auto',
        }}
      >
        {/* TELEGRAM'S APPROACH: Render ALL messages, slice viewport when idle */}
        <div ref={messagesContainer} style={{ width: '100%', padding: '8px 0' }}>
          <For each={props.messages}>
            {(message, index) => (
              <div class="message-item" data-index={index()}>
                <MessageItem message={message} />
              </div>
            )}
          </For>
        </div>
      </div>

      {!isScrolledToBottom() && (
        <button 
          class="scroll-to-bottom"
          onClick={() => {
            // Restore elements before scrolling
            viewportSlicer?.restoreElements();
            scrollToBottom();
          }}
        >
          ↓
        </button>
      )}

      <MessageInput onSendMessage={handleSendMessage} />
    </div>
  );
};

export default TelegramStyleChatInterface;

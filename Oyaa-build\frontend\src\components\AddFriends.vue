<template>
  <div class="add-friends-container">
    <!-- Header with back button -->
    <div class="page-header">
      <button class="back-button" @click="$router.push('/dashboard')">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>Add Friends</h1>
      <div class="header-spacer"></div>
    </div>

    <div class="add-friends">
      <div class="search-box">
        <div class="input-wrapper">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="search-icon"
          >
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <input
            v-model="searchUsername"
            type="text"
            placeholder="Enter Username"
            @keyup.enter="searchUser"
          />
          <!-- Suggestions Dropdown -->
          <ul v-if="suggestions.length" class="suggestions-list">
            <li
              v-for="s in suggestions"
              :key="s.id"
              @click="selectSuggestion(s.username)"
            >
              {{ s.username }}
            </li>
          </ul>
        </div>
        <button @click="searchUser" class="search-button">
          <span>Search</span>
        </button>
      </div>

      <!-- Error message -->
      <transition name="fade">
        <div v-if="error" class="error-message">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="error-icon"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          {{ error }}
        </div>
      </transition>

      <!-- Loading state -->
      <div v-if="loading" class="loading-state">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="loader"
        >
          <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
        </svg>
        <span>Searching...</span>
      </div>

      <!-- User result -->
      <div v-else-if="searchedUser" class="user-result">
  <div class="user-avatar">
    <img v-if="searchedUser.avatar" :src="searchedUser.avatar" alt="User Avatar" />
    <span v-else>{{ getInitials(searchedUser.username) }}</span>
  </div>
  <div class="user-info">
    <h3>{{ searchedUser.username }}</h3>
    <p v-if="searchedUser.description">{{ searchedUser.description }}</p>
  </div>
  <button
    @click="sendFriendRequest(searchedUser.id)"
    class="add-friend-button"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
      <circle cx="8.5" cy="7" r="4"></circle>
      <line x1="20" y1="8" x2="20" y2="14"></line>
      <line x1="23" y1="11" x2="17" y2="11"></line>
    </svg>
    Send Friend Request
  </button>
</div>
      <!-- Friend requests list -->
      <SentRequests ref="friendList" />
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { mapGetters } from 'vuex';
import SentRequests from './FriendRequests/SentRequests.vue';

export default {
  components: {
    SentRequests,
  },
  data() {
    return {
      searchUsername: '',
      suggestions: [], // Array to hold username suggestions
      searchedUser: null,
      loading: false,
      error: '',
      debounceTimer: null, // For debouncing API calls
    };
  },
  computed: {
    ...mapGetters('auth', ['user']),
  },
  watch: {
    // Watch searchUsername and fetch suggestions with debounce
    searchUsername() {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        this.fetchSuggestions();
      }, 300); // 300ms debounce
    },
  },
  methods: {
    getInitials(name) {
  if (!name) return '';
  const parts = name.split(' ');
  if (parts.length >= 2) {
    return (parts[0][0] + parts[1][0]).toUpperCase();
  } else {
    return name.substring(0, 2).toUpperCase();
  }
},
    // Fetch username suggestions from the backend
    async fetchSuggestions() {
      if (!this.searchUsername.trim()) {
        this.suggestions = [];
        return;
      }
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/users/suggest?q=${this.searchUsername}`,
          {
            headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
          }
        );
        this.suggestions = response.data.suggestions || [];
      } catch (error) {
        console.error('Error fetching suggestions:', error);
        this.suggestions = [];
      }
    },
    // Handle suggestion selection
    selectSuggestion(username) {
      this.searchUsername = username;
      this.suggestions = []; // Clear suggestions
      this.searchUser(); // Automatically trigger search
    },
    // Enhanced search with exact match and fuzzy fallback
    async searchUser() {
      if (!this.searchUsername.trim()) {
        this.error = 'Please enter a username.';
        this.clearErrorAfterDelay();
        return;
      }
      this.loading = true;
      this.error = '';
      this.searchedUser = null;
      try {
        // Attempt exact match search
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/users/search/${this.searchUsername}`,
          {
            headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
          }
        );
        if (response.data && response.data.user) {
          this.searchedUser = response.data.user;
        } else {
          // No exact match, try fuzzy search
          const fuzzyResponse = await axios.get(
            `${import.meta.env.VITE_API_BASE_URL}/api/users/search-fuzzy/${this.searchUsername}`,
            {
              headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
            }
          );
          if (fuzzyResponse.data.users && fuzzyResponse.data.users.length > 0) {
            const suggestions = fuzzyResponse.data.users
              .map(u => u.username)
              .join(', ');
            this.error = `No exact match found. Did you mean: ${suggestions}?`;
          } else {
            this.error = 'No user found with that username. Please try another.';
          }
          this.clearErrorAfterDelay();
        }
      } catch (err) {
        if (err.response && err.response.status === 404) {
          // Handle 404 with fuzzy search fallback
          const fuzzyResponse = await axios.get(
            `${import.meta.env.VITE_API_BASE_URL}/api/users/search-fuzzy/${this.searchUsername}`,
            {
              headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
            }
          ).catch(() => ({ data: { users: [] } })); // Fallback if fuzzy fails
          if (fuzzyResponse.data.users && fuzzyResponse.data.users.length > 0) {
            const suggestions = fuzzyResponse.data.users
              .map(u => u.username)
              .join(', ');
            this.error = `No exact match found. Did you mean: ${suggestions}?`;
          } else {
            this.error = 'No user found with that username. Please try another.';
          }
        } else {
          this.error = 'An error occurred while searching. Please try again.';
        }
        this.clearErrorAfterDelay();
      } finally {
        this.loading = false;
      }
    },
    async sendFriendRequest(receiverId) {
      try {
        // Store the username for better feedback messages
        const username = this.searchedUser ? this.searchedUser.username : 'this user';

        // Send the friend request via Vuex store
        await this.$store.dispatch('friendRequests/sendFriendRequest', receiverId);
        console.log('Friend request sent successfully');

        // No toast for successful actions - notifications will handle this

        // Clear the search form
        this.searchedUser = null;
        this.searchUsername = '';

        // Refresh the friend requests list
        this.$store.dispatch('friendRequests/fetchSentRequests');
        console.log('Refreshing sent requests list via store');
      } catch (error) {
        console.error('Error sending friend request:', error);

        // Get the username for personalized error messages
        const username = this.searchedUser ? this.searchedUser.username : 'this user';

        // Handle specific error messages
        let errorMessage = `Failed to send friend request to ${username}.`;
        let toastType = 'error';
        let duration = 5000; // 5 seconds by default

        const errorMsg = error.message || '';
        console.log('Error message:', errorMsg);

        // For certain error messages, show a more user-friendly message
        if (errorMsg.includes('already sent and pending') ||
            errorMsg.includes('Friend request already sent and pending')) {
          toastType = 'info';
          errorMessage = `You already have a pending friend request to ${username}. Please wait for a response.`;
          duration = 6000; // Show for longer
        } else if (errorMsg.includes('already friends') ||
                   errorMsg.includes('You are already friends with this user')) {
          toastType = 'info';
          errorMessage = `You are already friends with ${username}!`;
        } else if (errorMsg.includes('cannot send a friend request to yourself')) {
          toastType = 'info';
          errorMessage = 'You cannot send a friend request to yourself.';
        } else if (errorMsg.includes('Friend request already exists')) {
          toastType = 'info';
          errorMessage = `You've already sent a request to ${username}. Please wait for a response.`;
          duration = 6000; // Show for longer
        } else if (errorMsg) {
          // If we get any other error message, display it directly
          errorMessage = errorMsg;
        }

        // Show the appropriate toast notification
        this.$store.dispatch('app/showToast', {
          message: errorMessage,
          type: toastType,
          duration: duration,
        });
      }
    },
    clearErrorAfterDelay() {
      setTimeout(() => {
        this.error = '';
      }, 3000);
    },
  },
};
</script>

<style scoped>
.add-friends-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --error-color: #f87171;

  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

/* New header styles */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.header-spacer {
  width: 36px; /* Same width as back button for balance */
}

.add-friends {
  padding: 1.5rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-radius: 12px;
}

.search-box {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.input-wrapper {
  position: relative;
  flex: 1;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  font-size: 0.9375rem;
  transition: all var(--transition-fast);
}

input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}

input::placeholder {
  color: var(--text-secondary);
}

.search-button {
  padding: 0.75rem 1.25rem;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.search-button:active {
  transform: translateY(0);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-secondary);
  gap: 1rem;
}

.loader {
  animation: spin 1.2s linear infinite;
  color: var(--accent-primary);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  background-color: rgba(248, 113, 113, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(248, 113, 113, 0.2);
  border-radius: 8px;
}

.error-icon {
  color: var(--error-color);
  flex-shrink: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: all var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.user-result {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 10px;
  background-color: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  font-weight: 500;
  font-size: 1.125rem;
  flex-shrink: 0;
  overflow: hidden; /* Clips the image to the container's border-radius */
}
.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ensures the image fills the container without distortion */
}
.user-info {
  flex: 1;
  min-width: 0;
}

.user-info h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--text-primary);
}

.user-info p {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.add-friend-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.add-friend-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.add-friend-button:active {
  transform: translateY(0);
}
.suggestions-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestions-list li {
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.suggestions-list li:hover {
  background-color: var(--accent-tertiary);
}
@media (max-width: 640px) {
  .search-box {
    flex-direction: column;
  }

  .user-result {
    flex-direction: column;
    text-align: center;
  }

  .user-avatar {
    margin: 0 auto;
  }

  .add-friend-button {
    width: 100%;
    justify-content: center;
  }
}
</style>
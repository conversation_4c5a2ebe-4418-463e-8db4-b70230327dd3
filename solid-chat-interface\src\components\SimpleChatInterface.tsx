import { Component, createSignal, onMount, onCleanup, createEffect, For } from 'solid-js';
import { Message, PerformanceMetrics } from '../types/chat';
import MessageItem from './MessageItem';
import MessageInput from './MessageInput';
import './ChatInterface.css';

interface SimpleChatInterfaceProps {
  messages: Message[];
  onSendMessage: (text: string, media?: any) => void;
  onMetricsUpdate: (metrics: PerformanceMetrics) => void;
}

const SimpleChatInterface: Component<SimpleChatInterfaceProps> = (props) => {
  let scrollElement: HTMLDivElement | undefined;
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = createSignal(true);
  const [isScrolledToBottom, setIsScrolledToBottom] = createSignal(true);
  
  // Performance tracking
  const [fps, setFps] = createSignal(0);
  const [scrollJank, setScrollJank] = createSignal(0);
  let frameCount = 0;
  let lastTime = performance.now();
  let lastScrollTime = 0;
  let animationId: number;

  // FPS monitoring
  onMount(() => {
    const updateFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(updateFPS);
    };
    
    updateFPS();
    
    // Initial scroll to bottom
    setTimeout(() => scrollToBottom(), 100);
  });

  onCleanup(() => {
    if (animationId) cancelAnimationFrame(animationId);
  });

  // Update metrics - SIMPLE VERSION
  createEffect(() => {
    props.onMetricsUpdate({
      fps: fps(),
      renderTime: 0,
      memoryUsage: getMemoryUsage(),
      scrollJank: scrollJank(),
      domNodes: document.querySelectorAll('*').length,
      virtualization: {
        totalMessages: props.messages.length,
        renderedMessages: props.messages.length, // ALL RENDERED - NO VIRTUALIZATION
        virtualizationRatio: 0, // ZERO VIRTUALIZATION
        visibleRange: { 
          start: 0, 
          end: props.messages.length - 1 
        }
      }
    });
  });

  const getMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024)
      };
    }
    return { used: 0, total: 0 };
  };

  const handleScroll = () => {
    if (!scrollElement) return;
    
    // Simple scroll handling
    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    setIsScrolledToBottom(scrollHeight - scrollTop - clientHeight < 10);
    
    // Minimal jank detection
    const currentTime = performance.now();
    if (lastScrollTime > 0) {
      const timeDiff = currentTime - lastScrollTime;
      if (timeDiff > 50) { // Only count severe jank
        setScrollJank(prev => prev + 1);
      }
    }
    lastScrollTime = currentTime;
  };

  const scrollToBottom = () => {
    if (scrollElement) {
      scrollElement.scrollTop = scrollElement.scrollHeight;
    }
  };

  const handleSendMessage = (text: string, media?: any) => {
    props.onSendMessage(text, media);
    
    // Auto-scroll to bottom when sending a message
    setTimeout(() => {
      if (isAutoScrollEnabled()) {
        scrollToBottom();
      }
    }, 50);
  };

  // Auto-scroll when new messages arrive (only if already at bottom)
  createEffect(() => {
    if (props.messages.length > 0 && isScrolledToBottom() && isAutoScrollEnabled()) {
      setTimeout(() => scrollToBottom(), 50);
    }
  });

  return (
    <div class="chat-interface">
      <div 
        ref={scrollElement}
        class="messages-container"
        onScroll={handleScroll}
        style={{
          height: '500px',
          overflow: 'auto',
        }}
      >
        {/* SIMPLE RENDERING - NO VIRTUALIZATION BULLSHIT */}
        <div style={{ width: '100%', padding: '8px 0' }}>
          <For each={props.messages}>
            {(message, index) => (
              <div key={message.id} data-index={index()}>
                <MessageItem message={message} />
              </div>
            )}
          </For>
        </div>
      </div>

      {!isScrolledToBottom() && (
        <button 
          class="scroll-to-bottom"
          onClick={scrollToBottom}
        >
          ↓
        </button>
      )}

      <MessageInput onSendMessage={handleSendMessage} />
    </div>
  );
};

export default SimpleChatInterface;

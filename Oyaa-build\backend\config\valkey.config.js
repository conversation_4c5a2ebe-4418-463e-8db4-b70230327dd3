/**
 * VALKEY SERVICE COMPLETELY DISABLED
 *
 * This file provides mock implementations of all Valkey functions.
 * No actual connection to Valkey is attempted.
 */

const logger = require('../utils/logger.js');

logger.info('⚠️ Main Valkey service is COMPLETELY DISABLED - using mock implementations');

// Create mock clients that don't do anything
const valkeyClient = {
  on: () => {},
  get: async () => null,
  set: async () => true,
  del: async () => true
};

const pubSubClient = {
  on: () => {},
  publish: async () => true,
  subscribe: async () => true
};

// Create mock clients for Group Chat
const groupChatClient = {
  on: () => {},
  get: async () => null,
  set: async () => true,
  del: async () => true
};

const groupChatPubSubClient = {
  on: () => {},
  publish: async () => true,
  subscribe: async () => true
};

// Helper functions for main Valkey instance (mocked)
const valkeyGet = async () => {
  logger.debug('[MOCK] Getting key from Main Valkey');
  return null;
};

const valkeySet = async () => {
  logger.debug('[MOCK] Setting key in Main Valkey');
  return true;
};

const valkeyDel = async () => {
  logger.debug('[MOCK] Deleting key from Main Valkey');
  return true;
};

// Helper functions for Group Chat Valkey instance (mocked)
const groupChatValkeyGet = async () => {
  logger.debug('[MOCK] Getting key from Group Chat Valkey');
  return null;
};

const groupChatValkeySet = async () => {
  logger.debug('[MOCK] Setting key in Group Chat Valkey');
  return true;
};

const groupChatValkeyDel = async () => {
  logger.debug('[MOCK] Deleting key from Group Chat Valkey');
  return true;
};

module.exports = {
  // Main Valkey clients and methods
  valkeyClient,
  pubSubClient,
  valkeyGet,
  valkeySet,
  valkeyDel,

  // Group Chat Valkey clients and methods
  groupChatClient,
  groupChatPubSubClient,
  groupChatValkeyGet,
  groupChatValkeySet,
  groupChatValkeyDel
};
<template>
  <div class="emoji-picker" :class="{ 'mobile': isMobile }" @click.stop>
    <!-- Tabs -->
    <div class="tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.id"
        @click="activeTab = tab.id" 
        :class="{ 'active': activeTab === tab.id }"
        :aria-label="`${tab.name} tab`"
      >
        <span>{{ tab.name }}</span>
      </button>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <div class="search-wrapper">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="search-icon">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.3-4.3"/>
        </svg>
        <input
          type="text"
          v-model="searchQuery"
          :placeholder="`Search ${activeTab}s`"
          class="search-input"
          @input="handleSearch"
        />
        <button 
          v-if="searchQuery" 
          @click="clearSearch" 
          class="clear-search-button"
          aria-label="Clear search"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6 6 18"/>
            <path d="m6 6 12 12"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="content">
      <!-- Emoji Section -->
      <template v-if="activeTab === 'emoji'">
        <emoji-section 
          :search-query="searchQuery"
          @select="handleSelectEmoji"
        />
      </template>

      <!-- GIF Section -->
      <template v-else-if="activeTab === 'gif'">
        <gif-section 
          :search-query="searchQuery"
          @select="handleSelectGif"
        />
      </template>

      <!-- Sticker Section -->
      <template v-else-if="activeTab === 'sticker'">
        <sticker-section 
          :search-query="searchQuery"
          @select="handleSelectSticker"
        />
      </template>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import EmojiSection from './EmojiSection.vue';
import GifSection from './GifSection.vue';
import StickerSection from './StickerSection.vue';

export default {
  name: 'GroupEmojiPicker',
  components: { 
    EmojiSection,
    GifSection,
    StickerSection
  },
  props: {
    isOpen: { type: Boolean, default: false },
    triggerRef: { type: Object, default: null }, // Reference to the emoji button
  },
  emits: ['select', 'close'],
  setup(props, { emit }) {
    const activeTab = ref('emoji');
    const searchQuery = ref('');
    const isMobile = ref(window.innerWidth <= 768);
    
    // Track window size for responsive behavior
    const updateMobileState = () => {
      isMobile.value = window.innerWidth <= 768;
    };

    const tabs = [
      { id: 'emoji', name: 'Emoji' },
      { id: 'gif', name: 'GIFs' },
      { id: 'sticker', name: 'Stickers' }
    ];

    const handleSearch = () => {
      // No-op, handled by children
    };

    const clearSearch = () => {
      searchQuery.value = '';
    };

    const handleSelectEmoji = (emoji) => {
      emit('select', { type: 'emoji', data: emoji });
    };

    const handleSelectGif = (gif) => {
      emit('select', { type: 'gif', url: gif.images.original.url });
      emit('close');
    };

    const handleSelectSticker = (sticker) => {
      emit('select', { type: 'sticker', url: sticker.images.original.url });
      emit('close');
    };

    // Handle clicks outside to close the picker
    const handleClickOutside = (event) => {
      // Don't close if clicking on the emoji button or inside the picker
      const isClickInsidePicker = event.target.closest('.emoji-picker');
      const isClickOnTrigger = props.triggerRef && props.triggerRef.contains(event.target);
      
      if (!isClickInsidePicker && !isClickOnTrigger) {
        emit('close');
      }
    };

    watch(activeTab, () => {
      searchQuery.value = '';
    });

    watch(() => props.isOpen, (isOpen) => {
      if (isOpen) {
        searchQuery.value = '';
        activeTab.value = 'emoji'; // Reset on open
        
        // Add click outside listener when opened
        setTimeout(() => {
          document.addEventListener('click', handleClickOutside);
        }, 10);
      } else {
        // Remove listener when closed
        document.removeEventListener('click', handleClickOutside);
      }
    });

    onMounted(() => {
      window.addEventListener('resize', updateMobileState);
    });

    onBeforeUnmount(() => {
      window.removeEventListener('resize', updateMobileState);
      document.removeEventListener('click', handleClickOutside);
    });

    return {
      activeTab,
      searchQuery,
      isMobile,
      tabs,
      handleSearch,
      clearSearch,
      handleSelectEmoji,
      handleSelectGif,
      handleSelectSticker
    };
  }
};
</script>

<style scoped>
.emoji-picker {
  background: #222e35;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  width: 100%;
  max-width: 350px;
  height: 350px;
  display: flex;
  flex-direction: column;
  color: #e0e0e0;
  border: 1px solid #2a2b30;
  transition: all 0.2s ease;
  z-index: 10000;
}

/* Tabs styles */
.tabs {
  display: flex;
  border-bottom: 1px solid #394045;
  background: #222e35;
  padding: 0 8px;
}

.tabs button {
  flex: 1;
  padding: 12px 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #8a9aa4;
  font-size: 15px;
  font-weight: 500;
  transition: color 0.2s;
  position: relative;
}

.tabs button.active {
  color: #00a884;
}

.tabs button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: #00a884;
}

.tabs button:hover:not(.active) {
  color: #c0c0c0;
}

/* Search container */
.search-container {
  padding: 10px 12px;
  border-bottom: 1px solid #394045;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #8a9aa4;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  background: #1f2c33;
  border: none;
  border-radius: 8px;
  color: #e0e0e0;
  font-size: 14px;
  outline: none;
  transition: background-color 0.2s;
}

.search-input:focus {
  background: #2a3942;
}

.search-input::placeholder {
  color: #8a9aa4;
}

.clear-search-button {
  position: absolute;
  right: 10px;
  background: transparent;
  border: none;
  color: #8a9aa4;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: color 0.2s;
}

.clear-search-button:hover {
  color: #e0e0e0;
}

/* Content area */
.content {
  flex: 1;
  overflow-y: auto;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: #394045 #222e35;
}

.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-thumb {
  background: #394045;
  border-radius: 3px;
}

.content::-webkit-scrollbar-track {
  background: #222e35;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .emoji-picker.mobile {
    position: fixed;
    bottom: 70px;
    left: 0;
    right: 0;
    max-width: 100%;
    width: 100%;
    max-height: 50vh;
    height: auto;
    border-radius: 12px 12px 0 0;
    animation: slide-up 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    margin: 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
  }
  
  .tabs button {
    padding: 10px 4px;
    font-size: 14px;
  }
}

/* Small mobile optimizations */
@media (max-width: 480px) {
  .search-input {
    padding: 7px 10px 7px 32px;
    font-size: 13px;
  }
  
  .search-icon {
    left: 10px;
  }
}

/* Fix for iOS safe areas */
@supports (-webkit-touch-callout: none) {
  .emoji-picker.mobile {
    padding-bottom: env(safe-area-inset-bottom, 0);
  }
}

/* Animations */
@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
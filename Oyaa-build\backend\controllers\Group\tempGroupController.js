// backend/controllers/Group/tempGroupController.js
const TempGroupModel = require('../../models/Groups/tempGroupModel');
const TempGroupService = require('../../services/Groups/tempGroupService');

class TempGroupController {
  async createTempGroup(req, res) {
    try {
      const { groupName, category, description, expiresInHours } = req.body;
      const creatorId = req.user.userId;
      if (!groupName || !category) {
        return res.status(400).json({ message: 'Group name and category are required' });
      }
      const group = await TempGroupService.createTempGroup(creatorId, groupName, category, description, expiresInHours);
      res.status(201).json({ message: 'Temporary group created', linkToken: group.link_token, groupName: group.group_name });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async joinTempGroup(req, res) {
    try {
      const { linkToken, tempUsername } = req.body;
      const { sessionToken, groupId, groupName } = await TempGroupService.joinTempGroup(linkToken, tempUsername);
      res.cookie('sessionToken', sessionToken, { httpOnly: true, secure: true });
      res.status(200).json({ message: 'Joined temporary group', groupId, sessionToken, groupName });
    } catch (err) {
      res.status(400).json({ message: err.message });
    }
  }
  async getCreatedTempGroups(req, res) {
    try {
      const creatorId = parseInt(req.user.userId, 10);
      if (isNaN(creatorId)) {
        return res.status(400).json({ message: 'Invalid creatorId' });
      }
      const groups = await TempGroupModel.getTempGroupsByCreator(creatorId);
      res.status(200).json({ groups });
    } catch (err) {
      console.error('Error in getCreatedTempGroups:', err);
      res.status(500).json({ message: err.message });
    }
  }
}

module.exports = new TempGroupController();
import { Component, createSignal, onMount, onCleanup, createEffect } from 'solid-js';
import { createVirtualizer } from '@tanstack/solid-virtual';
import { Message, PerformanceMetrics } from '../types/chat';
import MessageItem from './MessageItem';
import MessageInput from './MessageInput';
import './ChatInterface.css';

interface ChatInterfaceProps {
  messages: Message[];
  onSendMessage: (text: string, media?: any) => void;
  onMetricsUpdate: (metrics: PerformanceMetrics) => void;
}

const ChatInterface: Component<ChatInterfaceProps> = (props) => {
  let scrollElement: HTMLDivElement | undefined;
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = createSignal(true);
  const [isScrolledToBottom, setIsScrolledToBottom] = createSignal(true);
  
  // Performance tracking
  const [fps, setFps] = createSignal(0);
  const [scrollJank, setScrollJank] = createSignal(0);
  let frameCount = 0;
  let lastTime = performance.now();
  let lastScrollTime = 0;
  let animationId: number;

  // Create virtualizer with TanStack Virtual
  const virtualizer = createVirtualizer({
    get count() { return props.messages.length; },
    getScrollElement: () => scrollElement,
    estimateSize: () => 80, // Estimated item height
    overscan: 10, // Render 10 extra items for smooth scrolling
  });

  // FPS monitoring
  onMount(() => {
    const updateFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(updateFPS);
    };
    
    updateFPS();
    
    // Initial scroll to bottom
    setTimeout(() => scrollToBottom(), 100);
  });

  onCleanup(() => {
    if (animationId) cancelAnimationFrame(animationId);
  });

  // Update metrics
  createEffect(() => {
    const virtualItems = virtualizer.getVirtualItems();
    const range = virtualizer.getVirtualItems();
    
    props.onMetricsUpdate({
      fps: fps(),
      renderTime: 0, // TanStack handles this internally
      memoryUsage: getMemoryUsage(),
      scrollJank: scrollJank(),
      domNodes: document.querySelectorAll('*').length,
      virtualization: {
        totalMessages: props.messages.length,
        renderedMessages: virtualItems.length,
        virtualizationRatio: props.messages.length > 0 ? 
          ((props.messages.length - virtualItems.length) / props.messages.length) * 100 : 0,
        visibleRange: { 
          start: range[0]?.index || 0, 
          end: range[range.length - 1]?.index || 0 
        }
      }
    });
  });

  const getMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024)
      };
    }
    return { used: 0, total: 0 };
  };

  const handleScroll = () => {
    if (!scrollElement) return;
    
    const currentTime = performance.now();
    if (lastScrollTime > 0) {
      const timeDiff = currentTime - lastScrollTime;
      if (timeDiff > 16.67) { // More than 60fps threshold
        setScrollJank(prev => prev + 1);
      }
    }
    lastScrollTime = currentTime;

    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    setIsScrolledToBottom(scrollHeight - scrollTop - clientHeight < 10);
  };

  const scrollToBottom = () => {
    if (scrollElement && props.messages.length > 0) {
      virtualizer.scrollToIndex(props.messages.length - 1, { align: 'end' });
    }
  };

  const handleSendMessage = (text: string, media?: any) => {
    props.onSendMessage(text, media);
    
    // Auto-scroll to bottom when sending a message
    setTimeout(() => {
      if (isAutoScrollEnabled()) {
        scrollToBottom();
      }
    }, 50);
  };

  // Auto-scroll when new messages arrive (only if already at bottom)
  createEffect(() => {
    if (props.messages.length > 0 && isScrolledToBottom() && isAutoScrollEnabled()) {
      setTimeout(() => scrollToBottom(), 50);
    }
  });

  return (
    <div class="chat-interface">
      <div 
        ref={scrollElement}
        class="messages-container"
        onScroll={handleScroll}
        style={{
          height: '500px',
          overflow: 'auto',
        }}
      >
        <div
          style={{
            height: `${virtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          {virtualizer.getVirtualItems().map((virtualItem) => (
            <div
              key={virtualItem.key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start}px)`,
              }}
              ref={(el) => virtualizer.measureElement(el)}
            >
              <MessageItem 
                message={props.messages[virtualItem.index]} 
              />
            </div>
          ))}
        </div>
      </div>

      {!isScrolledToBottom() && (
        <button 
          class="scroll-to-bottom"
          onClick={scrollToBottom}
        >
          ↓
        </button>
      )}

      <MessageInput onSendMessage={handleSendMessage} />
    </div>
  );
};

export default ChatInterface;

import { Component, createSignal, onMount, onCleanup, createEffect } from 'solid-js';
import { createVirtualizer } from '@tanstack/solid-virtual';
import { Message, PerformanceMetrics } from '../types/chat';
import MessageItem from './MessageItem';
import MessageInput from './MessageInput';
import './ChatInterface.css';

interface ChatInterfaceProps {
  messages: Message[];
  onSendMessage: (text: string, media?: any) => void;
  onMetricsUpdate: (metrics: PerformanceMetrics) => void;
}

const ChatInterface: Component<ChatInterfaceProps> = (props) => {
  let scrollElement: HTMLDivElement | undefined;
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = createSignal(true);
  const [isScrolledToBottom, setIsScrolledToBottom] = createSignal(true);

  // Performance tracking
  const [fps, setFps] = createSignal(0);
  const [scrollJank, setScrollJank] = createSignal(0);
  let frameCount = 0;
  let lastTime = performance.now();
  let lastScrollTime = 0;
  let animationId: number;

  // Create virtualizer with TanStack Virtual - optimized for smooth scrolling
  const virtualizer = createVirtualizer({
    get count() { return props.messages.length; },
    getScrollElement: () => scrollElement,
    estimateSize: () => 120, // More realistic item height
    overscan: 5, // Reduce overscan for better performance
    scrollMargin: scrollElement?.offsetTop ?? 0,
    gap: 4, // Add gap between items
  });

  // FPS monitoring
  onMount(() => {
    const updateFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(updateFPS);
    };

    updateFPS();

    // Initial scroll to bottom
    setTimeout(() => scrollToBottom(), 100);
  });

  onCleanup(() => {
    if (animationId) cancelAnimationFrame(animationId);
  });

  // Update metrics
  createEffect(() => {
    if (useVirtualization()) {
      const virtualItems = virtualizer.getVirtualItems();
      const range = virtualizer.getVirtualItems();

      props.onMetricsUpdate({
        fps: fps(),
        renderTime: 0, // TanStack handles this internally
        memoryUsage: getMemoryUsage(),
        scrollJank: scrollJank(),
        domNodes: document.querySelectorAll('*').length,
        virtualization: {
          totalMessages: props.messages.length,
          renderedMessages: virtualItems.length,
          virtualizationRatio: props.messages.length > 0 ?
            ((props.messages.length - virtualItems.length) / props.messages.length) * 100 : 0,
          visibleRange: {
            start: range[0]?.index || 0,
            end: range[range.length - 1]?.index || 0
          }
        }
      });
    } else {
      // Simple rendering mode
      props.onMetricsUpdate({
        fps: fps(),
        renderTime: 0,
        memoryUsage: getMemoryUsage(),
        scrollJank: scrollJank(),
        domNodes: document.querySelectorAll('*').length,
        virtualization: {
          totalMessages: props.messages.length,
          renderedMessages: props.messages.length, // All rendered in simple mode
          virtualizationRatio: 0, // No virtualization
          visibleRange: {
            start: 0,
            end: props.messages.length - 1
          }
        }
      });
    }
  });

  const getMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024)
      };
    }
    return { used: 0, total: 0 };
  };

  const handleScroll = () => {
    if (!scrollElement) return;

    // Simplified scroll handling to reduce jank
    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    setIsScrolledToBottom(scrollHeight - scrollTop - clientHeight < 10);

    // Less aggressive jank detection
    const currentTime = performance.now();
    if (lastScrollTime > 0) {
      const timeDiff = currentTime - lastScrollTime;
      if (timeDiff > 33) { // Only count as jank if > 30fps
        setScrollJank(prev => prev + 1);
      }
    }
    lastScrollTime = currentTime;
  };

  const scrollToBottom = () => {
    if (scrollElement && props.messages.length > 0) {
      if (useVirtualization()) {
        virtualizer.scrollToIndex(props.messages.length - 1, { align: 'end' });
      } else {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  };

  const handleSendMessage = (text: string, media?: any) => {
    props.onSendMessage(text, media);

    // Auto-scroll to bottom when sending a message
    setTimeout(() => {
      if (isAutoScrollEnabled()) {
        scrollToBottom();
      }
    }, 50);
  };

  // Auto-scroll when new messages arrive (only if already at bottom)
  createEffect(() => {
    if (props.messages.length > 0 && isScrolledToBottom() && isAutoScrollEnabled()) {
      setTimeout(() => scrollToBottom(), 50);
    }
  });

  // Use simple rendering for small datasets, virtualization for large ones
  const useVirtualization = () => props.messages.length > 1000;

  return (
    <div class="chat-interface">
      <div
        ref={scrollElement}
        class="messages-container"
        onScroll={handleScroll}
        style={{
          height: '500px',
          overflow: 'auto',
        }}
      >
        {useVirtualization() ? (
          // Virtualized rendering for large datasets
          <div
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              width: '100%',
              position: 'relative',
            }}
          >
            {virtualizer.getVirtualItems().map((virtualItem) => (
              <div
                key={virtualItem.key}
                data-index={virtualItem.index}
                style={{
                  position: 'absolute',
                  top: `${virtualItem.start}px`,
                  left: '0',
                  width: '100%',
                  'min-height': `${virtualItem.size}px`,
                }}
                ref={(el) => virtualizer.measureElement(el)}
              >
                <MessageItem
                  message={props.messages[virtualItem.index]}
                />
              </div>
            ))}
          </div>
        ) : (
          // Simple rendering for small datasets (smoother)
          <div style={{ width: '100%' }}>
            {props.messages.map((message, index) => (
              <div key={message.id} data-index={index}>
                <MessageItem message={message} />
              </div>
            ))}
          </div>
        )}
      </div>

      {!isScrolledToBottom() && (
        <button
          class="scroll-to-bottom"
          onClick={scrollToBottom}
        >
          ↓
        </button>
      )}

      <MessageInput onSendMessage={handleSendMessage} />
    </div>
  );
};

export default ChatInterface;

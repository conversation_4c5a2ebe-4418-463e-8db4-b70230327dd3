<template>
  <div class="progress-bar" v-bind="$attrs">
    <div
      class="progress-fill"
      :style="{ width: props.value + '%' }"
    ></div>
  </div>
</template>

<script setup>
const props = defineProps({
  value: Number
})
</script>

<style scoped>
.progress-bar {
  position: relative;
  height: 8px; /* equivalent to Tailwind h-2 */
  width: 100%;
  overflow: hidden;
  border-radius: 4px;
  background-color: #d0e2ff; /* a light blue (20% primary) */
}

.progress-fill {
  height: 100%;
  background-color: #007bff; /* primary color */
  transition: width 0.3s ease;
}
</style>
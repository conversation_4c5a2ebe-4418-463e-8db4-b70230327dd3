// backend/models/Media.js
const db = require('../../utils/db');

class Media {
  static async createMedia({ userId, chatId, mediaUrl, publicId, mediaType, thumbnailUrl, duration }) {
    const query = `
      INSERT INTO media (user_id, chat_id, media_url, public_id, media_type, thumbnail_url, duration, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
      RETURNING *;
    `;
    const values = [userId, chatId, mediaUrl, publicId, mediaType, thumbnailUrl, duration];
    const result = await db.query(query, values);
    return result.rows[0];
  }

  static async getMediaByChatId(chatId) {
    const query = `
      SELECT id, user_id, chat_id, media_url, public_id, media_type, thumbnail_url, duration, created_at 
      FROM media
      WHERE chat_id = $1
      ORDER BY created_at ASC;
    `;
    const result = await db.query(query, [chatId]);
    return result.rows;
  }

  static async getMediaByUserId(userId) {
    const query = `
      SELECT id, user_id, chat_id, media_url, public_id, media_type, thumbnail_url, duration, created_at 
      FROM media
      WHERE user_id = $1
      ORDER BY created_at DESC;
    `;
    const result = await db.query(query, [userId]);
    return result.rows;
  }

  static async deleteMedia(mediaId) {
    const query = `
      DELETE FROM media
      WHERE id = $1
      RETURNING *;
    `;
    const result = await db.query(query, [mediaId]);
    return result.rows[0];
  }
}

module.exports = Media;
<template>
  <div class="sent-requests-container">
    <div class="header">
      <h2>Friend Requests Sent</h2>
      <div class="live-indicator">
        <span class="live-dot"></span>
        <span class="live-text">Live Updates</span>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-state">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="loader"
      >
        <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
      </svg>
      <span>Loading requests...</span>
    </div>

    <!-- Error message -->
    <div v-else-if="error" class="error-message">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="error-icon"
      >
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
      {{ error }}
    </div>

    <!-- Empty state -->
    <div v-else-if="sentRequests.length === 0" class="empty-state">
      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
        <circle cx="8.5" cy="7" r="4"></circle>
        <line x1="20" y1="8" x2="20" y2="14"></line>
        <line x1="23" y1="11" x2="17" y2="11"></line>
      </svg>
      <p>No friend requests sent</p>
      <p class="hint">Search for users above to send friend requests</p>
      <div :class="['connection-status', connectionStatus]">
        <span class="status-dot"></span>
        <span v-if="connectionStatus === 'connecting'">Connecting...</span>
        <span v-else-if="connectionStatus === 'connected'">Live updates active</span>
        <span v-else>Connection error</span>
      </div>
    </div>

    <!-- Sent requests list -->
    <ul v-else class="sent-requests-list">
      <li v-for="request in sentRequests" :key="request.id" class="request-item">
        <div class="request-user">
          <div class="user-avatar">
            <img v-if="request.receiver_avatar" :src="request.receiver_avatar" alt="User Avatar" />
            <span v-else>{{ getInitials(request.receiver_username) }}</span>
          </div>
          <div class="user-info">
            <h3>{{ request.receiver_username }}</h3>
            <p v-if="request.receiver_description">{{ request.receiver_description }}</p>
          </div>
        </div>
        <div :class="['status', getStatusClass(request.status)]">
          {{ getStatusText(request.status) }}
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  name: 'SentRequests',

  data() {
    return {
      connectionStatus: 'connected' // Always show as connected since we're using SSE
    };
  },

  computed: {
    ...mapState('friendRequests', {
      allSentRequests: state => state.sentRequests,
      isLoading: state => state.loading.sent,
      error: state => state.error.sent
    }),

    // Show all sent requests, not just pending ones
    sentRequests() {
      return this.allSentRequests;
    }
  },

  created() {
    // Fetch friend requests
    this.fetchSentRequests();
  },

  methods: {
    ...mapActions('friendRequests', ['fetchSentRequests']),

    getInitials(name) {
      if (!name) return '';
      const parts = name.split(' ');
      if (parts.length >= 2) {
        return (parts[0][0] + parts[1][0]).toUpperCase();
      } else {
        return name.substring(0, 2).toUpperCase();
      }
    },

    getStatusClass(status) {
      if (!status) return 'pending';

      const statusLower = status.toLowerCase();
      if (statusLower === 'pending') return 'pending';
      if (statusLower === 'accepted') return 'accepted';
      if (statusLower === 'rejected') return 'rejected';
      return 'pending';
    },

    getStatusText(status) {
      if (!status) return 'Pending';

      const statusLower = status.toLowerCase();
      if (statusLower === 'pending') return 'Pending';
      if (statusLower === 'accepted') return 'Accepted';
      if (statusLower === 'rejected') return 'Rejected';
      return status;
    },


  }
};
</script>

<style scoped>
.sent-requests-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --error-color: #f87171;
  --success-color: #34d399;
  --warning-color: #fbbf24;

  margin-top: 2rem;
  padding: 1.5rem;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(52, 211, 153, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(52, 211, 153, 0.2);
  border-radius: 8px;
  font-size: 0.875rem;
}

.live-dot {
  width: 8px;
  height: 8px;
  background-color: var(--success-color);
  border-radius: 50%;
  display: inline-block;
  position: relative;
}

.live-dot::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background-color: rgba(52, 211, 153, 0.3);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% { transform: scale(0.8); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 0.4; }
  100% { transform: scale(0.8); opacity: 0.8; }
}

.live-text {
  font-weight: 500;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-secondary);
  gap: 1rem;
}

.loader {
  animation: spin 1.2s linear infinite;
  color: var(--accent-primary);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  background-color: rgba(248, 113, 113, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(248, 113, 113, 0.2);
  border-radius: 8px;
}

.error-icon {
  color: var(--error-color);
  flex-shrink: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem 1rem;
  color: var(--text-secondary);
}

.empty-state svg {
  color: var(--accent-tertiary);
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.empty-state .hint {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  background-color: var(--bg-tertiary);
}

.connection-status.connecting .status-dot {
  background-color: var(--warning-color);
}

.connection-status.connected .status-dot {
  background-color: var(--success-color);
}

.connection-status.error .status-dot {
  background-color: var(--error-color);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: inline-block;
}

.sent-requests-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.request-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.request-item:last-child {
  border-bottom: none;
}

.request-item:hover {
  background-color: var(--bg-tertiary);
}

.request-user {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
  background-color: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  font-weight: 500;
  font-size: 1rem;
  flex-shrink: 0;
  overflow: hidden; /* Clips the image */
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-info h3 {
  margin: 0;
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--text-primary);
}

.user-info p {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status.pending {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
}

.status.accepted {
  background-color: rgba(52, 211, 153, 0.15);
  color: var(--success-color);
}

.status.rejected {
  background-color: rgba(248, 113, 113, 0.15);
  color: var(--error-color);
}

@media (max-width: 640px) {
  .request-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .status {
    align-self: flex-start;
  }
}
</style>

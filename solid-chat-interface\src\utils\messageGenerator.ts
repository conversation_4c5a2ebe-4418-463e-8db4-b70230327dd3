import { Message } from '../types/chat';

const sampleTexts = [
  'Hello there! How are you doing today?',
  'This is a sample message to test the chat interface performance.',
  'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
  'Check out this amazing feature!',
  'The weather is really nice today, don\'t you think?',
  'I just finished working on this project.',
  'What do you think about the new design?',
  'This message contains some emojis 😀 🎉 🚀 💻 🔥 ⚡ 🎯 🚀',
  'Short msg',
  'Medium length message that spans multiple lines and contains various types of content to test rendering performance.',
  'Very long message: ' + 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. '.repeat(10),
  'Super long message: ' + 'A'.repeat(500),
  'Code snippet:\n```javascript\nfunction test() {\n  console.log("Hello World!");\n}\n```',
  'Multi-line\nmessage\nwith\nline\nbreaks',
  'Message with special characters: !@#$%^&*()_+-=[]{}|;:,.<>?',
  'Unicode test: 🌍🌎🌏 🇺🇸🇬🇧🇫🇷 αβγδε 中文测试 العربية',
  'Numbers and dates: 123456789 2024-01-01 12:34:56',
  'URL test: https://example.com/very/long/path/to/resource?param1=value1&param2=value2',
  'Markdown-like: **bold** *italic* `code` [link](url)',
  'Just a single character: A',
  'Empty-ish:   ',
  'Repeated pattern: ' + 'ABC123 '.repeat(20)
];

const sampleMedia = [
  { type: 'image', url: 'https://picsum.photos/400/300?random=', name: 'sample.jpg' },
  { type: 'audio', url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', name: 'audio.wav' },
  { type: 'video', url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', name: 'video.mp4' },
  { type: 'file', url: '#', name: 'document.pdf' }
];

export function generateSampleMessages(count: number): Message[] {
  const messages: Message[] = [];
  const startIndex = 0;

  for (let i = 0; i < count; i++) {
    const isMedia = Math.random() < 0.3; // 30% chance of media
    const messageType = isMedia ? 
      ['image', 'audio', 'video', 'file'][Math.floor(Math.random() * 4)] : 
      'text';

    let message: Message = {
      id: `msg-${startIndex + i}-${Date.now()}-${Math.random()}`,
      text: sampleTexts[Math.floor(Math.random() * sampleTexts.length)],
      timestamp: new Date(Date.now() - (count - i) * 60000), // Messages from count minutes ago to now
      sender: {
        id: Math.random() < 0.5 ? 'user1' : 'user2',
        name: Math.random() < 0.5 ? 'Alice' : 'Bob',
        avatar: `https://i.pravatar.cc/40?img=${Math.floor(Math.random() * 20) + 1}`
      },
      type: messageType as any
    };

    if (isMedia && messageType !== 'text') {
      const media = sampleMedia.find(m => m.type === messageType);
      if (media) {
        message.media = {
          url: messageType === 'image' ? media.url + (startIndex + i) : media.url,
          type: messageType as any,
          name: media.name,
          size: Math.floor(Math.random() * 1000000) + 100000 // Random size
        };
      }
    }

    messages.push(message);
  }

  return messages;
}

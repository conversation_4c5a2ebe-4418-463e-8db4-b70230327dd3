const logger = require('./logger');

const GeoUtils = {
  /**
   * Calculate distance between two coordinates using Haversine formula
   * @param {Object} coord1 - { latitude, longitude }
   * @param {Object} coord2 - { latitude, longitude }
   * @returns {number} Distance in meters
   */
  calculateHaversineDistance(coord1, coord2) {
    this.validateCoordinates(coord1);
    this.validateCoordinates(coord2);

    const R = 6371e3; // Earth radius in meters
    const φ1 = this.degreesToRadians(coord1.latitude);
    const φ2 = this.degreesToRadians(coord2.latitude);
    const Δφ = this.degreesToRadians(coord2.latitude - coord1.latitude);
    const Δλ = this.degreesToRadians(coord2.longitude - coord1.longitude);

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  },

  /**
   * Convert degrees to radians
   * @param {number} degrees 
   * @returns {number} radians
   */
  degreesToRadians(degrees) {
    return degrees * Math.PI / 180;
  },

  /**
   * Check if a point is within a radius of another point
   * @param {Object} center - { latitude, longitude }
   * @param {Object} point - { latitude, longitude }
   * @param {number} radius - in meters
   * @returns {boolean}
   */
  isPointWithinRadius(center, point, radius) {
    return this.calculateHaversineDistance(center, point) <= radius;
  },

  /**
   * Generate geohash string for approximate proximity matching
   * @param {number} lat 
   * @param {number} lng 
   * @param {number} precision - 1-12 (default 7)
   * @returns {string}
   */
  getGeohash(lat, lng, precision = 7) {
    // Note: Requires geohash library - install with: npm install geohash
    const geohash = require('geohash').encode(lat, lng, precision);
    return geohash;
  },

  /**
   * Validate coordinate object
   * @param {Object} coord - { latitude, longitude }
   * @throws {Error} on invalid coordinates
   */
  validateCoordinates(coord) {
    if (!coord || typeof coord !== 'object') {
      throw new Error('Invalid coordinate object');
    }
    
    const { latitude, longitude } = coord;
    if (typeof latitude !== 'number' || typeof longitude !== 'number') {
      throw new Error('Coordinates must be numbers');
    }

    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      throw new Error('Invalid coordinate values');
    }
  },

  /**
   * Convert coordinates to PostGIS geography point format
   * @param {number} lat 
   * @param {number} lng 
   * @returns {string} POINT(lng lat)
   */
  formatForPostGIS(lat, lng) {
    return `POINT(${lng} ${lat})`;
  }
};

module.exports = GeoUtils;
// models/TempChat.js
const { Client } = require('pg');
require('dotenv').config();

class TempChat {
  constructor() {
    this.client = new Client({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      database: process.env.DB_NAME,
      ssl: { rejectUnauthorized: false }, // Allow self-signed certificates
    });
  }

  async connect() {
    await this.client.connect();
    console.log('temp chat DB connected successfully');
  }


  async saveMessage(senderId, receiverId, message, sent_at) {
    // Convert receiverId to string to match the VARCHAR column type
    const receiverIdStr = String(receiverId);

    const query = `
      INSERT INTO temp_chats (sender_id, receiver_id, message, sent_at)
      VALUES ($1, $2, $3, $4)
      RETURNING *;
    `;
    const values = [senderId, receiverIdStr, message, sent_at];
    const result = await this.client.query(query, values);
    return result.rows[0];
  }

  async getMessages(userId, targetUserId) {
    // Convert targetUserId to string for the VARCHAR receiver_id column
    const targetUserIdStr = String(targetUserId);

    const query = `
      SELECT * FROM temp_chats
      WHERE (sender_id = $1 AND receiver_id = $2) OR (sender_id = $3 AND receiver_id = $4)
      ORDER BY sent_at;
    `;
    // sender_id is INTEGER, receiver_id is VARCHAR
    const result = await this.client.query(query, [userId, targetUserIdStr, targetUserId, String(userId)]);
    return result.rows;
  }

  async close() {
    await this.client.end();
  }
}

module.exports = TempChat;

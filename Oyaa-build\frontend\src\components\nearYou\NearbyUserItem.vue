<template>
  <div class="nearby-user-item">
    <template v-if="loading">
      <!-- Skeleton Loading State -->
      <div class="avatar skeleton-avatar"></div>
      <div class="user-details">
        <h3 class="skeleton-text skeleton-username"></h3>
        <p class="skeleton-text skeleton-description"></p>
      </div>
    </template>
    <template v-else>
      <!-- Real Content -->
      <div class="avatar" :style="avatarStyle">
        <span v-if="!user.avatarUrl">{{ getInitials(user.username) }}</span>
      </div>
      <div class="user-details">
        <h3>{{ user.username }}</h3>
        <p v-if="user.distance">{{ formatDistance(user.distance) }}</p>
      </div>
      <button @click="startChat" class="chat-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
        </svg>
        Chat
      </button>
    </template>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  name: "NearbyUserItem",
  props: {
    user: {
      type: Object,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    avatarStyle() {
      return this.user && this.user.avatarUrl
        ? { backgroundImage: `url(${this.user.avatarUrl})` }
        : {};
    },
  },
  methods: {
    ...mapActions('app', ['openTempChat']),
    async startChat() {
      // Set the active chat friend in Vuex
      await this.openTempChat(this.user);
      // Navigate to the temporary chat page with the friendId parameter
      this.$router.push({ name: 'TempChat', params: { friendId: this.user.id } });
    },
    formatDistance(distance) {
      return distance >= 1000
        ? (distance / 1000).toFixed(2) + " km"
        : distance.toFixed(0) + " m";
    },
    getInitials(name) {
      if (!name) return '';
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    },
    handleClick() {
      // Existing method (optional, can be removed if not needed)
      console.log("User details:", {
        id: this.user.id,
        username: this.user.username,
        ...this.user,
      });
    },
  },
};
</script>

<style scoped>
.nearby-user-item {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border-radius: 12px;
  margin: 0.75rem 0;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.nearby-user-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-tertiary);
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  background-color: var(--accent-tertiary);
  margin-right: 1rem;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  font-weight: 500;
  font-size: 1rem;
  flex-shrink: 0;
}

.user-details {
  flex-grow: 1;
  min-width: 0;
}

.user-details h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-details p {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.chat-button {
  margin-left: auto;
  padding: 0.5rem 1rem;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.chat-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.chat-button:active {
  transform: translateY(0);
}

/* Skeleton styles */
.skeleton-avatar,
.skeleton-text {
  background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-secondary) 50%, var(--bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-avatar {
  width: 48px;
  height: 48px;
  border-radius: 10px;
}

.skeleton-text {
  height: 1em;
  border-radius: 4px;
}

.skeleton-username {
  width: 60%;
  height: 1.2em;
  margin-bottom: 0.5em;
}

.skeleton-description {
  width: 40%;
  height: 1em;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
import axios from 'axios';

export const uploadTempChatMedia = async (file, userId, groupId, mediaType = 'image') => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('userId', userId); // Could be participantId from session token
  formData.append('groupId', groupId);
  formData.append('mediaType', mediaType);

  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
  const sessionToken = localStorage.getItem('tempGroupSessionToken');

  const response = await axios.post(`${API_BASE_URL}/api/temp-chat/media/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${sessionToken}`,
    },
  });

  return response.data; // Expected: { message, media: { mediaUrl, publicId, mediaType, duration } }
};
// frontend/src/composables/useGroupNotifications.js
import { io } from 'socket.io-client';

export default function useGroupNotifications() {
  const setupGroupWebSocket = (callback) => {
    const socket = io(`${import.meta.env.VITE_WS_URL}/groups`, {
      transports: ['websocket'], // Force WebSocket transport
      withCredentials: true,
    });

    socket.on('connect', () => {
      console.log('Group WebSocket connection established');
    });

    socket.on('GROUP_REQUEST', (data) => {
      console.log('New group notification received:', data);
      callback(data); // Pass the payload to the callback
    });

    socket.on('disconnect', () => {
      console.log('Group WebSocket disconnected');
    });

    socket.on('error', (error) => {
      console.error('Group WebSocket error:', error);
    });

    return () => {
      console.log('Cleaning up Group WebSocket connection...');
      socket.disconnect();
    };
  };

  return { setupGroupWebSocket };
}

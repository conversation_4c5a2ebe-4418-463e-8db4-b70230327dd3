const express = require('express');
const cors = require('cors');
const logger = require('./utils/logger');

// Try to initialize Firebase Admin SDK, but don't crash if it fails
try {
  const { initializeFirebaseAdmin } = require('./config/firebase.config');
  initializeFirebaseAdmin();
  logger.info('Firebase Admin SDK initialized');
} catch (error) {
  logger.error('Failed to initialize Firebase Admin SDK, continuing without Firebase', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    }
  });
  console.error('Failed to initialize Firebase Admin SDK, continuing without Firebase:', error.message);
}

const authRoutes = require('./routes/User/authRoutes');
const userRoutes = require('./routes/User/userRoutes');
const locationRoutes = require('./routes/User/locationRoutes');
const proximityRoutes = require('./routes/User/proximityRoutes');
const chatRoutes = require('./routes/User/chatRoutes');
const friendsRoutes = require('./routes/Friends/friendsRoutes');
const groupRoutes = require('./routes/Groups/groupRoutes');
const friendRequestRoutes = require('./routes/Friends/friendRequestRoutes');
const sseRoutes = require('./routes/sseRoutes');
const tempChatRoutes = require('./routes/Local/tempChatRoutes');
const localRoutes = require('./routes/Local/localRoutes');
const attachIO = require('./middleware/attachIO'); // Import the middleware
const mediaRoutes = require('./routes/User/mediaRoutes');  // Unified media endpoints
// Using enhanced group chat routes with edit/delete functionality
const enhancedGroupChatRoutes = require('./routes/Groups/enhancedGroupChatRoutes');
const worldChatRoutes = require('./routes/Groups/worldChatRoutes');
const groupMembersRoutes = require('./routes/Groups/groupMembersRoutes');
const groupRequestsRoutes = require('./routes/Groups/groupRequestsRoutes');
const groupMediaRoutes = require('./routes/Groups/groupMediaRoutes');
const tempGroupRoutes = require('./routes/Groups/tempGroupRoutes');
const checkUsernameRoutes = require('./routes/auth/checkUsername');
const app = express();

// Middleware
app.use(cors({
  origin: ['https://localhost:5175', 'https://***************:5175'],
  credentials: true,
}));

app.use(express.json());

// Attach io to req
app.use(attachIO);

// Import database utilities
const db = require('./utils/db');

// Default route for the root URL
app.get('/', (_req, res) => {
  res.json({ message: 'Server is running!' });
});

// Health check endpoint
app.get('/health', async (_req, res) => {
  try {
    // Check database health
    const dbHealth = await db.checkDatabaseHealth();

    // Get memory usage
    const memoryUsage = process.memoryUsage();

    // Get uptime
    const uptime = process.uptime();

    // Return health status
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: `${Math.floor(uptime / 60)} minutes, ${Math.floor(uptime % 60)} seconds`,
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
        external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`
      },
      database: dbHealth
    });
  } catch (error) {
    logger.error('Health check failed', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    });

    res.status(500).json({
      status: 'error',
      message: 'Health check failed',
      error: error.message
    });
  }
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/auth', checkUsernameRoutes); // Add the check username route
app.use('/api/users', userRoutes); // Use plural for resource routes
app.use('/api/location', locationRoutes);
app.use('/api/proximity', proximityRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/friends', friendsRoutes);
app.use('/api/groups', groupRoutes);
app.use('/api/friend-requests', friendRequestRoutes);
app.use('/api/temp-chat', tempChatRoutes);
app.use('/api/locals', localRoutes);
app.use('/api/sse', sseRoutes);
// Use enhanced group chat routes with all functionality
app.use('/api/group-chat', enhancedGroupChatRoutes);
app.use('/world-chat', worldChatRoutes);
app.use('/api/group-members', groupMembersRoutes);
app.use('/api/group-requests', groupRequestsRoutes);
app.use('/api/group-media', groupMediaRoutes);
app.use('/api/temp-groups', tempGroupRoutes);

// Mount all media endpoints under /api/media
app.use('/api/media', mediaRoutes);

// Global error handling middleware
app.use((req, res) => {
  // Handle 404 errors
  logger.warn(`404 - Not Found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    status: 'error',
    message: `Route not found: ${req.originalUrl}`
  });
});

// eslint-disable-next-line no-unused-vars
app.use((err, req, res, next) => {
  // Log the error with detailed information
  logger.error('Express error handler caught an error', {
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
      status: err.status || 500,
      code: err.code,
      path: req.path,
      method: req.method,
      body: req.body,
      params: req.params,
      query: req.query,
      headers: req.headers
    }
  });

  // Log to console for immediate visibility
  console.error('Express error:', err.message);
  console.error('Error Stack:', err.stack);

  // Send appropriate response to client
  const statusCode = err.status || 500;
  res.status(statusCode).json({
    status: 'error',
    message: err.message || 'Internal Server Error',
    error: process.env.NODE_ENV === 'development' ? {
      name: err.name,
      stack: err.stack,
      code: err.code
    } : undefined
  });
});

module.exports = app;

const express = require('express');
const router = express.Router();
const FriendRequestController = require('../../controllers/Friends/friendRequestController');
const authMiddleware = require('../../middleware/authMiddleware'); // Import auth middleware

// Remove WebSocket dependency - create controller without parameters
const friendRequestController = new FriendRequestController();

// Keep existing routes the same
router.post('/send', authMiddleware, (req, res) => friendRequestController.createRequest(req, res));
router.get('/:receiverId', (req, res) => friendRequestController.getRequests(req, res));
router.post('/accept/:requestId', (req, res) => friendRequestController.acceptRequest(req, res));
router.post('/reject/:requestId', (req, res) => friendRequestController.rejectRequest(req, res));
router.get('/sent/:senderId', (req, res) => friendRequestController.getSentRequests(req, res));
// New route to fetch authenticated user's friend requests
router.get('/', authMiddleware, (req, res) => friendRequestController.getMyRequests(req, res));

// New routes for the Vuex store
router.get('/received/:userId', authMiddleware, (req, res) => friendRequestController.getReceivedRequests(req, res));
// Enhanced route for sent requests that works with the Vuex store
router.get('/sent/:userId', authMiddleware, (req, res) => friendRequestController.getSentRequests(req, res));

module.exports = router;
<template>
  <div class="message-groups-container">
    <template v-for="(group, index) in messageGroups" :key="group.id">
      <!-- Group header with time divider if needed -->
      <div
        v-if="group.showHeader"
        class="message-group-header"
      >
        <div class="time-divider">
          <span class="time-divider-text">{{ formatDateHeader(group.timestamp) }}</span>
        </div>
      </div>

      <!-- Messages in this group -->
      <div class="message-group-wrapper">
        <group-message-group
          v-for="message in group.messages"
          :key="message.id"
          :message="message"
          :current-user="currentUser"
          :register-ref="registerRef"
          :show-avatar="message.id === group.firstMessageId && !isOwnMessage(message)"
          :show-name="message.id === group.firstMessageId && !isOwnMessage(message)"
          @show-context-menu="$emit('show-context-menu', $event, message)"
          @open-media="$emit('open-media', $event)"
        />
      </div>
    </template>

    <!-- Empty state when no messages -->
    <div v-if="!messageGroups.length" class="empty-messages">
      <div class="empty-messages-content">
        <div class="empty-icon">💬</div>
        <p>No messages yet</p>
        <p class="empty-subtitle">Start the conversation!</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import GroupMessageGroup from './GroupMessageGroup.vue';

const props = defineProps({
  messages: {
    type: Array,
    required: true
  },
  currentUser: {
    type: Object,
    required: true
  },
  registerRef: {
    type: Function,
    required: true
  }
});

const emit = defineEmits(['show-context-menu', 'open-media']);

const messageGroups = computed(() => {
  if (!props.messages || props.messages.length === 0) return [];

  const groups = [];
  let currentGroup = null;
  const TIME_GAP_THRESHOLD = 10 * 60 * 1000; // 10 minutes in milliseconds
  const SAME_SENDER_THRESHOLD = 5 * 60 * 1000; // 5 minutes in milliseconds

  // Sort messages by timestamp (oldest first)
  const sortedMessages = [...props.messages].sort((a, b) => {
    const timeA = new Date(a.sent_at || a.created_at).getTime();
    const timeB = new Date(b.sent_at || b.created_at).getTime();
    return timeA - timeB;
  });

  // Track the last date we've seen to detect day changes
  let lastDateString = '';

  sortedMessages.forEach((message, index) => {
    const messageTime = new Date(message.sent_at || message.created_at).getTime();
    const messageDate = new Date(messageTime);
    const messageDateString = messageDate.toDateString(); // Get date string for comparison

    const prevMessage = index > 0 ? sortedMessages[index - 1] : null;
    const prevMessageTime = prevMessage
      ? new Date(prevMessage.sent_at || prevMessage.created_at).getTime()
      : 0;

    const timeDiff = messageTime - prevMessageTime;
    const isSameSender = prevMessage && prevMessage.sender_id === message.sender_id;
    const isTimeGap = timeDiff > TIME_GAP_THRESHOLD;
    const isNewSenderGroup = !isSameSender || timeDiff > SAME_SENDER_THRESHOLD;

    // Check if this message is from a different day than the previous one
    const isDateChange = messageDateString !== lastDateString;

    // Update the last date string
    lastDateString = messageDateString;

    // Start a new group if:
    // 1. This is the first message
    // 2. The sender changed
    // 3. There's a significant time gap between messages from the same sender
    if (!currentGroup || isNewSenderGroup || isTimeGap) {
      // If there was a previous group, finalize it
      if (currentGroup) {
        groups.push(currentGroup);
      }

      // Create a new group
      currentGroup = {
        id: `group-${message.id}`,
        senderId: message.sender_id,
        messages: [message],
        timestamp: messageTime,
        firstMessageId: message.id,
        showHeader: isDateChange || index === 0, // Only show header if date changed or it's the first message
        hasTimeGap: isDateChange || isTimeGap // Show time gap for date changes or significant time gaps
      };
    } else {
      // Add to the current group
      currentGroup.messages.push(message);
    }
  });

  // Add the last group if it exists
  if (currentGroup) {
    groups.push(currentGroup);
  }

  return groups;
});

const isOwnMessage = (message) => {
  return message.sender_id === props.currentUser.id;
};

const formatDateHeader = (timestamp) => {
  if (!timestamp) return '';

  // Create date objects with time set to midnight for accurate day comparison
  const date = new Date(timestamp);
  date.setHours(0, 0, 0, 0);

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Calculate difference in days
  const diffTime = today.getTime() - date.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  // Format: Today, Yesterday, or date
  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    // For messages within the last week, show day name
    return date.toLocaleDateString(undefined, { weekday: 'long' });
  } else {
    // For older messages, show full date
    return date.toLocaleDateString(undefined, {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  }
};
</script>

<style scoped>
.message-groups-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
}

.message-group-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.message-group-header {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.time-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 20px 0 12px 0;
  position: relative;
}

.time-divider:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.time-divider-text {
  background-color: #121212;
  padding: 4px 14px;
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  position: relative;
  z-index: 2;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  width: 100%;
  padding: 20px;
}

.empty-messages-content {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.empty-subtitle {
  font-size: 14px;
  margin-top: 4px;
  opacity: 0.7;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .time-divider-text {
    font-size: 11px;
  }
}
</style>
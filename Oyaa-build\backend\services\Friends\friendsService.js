const FriendsModel = require('../../models/Friends/friendsModel');

class FriendsService {
  async addFriend(userId, friendId) {
    return await FriendsModel.addFriend(userId, friendId);
  }

  async removeFriend(userId, friendId) {
    return await FriendsModel.removeFriend(userId, friendId);
  }

  async getFriends(userId) {
    return await FriendsModel.getFriends(userId);
  }
  async areFriends(userId, friendId) {
    return await FriendsModel.areFriends(userId, friendId);
  }
}

module.exports = new FriendsService();
# Firebase Authentication Guide for Oyaa

This guide explains how to use Firebase Authentication with the Oyaa backend.

## Overview

Oyaa now uses Firebase Authentication for all authentication flows. This provides several benefits:

- Secure authentication with industry-standard practices
- Support for multiple authentication providers (email/password, Google, etc.)
- Password reset and email verification functionality
- Persistent sessions across devices

## Authentication Flow

1. **User Registration**:
   - User authenticates with Firebase (email/password or Google)
   - Frontend sends the Firebase ID token to the backend
   - Backend verifies the token and creates a user in the database
   - User is automatically added to the World Chat group

2. **User Login**:
   - User authenticates with Firebase
   - Frontend sends the Firebase ID token to the backend
   - Backend verifies the token and returns user information
   - If the user doesn't exist in the database, they are automatically registered

3. **Protected Routes**:
   - Frontend includes the Firebase ID token in the Authorization header
   - Backend verifies the token and checks if the user exists in the database
   - If the user exists, the request is processed
   - If the user doesn't exist but has a valid token, they are prompted to register

## API Endpoints

### Registration

```
POST /api/auth/register
```

**Headers**:
```
Authorization: Bearer <firebase_id_token>
```

**Request Body**:
```json
{
  "username": "desired_username",
  "avatar": "optional_avatar_url"
}
```

**Response**:
```json
{
  "message": "User registered",
  "user": {
    "id": 123,
    "username": "desired_username",
    "email": "<EMAIL>",
    "avatar": "avatar_url",
    "firebaseUid": "firebase_uid"
  }
}
```

### Login

```
POST /api/auth/login
```

**Headers**:
```
Authorization: Bearer <firebase_id_token>
```

**Response (Existing User)**:
```json
{
  "message": "Login successful",
  "user": {
    "id": 123,
    "username": "username",
    "email": "<EMAIL>",
    "avatar": "avatar_url",
    "firebaseUid": "firebase_uid"
  }
}
```

**Response (Auto-Registration)**:
```json
{
  "message": "User automatically registered and logged in",
  "user": {
    "id": 123,
    "username": "auto_generated_username",
    "email": "<EMAIL>",
    "avatar": "avatar_url",
    "firebaseUid": "firebase_uid"
  },
  "isNewUser": true
}
```

### Check Username Availability

```
GET /api/auth/check-username/:username
```

**Response**:
```json
{
  "available": true
}
```

## Frontend Implementation

### Firebase Configuration

```javascript
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: process.env.VUE_APP_FIREBASE_API_KEY,
  authDomain: process.env.VUE_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VUE_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VUE_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VUE_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VUE_APP_FIREBASE_APP_ID
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

export { auth };
```

### Email/Password Registration

```javascript
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { auth } from './firebaseConfig';

async function registerWithEmailPassword(email, password) {
  try {
    // Create user in Firebase
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    
    // Get ID token
    const idToken = await userCredential.user.getIdToken();
    
    // Register with backend
    const response = await axios.post('/api/auth/register', 
      { username, avatar },
      { headers: { Authorization: `Bearer ${idToken}` } }
    );
    
    return response.data;
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
}
```

### Google Sign-In

```javascript
import { GoogleAuthProvider, signInWithPopup } from 'firebase/auth';
import { auth } from './firebaseConfig';

async function signInWithGoogle() {
  try {
    const provider = new GoogleAuthProvider();
    
    // Sign in with Google
    const result = await signInWithPopup(auth, provider);
    
    // Get ID token
    const idToken = await result.user.getIdToken();
    
    // Login with backend
    const response = await axios.post('/api/auth/login',
      {},
      { headers: { Authorization: `Bearer ${idToken}` } }
    );
    
    return response.data;
  } catch (error) {
    console.error('Google sign-in error:', error);
    throw error;
  }
}
```

### Protected API Calls

```javascript
import { auth } from './firebaseConfig';

async function fetchProtectedData() {
  try {
    // Get current user
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Get fresh ID token
    const idToken = await user.getIdToken(true);
    
    // Make API call
    const response = await axios.get('/api/protected-endpoint',
      { headers: { Authorization: `Bearer ${idToken}` } }
    );
    
    return response.data;
  } catch (error) {
    console.error('API call error:', error);
    throw error;
  }
}
```

## Troubleshooting

### Invalid Token

If you receive a 401 Unauthorized error with the message "Invalid or expired token", try the following:

1. Make sure you're using a fresh ID token (call `getIdToken(true)`)
2. Check if the user is still authenticated with Firebase
3. Try signing out and signing in again

### User Not Found

If you receive a 403 Forbidden error with the message "Please complete registration first", it means your Firebase account is valid but you need to register with the backend:

1. Call the `/api/auth/register` endpoint with a username
2. The response will include `needsRegistration: true` if registration is required

### Firebase Configuration

If you're having issues with Firebase configuration:

1. Make sure all environment variables are set correctly
2. Check that your Firebase project has the appropriate authentication methods enabled
3. Verify that your domain is allowed in the Firebase console

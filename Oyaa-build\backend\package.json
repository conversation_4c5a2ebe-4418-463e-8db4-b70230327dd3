{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "diagnose": "node scripts/diagnose.js", "debug": "node --inspect server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"bad-words": "^3.0.4", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "celebrate": "^15.0.3", "cloudinary": "^2.5.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "geohash": "^0.0.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "pg": "^8.13.1", "redis": "^4.7.0", "socket.io": "^4.8.1", "vue-router": "^4.5.0", "winston": "^3.17.0", "ws": "^8.18.0"}, "devDependencies": {"nodemon": "^3.1.9"}}
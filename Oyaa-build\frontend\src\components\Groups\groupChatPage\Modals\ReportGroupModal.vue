<!-- frontend\src\components\Groups\groupChatPage\Modals\ReportGroupModal.vue -->
<template>
  <div v-if="props.show" class="modal-backdrop" @click="close"></div>
  <div v-if="props.show" class="modal-content" @click.stop>
    <!-- Header with close button -->
    <div class="modal-header">
      <h2>Report Group</h2>
      <button @click="close" class="close-button">✕</button>
    </div>

    <!-- Group info summary -->
    <div class="group-summary">
      <div class="avatar">
        <img :src="props.group.avatar" :alt="props.group.name" />
      </div>
      <h3>{{ props.group.name }}</h3>
    </div>

    <!-- Report form -->
    <form @submit.prevent="submitReport" class="report-form">
      <div class="form-description">
        <p>Please select a reason for reporting this group. Your report will be reviewed by our moderation team.</p>
      </div>

      <div class="separator"></div>

      <div class="report-options">
        <h4>Reason for reporting</h4>
        <div class="option-list">
          <label class="option-item" :class="{ 'selected': reportReason === 'spam' }">
            <input type="radio" name="reportReason" value="spam" v-model="reportReason" />
            <div class="option-content">
              <div class="option-title">Spam</div>
              <div class="option-description">The group is sending unwanted messages or promotions</div>
            </div>
          </label>

          <label class="option-item" :class="{ 'selected': reportReason === 'illegal' }">
            <input type="radio" name="reportReason" value="illegal" v-model="reportReason" />
            <div class="option-content">
              <div class="option-title">Illegal content</div>
              <div class="option-description">The group is sharing illegal content or activities</div>
            </div>
          </label>

          <label class="option-item" :class="{ 'selected': reportReason === 'violence' }">
            <input type="radio" name="reportReason" value="violence" v-model="reportReason" />
            <div class="option-content">
              <div class="option-title">Violence or harassment</div>
              <div class="option-description">The group promotes violence or harassment against individuals</div>
            </div>
          </label>

          <label class="option-item" :class="{ 'selected': reportReason === 'fake' }">
            <input type="radio" name="reportReason" value="fake" v-model="reportReason" />
            <div class="option-content">
              <div class="option-title">Fake or misleading</div>
              <div class="option-description">The group is impersonating or spreading misinformation</div>
            </div>
          </label>

          <label class="option-item" :class="{ 'selected': reportReason === 'other' }">
            <input type="radio" name="reportReason" value="other" v-model="reportReason" />
            <div class="option-content">
              <div class="option-title">Other</div>
              <div class="option-description">The issue doesn't fit into the categories above</div>
            </div>
          </label>
        </div>
      </div>

      <div v-if="reportReason" class="additional-details">
        <h4>Additional details (optional)</h4>
        <textarea
          ref="detailsTextarea"
          v-model="additionalDetails"
          placeholder="Please provide any additional information..."
          rows="4"
          maxlength="500"
        ></textarea>
        <div class="char-count" :class="{ 'warning': additionalDetails.length > 400 }">
          {{ additionalDetails.length }}/500
        </div>
      </div>

      <div class="action-buttons">
        <button type="button" @click="close" class="cancel-button">Cancel</button>
        <button type="submit" class="submit-button" :disabled="!reportReason || isSubmitting">
          {{ isSubmitting ? 'Submitting...' : 'Submit Report' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';


const props = defineProps({
  show: { type: Boolean, required: true },
  group: { type: Object, required: true },
});

const emit = defineEmits(['close', 'submit-report']);

const reportReason = ref('');
const additionalDetails = ref('');
const isSubmitting = ref(false);
const detailsTextarea = ref(null);

const close = () => {
  emit('close');
};

const submitReport = () => {
  if (!reportReason.value) return;
  isSubmitting.value = true;
  // Simulate API call
  setTimeout(() => {
    emit('submit-report', {
      reason: reportReason.value,
      details: additionalDetails.value,
    });
    reportReason.value = '';
    additionalDetails.value = '';
    isSubmitting.value = false;
    close();
  }, 1500);
};

// Initialize OverlayScrollbars on the textarea when the component mounts
onMounted(() => {
  if (detailsTextarea.value && window.OverlayScrollbars) {
    window.OverlayScrollbars(detailsTextarea.value, {
      scrollbars: {
        autoHide: 'move', // Match the behavior of other scrollbars in your app
      },
    });
  }
});
</script>

<style scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
}

/* Modal content */
.modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 425px;
  max-height: 90vh;
  overflow-y: scroll;
  z-index: 1001;
  border-radius: 8px;
  background: linear-gradient(337deg, #40444b30, #33333387);
background-color: rgba(0, 0, 0, 0.751);
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  color: var(--text-primary);
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Hide scrollbar for Chrome, Safari, and Opera */
.modal-content::-webkit-scrollbar {
  display: none;
}

/* Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: color 0.2s;
}

.close-button:hover {
  color: var(--text-primary);
}

/* Group summary */
.group-summary {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background-color: var(--bg-tertiary);
  border-radius: 8px;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.group-summary h3 {
  font-size: 16px;
  font-weight: 500;
}

/* Form description */
.form-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Separator */
.separator {
  height: 1px;
  background-color: var(--border-color);
  width: 100%;
  margin: 8px 0;
}

/* Report options */
.report-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.report-options h4 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.option-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: var(--bg-tertiary);
  cursor: pointer;
  transition: background-color 0.2s;
}

.option-item:hover {
  background-color: var(--bg-hover);
}

.option-item.selected {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.option-item input {
  margin-top: 4px;
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.option-title {
  font-size: 14px;
  font-weight: 500;
}

.option-description {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Additional details */
.additional-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.additional-details h4 {
  font-size: 16px;
  font-weight: 500;
}

.additional-details textarea {
  padding: 12px;
  border-radius: 8px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 14px;
  resize: none;
  overflow-y: auto; /* Enable scrolling when content overflows */
  scrollbar-width: thin; /* Firefox: thinner scrollbar */
  scrollbar-color: #202225 transparent; /* Firefox: thumb color and track */
}

/* Custom scrollbar for WebKit browsers (Chrome, Safari, etc.) */
.additional-details textarea::-webkit-scrollbar {
  width: 8px; /* Match ChatTextArea.vue */
}

.additional-details textarea::-webkit-scrollbar-track {
  background: transparent; /* Match ChatTextArea.vue */
}

.additional-details textarea::-webkit-scrollbar-thumb {
  background-color: #202225; /* Match ChatTextArea.vue */
  border-radius: 4px; /* Match ChatTextArea.vue */
}

.additional-details textarea:focus {
  outline: none;
  border-color: var(--text-secondary);
}

.char-count {
  align-self: flex-end;
  font-size: 12px;
  color: var(--text-secondary);
}

.char-count.warning {
  color: var(--accent-color);
}

/* Action buttons */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancel-button {
  padding: 10px 16px;
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: var(--bg-hover);
}

.submit-button {
  padding: 10px 16px;
  background-color: var(--accent-color);
  border: none;
  color: var(--text-primary);
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover:not(:disabled) {
  background-color: #d03737; /* Darker shade of accent color */
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .modal-content {
    width: 100%;
    height: 90vh;
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    transform: none;
    border-radius: 16px 16px 0 0;
  }
}
</style>
<template>
  <div class="avatar-selector" :class="{ 'dark': isDarkMode }">
    <!-- Header -->
    <div class="avatar-selector-header">
      <h2>Choose Your Avatar</h2>
      <button class="close-button" aria-label="Close" @click="$emit('close')">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M18 6 6 18"></path><path d="m6 6 12 12"></path>
        </svg>
      </button>
    </div>
    
    <div class="avatar-selector-content">
      <!-- Mobile View -->
      <div class="mobile-view">
        <!-- Mobile Search -->
        <div class="mobile-search-container">
          <div class="search-input-wrapper">
            <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path>
            </svg>
            <input
              type="text"
              placeholder="Search avatars..."
              v-model="searchQuery"
              class="search-input"
            />
            <button v-if="searchQuery" class="clear-search-btn" @click="clearSearch">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 6 6 18"></path><path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Mobile Category Tabs -->
        <div class="mobile-category-tabs">
          <div class="tabs-scroll-container">
            <button
              v-for="category in categories"
              :key="category.id"
              @click="selectCategory(category.id)"
              :class="['category-tab', { active: activeCategory === category.id }]"
              :aria-selected="activeCategory === category.id"
            >
              <component :is="categoryIcon(category.icon)" class="category-icon" />
              <span class="category-name">{{ category.name }}</span>
            </button>
          </div>
        </div>
        
        <!-- Mobile Results Count -->
        <div class="mobile-results-count" v-if="searchQuery">
          <span>{{ filteredAvatars.length }} results</span>
          <button class="clear-search" @click="clearSearch">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6 6 18"></path><path d="m6 6 12 12"></path>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Desktop Sidebar -->
      <div class="avatar-selector-sidebar">
        <!-- Search -->
        <div class="search-container">
          <div class="search-input-wrapper">
            <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path>
            </svg>
            <input
              type="text"
              placeholder="Search avatars..."
              v-model="searchQuery"
              class="search-input"
            />
            <button v-if="searchQuery" class="clear-search-btn" @click="clearSearch">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 6 6 18"></path><path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <nav class="category-nav">
          <button
            v-for="category in categories"
            :key="category.id"
            @click="selectCategory(category.id)"
            :class="['category-button', { active: activeCategory === category.id }]"
          >
            <component :is="categoryIcon(category.icon)" class="category-icon" />
            <span>{{ category.name }}</span>
            <span class="category-count">{{ category.avatars.length }}</span>
          </button>
        </nav>
      </div>
      
      <!-- Avatar Grid -->
      <div class="avatar-grid-container">
        <!-- Category title (desktop only) -->
        <div class="category-title-bar desktop-only">
          <div class="category-title">
            <component v-if="!searchQuery" :is="categoryIcon(currentCategory.icon)" class="category-title-icon" />
            <svg v-else class="category-title-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path>
            </svg>
            <h3>
              <template v-if="searchQuery">Search results: {{ filteredAvatars.length }}</template>
              <template v-else>{{ currentCategory.name }}</template>
            </h3>
          </div>
        </div>
        
        <!-- Scrollable avatar grid -->
        <div 
          ref="scrollContainer" 
          class="avatar-grid-scroll" 
          @scroll="checkScroll"
        >
          <div v-if="filteredAvatars.length > 0" class="avatar-grid">
            <button
              v-for="avatar in filteredAvatars"
              :key="avatar"
              @click="selectAvatar(avatar)"
              @keydown="handleKeyDown($event, avatar)"
              :class="['avatar-item', { selected: selectedAvatar === avatar }]"
              tabindex="0"
              :aria-label="`Select avatar ${avatar}`"
              :aria-selected="selectedAvatar === avatar"
            >
              <!-- Avatar image -->
              <div class="avatar-circle">
                <img :src="`/Avatar/${avatar}.svg`" :alt="avatar" />
              </div>
              
              <div v-if="selectedAvatar === avatar" class="selected-indicator">
                <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6 9 17l-5-5"></path>
                </svg>
              </div>
            </button>
          </div>
          <div v-else class="empty-results">
            <div class="empty-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path>
              </svg>
            </div>
            <h3>No avatars found</h3>
            <p>Try a different search term or browse categories.</p>
          </div>
        </div>
        
        <!-- Selection Footer -->
        <transition name="slide-up">
          <div v-if="selectedAvatar" class="selection-footer">
            <div class="selection-info">
              <div class="selected-avatar-preview">
                <img :src="`/Avatar/${selectedAvatar}.svg`" :alt="selectedAvatar" />
              </div>
              <div class="selected-avatar-text">
                <div class="selected-label">Selected</div>
                <div class="selected-name">{{ selectedAvatar }}</div>
              </div>
            </div>
            
            <button class="confirm-button" @click="confirmSelection">
              Confirm
            </button>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PickAvatar',
  props: {
    isDarkMode: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      selectedAvatar: null,
      activeCategory: 'recent',
      searchQuery: '',
      showScrollIndicator: false,
      isMobileDevice: false,
      viewportHeight: 0,
      categories: [
        {
          id: "recent",
          name: "Recent",
          icon: "clock",
          avatars: ["default", "cat", "dog", "02-man", "04-girl", "05-boy"]
        },
        {
          id: "people",
          name: "People",
          icon: "user",
          avatars: [
            "man", "man2", "man3", "man4", "man5", "man6", "man7",
          "man8", "man9", "man10", "man11", "man12", "man13",
          "woman", "woman2", "woman3", "woman4", "woman5", "woman6",
          "woman7", "woman8", "woman9", "woman10", "woman11",
            "02-man", "04-girl", "05-boy", "07-man", "10-girl", 
            "11-girl", "14-boy", "16-girl", "18-baby", "24-girl", 
            "29-girl", "31-boy", "38-girl", "39-girl", "42-boy", "48-girl"
          ]
        },
        {
          id: "profession",
          name: "Profession",
          icon: "briefcase",
          avatars: [
            "08-police", "13-rock", "17-nun", "20-clown", "27-ninja", 
            "28-soldier", "37-dj", "40-safari", "41-cowboy", "43-santa clause", 
            "45-doctor", "47-scientist", "49-bellboy"
          ]
        },
        {
          id: "cultural",
          name: "Cultural",
          icon: "globe",
          avatars: [
            "01-mexican", "06-chinese", "09-french", "12-arab", "15-chinese", 
            "19-vietnam", "21-indian", "22-portuguese", "25-armenian", 
            "26-japan", "30-viking", "32-arab", "33-indian", "35-armenian", "44-native", "46-russia"
          ]
        },
        {
          id: "special",
          name: "Special",
          icon: "crown",
          avatars: ["03-pirates", "50-king", "default", "defaultFriend", "defaultLocal"]
        },
        {
          id: "favorites",
          name: "Favorites",
          icon: "heart",
          avatars: ["cat", "dog", "23-oldster", "34-oldster"]
        }
      ]
    }
  },
  computed: {
    currentCategory() {
      return this.categories.find(c => c.id === this.activeCategory) || this.categories[0];
    },
    filteredAvatars() {
      if (this.searchQuery) {
        return this.categories.flatMap(c => c.avatars).filter(avatar => 
          avatar.toLowerCase().includes(this.searchQuery.toLowerCase())
        );
      }
      return this.currentCategory.avatars;
    }
  },
  mounted() {
    this.checkScroll();
    this.detectMobileDevice();
    this.updateViewportHeight();
    this.adjustHeightForMobile();
    
    window.addEventListener('resize', this.checkScroll);
    window.addEventListener('resize', this.detectMobileDevice);
    window.addEventListener('resize', this.updateViewportHeight);
    window.addEventListener('resize', this.adjustHeightForMobile);
    window.addEventListener('orientationchange', this.updateViewportHeight);
    window.addEventListener('orientationchange', this.adjustHeightForMobile);
    
    // Scroll active tab into view on mobile
    this.$nextTick(() => {
      this.scrollActiveCategoryIntoView();
    });
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.checkScroll);
    window.removeEventListener('resize', this.detectMobileDevice);
    window.removeEventListener('resize', this.updateViewportHeight);
    window.removeEventListener('resize', this.adjustHeightForMobile);
    window.removeEventListener('orientationchange', this.updateViewportHeight);
    window.removeEventListener('orientationchange', this.adjustHeightForMobile);
  },
  methods: {
    clearSearch() {
      this.searchQuery = '';
    },
    selectCategory(categoryId) {
      this.activeCategory = categoryId;
      this.searchQuery = '';
      this.$nextTick(() => {
        this.checkScroll();
        this.scrollActiveCategoryIntoView();
      });
    },
    scrollActiveCategoryIntoView() {
      // Find the active tab element
      const activeTab = document.querySelector('.category-tab.active');
      const tabsContainer = document.querySelector('.tabs-scroll-container');
      
      if (activeTab && tabsContainer) {
        // Calculate the scroll position to center the active tab
        const containerWidth = tabsContainer.offsetWidth;
        const tabWidth = activeTab.offsetWidth;
        const tabLeft = activeTab.offsetLeft;
        
        const scrollPosition = tabLeft - (containerWidth / 2) + (tabWidth / 2);
        
        // Scroll the tabs container
        tabsContainer.scrollTo({
          left: scrollPosition,
          behavior: 'smooth'
        });
      }
    },
    selectAvatar(avatar) {
      this.selectedAvatar = avatar;
    },
    confirmSelection() {
      this.$emit('avatar-selected', `/Avatar/${this.selectedAvatar}.svg`);
    },
    handleKeyDown(event, avatar) {
      if (event.key === 'Enter' || event.key === ' ') {
        this.selectAvatar(avatar);
        event.preventDefault();
      }
    },
    checkScroll() {
      const container = this.$refs.scrollContainer;
      if (container) {
        this.showScrollIndicator = container.scrollHeight > container.clientHeight;
      }
    },
    detectMobileDevice() {
      // Check if device is mobile based on screen width
      this.isMobileDevice = window.innerWidth < 768;
    },
    updateViewportHeight() {
      // Get the actual viewport height
      this.viewportHeight = window.innerHeight;
    },
    adjustHeightForMobile() {
      const selector = document.querySelector('.avatar-selector');
      if (!selector) return;
      
      if (this.isMobileDevice) {
        // On mobile, use a percentage of the viewport height
        // Use a smaller percentage for very small screens
        let heightPercentage = 0.8; // Default 80%
        
        if (this.viewportHeight < 600) {
          heightPercentage = 0.7; // 70% for smaller screens
        }
        if (this.viewportHeight < 500) {
          heightPercentage = 0.6; // 60% for very small screens
        }
        
        const maxHeight = Math.min(this.viewportHeight * heightPercentage, 600);
        selector.style.height = `${maxHeight}px`;
        selector.style.maxHeight = `${maxHeight}px`;
        
        // Add a fixed position for mobile to ensure it stays in viewport
        selector.style.position = 'fixed';
        selector.style.top = '50%';
        selector.style.left = '50%';
        selector.style.transform = 'translate(-50%, -50%)';
        selector.style.zIndex = '9999';
      } else {
        // On desktop/tablet, use the original styles
        selector.style.height = '';
        selector.style.maxHeight = '80vh';
        selector.style.position = '';
        selector.style.top = '';
        selector.style.left = '';
        selector.style.transform = '';
        selector.style.zIndex = '';
      }
    },
    categoryIcon(icon) {
      switch(icon) {
        case 'user':
          return 'UserIcon';
        case 'briefcase':
          return 'BriefcaseIcon';
        case 'globe':
          return 'GlobeIcon';
        case 'crown':
          return 'CrownIcon';
        case 'heart':
          return 'HeartIcon';
        case 'clock':
          return 'ClockIcon';
        default:
          return 'UserIcon';
      }
    }
  },
  components: {
    UserIcon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      `
    },
    BriefcaseIcon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect width="20" height="14" x="2" y="7" rx="2" ry="2"></rect>
          <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
        </svg>
      `
    },
    GlobeIcon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
          <path d="M2 12h20"></path>
        </svg>
      `
    },
    CrownIcon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14"></path>
        </svg>
      `
    },
    HeartIcon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path>
        </svg>
      `
    },
    ClockIcon: {
      template: `
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <polyline points="12 6 12 12 16 14"></polyline>
        </svg>
      `
    }
  }
}
</script>

<style scoped>
/* Dark Theme Variables */
.avatar-selector {
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --text-primary: #111827;
  --text-secondary: #4b5563;
  --border-color: #e5e7eb;
  --accent-color: #6366f1;
  --accent-hover: #4f46e5;
  --input-bg: #f3f4f6;
  --input-border: #d1d5db;
  --card-bg: #ffffff;
  --card-hover: #f3f4f6;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --scrollbar-thumb: #d1d5db;
  --scrollbar-track: transparent;
}

/* Dark mode styles */
.avatar-selector.dark {
  /* The sign-up container dark mode uses a gradient from #18181b to #09090b.
     Here we choose #18181b as the primary dark background and #09090b as the secondary. */
     --bg-primary: #18181b;
  --bg-secondary: #09090b;
  
  /* Text in dark mode becomes white, with secondary elements using a softer gray */
  --text-primary: #ffffff;
  --text-secondary: #9ca3af;
  
  /* The dark mode input/form border becomes a medium gray (#444) */
  --border-color: #444;
  
  /* Accent colors remain the same, with the hover color adapted from the light theme */
  --accent-color: #6366f1;
  --accent-hover: #4f46e5;
  
  /* For dark mode, use the same dark background for inputs and cards */
  --input-bg: #18181b;
  --input-border: #444;
  --card-bg: #18181b;
  
  /* For a hover effect on cards, a slightly lighter tone is used */
  --card-hover: #222222;
  
  /* Dark mode shadows are stronger: rgba(0, 0, 0, 0.5) */
  --shadow-color: rgba(0, 0, 0, 0.5);
  
  /* Scrollbar styling for dark mode */
  --scrollbar-thumb: #4b5563;
  --scrollbar-track: #18181b;
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Avatar Selector Container */
.avatar-selector {
  width: 95%;
  max-width: 95%;
  height: 70vh; /* Reduced height for mobile */
  max-height: 70vh; /* Reduced max-height for mobile */
  margin: 0 auto;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 10px 25px -5px var(--shadow-color);
 
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  position: relative;
}

@media (min-width: 640px) {
  .avatar-selector {
    max-width: 90%;
    height: 80vh;
    max-height: 80vh;
  }
}

@media (min-width: 768px) {
  .avatar-selector {
    max-width: 700px;
    height: 80vh;
    max-height: 80vh;
  }
}

/* Header */
.avatar-selector-header {
  padding: 0.5rem 0.75rem;
 
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  transition: border-color 0.3s ease;
}

@media (max-width: 360px) {
  .avatar-selector-header {
    padding: 0.375rem 0.5rem;
  }
}

.avatar-selector-header h2 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

@media (min-width: 640px) {
  .avatar-selector-header h2 {
    font-size: 1.125rem;
  }
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem; /* Smaller for mobile */
  height: 1.75rem; /* Smaller for mobile */
  border-radius: 9999px;
  background: transparent;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color 0.2s, color 0.3s ease;
}

.close-button:hover {
  background-color: var(--card-hover);
}

/* Content Layout */
.avatar-selector-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

@media (min-width: 768px) {
  .avatar-selector-content {
    flex-direction: row;
  }
}

/* Mobile View */
.mobile-view {
  display: flex;
  flex-direction: column;
  
  background-color: var(--bg-secondary);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

@media (min-width: 768px) {
  .mobile-view {
    display: none;
  }
}

/* Mobile Search */
.mobile-search-container {
  padding: 0.375rem 0.5rem;
  
  transition: border-color 0.3s ease;
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.375rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.search-input {
  width: 100%;
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: 0.25rem;
  padding: 0.375rem 0.5rem 0.375rem 1.5rem; /* Smaller for mobile */
  color: var(--text-primary);
  font-size: 0.75rem; /* Smaller for mobile */
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

@media (min-width: 640px) {
  .search-input {
    padding: 0.5rem 0.75rem 0.5rem 2rem;
    font-size: 0.875rem;
  }
  
  .search-icon {
    left: 0.5rem;
  }
}

.search-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
  transition: color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.clear-search-btn {
  position: absolute;
  right: 0.375rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 9999px;
  background-color: var(--card-bg);
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.clear-search-btn:hover {
  background-color: var(--card-hover);
  color: var(--text-primary);
}

/* Mobile Category Tabs */
.mobile-category-tabs {
  overflow-x: auto;
  scrollbar-width: none; /* Hide scrollbar for Firefox */
  -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  padding: 0.35rem 0;
  
  transition: border-color 0.3s ease;
}

.mobile-category-tabs::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome and Safari */
}

.tabs-scroll-container {
  display: flex;
  padding: 0 0.5rem;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 9999px;
  color: var(--text-secondary);
  font-size: 0.7rem;
  font-weight: 500;
  white-space: nowrap;
  margin-right: 0.375rem;
  transition: all 0.2s ease, background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.category-tab:last-child {
  margin-right: 0;
}

.category-tab.active {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.category-tab:hover:not(.active) {
  background-color: var(--card-hover);
  border-color: var(--accent-color);
}

.category-tab .category-icon {
  width: 0.75rem;
  height: 0.75rem;
}

/* Hide category name on very small screens */
@media (max-width: 320px) {
  .category-tab .category-name {
    display: none;
  }
  
  .category-tab {
    padding: 0.25rem;
  }
  
  .category-tab .category-icon {
    margin: 0;
  }
}

/* Mobile Results Count */
.mobile-results-count {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.375rem 0.5rem;
  font-size: 0.7rem;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.clear-search {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 9999px;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.clear-search:hover {
  background-color: var(--card-hover);
  border-color: var(--accent-color);
}

/* Sidebar */
.avatar-selector-sidebar {
  display: none;
  width: 100%;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

@media (min-width: 768px) {
  .avatar-selector-sidebar {
    display: block;
    width: 12rem;
    border-bottom: none;
    border-right: 1px solid var(--border-color);
  }
}

/* Search */
.search-container {
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  transition: border-color 0.3s ease;
}

/* Category Navigation */
.category-nav {
  padding: 0.375rem;
  max-height: 250px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

.category-nav::-webkit-scrollbar {
  width: 3px;
}

.category-nav::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
}

.category-nav::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 20px;
}

.category-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 0.75rem;
  border-radius: 0.375rem;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 0.8125rem;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s, transform 0.1s;
}

.category-button:hover {
  background-color: var(--card-hover);
  color: var(--text-primary);
  transform: translateX(2px);
}

.category-button.active {
  background-color: var(--card-bg);
  color: var(--accent-color);
  font-weight: 600;
}

.category-icon {
  width: 0.875rem;
  height: 0.875rem;
  transition: color 0.2s;
}

.category-button.active .category-icon {
  color: var(--accent-color);
}

.category-count {
  margin-left: auto;
  font-size: 0.6875rem;
  color: var(--text-secondary);
  background-color: var(--card-bg);
  padding: 0.125rem 0.25rem;
  border-radius: 9999px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Avatar Grid Container */
.avatar-grid-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease;
}

/* Category Title Bar */
.category-title-bar {
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  transition: border-color 0.3s ease;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .desktop-only {
    display: flex;
  }
}

.category-title {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.category-title-icon {
  width: 1rem;
  height: 1rem;
  color: var(--accent-color);
  transition: color 0.3s ease;
}

.category-title h3 {
  font-weight: 600;
  font-size: 0.9375rem;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

/* Avatar Grid Scroll Container */
.avatar-grid-scroll {
  flex: 1;
  padding: 0.5rem;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  transition: padding 0.3s ease;
}

@media (min-width: 640px) {
  .avatar-grid-scroll {
    padding: 0.75rem;
  }
}

/* Custom Scrollbar */
.avatar-grid-scroll::-webkit-scrollbar {
  width: 3px;
}

.avatar-grid-scroll::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
}

.avatar-grid-scroll::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 20px;
}

.avatar-grid-scroll::-webkit-scrollbar-thumb:hover {
  background-color: var(--accent-color);
}

/* Avatar Grid */
.avatar-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.375rem;
  transition: gap 0.3s ease;
}

@media (max-width: 320px) {
  .avatar-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 0.25rem;
  }
}

@media (min-width: 480px) {
  .avatar-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 0.5rem;
  }
}

@media (min-width: 768px) {
  .avatar-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 0.75rem;
  }
}

@media (min-width: 1024px) {
  .avatar-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Avatar Item */
.avatar-item {
  position: relative;
  aspect-ratio: 1/1;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease, background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, transform 0.2s ease;
  min-width: 0; /* Prevent overflow */
  touch-action: manipulation; /* Disable browser handling of gestures */
}

.avatar-item:hover {
  background-color: var(--card-hover);
  border-color: var(--accent-color);
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar-item:active {
  transform: scale(0.95);
}

.avatar-item.selected {
  border: 1px solid var(--accent-color);
  box-shadow: 0 0 0 1px var(--bg-primary), 0 0 0 2px var(--accent-color);
}

.avatar-item:focus {
  outline: none;
  box-shadow: 0 0 0 1px var(--bg-primary), 0 0 0 2px var(--accent-color);
}

.avatar-circle {
  width: 100%;
  height: 100%;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: var(--card-bg);
  transition: background-color 0.3s ease;
}

.avatar-circle img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.avatar-item:hover .avatar-circle img {
  transform: scale(1.05);
}

.selected-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 14px;
  height: 14px;
  border-radius: 9999px;
  background-color: var(--accent-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: scale-in 0.2s ease-out;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

@media (max-width: 320px) {
  .selected-indicator {
    width: 12px;
    height: 12px;
  }
}

@keyframes scale-in {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

/* Empty Results */
.empty-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 1rem 0.75rem;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.empty-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.empty-results h3 {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.empty-results p {
  color: var(--text-secondary);
  font-size: 0.75rem;
  max-width: 16rem;
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* Selection Footer */
.selection-footer {
  padding: 0.5rem 0.75rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.selected-avatar-preview {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 1px solid var(--accent-color);
  box-shadow: 0 0 0 1px var(--bg-primary), 0 0 0 2px var(--accent-color);
  transition: background-color 0.3s ease;
}

@media (min-width: 640px) {
  .selected-avatar-preview {
    width: 2.5rem;
    height: 2.5rem;
  }
}

.selected-avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.selected-avatar-text {
  display: flex;
  flex-direction: column;
}

.selected-label {
  font-size: 0.625rem;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.selected-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
  transition: color 0.3s ease;
  max-width: 80px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (min-width: 640px) {
  .selected-label {
    font-size: 0.6875rem;
  }
  
  .selected-name {
    font-size: 0.8125rem;
    max-width: 100px;
  }
}

.confirm-button {
  padding: 0.375rem 0.75rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s, box-shadow 0.2s;
}

@media (min-width: 640px) {
  .confirm-button {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
  }
}

.confirm-button:hover {
  background-color: var(--accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

.confirm-button:active {
  transform: translateY(1px);
}

.confirm-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--bg-primary), 0 0 0 3px rgba(99, 102, 241, 0.5);
}

/* Animations */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up-enter-from,
.slide-up-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* Focus styles for keyboard navigation */
:focus-visible {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* Mobile viewport fix for iOS */
@supports (-webkit-touch-callout: none) {
  .avatar-selector {
    height: -webkit-fill-available;
    max-height: -webkit-fill-available;
  }
}
</style>
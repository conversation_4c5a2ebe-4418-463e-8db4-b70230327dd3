<template>
  <div class="group-requests-icon" @click="goToGroupRequests">
    <UsersIcon />
    <span v-if="groupRequestsCount > 0" class="badge">{{ groupRequestsCount }}</span>
  </div>
</template>

<script>
import { UsersIcon } from 'lucide-vue-next';

export default {
  components: {
    UsersIcon
  },
  props: {
    groupRequestsCount: {
      type: Number,
      required: true,
    },
  },
  methods: {
    goToGroupRequests() {
      this.$router.push('/group-requests');
    },
  },
};
</script>

<style scoped>
.group-requests-icon {
  position: relative;
  display: inline-block;
  cursor: pointer;
  padding: 8px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.group-requests-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.group-requests-icon svg {
  width: 22px;
  height: 22px;
  color: #a0a0a0;
  transition: color 0.3s ease;
}

.group-requests-icon:hover svg {
  color: #e2e2e2;
}

.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #6366f1;
  color: #ffffff;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.group-requests-icon:hover .badge {
  transform: scale(1.1);
  background-color: #4f46e5;
}
</style>
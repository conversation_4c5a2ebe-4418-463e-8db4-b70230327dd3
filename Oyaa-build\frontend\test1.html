<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OverlayScrollbars Test Page</title>
    <!-- OverlayScrollbars CSS from CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/overlayscrollbars/css/OverlayScrollbars.min.css" />
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: #fafafa;
      }
      .scroll-container {
        width: 300px;
        height: 200px;
        margin: 40px auto;
        border: 1px solid #ccc;
        overflow: auto; /* Ensure content is scrollable */
        padding: 10px;
        background: #fff;
      }
      .content {
        /* Make the content tall enough to cause scrolling */
        height: 600px;
        background: linear-gradient(to bottom, #f0f0f0, #d0d0d0);
        padding: 10px;
      }
    </style>
  </head>
  <body>
    <div class="scroll-container">
      <div class="content">
        <h2>Test Content</h2>
        <p>
          This is a test to verify the custom scrollbar using OverlayScrollbars.
          Scroll down to see the custom scrollbar in action.
        </p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum vitae sem a neque facilisis semper. Donec maximus, felis ac faucibus tempus, sapien ipsum accumsan libero, vel luctus nibh lectus non erat.</p>
        <p>Aliquam in lorem euismod, tincidunt dui in, condimentum lacus. Praesent id malesuada lorem, sit amet luctus nunc. Vivamus hendrerit purus ac felis porta, at dictum lorem egestas.</p>
        <p>Sed scelerisque libero non est aliquet, quis fringilla dui lobortis. Suspendisse potenti.</p>
      </div>
    </div>
    <!-- OverlayScrollbars JS from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/overlayscrollbars/js/OverlayScrollbars.min.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const container = document.querySelector('.scroll-container');
        if (container && window.OverlayScrollbars) {
          OverlayScrollbars(container, {
            scrollbars: {
              autoHide: 'move' // Scrollbar shows when the mouse moves over the container
            }
          });
        }
      });
    </script>
  </body>
</html>

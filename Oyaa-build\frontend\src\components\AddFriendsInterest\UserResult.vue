<template>
  <div class="user-result">
    <div class="user-avatar">
      <img v-if="user.avatar" :src="user.avatar" :alt="user.username" class="avatar-image" />
      <span v-else>{{ getInitials(user.username) }}</span>
    </div>
    <div class="user-info">
      <div class="user-header">
        <h3 class="user-name">{{ user.username }}</h3>
        <div class="match-count">
          <span class="match-number">{{ user.match_count }}</span> shared interests
        </div>
      </div>
      <div class="matching-tags">
        <span v-for="tag in user.matching_tags" :key="tag" class="matching-tag">
          {{ tag }}
        </span>
      </div>
    </div>
    <div class="user-action">
      <button v-if="user.is_friend" disabled class="friend-status">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="20 6 9 17 4 12"></polyline>
        </svg>
        Friends
      </button>
      <button v-else-if="user.has_sent_request" disabled class="request-sent">
        Request Sent
      </button>
      <button v-else @click="$emit('send-friend-request', user.id)" class="add-friend-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="8.5" cy="7" r="4"></circle>
          <line x1="20" y1="8" x2="20" y2="14"></line>
          <line x1="23" y1="11" x2="17" y2="11"></line>
        </svg>
        Add Friend
      </button>
      <button @click="$emit('start-chat', user.id)" class="chat-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
        </svg>
        Chat
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    user: {
      type: Object,
      required: true,
    },
  },
  methods: {
    getInitials(name) {
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    },
  },
};
</script>

<style scoped>
.user-result {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  transition: all var(--transition-fast);
  border: 1px solid var(--border-color);
}

.user-result:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-tertiary);
}

.user-avatar {
  width: 2.75rem;
  height: 2.75rem;
  border-radius: 10px;
  background-color: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border: 1px solid var(--border-color);
  color: var(--accent-primary);
  font-size: 1rem;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  object-fit: cover;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-header {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

@media (min-width: 640px) {
  .user-header {
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
  }
}

.user-name {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
}

.match-count {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.match-number {
  color: var(--accent-primary);
  font-weight: 600;
}

.matching-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
  margin-top: 0.625rem;
}

.matching-tag {
  font-size: 0.75rem;
  color: var(--accent-primary);
  background-color: var(--accent-tertiary);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 6px;
  padding: 0.125rem 0.5rem;
}

.user-action {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.add-friend-button,
.chat-button {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  font-size: 0.8125rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.add-friend-button:hover,
.chat-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.add-friend-button:active,
.chat-button:active {
  transform: translateY(0);
}

.friend-status,
.request-sent {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  font-size: 0.8125rem;
  cursor: default;
  white-space: nowrap;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .user-result {
    padding: 0.75rem;
    gap: 0.75rem;
  }
  
  .user-avatar {
    width: 2.25rem;
    height: 2.25rem;
  }
  
  .user-name {
    font-size: 0.9375rem;
  }
  
  .matching-tags {
    margin-top: 0.5rem;
    gap: 0.25rem;
  }
  
  .matching-tag {
    font-size: 0.6875rem;
    padding: 0.125rem 0.375rem;
  }
  
  .add-friend-button,
  .chat-button,
  .friend-status,
  .request-sent {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .add-friend-button svg,
  .chat-button svg,
  .friend-status svg {
    width: 12px;
    height: 12px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .user-action {
    flex-direction: column;
    gap: 0.375rem;
  }
  
  .add-friend-button,
  .chat-button,
  .friend-status,
  .request-sent {
    padding: 0.3125rem 0.5rem;
    font-size: 0.6875rem;
  }
  
  .add-friend-button svg,
  .chat-button svg,
  .friend-status svg {
    display: none;
  }
}
</style>
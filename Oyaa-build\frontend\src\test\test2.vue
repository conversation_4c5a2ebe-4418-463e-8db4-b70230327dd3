<template>
  <div class="emoji-container" ref="emojiContainer">
    <div class="textarea-container">
      <textarea 
        v-model="message" 
        placeholder="Type a message..." 
        class="message-textarea"
        ref="messageTextarea"
        @keydown="handleKeyDown"
        aria-label="Message input"
      ></textarea>
      <div class="textarea-actions">
        <button 
          @click="toggleEmojiMenu" 
          class="action-button emoji-button"
          aria-label="Open emoji menu"
          :aria-expanded="showEmojiMenu"
        >
          <i class="emoji-icon">😊</i>
        </button>
        <button 
          class="action-button gif-button"
          @click="openGifTab"
          aria-label="Open GIF menu"
        >
          <i class="gif-icon">GIF</i>
        </button>
      </div>
    </div>

    <transition name="slide-up">
      <div v-if="showEmojiMenu" class="emoji-menu" ref="emojiMenu">
        <div class="emoji-menu-header">
          <div class="emoji-tabs">
            <button 
              v-for="tab in tabs" 
              :key="tab.id"
              @click="setActiveTab(tab.id)"
              :class="['tab-button', { active: activeTab === tab.id }]"
              :aria-selected="activeTab === tab.id"
              role="tab"
            >
              <span class="tab-icon">{{ tab.icon }}</span>
              <span class="tab-name">{{ tab.name }}</span>
            </button>
          </div>
          
          <div class="emoji-search">
            <div class="search-input-wrapper">
              <span class="search-icon">🔍</span>
              <input 
                type="text" 
                v-model="searchQuery" 
                :placeholder="`Search ${tabs.find(tab => tab.id === activeTab)?.name}...`" 
                class="search-input"
                ref="searchInput"
                @focus="searchFocused = true"
                @blur="searchFocused = false"
              />
              <button 
                v-if="searchQuery" 
                @click="clearSearch" 
                class="clear-search"
                aria-label="Clear search"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
        
        <div class="emoji-content" ref="emojiContent">
          <!-- Recent Emojis -->
          <div v-if="activeTab === 'emoji' && recentEmojis.length > 0 && !searchQuery" class="emoji-section">
            <h3 class="section-title">Recently Used</h3>
            <div class="emoji-grid">
              <button 
                v-for="emoji in recentEmojis" 
                :key="`recent-${emoji.symbol}`"
                @click="insertEmoji(emoji.symbol)"
                class="emoji-item"
                :title="emoji.name"
                :aria-label="`Emoji: ${emoji.name}`"
              >
                {{ emoji.symbol }}
              </button>
            </div>
          </div>
          
          <!-- Regular Emojis -->
          <div v-if="activeTab === 'emoji'" class="emoji-section">
            <template v-if="!searchQuery">
              <div v-for="category in emojiCategories" :key="category.id" class="emoji-category">
                <h3 class="section-title" :id="`category-${category.id}`">{{ category.name }}</h3>
                <div class="emoji-grid">
                  <button 
                    v-for="emoji in category.emojis" 
                    :key="emoji.symbol"
                    @click="insertEmoji(emoji.symbol)"
                    class="emoji-item"
                    :title="emoji.name"
                    :aria-label="`Emoji: ${emoji.name}`"
                  >
                    {{ emoji.symbol }}
                  </button>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="emoji-search-results">
                <h3 class="section-title">Search Results</h3>
                <div v-if="filteredEmojis.length === 0" class="no-results">
                  No emojis found for "{{ searchQuery }}"
                </div>
                <div v-else class="emoji-grid">
                  <button 
                    v-for="emoji in filteredEmojis" 
                    :key="emoji.symbol"
                    @click="insertEmoji(emoji.symbol)"
                    class="emoji-item"
                    :title="emoji.name"
                    :aria-label="`Emoji: ${emoji.name}`"
                  >
                    {{ emoji.symbol }}
                  </button>
                </div>
              </div>
            </template>
          </div>
          
          <!-- Category Navigation for Emojis -->
          <div v-if="activeTab === 'emoji' && !searchQuery" class="category-navigation">
            <button 
              v-for="category in emojiCategories" 
              :key="`nav-${category.id}`"
              @click="scrollToCategory(category.id)"
              class="category-nav-button"
              :title="category.name"
              :aria-label="`Go to ${category.name} category`"
            >
              {{ category.icon }}
            </button>
          </div>
          
          <!-- GIFs -->
          <div v-else-if="activeTab === 'gif'" class="gif-section">
            <div v-if="loading" class="loading-container">
              <div class="loading-spinner"></div>
              <p>Loading GIFs...</p>
            </div>
            <div v-else-if="gifs.length === 0" class="no-results">
              <p>No GIFs found. Try another search.</p>
            </div>
            <div v-else class="gif-grid">
              <div 
                v-for="gif in gifs" 
                :key="gif.id"
                @click="insertGif(gif)"
                class="gif-item"
                :title="gif.title"
              >
                <div class="gif-preview">
                  <img :src="gif.previewUrl" :alt="gif.title" loading="lazy" />
                </div>
              </div>
            </div>
          </div>
          
          <!-- Stickers -->
          <div v-else-if="activeTab === 'sticker'" class="sticker-section">
            <div v-if="loading" class="loading-container">
              <div class="loading-spinner"></div>
              <p>Loading stickers...</p>
            </div>
            <div v-else-if="stickers.length === 0" class="no-results">
              <p>No stickers found. Try another search.</p>
            </div>
            <div v-else class="sticker-grid">
              <div 
                v-for="sticker in stickers" 
                :key="sticker.id"
                @click="insertSticker(sticker)"
                class="sticker-item"
                :title="sticker.title"
              >
                <div class="sticker-preview">
                  <img :src="sticker.previewUrl" :alt="sticker.title" loading="lazy" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { debounce } from 'lodash-es'

export default {
  name: 'EmojiMenu',
  setup() {
    // Refs for DOM elements
    const emojiContainer = ref(null)
    const emojiMenu = ref(null)
    const emojiContent = ref(null)
    const messageTextarea = ref(null)
    const searchInput = ref(null)
    
    // State
    const message = ref('')
    const showEmojiMenu = ref(false)
    const activeTab = ref('emoji')
    const searchQuery = ref('')
    const loading = ref(false)
    const searchFocused = ref(false)
    const recentEmojis = ref([])
    
    // Tabs configuration
    const tabs = [
      { id: 'emoji', name: 'Emojis', icon: '😊' },
      { id: 'gif', name: 'GIFs', icon: '🎬' },
      { id: 'sticker', name: 'Stickers', icon: '🏷️' }
    ]
    
    // Emoji categories
    const emojiCategories = [
      {
        id: 'smileys',
        name: 'Smileys & Emotion',
        icon: '😊',
        emojis: [
          { name: 'Smile', symbol: '😊', keywords: ['happy', 'smile', 'joy'] },
          { name: 'Laugh', symbol: '😂', keywords: ['laugh', 'joy', 'lol'] },
          { name: 'Wink', symbol: '😉', keywords: ['wink', 'flirt'] },
          { name: 'Heart Eyes', symbol: '😍', keywords: ['love', 'heart eyes', 'adore'] },
          { name: 'Kissing', symbol: '😘', keywords: ['kiss', 'love'] },
          { name: 'Thinking', symbol: '🤔', keywords: ['think', 'wondering', 'hmm'] },
          { name: 'Neutral', symbol: '😐', keywords: ['neutral', 'meh', 'poker face'] },
          { name: 'Expressionless', symbol: '😑', keywords: ['expressionless', 'blank'] },
          { name: 'No Mouth', symbol: '😶', keywords: ['no mouth', 'silent', 'quiet'] },
          { name: 'Zipper Mouth', symbol: '🤐', keywords: ['zipper', 'quiet', 'secret'] },
          { name: 'Hushed', symbol: '😯', keywords: ['hushed', 'surprised', 'wow'] },
          { name: 'Sleepy', symbol: '😪', keywords: ['sleepy', 'tired', 'drowsy'] },
          { name: 'Tired', symbol: '😫', keywords: ['tired', 'exhausted'] },
          { name: 'Sleeping', symbol: '😴', keywords: ['sleeping', 'zzz', 'sleep'] },
          { name: 'Relieved', symbol: '😌', keywords: ['relieved', 'relaxed', 'phew'] },
          { name: 'Stuck Out Tongue', symbol: '😛', keywords: ['tongue', 'playful', 'silly'] },
        ]
      },
      {
        id: 'people',
        name: 'People & Body',
        icon: '👋',
        emojis: [
          { name: 'Wave', symbol: '👋', keywords: ['wave', 'hello', 'goodbye'] },
          { name: 'Raised Hand', symbol: '✋', keywords: ['hand', 'high five', 'stop'] },
          { name: 'OK Hand', symbol: '👌', keywords: ['ok', 'okay', 'perfect'] },
          { name: 'Thumbs Up', symbol: '👍', keywords: ['like', 'approve', 'thumbs up'] },
          { name: 'Thumbs Down', symbol: '👎', keywords: ['dislike', 'disapprove', 'thumbs down'] },
          { name: 'Clap', symbol: '👏', keywords: ['applause', 'clap', 'praise'] },
          { name: 'Pray', symbol: '🙏', keywords: ['pray', 'please', 'hope'] },
          { name: 'Muscle', symbol: '💪', keywords: ['muscle', 'strong', 'flex'] },
          { name: 'Point Up', symbol: '☝️', keywords: ['point', 'up', 'finger'] },
          { name: 'Point Down', symbol: '👇', keywords: ['point', 'down', 'finger'] },
          { name: 'Point Left', symbol: '👈', keywords: ['point', 'left', 'finger'] },
          { name: 'Point Right', symbol: '👉', keywords: ['point', 'right', 'finger'] },
          { name: 'Raised Hands', symbol: '🙌', keywords: ['celebration', 'praise', 'hooray'] },
          { name: 'Folded Hands', symbol: '🙏', keywords: ['please', 'hope', 'pray'] },
        ]
      },
      {
        id: 'nature',
        name: 'Animals & Nature',
        icon: '🐱',
        emojis: [
          { name: 'Dog Face', symbol: '🐶', keywords: ['dog', 'pet', 'animal'] },
          { name: 'Cat Face', symbol: '🐱', keywords: ['cat', 'pet', 'animal'] },
          { name: 'Mouse Face', symbol: '🐭', keywords: ['mouse', 'animal'] },
          { name: 'Hamster Face', symbol: '🐹', keywords: ['hamster', 'pet', 'animal'] },
          { name: 'Rabbit Face', symbol: '🐰', keywords: ['rabbit', 'bunny', 'animal'] },
          { name: 'Fox Face', symbol: '🦊', keywords: ['fox', 'animal'] },
          { name: 'Bear Face', symbol: '🐻', keywords: ['bear', 'animal'] },
          { name: 'Panda Face', symbol: '🐼', keywords: ['panda', 'animal'] },
          { name: 'Koala Face', symbol: '🐨', keywords: ['koala', 'animal'] },
          { name: 'Tiger Face', symbol: '🐯', keywords: ['tiger', 'animal'] },
          { name: 'Lion Face', symbol: '🦁', keywords: ['lion', 'animal'] },
          { name: 'Cow Face', symbol: '🐮', keywords: ['cow', 'animal'] },
          { name: 'Pig Face', symbol: '🐷', keywords: ['pig', 'animal'] },
          { name: 'Frog Face', symbol: '🐸', keywords: ['frog', 'animal'] },
        ]
      },
      {
        id: 'food',
        name: 'Food & Drink',
        icon: '🍔',
        emojis: [
          { name: 'Hamburger', symbol: '🍔', keywords: ['hamburger', 'burger', 'food'] },
          { name: 'Pizza', symbol: '🍕', keywords: ['pizza', 'food', 'slice'] },
          { name: 'Hot Dog', symbol: '🌭', keywords: ['hot dog', 'food'] },
          { name: 'Sandwich', symbol: '🥪', keywords: ['sandwich', 'food'] },
          { name: 'Taco', symbol: '🌮', keywords: ['taco', 'food', 'mexican'] },
          { name: 'Burrito', symbol: '🌯', keywords: ['burrito', 'food', 'mexican'] },
          { name: 'Fries', symbol: '🍟', keywords: ['fries', 'food', 'french fries'] },
          { name: 'Sushi', symbol: '🍣', keywords: ['sushi', 'food', 'japanese'] },
          { name: 'Bento Box', symbol: '🍱', keywords: ['bento', 'food', 'japanese'] },
          { name: 'Curry Rice', symbol: '🍛', keywords: ['curry', 'food', 'rice'] },
          { name: 'Spaghetti', symbol: '🍝', keywords: ['spaghetti', 'pasta', 'food'] },
          { name: 'Bread', symbol: '🍞', keywords: ['bread', 'food'] },
          { name: 'Croissant', symbol: '🥐', keywords: ['croissant', 'food', 'bakery'] },
          { name: 'Cake', symbol: '🎂', keywords: ['cake', 'birthday', 'celebration'] },
          { name: 'Cookie', symbol: '🍪', keywords: ['cookie', 'food', 'dessert'] },
          { name: 'Chocolate Bar', symbol: '🍫', keywords: ['chocolate', 'food', 'dessert'] },
        ]
      },
      {
        id: 'activities',
        name: 'Activities',
        icon: '⚽',
        emojis: [
          { name: 'Soccer Ball', symbol: '⚽', keywords: ['soccer', 'football', 'sport'] },
          { name: 'Basketball', symbol: '🏀', keywords: ['basketball', 'sport'] },
          { name: 'Football', symbol: '🏈', keywords: ['football', 'american football', 'sport'] },
          { name: 'Baseball', symbol: '⚾', keywords: ['baseball', 'sport'] },
          { name: 'Tennis', symbol: '🎾', keywords: ['tennis', 'sport'] },
          { name: 'Volleyball', symbol: '🏐', keywords: ['volleyball', 'sport'] },
          { name: 'Rugby Football', symbol: '🏉', keywords: ['rugby', 'sport'] },
          { name: 'Pool 8 Ball', symbol: '🎱', keywords: ['pool', 'billiards', 'game'] },
          { name: 'Badminton', symbol: '🏸', keywords: ['badminton', 'sport'] },
          { name: 'Ping Pong', symbol: '🏓', keywords: ['ping pong', 'table tennis', 'sport'] },
          { name: 'Boxing Glove', symbol: '🥊', keywords: ['boxing', 'glove', 'sport'] },
          { name: 'Medal', symbol: '🏅', keywords: ['medal', 'award', 'winner'] },
          { name: 'Trophy', symbol: '🏆', keywords: ['trophy', 'award', 'winner'] },
          { name: 'Video Game', symbol: '🎮', keywords: ['video game', 'controller', 'gaming'] },
        ]
      },
      {
        id: 'travel',
        name: 'Travel & Places',
        icon: '✈️',
        emojis: [
          { name: 'Airplane', symbol: '✈️', keywords: ['airplane', 'flight', 'travel'] },
          { name: 'Car', symbol: '🚗', keywords: ['car', 'automobile', 'vehicle'] },
          { name: 'Bus', symbol: '🚌', keywords: ['bus', 'vehicle', 'transport'] },
          { name: 'Train', symbol: '🚆', keywords: ['train', 'railway', 'transport'] },
          { name: 'Metro', symbol: '🚇', keywords: ['metro', 'subway', 'underground'] },
          { name: 'Tram', symbol: '🚊', keywords: ['tram', 'transport'] },
          { name: 'Taxi', symbol: '🚕', keywords: ['taxi', 'cab', 'vehicle'] },
          { name: 'Ambulance', symbol: '🚑', keywords: ['ambulance', 'emergency', 'vehicle'] },
          { name: 'Fire Engine', symbol: '🚒', keywords: ['fire engine', 'emergency', 'vehicle'] },
          { name: 'Police Car', symbol: '🚓', keywords: ['police car', 'emergency', 'vehicle'] },
          { name: 'Ship', symbol: '🚢', keywords: ['ship', 'boat', 'cruise'] },
          { name: 'Rocket', symbol: '🚀', keywords: ['rocket', 'launch', 'space'] },
          { name: 'Motorcycle', symbol: '🏍️', keywords: ['motorcycle', 'bike', 'vehicle'] },
          { name: 'Bicycle', symbol: '🚲', keywords: ['bicycle', 'bike', 'cycling'] },
        ]
      },
      {
        id: 'objects',
        name: 'Objects',
        icon: '💡',
        emojis: [
          { name: 'Light Bulb', symbol: '💡', keywords: ['light bulb', 'idea', 'bright'] },
          { name: 'Bomb', symbol: '💣', keywords: ['bomb', 'explosion'] },
          { name: 'Money Bag', symbol: '💰', keywords: ['money bag', 'cash', 'rich'] },
          { name: 'Gem Stone', symbol: '💎', keywords: ['gem', 'diamond', 'jewel'] },
          { name: 'Hammer', symbol: '🔨', keywords: ['hammer', 'tool'] },
          { name: 'Wrench', symbol: '🔧', keywords: ['wrench', 'tool'] },
          { name: 'Screwdriver', symbol: '🪛', keywords: ['screwdriver', 'tool'] },
          { name: 'Nut and Bolt', symbol: '🔩', keywords: ['nut and bolt', 'tool'] },
          { name: 'Gear', symbol: '⚙️', keywords: ['gear', 'cog', 'settings'] },
          { name: 'Microscope', symbol: '🔬', keywords: ['microscope', 'science', 'laboratory'] },
          { name: 'Telescope', symbol: '🔭', keywords: ['telescope', 'space', 'astronomy'] },
          { name: 'Satellite Antenna', symbol: '📡', keywords: ['satellite', 'antenna', 'signal'] },
          { name: 'Syringe', symbol: '💉', keywords: ['syringe', 'needle', 'medicine'] },
          { name: 'Pill', symbol: '💊', keywords: ['pill', 'medicine', 'drug'] },
        ]
      },
      {
        id: 'symbols',
        name: 'Symbols',
        icon: '❤️',
        emojis: [
          { name: 'Red Heart', symbol: '❤️', keywords: ['heart', 'love', 'red'] },
          { name: 'Orange Heart', symbol: '🧡', keywords: ['heart', 'love', 'orange'] },
          { name: 'Yellow Heart', symbol: '💛', keywords: ['heart', 'love', 'yellow'] },
          { name: 'Green Heart', symbol: '💚', keywords: ['heart', 'love', 'green'] },
          { name: 'Blue Heart', symbol: '💙', keywords: ['heart', 'love', 'blue'] },
          { name: 'Purple Heart', symbol: '💜', keywords: ['heart', 'love', 'purple'] },
          { name: 'Black Heart', symbol: '🖤', keywords: ['heart', 'love', 'black'] },
          { name: 'Broken Heart', symbol: '💔', keywords: ['broken heart', 'heartbreak'] },
          { name: 'Heart with Arrow', symbol: '💘', keywords: ['heart with arrow', 'cupid', 'love'] },
          { name: 'Sparkling Heart', symbol: '💖', keywords: ['sparkling heart', 'sparkle', 'love'] },
          { name: 'Growing Heart', symbol: '💗', keywords: ['growing heart', 'love'] },
          { name: 'Beating Heart', symbol: '💓', keywords: ['beating heart', 'heartbeat', 'love'] },
          { name: 'Revolving Hearts', symbol: '💞', keywords: ['revolving hearts', 'love'] },
          { name: 'Two Hearts', symbol: '💕', keywords: ['two hearts', 'love'] },
          { name: 'Heart Decoration', symbol: '💟', keywords: ['heart decoration', 'love'] },
          { name: 'Heart Exclamation', symbol: '❣️', keywords: ['heart exclamation', 'love'] },
        ]
      },
      {
        id: 'flags',
        name: 'Flags',
        icon: '🏁',
        emojis: [
          { name: 'Chequered Flag', symbol: '🏁', keywords: ['chequered flag', 'race', 'finish'] },
          { name: 'Triangular Flag', symbol: '🚩', keywords: ['triangular flag', 'mark', 'location'] },
          { name: 'Crossed Flags', symbol: '🎌', keywords: ['crossed flags', 'japanese', 'celebration'] },
          { name: 'Black Flag', symbol: '🏴', keywords: ['black flag', 'pirate'] },
          { name: 'White Flag', symbol: '🏳️', keywords: ['white flag', 'surrender'] },
          { name: 'Rainbow Flag', symbol: '🏳️‍🌈', keywords: ['rainbow flag', 'pride', 'lgbt'] },
          { name: 'Transgender Flag', symbol: '🏳️‍⚧️', keywords: ['transgender flag', 'pride', 'lgbt'] },
          { name: 'Pirate Flag', symbol: '🏴‍☠️', keywords: ['pirate flag', 'skull and crossbones'] },
        ]
      }
    ]
    
    // GIFs and Stickers data
    const gifs = ref([])
    const stickers = ref([])
    
    // Filter emojis based on search query
    const filteredEmojis = computed(() => {
      if (!searchQuery.value) return []
      
      const query = searchQuery.value.toLowerCase()
      const results = []
      
      emojiCategories.forEach(category => {
        category.emojis.forEach(emoji => {
          if (
            emoji.name.toLowerCase().includes(query) || 
            emoji.keywords.some(keyword => keyword.includes(query))
          ) {
            results.push(emoji)
          }
        })
      })
      
      return results
    })
    
    // Load recent emojis from localStorage
    const loadRecentEmojis = () => {
      try {
        const stored = localStorage.getItem('recentEmojis')
        if (stored) {
          recentEmojis.value = JSON.parse(stored).slice(0, 16) // Limit to 16 recent emojis
        }
      } catch (error) {
        console.error('Error loading recent emojis:', error)
      }
    }
    
    // Save recent emojis to localStorage
    const saveRecentEmojis = (emoji) => {
      try {
        // Find the full emoji object
        let emojiObj = null
        for (const category of emojiCategories) {
          const found = category.emojis.find(e => e.symbol === emoji)
          if (found) {
            emojiObj = found
            break
          }
        }
        
        if (!emojiObj) return
        
        // Update recent emojis
        const recent = recentEmojis.value.filter(e => e.symbol !== emoji)
        recent.unshift(emojiObj)
        recentEmojis.value = recent.slice(0, 16)
        
        // Save to localStorage
        localStorage.setItem('recentEmojis', JSON.stringify(recentEmojis.value))
      } catch (error) {
        console.error('Error saving recent emojis:', error)
      }
    }
    
    // Toggle emoji menu
    const toggleEmojiMenu = () => {
      showEmojiMenu.value = !showEmojiMenu.value
      
      if (showEmojiMenu.value) {
        nextTick(() => {
          if (activeTab.value !== 'emoji') {
            fetchContent()
          }
        })
      }
    }
    
    // Set active tab
    const setActiveTab = (tabId) => {
      activeTab.value = tabId
      searchQuery.value = ''
      
      if (tabId !== 'emoji') {
        nextTick(() => {
          fetchContent()
        })
      }
    }
    
    // Open GIF tab directly
    const openGifTab = () => {
      if (!showEmojiMenu.value) {
        showEmojiMenu.value = true
      }
      setActiveTab('gif')
    }
    
    // Clear search
    const clearSearch = () => {
      searchQuery.value = ''
      if (searchInput.value) {
        searchInput.value.focus()
      }
    }
    
    // Insert emoji into textarea
    const insertEmoji = (emoji) => {
      const textarea = messageTextarea.value
      const startPos = textarea.selectionStart
      const endPos = textarea.selectionEnd
      
      message.value = message.value.substring(0, startPos) + emoji + message.value.substring(endPos)
      
      // Set cursor position after the inserted emoji
      nextTick(() => {
        textarea.focus()
        textarea.selectionStart = startPos + emoji.length
        textarea.selectionEnd = startPos + emoji.length
      })
      
      // Add to recent emojis
      saveRecentEmojis(emoji)
    }
    
    // Insert GIF into textarea
    const insertGif = (gif) => {
      // In a real app, you would insert the GIF properly
      // For this example, we'll just add a placeholder
      const gifText = `[GIF: ${gif.title}]`
      const textarea = messageTextarea.value
      const startPos = textarea.selectionStart
      const endPos = textarea.selectionEnd
      
      message.value = message.value.substring(0, startPos) + gifText + message.value.substring(endPos)
      
      // Set cursor position after the inserted GIF text
      nextTick(() => {
        textarea.focus()
        textarea.selectionStart = startPos + gifText.length
        textarea.selectionEnd = startPos + gifText.length
      })
      
      showEmojiMenu.value = false
    }
    
    // Insert sticker into textarea
    const insertSticker = (sticker) => {
      // In a real app, you would insert the sticker properly
      // For this example, we'll just add a placeholder
      const stickerText = `[Sticker: ${sticker.title}]`
      const textarea = messageTextarea.value
      const startPos = textarea.selectionStart
      const endPos = textarea.selectionEnd
      
      message.value = message.value.substring(0, startPos) + stickerText + message.value.substring(endPos)
      
      // Set cursor position after the inserted sticker text
      nextTick(() => {
        textarea.focus()
        textarea.selectionStart = startPos + stickerText.length
        textarea.selectionEnd = startPos + stickerText.length
      })
      
      showEmojiMenu.value = false
    }
    
    // Fetch GIFs or stickers from API
    const fetchContent = async () => {
      if (activeTab.value === 'emoji') return
      
      loading.value = true
      
      try {
        if (activeTab.value === 'gif') {
          // Using Tenor API (in a real app, you'd use your API key)
          const response = await fetch(`https://g.tenor.com/v1/search?q=${encodeURIComponent(searchQuery.value || 'trending')}&key=LIVDSRZULELA&limit=20`)
          const data = await response.json()
          
          gifs.value = data.results.map(item => ({
            id: item.id,
            title: item.title,
            previewUrl: item.media[0].tinygif.url,
            url: item.media[0].gif.url
          }))
        } else if (activeTab.value === 'sticker') {
          // Using Tenor API for stickers
          const response = await fetch(`https://g.tenor.com/v1/search?q=${encodeURIComponent(searchQuery.value || 'trending')}&key=LIVDSRZULELA&limit=20&media_filter=minimal&contentfilter=high`)
          const data = await response.json()
          
          stickers.value = data.results.map(item => ({
            id: item.id,
            title: item.title,
            previewUrl: item.media[0].tinygif.url,
            url: item.media[0].tinygif.url
          }))
        }
      } catch (error) {
        console.error('Error fetching content:', error)
        if (activeTab.value === 'gif') {
          gifs.value = []
        } else {
          stickers.value = []
        }
      } finally {
        loading.value = false
      }
    }
    
    // Scroll to category
    const scrollToCategory = (categoryId) => {
      const element = document.getElementById(`category-${categoryId}`)
      if (element && emojiContent.value) {
        emojiContent.value.scrollTo({
          top: element.offsetTop - 10,
          behavior: 'smooth'
        })
      }
    }
    
    // Handle keyboard navigation
    const handleKeyDown = (event) => {
      // Escape key closes the emoji menu
      if (event.key === 'Escape' && showEmojiMenu.value) {
        showEmojiMenu.value = false
      }
      
      // Ctrl+E or Cmd+E to toggle emoji menu
      if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
        event.preventDefault()
        toggleEmojiMenu()
      }
    }
    
    // Click outside to close
    const handleClickOutside = (event) => {
      if (
        showEmojiMenu.value && 
        emojiContainer.value && 
        !emojiContainer.value.contains(event.target)
      ) {
        showEmojiMenu.value = false
      }
    }
    
    // Debounced search function
    const debouncedSearch = debounce(() => {
      if (activeTab.value !== 'emoji') {
        fetchContent()
      }
    }, 300)
    
    // Watch for changes in search query
    watch(searchQuery, () => {
      debouncedSearch()
    })
    
    // Lifecycle hooks
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
      document.addEventListener('keydown', handleKeyDown)
      loadRecentEmojis()
    })
    
    onBeforeUnmount(() => {
      document.removeEventListener('click', handleClickOutside)
      document.removeEventListener('keydown', handleKeyDown)
    })
    
    return {
      // Refs
      emojiContainer,
      emojiMenu,
      emojiContent,
      messageTextarea,
      searchInput,
      
      // State
      message,
      showEmojiMenu,
      activeTab,
      searchQuery,
      loading,
      searchFocused,
      recentEmojis,
      
      // Data
      tabs,
      emojiCategories,
      gifs,
      stickers,
      filteredEmojis,
      
      // Methods
      toggleEmojiMenu,
      setActiveTab,
      openGifTab,
      clearSearch,
      insertEmoji,
      insertGif,
      insertSticker,
      scrollToCategory,
      handleKeyDown
    }
  }
}
</script>

<style>
:root {
  --dark-bg: #121212;
  --darker-bg: #0a0a0a;
  --menu-bg: #1a1a1a;
  --accent: #7289da;
  --accent-hover: #5b6eae;
  --text: #e0e0e0;
  --text-secondary: #a0a0a0;
  --text-muted: #666666;
  --border: #2c2c2c;
  --hover: #252525;
  --active: #303030;
  --shadow: rgba(0, 0, 0, 0.4);
}

/* Base styles */
.emoji-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--text);
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

/* Textarea styles */
.textarea-container {
  position: relative;
  width: 100%;
  background-color: var(--darker-bg);
  border-radius: 8px;
  border: 1px solid var(--border);
  overflow: hidden;
  transition: border-color 0.2s;
}

.textarea-container:focus-within {
  border-color: var(--accent);
}

.message-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px 80px 12px 12px;
  background-color: transparent;
  border: none;
  color: var(--text);
  font-size: 16px;
  resize: vertical;
  outline: none;
  line-height: 1.5;
}

.textarea-actions {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
}

.action-button {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 20px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.action-button:hover {
  background-color: var(--hover);
  color: var(--text);
}

.emoji-icon {
  font-style: normal;
}

.gif-icon {
  font-size: 14px;
  font-weight: bold;
  font-style: normal;
  background-color: var(--text-secondary);
  color: var(--darker-bg);
  padding: 2px 4px;
  border-radius: 4px;
}

/* Emoji menu styles */
.emoji-menu {
  position: absolute;
  bottom: calc(100% + 10px);
  right: 0;
  width: 100%;
  max-height: 400px;
  background-color: var(--menu-bg);
  border-radius: 8px;
  box-shadow: 0 4px 20px var(--shadow);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  border: 1px solid var(--border);
  overflow: hidden;
}

.emoji-menu-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: var(--menu-bg);
}

.emoji-tabs {
  display: flex;
  border-bottom: 1px solid var(--border);
  background-color: var(--darker-bg);
}

.tab-button {
  padding: 10px 15px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 16px;
  flex: 1;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.tab-button.active {
  color: var(--accent);
  border-bottom: 2px solid var(--accent);
  background-color: var(--menu-bg);
}

.tab-button:hover:not(.active) {
  background-color: var(--hover);
  color: var(--text);
}

.tab-icon {
  font-size: 18px;
}

.tab-name {
  display: none;
}

.emoji-search {
  padding: 10px;
  border-bottom: 1px solid var(--border);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 10px;
  color: var(--text-secondary);
  font-size: 14px;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 32px;
  border-radius: 4px;
  border: 1px solid var(--border);
  background-color: var(--darker-bg);
  color: var(--text);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: var(--accent);
}

.clear-search {
  position: absolute;
  right: 10px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.clear-search:hover {
  color: var(--text);
  background-color: var(--hover);
}

/* Emoji content styles */
.emoji-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: var(--border) transparent;
}

.emoji-content::-webkit-scrollbar {
  width: 6px;
}

.emoji-content::-webkit-scrollbar-track {
  background: transparent;
}

.emoji-content::-webkit-scrollbar-thumb {
  background-color: var(--border);
  border-radius: 3px;
}

.emoji-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 8px 0;
  padding: 0 4px;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  aspect-ratio: 1;
  font-size: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
}

.emoji-item:hover {
  background-color: var(--hover);
  transform: scale(1.1);
}

.emoji-item:active {
  transform: scale(0.95);
}

.emoji-category {
  margin-bottom: 16px;
}

/* Category navigation */
.category-navigation {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  background-color: var(--darker-bg);
  padding: 8px;
  border-top: 1px solid var(--border);
  z-index: 2;
}

.category-nav-button {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 16px;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.category-nav-button:hover {
  background-color: var(--hover);
  color: var(--text);
}

/* GIF and Sticker styles */
.gif-grid, .sticker-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.gif-item, .sticker-item {
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.2s;
  background-color: var(--darker-bg);
}

.gif-item:hover, .sticker-item:hover {
  transform: translateY(-2px);
}

.gif-preview, .sticker-preview {
  width: 100%;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.gif-preview img, .sticker-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Loading styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid var(--border);
  border-top-color: var(--accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* No results styles */
.no-results {
  text-align: center;
  padding: 20px;
  color: var(--text-secondary);
}

/* Animations */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

/* Responsive styles */
@media (min-width: 768px) {
  .tab-name {
    display: inline;
  }
  
  .emoji-menu {
    width: 400px;
  }
  
  .emoji-grid {
    grid-template-columns: repeat(8, 1fr);
  }
  
  .gif-grid, .sticker-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .gif-grid, .sticker-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .emoji-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    max-height: 60vh;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
  }
  
  .emoji-item {
    font-size: 20px;
  }
}
</style>
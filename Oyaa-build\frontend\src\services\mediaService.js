// frontend/src/services/mediaService.js
import axios from 'axios';

export const uploadMedia = async (file, userId, chatId, mediaType = 'image') => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('userId', userId);
  if (chatId) {
    formData.append('chatId', chatId);
  }
  formData.append('mediaType', mediaType);

  // Use the base URL from environment variables
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

  const response = await axios.post(`${API_BASE_URL}/api/media/upload`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });

  return response.data; // Expected to include { message, media }
};

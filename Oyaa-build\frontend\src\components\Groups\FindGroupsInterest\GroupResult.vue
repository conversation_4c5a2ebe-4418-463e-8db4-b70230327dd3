<template>
  <div class="group-result">
    <div class="group-avatar">
      <img v-if="group.avatar" :src="group.avatar" :alt="group.name" class="avatar-image" />
      <span v-else>{{ getInitials(group.name) }}</span>
    </div>
    <div class="group-info">
      <div class="group-header">
        <h3 class="group-name">{{ group.name }}</h3>
        <div class="match-count">
          <span class="match-number">{{ group.matching_tags.length }}</span> matching interest{{ group.matching_tags.length !== 1 ? 's' : '' }}
        </div>
      </div>
      <p class="group-description">{{ group.description }}</p>
      <div class="matching-tags">
        <span v-for="tag in group.matching_tags" :key="tag" class="matching-tag">
          {{ tag }}
        </span>
      </div>
    </div>
    <div class="group-action">
      <button @click="$emit('join-group', group.id)" class="join-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="8.5" cy="7" r="4"></circle>
          <line x1="20" y1="8" x2="20" y2="14"></line>
          <line x1="23" y1="11" x2="17" y2="11"></line>
        </svg>
        Join Group
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    group: {
      type: Object,
      required: true,
    },
  },
  methods: {
    getInitials(name) {
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    },
  },
};
</script>

<style scoped>
.group-result {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  transition: all var(--transition-fast);
  border: 1px solid var(--border-color);
}

.group-result:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-tertiary);
}

.group-avatar {
  width: 2.75rem;
  height: 2.75rem;
  border-radius: 10px;
  background-color: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border: 1px solid var(--border-color);
  color: var(--accent-primary);
  font-size: 1rem;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  object-fit: cover;
}

.group-info {
  flex: 1;
  min-width: 0;
}

.group-header {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

@media (min-width: 640px) {
  .group-header {
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
  }
}

.group-name {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
}

.group-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0.25rem 0 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.3;
}

.match-count {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.match-number {
  color: var(--accent-primary);
  font-weight: 600;
}

.matching-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
  margin-top: 0.625rem;
}

.matching-tag {
  font-size: 0.75rem;
  color: var(--accent-primary);
  background-color: var(--accent-tertiary);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 6px;
  padding: 0.125rem 0.5rem;
}

.group-action {
  margin-left: auto;
}

.join-button {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 0.875rem;
  font-size: 0.8125rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.join-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.join-button:active {
  transform: translateY(0);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .group-result {
    padding: 0.75rem;
    gap: 0.75rem;
  }
  
  .group-avatar {
    width: 2.25rem;
    height: 2.25rem;
  }
  
  .group-name {
    font-size: 0.9375rem;
  }
  
  .group-description {
    font-size: 0.8125rem;
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }
  
  .matching-tags {
    margin-top: 0.5rem;
    gap: 0.25rem;
  }
  
  .matching-tag {
    font-size: 0.6875rem;
    padding: 0.125rem 0.375rem;
  }
  
  .join-button {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .join-button svg {
    width: 12px;
    height: 12px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .join-button {
    padding: 0.3125rem 0.5rem;
    font-size: 0.6875rem;
  }
  
  .join-button svg {
    display: none;
  }
}
</style>
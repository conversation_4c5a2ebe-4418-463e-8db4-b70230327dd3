// backend/config/cloudinary.js
require('dotenv').config();
const cloudinary = require('cloudinary').v2;

// If CLOUDINARY_URL is set in your .env, the SDK will automatically pick it up.
cloudinary.api.ping((error, result) => {
  if (error) {
    console.error('✅ Cloudinary connection failed:', error);
  } else {
    console.log('✅ Cloudinary connected successfully:', result);
  }
});

module.exports = cloudinary;

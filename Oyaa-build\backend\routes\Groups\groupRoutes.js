const express = require('express');
const groupController = require('../../controllers/Group/groupController');
const authMiddleware = require('../../middleware/authMiddleware'); // Import the middleware
const router = express.Router();

router.get('/search', groupController.searchGroups);
router.post('/create', groupController.createGroup);
router.get('/group-requests/:userId', groupController.getGroupRequests);
router.get('/group-requests', groupController.getGroupRequests);
router.get('/:groupId(\\d+)', groupController.getGroupById);
router.get('/user/:userId', groupController.getGroups);
router.get('/trending', groupController.getTrendingGroups);
router.get('/nearby', groupController.getNearbyGroups);
router.get('/search-by-tags', groupController.searchGroupsByTags);
router.get('/tags/suggestions', groupController.getTagSuggestions);
router.put('/:groupId(\\d+)/name', authMiddleware, groupController.updateGroupName);
router.put('/:groupId(\\d+)/description', authMiddleware, groupController.updateGroupDescription);
router.put('/:groupId(\\d+)/tags', authMiddleware, groupController.updateGroupTags);

// Updated route with authMiddleware
router.put('/:groupId(\\d+)/avatar', authMiddleware, groupController.updateGroupAvatar);

module.exports = router;
<!-- frontend/src/components/chatPage/MessageComponents/MessageChatList.vue -->
<template>
  <TransitionGroup name="message-fade" tag="div">
    <template v-for="(group, index) in groupedMessages" :key="group.id">
      <DateHeader
        v-if="shouldShowDateHeader(index)"
        :date="group.messages[0].rawSentAt || group.messages[0].sent_at"
      />
      <MessageGroup
        :group="group"
        :current-user="currentUser"
        :show-meta="shouldShowMeta(index)"
        @reply="handleReply"
        @react="handleReact"
      />
    </template>
  </TransitionGroup>
</template>

<script setup>
import { computed } from 'vue';
import DateHeader from './DateHeader.vue';
import MessageGroup from './MessageGroup.vue';

const props = defineProps({
  messages: { type: Array, required: true },
  currentUser: { type: Object, required: true },
});
const emit = defineEmits(['reply', 'react']);

// Group messages using the raw timestamp if available; otherwise fallback to sent_at.
const groupedMessages = computed(() => {
  return props.messages.reduce((groups, message) => {
    const lastGroup = groups[groups.length - 1];
    const currentTime = new Date(message.rawSentAt || message.sent_at);
    if (
      lastGroup &&
      lastGroup.sender_id === message.sender_id &&
      (currentTime - new Date(lastGroup.messages[lastGroup.messages.length - 1].rawSentAt || lastGroup.messages[lastGroup.messages.length - 1].sent_at)) < 300000
    ) {
      lastGroup.messages.push(message);
    } else {
      groups.push({
        id: message.id,
        sender_id: message.sender_id,
        sender_name: message.sender_name,
        sender_avatar: message.sender_avatar,
        messages: [message],
      });
    }
    return groups;
  }, []);
});

// Determine if the meta info (avatar/username) should be shown for this group.
const shouldShowMeta = (index) => {
  if (index === 0) return true;
  const currentGroup = groupedMessages.value[index];
  const previousGroup = groupedMessages.value[index - 1];
  return (
    currentGroup.sender_id !== previousGroup.sender_id ||
    (new Date(currentGroup.messages[0].rawSentAt || currentGroup.messages[0].sent_at) -
      new Date(previousGroup.messages[previousGroup.messages.length - 1].rawSentAt || previousGroup.messages[previousGroup.messages.length - 1].sent_at)) >= 300000
  );
};

// Show a date header if the current group’s date differs from the previous group.
const shouldShowDateHeader = (index) => {
  if (index === 0) return true;
  const currentDate = new Date(groupedMessages.value[index].messages[0].rawSentAt || groupedMessages.value[index].messages[0].sent_at).toDateString();
  const previousDate = new Date(groupedMessages.value[index - 1].messages[0].rawSentAt || groupedMessages.value[index - 1].messages[0].sent_at).toDateString();
  return currentDate !== previousDate;
};

const handleReply = (message) => {
  emit('reply', message);
};

const handleReact = (message) => {
  emit('react', message);
};
</script>

<style scoped>
.message-fade-enter-active,
.message-fade-leave-active {
  transition: all 0.3s ease;
}
.message-fade-enter-from,
.message-fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>

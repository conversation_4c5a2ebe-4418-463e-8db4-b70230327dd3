// frontend/src/composables/useGroup/useGroupChatApi.js
import axios from '@/api/axios';

export default function useGroupChatApi() {
  const fetchGroupMessages = async (groupId, page, pageSize, isWorldChat) => {
    const endpoint = isWorldChat
      ? '/world-chat/messages'
      : `/api/group-chat/${groupId}/messages`;
    const response = await axios.get(endpoint, {
      params: { page, pageSize },
    });
    return response.data; // Return the full response { messages, isWorldChat }
  };

  const sendGroupMessage = async (groupId, message, replyId, media) => {
    const payload = { groupId, message, replyId, media };
    const response = await axios.post('/api/group-chat/send', payload);
    return response.data.chatMessage;
  };

  const starMessage = async (groupId, messageId, action) => {
    const url = `/api/group-chat/${groupId}/messages/${messageId}/${action}`;
    const method = action === 'star' ? 'post' : 'delete';
    await axios[method](url);
  };

  const editMessage = async (messageId, newContent) => {
    const response = await axios.put(`/api/group-chat/messages/${messageId}`, { message: newContent });
    return response.data.chatMessage;
  };

  const deleteMessage = async (messageId) => {
    const response = await axios.delete(`/api/group-chat/messages/${messageId}`);
    return response.data;
  };

  const muteUser = (groupId, userId) => axios.post(`/api/group-chat/${groupId}/mute/${userId}`);

  const getUserById = async (userId) => {
    const response = await axios.get(`/api/users/${userId}`);
    return response.data;
  };

  const checkIfMuted = async (groupId, userId) => {
    const response = await axios.get(`/api/group-chat/${groupId}/mute-status/${userId}`);
    return response.data.isMuted;
  };

  return {
    fetchGroupMessages,
    sendGroupMessage,
    starMessage,
    editMessage,
    deleteMessage,
    muteUser,
    getUserById,
    checkIfMuted,
  };
}
<template>
  <div ref="pickerRef" class="emoji-picker">
    <div class="emoji-list">
      <span 
        v-for="(emoji, index) in emojis" 
        :key="index" 
        class="emoji-item" 
        @click="selectEmoji(emoji)">
        {{ emoji }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

// Define a list of emojis to display
const emojis = ref([
  '😀','😃','😄','😁','😆','😅','😂','🤣','😊','😇',
  '🙂','🙃','😉','😌','😍','🥰','😘','😗','😙','😚',
  '😋','😜','😝','😛','🤑','🤗','🤭','🤫','🤔','🤐'
]);

// Declare the events that can be emitted
const emit = defineEmits(['emojiSelected', 'closePicker']);

// Reference to the emoji picker container
const pickerRef = ref(null);

// Function to handle emoji selection
const selectEmoji = (emoji) => {
  emit('emojiSelected', emoji);
};

// Listen for clicks outside the emoji picker and emit the close event
const handleClickOutside = (event) => {
  if (pickerRef.value && !pickerRef.value.contains(event.target)) {
    emit('closePicker');
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.emoji-picker {
  position: absolute;
  bottom: 100%;
  left: 0;
  background-color: #383a40;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 8px;
}

.emoji-list {
  display: flex;
  flex-wrap: wrap;
}

.emoji-item {
  cursor: pointer;
  margin: 4px;
  font-size: 1.2rem;
  transition: transform 0.1s;
}

.emoji-item:hover {
  transform: scale(1.2);
}
</style>

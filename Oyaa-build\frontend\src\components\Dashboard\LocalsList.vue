<template>
  <div class="locals-container">
    <SearchBar @search="handleSearch" />
    
    <div class="list">
      <!-- Skeleton loading view -->
      <ul v-if="loading" class="locals-list">
        <li v-for="i in 5" :key="i" class="local-item skeleton">
          <div class="avatar skeleton-avatar"></div>
          <div class="local-info">
            <span class="local-name skeleton-text"></span>
            <span class="local-description skeleton-text"></span>
          </div>
          <div class="meta">
            <span class="timestamp skeleton-text"></span>
          </div>
        </li>
      </ul>
      
      <!-- Actual locals list or message -->
      <div v-else>
        <div v-if="locals.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </div>
          <p>You don't have any local friends yet.</p>
          <p>Click the action button below to add local friends.</p>
        </div>
        <div v-else-if="filteredLocals.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </div>
          <p>No locals match your search.</p>
        </div>
        <ul v-else class="locals-list">
          <li 
            v-for="local in filteredLocals" 
            :key="local.local_id"
            class="local-item"
            @click="openChat(local)"
          >
            <img class="avatar" :src="getLocalAvatar(local)" alt="Local Avatar" />
            <div class="local-info">
              <span class="local-name">
                {{ local.local_username || local.data?.username || 'Unknown' }}
              </span>
              <span class="local-description">
                {{ getLastMessage(local) }}
              </span>
            </div>
            <div class="meta">
              <span class="timestamp">{{ formatTimestamp(getLastTimestamp(local)) }}</span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { mapGetters, mapActions, mapState } from 'vuex';
import SearchBar from './SearchBar.vue';

export default {
  components: {
    SearchBar
  },
  data() {
    return {
      locals: [],
      loading: false,
      searchQuery: ''
    };
  },
  computed: {
    ...mapGetters('auth', ['user']),
    ...mapState('LocalChat', ['lastChats']),
    filteredLocals() {
      if (!this.searchQuery) return this.locals;
      return this.locals.filter(local => {
        const username = local.local_username || (local.data && local.data.username) || '';
        return username.toLowerCase().includes(this.searchQuery.toLowerCase());
      });
    }
  },
  async created() {
    if (this.user) {
      await this.fetchLocals(this.user.id);
      this.$store.dispatch('LocalChat/initializeSocket');
    }
  },
  methods: {
    ...mapActions('app', ['openTempChat']),
    async fetchLocals(userId) {
      this.loading = true;
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/locals/${userId}`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`,
            },
          }
        );
        if (response.data?.locals?.length) {
          this.locals = response.data.locals;
          this.$store.commit('LocalChat/SET_LAST_CHATS', this.locals);
        } else {
          this.locals = [];
        }
      } catch (error) {
        console.error('Error fetching locals:', error);
        this.locals = [];
      } finally {
        this.loading = false;
      }
    },
    getLocalAvatar(local) {
      return local.avatar || (local.data && local.data.avatar) || '/Avatar/defaultLocal.svg';
    },
    getLastMessage(local) {
      const lastChat = this.lastChats[local.local_id];
      return lastChat ? lastChat.message : (local.last_message || 'No messages yet');
    },
    getLastTimestamp(local) {
      const lastChat = this.lastChats[local.local_id];
      return lastChat ? lastChat.timestamp : (local.last_message_time || null);
    },
    formatTimestamp(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    },
    async openChat(local) {
      const chatFriend = {
        id: local.local_id,
        username: local.local_username || (local.data && local.data.username) || 'Unknown',
        ...local.data,
      };
      await this.openTempChat(chatFriend);
      this.$router.push({ name: 'TempChat', params: { friendId: local.local_id } });
    },
    handleSearch(query) {
      this.searchQuery = query;
    }
  },
};
</script>

<style scoped>
.locals-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 15vh;
}

.locals-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #a0a0a0;
  height: 50vh;
}

.empty-icon {
  margin-bottom: 16px;
  color: #6366f1;
  opacity: 0.7;
}

.empty-state p {
  margin: 4px 0;
  font-size: 15px;
}

.empty-state p:first-of-type {
  color: #e2e2e2;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
}

.local-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  margin: 4px 8px;
}

.local-item:hover {
  background-color: rgba(26, 26, 34, 0.8);
  transform: translateY(-2px);
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 12px;
  flex-shrink: 0;
  object-fit: cover;
  border: 1px solid rgba(46, 46, 58, 0.8);
  background-color: #1a1a22;
}

.local-info {
  flex: 1;
  min-width: 0;
  margin-right: 12px;
}

.local-name {
  display: block;
  color: #e2e2e2;
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 4px;
}

.local-description {
  display: block;
  color: #a0a0a0;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.meta {
  display: flex;
  align-items: center;
}

.timestamp {
  font-size: 12px;
  color: #a0a0a0;
}

/* Skeleton loading styles */
.skeleton {
  animation: pulse 1.5s infinite;
}

.skeleton-avatar {
  background-color: rgba(26, 26, 34, 0.8);
  border: 1px solid rgba(46, 46, 58, 0.8);
}

.skeleton-text {
  height: 1em;
  margin-bottom: 0.5em;
  background-color: rgba(26, 26, 34, 0.8);
  border-radius: 4px;
}

.local-item.skeleton .local-name {
  width: 60%;
}

.local-item.skeleton .local-description {
  width: 80%;
}

.local-item.skeleton .timestamp {
  width: 40px;
  height: 12px;
  border-radius: 4px;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}
</style>
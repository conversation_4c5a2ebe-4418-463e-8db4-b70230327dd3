<!-- frontend/src/components/TempChatPage/MessagesList.vue -->
<template>
    <div class="messages" ref="messagesContainer">
      <div
        v-for="(msg, index) in messages"
        :key="msg.id || index"
        :class="{
          message: true,
          'my-message': msg.sender_id === currentUserId,
          'their-message': msg.sender_id !== currentUserId
        }"
      >
        <p class="message-text">{{ msg.message }}</p>
        <span class="timestamp">{{ formatTimestamp(msg.sent_at) }}</span>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: "MessagesList",
    props: {
      messages: {
        type: Array,
        required: true
      },
      currentUserId: {
        type: [Number, String],
        required: true
      }
    },
    methods: {
      formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString();
      },
      scrollToBottom() {
        this.$nextTick(() => {
          const container = this.$refs.messagesContainer;
          if (container) {
            container.scrollTop = container.scrollHeight;
          }
        });
      }
    },
    watch: {
      messages: {
        handler() {
          this.scrollToBottom();
        },
        deep: true
      }
    },
    mounted() {
      this.scrollToBottom();
    }
  }
  </script>
  
  <style scoped>
  .messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background-color: #f9f9f9;
  }
  .message {
    margin-bottom: 10px;
    max-width: 70%;
    padding: 10px;
    border-radius: 5px;
    word-wrap: break-word;
  }
  .my-message {
    background-color: #d1e7dd;
    align-self: flex-end;
  }
  .their-message {
    background-color: #f8d7da;
    align-self: flex-start;
  }
  .message-text {
    margin: 0;
  }
  .timestamp {
    display: block;
    font-size: 0.75em;
    color: #666;
    text-align: right;
    margin-top: 5px;
  }
  </style>
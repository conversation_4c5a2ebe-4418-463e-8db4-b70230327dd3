<!-- Oyaa-build/frontend/src/components/LandingPage.vue -->
<template>
  <div class="welcome-container" :class="{ 'dark-mode': isDarkMode }">
    <!-- Particle background -->
    <ParticleBackground :theme="isDarkMode" v-if="animationStage === 3" />

    <!-- Theme toggle -->
    <ThemeToggle @toggle="toggleTheme" :isDarkMode="isDarkMode" />

    <main class="main-content">
      <div class="content-wrapper">
        <!-- Stage 1: Logo Only -->
        <transition name="fade" mode="out-in">
          <div v-if="animationStage === 1" key="logo-only" class="logo-only">
            <img :src="isDarkMode ? '/Oyaalogo-DB.svg' : '/Oyaa-logo-LB.svg'" alt="Oyaa Logo" class="logo" />
          </div>

          <!-- Stage 2: Logo with App Name and Version -->
          <div v-else-if="animationStage === 2" key="logo-with-name" class="logo-with-name">
            <img :src="isDarkMode ? '/Oyaalogo-DB.svg' : '/Oyaa-logo-LB.svg'" alt="Oyaa Logo" class="logo" />
            <h1 class="app-name">Oyaa</h1>
            <p class="version">Version 6.1</p>
          </div>

          <!-- Stage 3: Welcome Content -->
          <div v-else key="welcome-content" class="welcome-content">
            <div class="welcome-header">
              <img :src="isDarkMode ? '/Oyaalogo-DB.svg' : '/Oyaa-logo-LB.svg'" alt="Oyaa Logo" class="logo welcome-logo" />
              <h2>Welcome to Oyaa</h2>
              <p class="tagline">Connect. Share. Experience.</p>
            </div>

            <div class="button-group">
              <button class="sign-in-button" @click="goSignIn">Log in</button>
              <button class="sign-up-button" @click="goSignUp">Sign up</button>
            </div>

            <div class="footer-links">
              <p>By continuing, you agree to our</p>
              <div class="links">
                <router-link to="/terms-of-service">Terms of Service</router-link>
                <span>•</span>
                <router-link to="/privacy-policy">Privacy Policy</router-link>
              </div>
              <p class="copyright">© 2025 Oyaa. All rights reserved.</p>
            </div>
          </div>
        </transition>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import ParticleBackground from '@/components/ParticleBackground.vue';
import ThemeToggle from '@/components/ThemeToggle.vue';

// State
const router = useRouter();
const isDarkMode = ref(true); // Default to dark mode
const animationStage = ref(1);

// Methods
const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value;
};

const goSignIn = () => {
  router.push('/signin');
};

const goSignUp = () => {
  router.push('/signup');
};

// Animation sequence
onMounted(() => {
  document.title = "Oyaa";
  const timer1 = setTimeout(() => {
    animationStage.value = 2;
  }, 1000);
  const timer2 = setTimeout(() => {
    animationStage.value = 3;
  }, 3000);
  return () => {
    clearTimeout(timer1);
    clearTimeout(timer2);
  };
});
</script>

<style scoped>
/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Rubik', sans-serif;
}

.welcome-container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  transition: background-color 0.3s ease;
  background: linear-gradient(to bottom right, #f8fafc, #e2e8f0);
  color: #1e293b;
}

.welcome-container.dark-mode {
  background: linear-gradient(to bottom right, #18181b, #09090b);
  color: #ffffff;
}

/* Main Content */
.main-content {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.content-wrapper {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 28rem;
  padding: 0 1rem;
}

/* Logo Only and Logo with Name */
.logo-only,
.logo-with-name {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  width: 80px;
  height: auto;
}

.app-name {
  font-size: 24px;
  color: #151717;
  margin-top: 10px;
}

.dark-mode .app-name {
  color: #ffffff;
}

.version {
  font-size: 14px;
  color: #888;
  margin-top: 5px;
}

.dark-mode .version {
  color: #a1a1aa;
}

/* Welcome Content */
.welcome-content {
  animation: fadeIn 0.8s forwards;
}

.welcome-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.5rem;
  animation: slideUp 0.5s forwards;
}

.welcome-logo {
  margin-bottom: 1.5rem;
}

.welcome-header h2 {
  font-size: 2.25rem;
  font-weight: 700;
  letter-spacing: -0.025em;
  color: #0f172a;
}

.dark-mode .welcome-header h2 {
  color: #ffffff;
}

.welcome-header .tagline {
  margin-top: 0.5rem;
  text-align: center;
  color: #475569;
  animation: fadeIn 0.5s 0.2s forwards;
  opacity: 0;
}

.dark-mode .welcome-header .tagline {
  color: #a1a1aa;
}

/* Button Group */
.button-group {
  margin-top: 1.5rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: slideUp 0.5s 0.3s forwards;
  opacity: 0;
  transform: translateY(10px);
}

.button-group button {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sign-in-button {
  background-color: #151717;
  color: #ffffff;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  animation: lightPulse 2s infinite 1s;
}

.dark-mode .sign-in-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  backdrop-filter: blur(8px);
  animation: darkPulse 2s infinite 1s;
}

.sign-in-button:hover {
  background-color: #292929;
  transform: scale(1.02);
}

.dark-mode .sign-in-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.sign-up-button {
  background-color: transparent;
  color: #151717;
  border: 1px solid #94a3b8;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.dark-mode .sign-up-button {
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(8px);
}

.sign-up-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: #64748b;
  transform: scale(1.02);
}

.dark-mode .sign-up-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

@keyframes lightPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(30, 41, 59, 0);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(30, 41, 59, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(30, 41, 59, 0);
  }
}

@keyframes darkPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* Footer Links */
.footer-links {
  margin-top: 2rem;
  text-align: center;
  font-size: 0.75rem;
  animation: fadeIn 0.5s 0.7s forwards;
  opacity: 0;
}

.footer-links p {
  color: #64748b;
}

.dark-mode .footer-links p {
  color: #52525b;
}

.footer-links .links {
  margin-top: 0.25rem;
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.footer-links a {
  color: #475569;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.dark-mode .footer-links a {
  color: #71717a;
}

.footer-links a:hover {
  color: #1e293b;
}

.dark-mode .footer-links a:hover {
  color: #a1a1aa;
}

.footer-links span {
  color: #64748b;
}

.dark-mode .footer-links span {
  color: #3f3f46;
}

.copyright {
  margin-top: 1rem;
  color: #94a3b8 !important;
}

.dark-mode .copyright {
  color: #3f3f46 !important;
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .content-wrapper {
    padding: 0 1.5rem;
  }

  .logo {
    width: 60px;
  }

  .app-name {
    font-size: 20px;
  }

  .welcome-header h2 {
    font-size: 1.75rem;
  }

  .button-group button {
    padding: 0.65rem 0.85rem;
    font-size: 0.9rem;
  }
}
</style>
// backend/controllers/Local/tempChatController.js
const tempChatService = require('../../services/Local/tempChatService');
const localService = require('../../services/Local/localService');
const userService = require('../../services/User/userService');

class TempChatController {
  async send(req, res) {
    const { senderId, receiverId, message } = req.body;
    try {
      const sentMessage = await tempChatService.sendMessage(senderId, receiverId, message);
      res.status(200).json({ message: 'Message sent successfully', data: sentMessage });
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: err.message });
    }
  }

  async retrieve(req, res) {
    const { userId, targetUserId } = req.query;
    try {
      const messages = await tempChatService.getMessages(userId, targetUserId);
      res.status(200).json(messages);
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: err.message });
    }
  }

  // New method to initiate a chat and add users as locals
  async initiateChat(req, res) {
    const { userId, targetUserId } = req.body;

    if (!userId || !targetUserId) {
      return res.status(400).json({ error: 'Missing userId or targetUserId' });
    }

    try {
      // Fetch user details to include usernames
      const currentUser = await userService.getUserById(userId);
      const targetUser = await userService.getUserById(targetUserId);

      if (!currentUser || !targetUser) {
        return res.status(404).json({ error: 'One or both users not found' });
      }

      // Add target user as a local for the current user
      await localService.addOrUpdateLocal(userId, {
        id: targetUserId,
        username: targetUser.username,
      });

      // Add current user as a local for the target user
      await localService.addOrUpdateLocal(targetUserId, {
        id: userId,
        username: currentUser.username,
      });

      res.status(200).json({ message: 'Chat initiated and locals added successfully' });
    } catch (err) {
      console.error('Error initiating chat:', err);
      res.status(500).json({ error: 'Failed to initiate chat: ' + err.message });
    }
  }
}

module.exports = new TempChatController();
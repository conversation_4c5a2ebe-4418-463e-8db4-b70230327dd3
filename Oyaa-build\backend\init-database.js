// init-database.js
require('dotenv').config();
const { Client } = require('pg');

// Database configuration
const client = new Client({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  ssl: {
    rejectUnauthorized: false, // Allow self-signed certificates
  },
});

// Connect to the database
async function initDatabase() {
  try {
    await client.connect();
    console.log('✅ Database connected successfully!');

    // Working with a fresh database, so we'll skip the reset steps
    console.log('Working with a fresh database...');

    // Now create the tables
    console.log('Creating database tables...');

    // Install PostGIS extension for geographic data
    await client.query(`CREATE EXTENSION IF NOT EXISTS postgis;`);
    console.log('✅ PostGIS extension installed');

    // Users table
    await client.query(`
      CREATE TABLE users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        avatar VARCHAR(255),
        description TEXT,
        tags TEXT[],
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Users table created');

    // Location table
    await client.query(`
      CREATE TABLE locations (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        latitude DOUBLE PRECISION,
        longitude DOUBLE PRECISION,
        location GEOGRAPHY(POINT, 4326),
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id)
      );
    `);
    console.log('✅ Locations table created');

    // Friends table
    await client.query(`
      CREATE TABLE friends (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        friend_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, friend_id)
      );
    `);
    console.log('✅ Friends table created');

    // Friend requests table
    await client.query(`
      CREATE TABLE friend_requests (
        id SERIAL PRIMARY KEY,
        sender_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        receiver_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        status VARCHAR(20) DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(sender_id, receiver_id)
      );
    `);
    console.log('✅ Friend requests table created');

    // Chats table
    await client.query(`
      CREATE TABLE chats (
        id SERIAL PRIMARY KEY,
        sender_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        receiver_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        message TEXT,
        sent_at TIMESTAMP,
        read_at TIMESTAMP,
        reply_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Chats table created');

    // Add self-reference after table creation
    await client.query(`
      ALTER TABLE chats
      ADD CONSTRAINT chats_reply_id_fkey
      FOREIGN KEY (reply_id) REFERENCES chats(id) ON DELETE SET NULL;
    `);

    // Groups table
    await client.query(`
      CREATE TABLE groups (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        creator_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
        description TEXT,
        avatar VARCHAR(255),
        is_world_chat BOOLEAN DEFAULT FALSE,
        latitude DOUBLE PRECISION,
        longitude DOUBLE PRECISION,
        location GEOGRAPHY(POINT, 4326),
        tags TEXT[],
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Groups table created');

    // Group members table
    await client.query(`
      CREATE TABLE group_members (
        id SERIAL PRIMARY KEY,
        group_id INTEGER REFERENCES groups(id) ON DELETE CASCADE,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        role VARCHAR(20) DEFAULT 'member',
        joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(group_id, user_id)
      );
    `);
    console.log('✅ Group members table created');

    // Group chats table
    await client.query(`
      CREATE TABLE group_chats (
        id SERIAL PRIMARY KEY,
        group_id INTEGER REFERENCES groups(id) ON DELETE CASCADE,
        sender_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        message TEXT,
        sent_at TIMESTAMP,
        reply_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Group chats table created');

    // Add self-reference after table creation
    await client.query(`
      ALTER TABLE group_chats
      ADD CONSTRAINT group_chats_reply_id_fkey
      FOREIGN KEY (reply_id) REFERENCES group_chats(id) ON DELETE SET NULL;
    `);

    // Group requests table
    await client.query(`
      CREATE TABLE group_requests (
        id SERIAL PRIMARY KEY,
        group_id INTEGER REFERENCES groups(id) ON DELETE CASCADE,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        status VARCHAR(20) DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(group_id, user_id)
      );
    `);
    console.log('✅ Group requests table created');

    // Media table
    await client.query(`
      CREATE TABLE media (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        chat_id INTEGER REFERENCES chats(id) ON DELETE CASCADE,
        media_url VARCHAR(255) NOT NULL,
        public_id VARCHAR(255),
        media_type VARCHAR(50) NOT NULL,
        thumbnail_url VARCHAR(255),
        duration INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Media table created');

    // Group media table
    await client.query(`
      CREATE TABLE group_media (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        group_chat_id INTEGER REFERENCES group_chats(id) ON DELETE CASCADE,
        media_url VARCHAR(255) NOT NULL,
        public_id VARCHAR(255),
        media_type VARCHAR(50) NOT NULL,
        thumbnail_url VARCHAR(255),
        duration INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Group media table created');

    // Locals table
    await client.query(`
      CREATE TABLE locals (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        local_id VARCHAR(100) NOT NULL,
        local_username VARCHAR(100) NOT NULL,
        data JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, local_id)
      );
    `);
    console.log('✅ Locals table created');

    // Temp chats table
    await client.query(`
      CREATE TABLE temp_chats (
        id SERIAL PRIMARY KEY,
        sender_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        receiver_id VARCHAR(100) NOT NULL,
        message TEXT,
        sent_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Temp chats table created');

    // Temp groups table
    await client.query(`
      CREATE TABLE temp_groups (
        id SERIAL PRIMARY KEY,
        creator_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        group_name VARCHAR(100) NOT NULL,
        category VARCHAR(50),
        description TEXT,
        link_token VARCHAR(100) UNIQUE NOT NULL,
        status VARCHAR(20) DEFAULT 'active',
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ Temp groups table created');

    // Message delivery status table
    await client.query(`
      CREATE TABLE message_delivery_status (
        id SERIAL PRIMARY KEY,
        message_id INTEGER NOT NULL,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        status VARCHAR(20) NOT NULL, -- 'sent', 'delivered'
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(message_id, user_id)
      );

      -- Create indexes for faster queries
      CREATE INDEX idx_message_delivery_status_message_id ON message_delivery_status(message_id);
      CREATE INDEX idx_message_delivery_status_user_id ON message_delivery_status(user_id);
    `);
    console.log('✅ Message delivery status table created');

    // Create World Chat group
    await client.query(`
      INSERT INTO groups (id, name, creator_id, description, is_world_chat)
      VALUES (1, 'World Chat', NULL, 'Just have fun', TRUE);
    `);
    console.log('✅ World Chat group created');

    console.log('✅ Database initialization completed successfully!');
  } catch (err) {
    console.error('❌ Database initialization error:', err);
  } finally {
    await client.end();
  }
}

initDatabase();

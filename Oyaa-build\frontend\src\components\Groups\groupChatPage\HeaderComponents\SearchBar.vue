<template>
    <div class="search-container">
      <div class="search-input-wrapper">
        <i class="fas fa-search search-icon"></i>
        <input
          type="text"
          class="search-input"
          placeholder="Search in conversation..."
          ref="searchInput"
          v-model="localSearchQuery"
          @input="handleSearch"
          @keydown.esc="cancelSearch"
          aria-label="Search in conversation"
        />
        <button
          v-if="localSearchQuery"
          class="clear-button"
          @click="clearSearch"
          aria-label="Clear search"
        >
          <i class="fas fa-trash"></i>
        </button>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, watch, nextTick } from 'vue';
  
  const props = defineProps({
    searchQuery: {
      type: String,
      default: '',
    },
  });
  
  const emit = defineEmits(['update:searchQuery', 'search', 'clear', 'cancel']);
  
  const localSearchQuery = ref(props.searchQuery);
  const searchInput = ref(null);
  
  // Sync prop with local value
  watch(() => props.searchQuery, (newVal) => {
    localSearchQuery.value = newVal;
  });
  
  // Focus input when mounted
  watch(() => true, async () => {
    await nextTick();
    searchInput.value?.focus();
  }, { immediate: true });
  
  const handleSearch = () => {
    emit('update:searchQuery', localSearchQuery.value);
    emit('search', localSearchQuery.value);
  };
  
  const clearSearch = () => {
    localSearchQuery.value = '';
    emit('update:searchQuery', '');
    emit('clear');
    nextTick(() => {
      searchInput.value?.focus();
    });
  };
  
  const cancelSearch = () => {
    emit('cancel');
  };
  </script>
  
  <style scoped>
  .search-container {
    flex: 1;
    display: flex;
    align-items: center;
  }
  
  .search-input-wrapper {
    position: relative;
    width: 100%;
  }
  
  .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 16px;
    pointer-events: none;
  }
  
  .search-input {
    width: 100%;
    background-color: var(--bg-tertiary);
    border: none;
    border-radius: 6px;
    padding: 10px 32px 10px 32px;
    color: var(--text-primary);
    font-size: 14px;
  }
  
  .search-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  }
  
  .search-input::placeholder {
    color: var(--text-secondary);
  }
  
  .clear-button {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .clear-button i {
    font-size: 16px;
  }
  
  .clear-button:hover i {
    color: var(--text-primary);
  }
  </style>
// frontend/src/composables/useFriendNotifications.js
import { io } from 'socket.io-client';
// Import the store instance directly
import store from '@/store';
import { ref } from 'vue';
import axios from 'axios';

// Function to play notification sound
function playNotificationSound() {
  const audio = new Audio('/sounds/mixkit-software-interface-start-2574.mp3');
  audio.preload = 'auto';
  audio.load();
  audio.play().catch((error) => {
    console.error('Error playing notification sound:', error);
  });
}

export default function useFriendNotifications() {
  // Create refs to store friend requests
  const friendRequests = ref([]);
  const sentRequests = ref([]);

  // Get the token
  const token = localStorage.getItem('token');

  // Create the socket instance for the /friend-requests namespace
  const socket = io(`${import.meta.env.VITE_API_BASE_URL}/friend-requests`, {
    transports: ['websocket', 'polling'], // Allow both WebSocket and polling
    withCredentials: true,
    auth: {
      token: token
    },
    query: {
      token: token // Also provide token as query parameter for fallback
    }
  });

  console.log('Connecting to WebSocket at:', `${import.meta.env.VITE_API_BASE_URL}/friend-requests`);

  // Once connected, join the room for the current user
  socket.on('connect', () => {
    console.log('Friend Requests WebSocket connection established');
    const currentUserId = store.getters['auth/userId'];
    if (currentUserId) {
      socket.emit('join', currentUserId);
      // Request initial friend requests list
      socket.emit('getRequests', { userId: currentUserId });

      // Emit a custom event that components can listen for
      const connectEvent = new CustomEvent('friendsocket:connected');
      window.dispatchEvent(connectEvent);
    } else {
      console.error('User ID not found in store during join');
    }
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('Friend Requests WebSocket disconnected');
    // Emit a custom event that components can listen for
    const disconnectEvent = new CustomEvent('friendsocket:disconnected');
    window.dispatchEvent(disconnectEvent);
  });

  // Handle connection error
  socket.on('connect_error', (error) => {
    console.error('Friend Requests WebSocket connection error:', error);
    // Emit a custom event that components can listen for
    const errorEvent = new CustomEvent('friendsocket:error', { detail: error });
    window.dispatchEvent(errorEvent);
  });

  // Function to set up listeners and pass incoming events to the provided callback
  const setupFriendWebSocket = (callback) => {
    // When a new friend request is created:
    socket.on('NEW_FRIEND_REQUEST', (data) => {
      console.log('New friend request received:', data);
      data.type = 'NEW_FRIEND_REQUEST';
      callback(data);

      // If this is a request for the current user, add it to the list
      if (data.receiverId === store.getters['auth/userId']) {
        friendRequests.value.unshift(data.request);

        // Create a notification for the GlobalNotificationCenter
        const notification = {
          id: `fr_${data.request.id}`,
          type: 'friendRequest',
          message: `${data.request.sender_username} sent you a friend request`,
          timestamp: new Date().toISOString(),
          avatar: data.request.sender_avatar,
          sender: {
            id: data.request.sender_id,
            name: data.request.sender_username
          },
          data: data.request
        };

        // Add the notification to the store
        store.commit('app/ADD_NOTIFICATION', notification);

        // Play notification sound
        playNotificationSound();

        // Also show a toast notification
        store.dispatch('app/showToast', {
          message: `New friend request from ${data.request.sender_username}`,
          type: 'info',
          duration: 5000
        });
      }
    });

    // When a friend request status is updated (e.g., accepted or rejected):
    socket.on('FRIEND_REQUEST_UPDATE', (data) => {
      console.log('Friend request status updated:', data);
      data.type = 'FRIEND_REQUEST_UPDATE';
      callback(data);

      // Update the request in our local list
      const index = friendRequests.value.findIndex(req => req.id === data.request.id);
      if (index !== -1) {
        friendRequests.value[index].status = data.request.status;

        // If this is the sender of the request, show a notification about the status change
        if (data.senderId === store.getters['auth/userId']) {
          const requestStatus = data.request.status;
          const statusText = requestStatus === 'accepted' ? 'accepted' : 'rejected';
          const toastType = requestStatus === 'accepted' ? 'success' : 'info';

          // Play notification sound for status updates
          playNotificationSound();

          // Show toast notification
          store.dispatch('app/showToast', {
            message: `Your friend request was ${statusText}`,
            type: toastType,
            duration: 5000
          });
        }

        // Remove the request after a delay if it was accepted or rejected
        if (data.request.status === 'accepted' || data.request.status === 'rejected') {
          setTimeout(() => {
            friendRequests.value = friendRequests.value.filter(req => req.id !== data.request.id);
          }, 1000);
        }
      }
    });

    // When receiving the initial list of friend requests
    socket.on('FRIEND_REQUESTS_LIST', (data) => {
      console.log('Received friend requests list:', data);

      // Handle received requests (for the receiver)
      if (data.requests) {
        friendRequests.value = data.requests;
      }

      // Handle sent requests (for the sender)
      if (data.sentRequests) {
        // Create a custom event with type for the callback to handle
        const sentRequestsData = {
          type: 'FRIEND_REQUESTS_LIST',
          sentRequests: data.sentRequests
        };
        callback(sentRequestsData);
      }
    });

    // Handle the new dedicated sent requests list event
    socket.on('SENT_REQUESTS_LIST', (data) => {
      console.log('Received SENT_REQUESTS_LIST event:', data);

      if (data.sentRequests) {
        // Update the sentRequests ref
        sentRequests.value = data.sentRequests;

        // Also pass to callback for components to handle
        const sentRequestsData = {
          type: 'SENT_REQUESTS_LIST',
          sentRequests: data.sentRequests
        };
        callback(sentRequestsData);
      }
    });

    // Handle manual updates (from HTTP fallbacks)
    socket.on('manualUpdate', (data) => {
      console.log('Received manual update:', data);
      callback(data);

      // Update the request in our local list
      if (data.type === 'FRIEND_REQUEST_UPDATE') {
        const index = friendRequests.value.findIndex(req => req.id === data.request.id);
        if (index !== -1) {
          friendRequests.value[index].status = data.request.status;

          // Remove the request after a delay if it was accepted or rejected
          if (data.request.status === 'accepted' || data.request.status === 'rejected') {
            setTimeout(() => {
              friendRequests.value = friendRequests.value.filter(req => req.id !== data.request.id);
            }, 1000);
          }
        }
      }
    });

    socket.on('disconnect', () => {
      console.log('Friend Requests WebSocket disconnected');
    });

    socket.on('error', (error) => {
      console.error('Friend Requests WebSocket error:', error);
    });

    // Return a cleanup function if needed
    return () => {
      console.log('Cleaning up Friend Requests WebSocket connection...');
      socket.disconnect();
    };
  };

  // Function to get all sent friend requests (including all statuses)
  const getAllSentRequests = () => {
    const userId = store.getters['auth/userId'];
    console.log(`Getting all sent friend requests for user ${userId} via WebSocket`);

    return new Promise((resolve, reject) => {
      socket.emit('getAllSentRequests', { userId }, (response) => {
        if (response.success) {
          console.log('All sent friend requests received successfully:', response);
          resolve(response);
        } else {
          console.error('Error getting all sent friend requests:', response.error);
          reject(new Error(response.error));
        }
      });
    });
  };

  // Function to send a friend request via WebSocket
  const sendFriendRequest = (receiverId) => {
    const senderId = store.getters['auth/userId'];
    console.log(`Sending friend request from ${senderId} to ${receiverId} via WebSocket`);

    return new Promise((resolve, reject) => {
      socket.emit('sendFriendRequest', { senderId, receiverId }, (response) => {
        if (response.success) {
          console.log('Friend request sent successfully:', response);
          resolve(response);
        } else {
          console.error('Error sending friend request:', response.error);
          reject(new Error(response.error));
        }
      });
    });
  };

  // Track requests that are being processed to prevent duplicates
  const processingRequests = new Set();

  // Function to accept a friend request via WebSocket with HTTP fallback
  const acceptFriendRequest = (requestId) => {
    console.log(`Accepting friend request ${requestId} via WebSocket`);

    // Check if this request is already being processed
    if (processingRequests.has(`accept-${requestId}`)) {
      console.log(`Request ${requestId} is already being processed, ignoring duplicate call`);
      return Promise.reject(new Error('Request already being processed'));
    }

    // Mark this request as being processed
    processingRequests.add(`accept-${requestId}`);

    return new Promise((resolve, reject) => {
      // Track if the request has been resolved or rejected
      let isHandled = false;

      // Function to handle response
      const handleResponse = (response, source) => {
        if (isHandled) return; // Prevent handling multiple times
        isHandled = true;

        // Remove from processing set
        processingRequests.delete(`accept-${requestId}`);

        if (response.success) {
          console.log(`Friend request accepted successfully via ${source}:`, response);
          resolve(response);
        } else {
          console.error(`Error accepting friend request via ${source}:`, response.error);
          reject(new Error(response.error));
        }
      };

      // Try WebSocket first
      socket.emit('acceptFriendRequest', { requestId }, (response) => {
        handleResponse(response, 'WebSocket');
      });

      // Set a timeout to try HTTP if WebSocket doesn't respond
      setTimeout(async () => {
        if (isHandled) return; // Already handled by WebSocket

        console.log('WebSocket request timed out, falling back to HTTP');

        try {
          // Fallback to HTTP request
          const response = await axios.post(
            `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/accept/${requestId}`,
            {},
            { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
          );

          const result = response.data;

          // Manually emit a FRIEND_REQUEST_UPDATE event to update the UI
          const updatePayload = {
            type: 'FRIEND_REQUEST_UPDATE',
            request: {
              id: requestId,
              status: 'accepted',
              // Add other fields if available
              sender_id: result.sender_id,
              receiver_id: result.receiver_id
            },
            senderId: result.sender_id,
            receiverId: result.receiver_id,
          };

          // Emit the event to trigger the same handlers as WebSocket would
          socket.emit('manualUpdate', updatePayload);

          // Process the event locally
          const index = friendRequests.value.findIndex(req => req.id === requestId);
          if (index !== -1) {
            friendRequests.value[index].status = 'accepted';

            // Remove the request after a delay
            setTimeout(() => {
              friendRequests.value = friendRequests.value.filter(req => req.id !== requestId);
            }, 1000);
          }

          handleResponse({ success: true, result: response.data }, 'HTTP');
        } catch (error) {
          console.error('HTTP fallback failed:', error);
          handleResponse({ success: false, error: error.message }, 'HTTP');
        }
      }, 3000); // 3 second timeout before trying HTTP

      // Set a final timeout to clean up if neither method works
      setTimeout(() => {
        if (!isHandled) {
          processingRequests.delete(`accept-${requestId}`);
          reject(new Error('Request timed out after all attempts'));
        }
      }, 15000); // 15 second total timeout
    });
  };

  // Function to reject a friend request via WebSocket with HTTP fallback
  const rejectFriendRequest = (requestId) => {
    console.log(`Rejecting friend request ${requestId} via WebSocket`);

    // Check if this request is already being processed
    if (processingRequests.has(`reject-${requestId}`)) {
      console.log(`Request ${requestId} is already being processed, ignoring duplicate call`);
      return Promise.reject(new Error('Request already being processed'));
    }

    // Mark this request as being processed
    processingRequests.add(`reject-${requestId}`);

    return new Promise((resolve, reject) => {
      // Track if the request has been resolved or rejected
      let isHandled = false;

      // Function to handle response
      const handleResponse = (response, source) => {
        if (isHandled) return; // Prevent handling multiple times
        isHandled = true;

        // Remove from processing set
        processingRequests.delete(`reject-${requestId}`);

        if (response.success) {
          console.log(`Friend request rejected successfully via ${source}:`, response);
          resolve(response);
        } else {
          console.error(`Error rejecting friend request via ${source}:`, response.error);
          reject(new Error(response.error));
        }
      };

      // Try WebSocket first
      socket.emit('rejectFriendRequest', { requestId }, (response) => {
        handleResponse(response, 'WebSocket');
      });

      // Set a timeout to try HTTP if WebSocket doesn't respond
      setTimeout(async () => {
        if (isHandled) return; // Already handled by WebSocket

        console.log('WebSocket request timed out, falling back to HTTP');

        try {
          // Fallback to HTTP request
          const response = await axios.post(
            `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/reject/${requestId}`,
            {},
            { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
          );

          const result = response.data;

          // Manually emit a FRIEND_REQUEST_UPDATE event to update the UI
          const updatePayload = {
            type: 'FRIEND_REQUEST_UPDATE',
            request: {
              id: requestId,
              status: 'rejected',
              // Add other fields if available
              sender_id: result.sender_id,
              receiver_id: result.receiver_id
            },
            senderId: result.sender_id,
            receiverId: result.receiver_id,
          };

          // Emit the event to trigger the same handlers as WebSocket would
          socket.emit('manualUpdate', updatePayload);

          // Process the event locally
          const index = friendRequests.value.findIndex(req => req.id === requestId);
          if (index !== -1) {
            friendRequests.value[index].status = 'rejected';

            // Remove the request after a delay
            setTimeout(() => {
              friendRequests.value = friendRequests.value.filter(req => req.id !== requestId);
            }, 1000);
          }

          handleResponse({ success: true, result: response.data }, 'HTTP');
        } catch (error) {
          console.error('HTTP fallback failed:', error);
          handleResponse({ success: false, error: error.message }, 'HTTP');
        }
      }, 3000); // 3 second timeout before trying HTTP

      // Set a final timeout to clean up if neither method works
      setTimeout(() => {
        if (!isHandled) {
          processingRequests.delete(`reject-${requestId}`);
          reject(new Error('Request timed out after all attempts'));
        }
      }, 15000); // 15 second total timeout
    });
  };

  return {
    setupFriendWebSocket,
    sendFriendRequest,
    acceptFriendRequest,
    rejectFriendRequest,
    getAllSentRequests, // Add the new function
    friendRequests,
    sentRequests, // Expose the sent requests ref
    socket // Expose the socket for direct access
  };
}

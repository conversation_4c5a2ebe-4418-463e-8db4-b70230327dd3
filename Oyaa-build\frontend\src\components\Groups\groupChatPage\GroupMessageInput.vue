<template>
  <div class="group-message-input" ref="messageInputContainer">
    <!-- Compressing and Uploading indicators -->
    <div v-if="isCompressing" class="upload-indicator">
      <span class="spinner"></span>
      <span class="upload-text">Compressing media...</span>
    </div>
    <div v-else-if="isUploading" class="upload-indicator">
      <span class="spinner"></span>
      <span class="upload-text">Uploading media...</span>
    </div>
    
    <!-- Attachment preview -->
    <div class="attachment-preview-container" :class="{ 'hidden': !selectedFile }">
      <GroupMediaAttachments ref="mediaAttachments" @file-changed="handleFileChanged" />
    </div>
    
    <!-- Reply preview -->
    <div v-if="replyMessage" class="reply-preview-container">
      <ReplyPreview :reply="replyMessage" @cancel-reply="cancelReply" />
    </div>
    
    <!-- Main input container -->
    <div
      class="message-input-container"
      @dragover.prevent
      @dragenter.prevent="handleDragEnter"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <div v-if="inputMode === 'text'" class="message-input-wrapper" :class="{ 'focused': isFocused, 'drag-over': isDragging }">
        <div class="input-area">
          <!-- Attach button -->
          <button class="icon-button attach-button" @click="handleAttach" title="Attach file">
            <i class="fas fa-paperclip"></i>
          </button>
          
          <!-- Text input area -->
          <div class="textarea-container">
            <ChatTextArea
              v-model="message"
              @input="handleTyping"
              @focus="handleInputFocus"
              @blur="handleInputBlur"
              @paste-image="handlePastedImage"
              @send="handleSendMessage"
              :placeholder="isMuted ? 'You are muted in this group' : 'Type a message...'"
              :disabled="isMuted"
            />
          </div>
          
          <!-- Action buttons -->
          <div class="action-buttons">
            <button 
              v-if="!message.trim() && !selectedFile" 
              class="icon-button mic-button" 
              @click="switchToVoiceMode" 
              title="Voice message"
              :disabled="isMuted"
            >
              <i class="fas fa-microphone"></i>
            </button>
            
            <button 
              class="icon-button emoji-button" 
              @click="toggleEmojiPicker" 
              title="Emoji"
              :disabled="isMuted"
            >
              <i class="fas fa-smile"></i>
            </button>
            
            <button 
              v-if="message.trim() || selectedFile" 
              class="icon-button send-button" 
              @click="handleSendMessage" 
              title="Send"
              :disabled="isMuted || isOverCharLimit"
            >
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Voice input mode -->
      <div v-else-if="inputMode === 'voice'" class="voice-input-container">
        <GroupVoice 
          @sendVoiceNote="handleSendVoiceNote" 
          @cancel="switchToTextMode" 
        />
      </div>
      
      <!-- Character limit indicator -->
      <div v-if="inputMode === 'text' && message.length > 1500" class="character-limit">
        <span :class="{ 'limit-exceeded': message.length > 1900 }">
          {{ message.length }}/2000
        </span>
      </div>
    </div>
    
    <!-- Upload indicator -->
    <div v-if="isUploading" class="upload-indicator">
      <span class="spinner"></span>
      <span class="upload-text">Uploading media...</span>
    </div>
    
    <!-- In template section, replace EmojiPicker with GroupEmojiPicker -->
    <Teleport to="body">
      <div v-if="showEmojiPicker" 
           class="emoji-picker-teleport-container" 
           :style="emojiPickerPosition"
           @click.stop>
        <GroupEmojiPicker 
          @select="handleEmojiSelect"
          @close="showEmojiPicker = false"
        />
      </div>
    </Teleport>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import ChatTextArea from './MessageInputComponents/ChatTextArea.vue';
import GroupMediaAttachments from './GroupMediaAttachments.vue';
import GroupEmojiPicker from './MessageInputComponents/GroupEmojiPicker.vue';
import GroupVoice from './GroupVoice.vue';
import ReplyPreview from './MessageInputComponents/ReplyPreview.vue';
import groupSocket from '@/groupSocket';
import { uploadGroupMedia } from '@/services/groupMediaService';
import { mapState } from 'vuex';

export default {
  name: 'GroupMessageInput',
  components: {
    ChatTextArea,
    GroupEmojiPicker,
    GroupMediaAttachments,
    GroupVoice,
    ReplyPreview,
  },
  props: {
    group: { type: Object, required: true },
    replyMessage: { type: Object, default: null },
    isMuted: { type: Boolean, default: false },
    isUploading: { type: Boolean, default: false },
  },
  setup() {
    const messageInputContainer = ref(null);
    const isKeyboardVisible = ref(false);
    const viewportHeight = ref(window.innerHeight);
    const initialViewportHeight = ref(window.innerHeight);
    
    // Handle viewport resize and keyboard appearance
    const handleResize = () => {
      const currentHeight = window.innerHeight;
      
      // If height decreased significantly, keyboard is likely visible
      if (initialViewportHeight.value - currentHeight > 150) {
        isKeyboardVisible.value = true;
        
        // On iOS, we need to scroll to the input
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
          setTimeout(() => {
            if (messageInputContainer.value) {
              messageInputContainer.value.scrollIntoView({ behavior: 'smooth' });
            }
          }, 100);
        }
      } else {
        isKeyboardVisible.value = false;
      }
      
      viewportHeight.value = currentHeight;
    };
    
    // Setup event listeners
    onMounted(() => {
      initialViewportHeight.value = window.innerHeight;
      window.addEventListener('resize', handleResize);
      
      // For iOS devices, also listen for orientation changes
      window.addEventListener('orientationchange', () => {
        setTimeout(() => {
          initialViewportHeight.value = window.innerHeight;
        }, 300);
      });
      
      // Fix for iOS Safari: detect when virtual keyboard appears
      if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        document.addEventListener('focusin', () => {
          // Element is focused, keyboard likely visible
          setTimeout(() => {
            if (messageInputContainer.value) {
              window.scrollTo(0, document.body.scrollHeight);
            }
          }, 300);
        });
      }
      
      // Ensure the input is visible on initial load
      setTimeout(() => {
        if (messageInputContainer.value) {
          // Force layout recalculation
          messageInputContainer.value.style.display = 'none';
          void messageInputContainer.value.offsetHeight; // Trigger reflow
          messageInputContainer.value.style.display = '';
        }
      }, 500);
    });
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        document.removeEventListener('focusin', handleResize);
      }
    });
    
    return {
      messageInputContainer,
      isKeyboardVisible,
      viewportHeight
    };
  },
  data() {
    return {
      isCompressing: false,
      message: '',
      showEmojiPicker: false,
      typingTimeout: null,
      selectedFile: null,
      isDragging: false,
      dragCounter: 0,
      inputMode: 'text',
      isFocused: false,
      emojiButtonPosition: { top: 0, left: 0 },
      visibleHeight: window.innerHeight,
    };
  },
  computed: {
    ...mapState({ currentUser: (state) => state.auth.user }),
    isOverCharLimit() {
      return this.message.length > 2000;
    },
    emojiPickerPosition() {
      // Adjust position based on whether we're on mobile and keyboard is visible
      const isMobile = window.innerWidth <= 768;
      
      if (isMobile) {
        // On mobile, position at the bottom of the visible area
        return {
          bottom: '70px',
          left: '2.5%',
          position: 'fixed',
          width: '95%',
          top: 'auto'
        };
      } else {
        // On desktop, position relative to the emoji button
        return {
          top: `${this.emojiButtonPosition.top - 320}px`,
          left: `${this.emojiButtonPosition.left}px`
        };
      }
    }
  },
  methods: {
    handleInputFocus() {
      this.isFocused = true;
      
      // For iOS devices, scroll to make input visible
      if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        setTimeout(() => {
          if (this.$refs.messageInputContainer) {
            this.$refs.messageInputContainer.scrollIntoView({ behavior: 'smooth' });
            
            // Also scroll the window to ensure the input is visible
            window.scrollTo(0, document.body.scrollHeight);
          }
        }, 300);
      }
    },
    
    toggleEmojiPicker() {
      if (this.isMuted) return;
      
      // Get position of emoji button for positioning the picker
      const emojiButton = document.querySelector('.emoji-button');
      if (emojiButton) {
        const rect = emojiButton.getBoundingClientRect();
        
        // Determine if we're on mobile based on screen width
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
          // On mobile, position will be handled by CSS and computed properties
          this.emojiButtonPosition = {
            top: 0,
            left: 0
          };
        } else {
          // On desktop, calculate position based on button
          this.emojiButtonPosition = {
            top: rect.top,
            left: Math.min(rect.left, window.innerWidth - 350) // Prevent overflow to the right
          };
        }
      }
      
      this.showEmojiPicker = !this.showEmojiPicker;
      
      // Add a click event listener to close the picker when clicking outside
      if (this.showEmojiPicker) {
        this.$nextTick(() => {
          document.addEventListener('click', this.closeEmojiPickerOnClickOutside);
        });
      } else {
        document.removeEventListener('click', this.closeEmojiPickerOnClickOutside);
      }
    },
    
    closeEmojiPickerOnClickOutside(event) {
      // Close emoji picker when clicking outside
      if (this.showEmojiPicker && !event.target.closest('.emoji-button') && !event.target.closest('.emoji-picker-container')) {
        this.showEmojiPicker = false;
        document.removeEventListener('click', this.closeEmojiPickerOnClickOutside);
      }
    },
    
    handleInputBlur() {
      this.isFocused = false;
      // Don't close emoji picker on input blur to allow selecting emojis
    },
    
    handleEmojiSelect(selected) {
  if (this.isMuted) return;
  
  switch (selected.type) {
    case 'emoji':
      this.message += selected.data.i;
      break;
    case 'gif':
    case 'sticker':
      this.handleMediaFromUrl(selected.url, selected.type);
      break;
  }
},

    async handleMediaFromUrl(url, type) {
      try {
        const response = await fetch(url);
        const blob = await response.blob();
        const file = new File([blob], `${type}.${type === 'gif' ? 'gif' : 'png'}`, {
          type: blob.type
        });
        
        this.selectedFile = file;
        if (this.$refs.mediaAttachments) {
          this.$refs.mediaAttachments.setFile(file);
        }
      } catch (error) {
        console.error(`Error loading ${type}:`, error);
        alert(`Failed to load ${type}`);
      }
    },
    
    async handleSendMessage() {
      if (this.isMuted || (!this.message.trim() && !this.selectedFile)) return;
      if (this.isOverCharLimit) return;

      if (!this.group?.id || !this.currentUser?.id) {
        console.error('Missing group or user data');
        alert('Cannot send message: missing information');
        return;
      }

      const payload = { message: this.message.trim(), reply: this.replyMessage };

      if (this.selectedFile) {
        try {
          let mediaType;
          if (this.selectedFile.type.startsWith('image/')) {
            mediaType = 'image';
          } else if (this.selectedFile.type.startsWith('video/')) {
            mediaType = 'video';
          } else if (this.selectedFile.type.startsWith('audio/')) {
            mediaType = 'audio'; // Added audio detection
          } else {
            alert('Unsupported file type');
            return;
          }

          this.isCompressing = mediaType === 'image' && this.selectedFile.type !== 'image/gif';
          const response = await uploadGroupMedia(this.selectedFile, this.currentUser.id, this.group.id, mediaType);
          if (response.media) payload.media = response.media;
        } catch (error) {
          console.error('Media upload error:', error);
          alert('Media upload failed.');
          return;
        } finally {
          this.isCompressing = false;
        }
      }

      this.$emit('send-message', payload);
      this.message = '';
      this.selectedFile = null;
      if (this.$refs.mediaAttachments) this.$refs.mediaAttachments.removeFile();
      this.cancelReply();
      this.handleStopTyping();
      this.showEmojiPicker = false;
    },
    
    handleAttach() {
      if (this.isMuted) return;
      if (this.$refs.mediaAttachments?.triggerFileInput) {
        this.$refs.mediaAttachments.triggerFileInput();
      } else {
        console.error('MediaAttachments ref or triggerFileInput method not found');
      }
    },
    
    cancelReply() {
      this.$emit('cancel-reply');
    },
    
    handleTyping() {
      if (this.isMuted) return;
      if (this.typingTimeout) clearTimeout(this.typingTimeout);
      groupSocket.emit('groupTyping', { groupId: this.group.id, username: this.currentUser.username });
      this.typingTimeout = setTimeout(() => this.handleStopTyping(), 2000);
    },
    
    handleStopTyping() {
      groupSocket.emit('groupStopTyping', { groupId: this.group.id, username: this.currentUser.username });
      if (this.typingTimeout) {
        clearTimeout(this.typingTimeout);
        this.typingTimeout = null;
      }
    },
    
    handleFileChanged(file) {
      if (this.isMuted) return;
      this.selectedFile = file;
    },
    
    async handleSendVoiceNote(audioFile) {
      if (this.isMuted) return;
      try {
        const response = await uploadGroupMedia(audioFile, this.currentUser.id, this.group.id, 'audio');
        const payload = { message: '', media: response.media, reply: this.replyMessage };
        this.$emit('send-message', payload);
        this.cancelReply();
        this.switchToTextMode();
      } catch (error) {
        console.error('Voice note upload error:', error);
        alert('Failed to send voice note.');
      }
    },
    
    switchToVoiceMode() {
      if (this.isMuted) return;
      this.inputMode = 'voice';
      this.showEmojiPicker = false;
    },
    
    switchToTextMode() {
      this.inputMode = 'text';
    },
    
    handlePastedImage(file) {
      if (this.isMuted) return;
      if (this.$refs.mediaAttachments) this.$refs.mediaAttachments.setFile(file);
    },
    
    handleDragEnter() {
      if (this.isMuted) return;
      this.dragCounter++;
      if (this.dragCounter === 1) this.isDragging = true;
    },
    
    handleDragLeave() {
      this.dragCounter--;
      if (this.dragCounter === 0) this.isDragging = false;
    },
    
    handleDrop(event) {
      if (this.isMuted) return;
      this.isDragging = false;
      this.dragCounter = 0;
      const files = event.dataTransfer.files;
      if (files.length > 0 && (files[0].type.startsWith('image/') || files[0].type.startsWith('video/') || files[0].type.startsWith('audio/'))) {
        this.$refs.mediaAttachments.setFile(files[0]);
      } else {
        alert('Only image, video, and audio files are allowed.');
      }
    },
  },
  
  beforeUnmount() {
    // Clean up event listeners
    document.removeEventListener('click', this.closeEmojiPickerOnClickOutside);
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
  }
};
</script>

<style scoped>
/* Modern sleek dark theme for message input */
.group-message-input {
 
  padding: 10px 16px;
  
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  width: 100%;
  transform: translateZ(0); /* Force hardware acceleration */
  will-change: transform; /* Optimize for animations */
  box-sizing: border-box;
 
}

.attachment-preview-container,
.reply-preview-container {
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-input-container {
  position: relative;
}

.message-input-wrapper {
  background-color: rgba(255, 255, 255, 0.07);
  border-radius: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
}

.message-input-wrapper.focused {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(52, 152, 219, 0.5);
}

.message-input-wrapper.drag-over {
  background-color: rgba(52, 152, 219, 0.15);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.5);
  border-color: rgba(52, 152, 219, 0.8);
}

.input-area {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  gap: 10px;
   
  backdrop-filter: blur(50px);
  -webkit-backdrop-filter: blur(50px);
  
}

.textarea-container {
  flex-grow: 1;
  min-height: 24px;

  overflow-y: auto;
  padding: 4px 0;
}

.icon-button {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: none;
  color: #a0a0a0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s ease;
  font-size: 16px;
}

.icon-button:hover:not(:disabled) {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.icon-button:active:not(:disabled) {
  transform: scale(0.95) translateY(0);
}

.icon-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.attach-button {
  margin-left: 2px;
  color: #9ca3af;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mic-button {
  color: #9ca3af;
}

.emoji-button {
  color: #fbbf24;
}

.send-button {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.3);
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9, #2573a7);
  color: white;
}

.character-limit {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
  padding-right: 8px;
}

.character-limit span {
  font-size: 12px;
  color: #a0a0a0;
  background: rgba(0, 0, 0, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
}

.character-limit .limit-exceeded {
  color: #f87171;
  background: rgba(248, 113, 113, 0.2);
}

.voice-input-container {
  padding: 12px 0;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  margin: 4px 0;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.attachment-preview-container.hidden {
  display: none;
}

.upload-indicator {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(17, 24, 39, 0.9);
  color: white;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: 20px;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 90%;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .group-message-input {
    padding: 8px 12px;
  }
  
  .message-input-wrapper {
    border-radius: 18px;
  }
  
  .input-area {
    padding: 6px 8px;
    gap: 6px;
  }
  
  .icon-button {
    width: 30px;
    height: 30px;
    font-size: 14px;
  }
  
  .textarea-container {
    min-height: 20px;
    max-height: 100px;
  }
}

/* Extra small screens */
@media (max-width: 360px) {
  .group-message-input {
    padding: 6px 8px;
  }
  
  .message-input-wrapper {
    border-radius: 16px;
  }
  
  .input-area {
    padding: 4px 6px;
    gap: 4px;
  }
  
  .icon-button {
    width: 30px;
    height: 30px;
    font-size: 12px;
  }
}

/* iOS specific fixes */
@supports (-webkit-touch-callout: none) {
  .group-message-input {
    /* Add padding for iOS safe area */
    padding-bottom: max(16px, env(safe-area-inset-bottom, 16px));
  }
}

.emoji-picker-teleport-container {
  position: absolute; /* For desktop positioning */
  z-index: 10000;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
  .emoji-picker-teleport-container {
    position: fixed; /* For mobile, fix to bottom */
    width: 95%;
    left: 2.5% !important;
    right: 2.5% !important;
    transform: none !important;
    bottom: 70px !important;
    top: auto !important;
    border-radius: 16px;
    overflow: hidden;
  }
}

/* Animation for buttons */
.icon-button {
  transition: transform 0.2s ease, background-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
}

/* Pulse animation for send button when active */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
  100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

.send-button:not(:disabled) {
  animation: pulse 2s infinite;
}
</style>
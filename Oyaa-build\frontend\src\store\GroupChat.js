// frontend/src/store/GroupChat.js
export default {
  namespaced: true,
  state: {
    messages: [],
    typingStatuses: {},
    group: null, // Add group to state
    hasMoreMessages: true, // Flag to indicate if there are more messages to load
  },
  mutations: {
    ADD_MESSAGE(state, message) {
      // Check if message already exists to prevent duplicates
      const messageExists = state.messages.some(m => m.id === message.id);

      if (!messageExists) {
        // Add message to the end (newest messages at the bottom)
        state.messages.push(message);
        console.log(`GroupChat store: Added message with ID ${message.id}`);
      } else {
        console.log(`GroupChat store: Skipped duplicate message with ID ${message.id}`);
      }
    },
    SET_MESSAGES(state, messages) {
      state.messages = messages;
    },
    SET_TYPING_STATUS(state, { userId, status }) {
      state.typingStatuses = { ...state.typingStatuses, [userId]: status };
    },
    CLEAR_MESSAGES(state) {
      state.messages = [];
    },
    SET_GROUP(state, group) {
      state.group = group;
    },
    UPDATE_GROUP(state, updatedData) {
      if (state.group && state.group.id === updatedData.groupId) {
        state.group = { ...state.group, ...updatedData };
      }
    },
    SET_HAS_MORE_MESSAGES(state, hasMore) {
      state.hasMoreMessages = hasMore;
    },
  },
  actions: {
    addMessage({ commit, state }, message) {
      // Additional check at the action level to prevent duplicates
      if (!message || !message.id) {
        console.error('GroupChat store: Invalid message received', message);
        return;
      }

      // Check if message already exists in state before committing
      const messageExists = state.messages.some(m => m.id === message.id);
      if (messageExists) {
        console.log(`GroupChat store: Action skipped duplicate message with ID ${message.id}`);
        return;
      }

      commit('ADD_MESSAGE', message);
    },
    setMessages({ commit }, messages) {
      commit('SET_MESSAGES', messages);
    },
    updateTypingStatus({ commit }, payload) {
      commit('SET_TYPING_STATUS', payload);
    },
    clearMessages({ commit }) {
      commit('CLEAR_MESSAGES');
    },
    setGroup({ commit }, group) {
      commit('SET_GROUP', group);
    },
    updateGroup({ commit }, updatedData) {
      commit('UPDATE_GROUP', updatedData);
    },
    setHasMoreMessages({ commit }, hasMore) {
      commit('SET_HAS_MORE_MESSAGES', hasMore);
    },
    // Handle socket reconnection
    socketReconnected({ state, dispatch }) {
      console.log('GroupChat store: Socket reconnected, current group:', state.group?.id);

      // If we have a current group, notify components about the reconnection
      if (state.group && state.group.id) {
        // Dispatch a custom event that components can listen for
        window.dispatchEvent(new CustomEvent('socket-reconnected', {
          detail: { groupId: state.group.id }
        }));
      }
    },
  },
};
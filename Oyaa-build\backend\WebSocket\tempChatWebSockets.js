// backend/tempChatWebSockets.js
const tempChatService = require('../services/Local/tempChatService');
const localService = require('../services/Local/localService');

function getTempChatRoomName(userId1, userId2) {
  // Convert both IDs to strings before sorting
  const sortedIds = [String(userId1), String(userId2)].sort((a, b) =>
    a.localeCompare(b)
  );
  return `temp_chat_${sortedIds[0]}_${sortedIds[1]}`;
}

// backend/WebSocket/tempChatWebSockets.js
module.exports = (io) => {
  const tempChatNamespace = io.of('/temp-chat');

  tempChatNamespace.on('connection', (socket) => {
    console.log(`Back-end: New WebSocket connection for temporary chat (socket id: ${socket.id})`);

    // Allow users to join their personal room
    socket.on('joinPersonalRoom', (userId) => {
      const room = `user_${userId}`;
      socket.join(room);
      console.log(`User ${userId} joined personal room ${room} in /temp-chat namespace`);
    });

    socket.on('joinTempChat', ({ userId, targetUserId }) => {
      if (!userId || !targetUserId) {
        console.error('Back-end: Missing userId or targetUserId in joinTempChat');
        return;
      }
      const room = getTempChatRoomName(userId, targetUserId);
      socket.join(room);
      console.log(`Back-end: User ${userId} joined temporary chat room ${room}`);
    });

    socket.on('sendTempMessage', async (data) => {
      console.log("Back-end received sendTempMessage:", data);
      const { sender_id, receiver_id, message } = data;

      if (!sender_id || !receiver_id || !message) {
        console.error('Back-end: Missing required fields in sendTempMessage', data);
        return;
      }

      try {
        const savedMessage = await tempChatService.sendMessage(sender_id, receiver_id, message);
        const room = getTempChatRoomName(sender_id, receiver_id);
        tempChatNamespace.to(room).emit('newTempMessage', savedMessage);
        console.log(`Back-end: Temporary message sent to room ${room}:`, savedMessage);

        // Update locals for both users
        await localService.addOrUpdateLocal(sender_id, { id: receiver_id });
        await localService.addOrUpdateLocal(receiver_id, { id: sender_id });

        // Emit last message update to personal rooms
        tempChatNamespace.to(`user_${sender_id}`).emit('lastTempMessageUpdate', {
          friendId: receiver_id,
          message: savedMessage.message,
          timestamp: savedMessage.sent_at,
          senderId: sender_id
        });
        tempChatNamespace.to(`user_${receiver_id}`).emit('lastTempMessageUpdate', {
          friendId: sender_id,
          message: savedMessage.message,
          timestamp: savedMessage.sent_at,
          senderId: sender_id
        });
      } catch (err) {
        console.error('Back-end: Failed to send temporary message:', err);
        socket.emit('error', { message: 'Failed to send temporary message' });
      }
    });

    socket.on('disconnect', () => {
      console.log(`Back-end: WebSocket disconnected for temporary chat (socket id: ${socket.id})`);
    });
  });
};
/**
 * Emoji utility functions for consistent rendering and detection
 */

// Check if a message consists of only emojis (for special rendering)
export function isEmojiOnlyMessage(message) {
  if (!message || typeof message !== 'string') return false;
  
  // Clean string from whitespace
  const cleanMessage = message.trim();
  
  // If empty after trimming, it's not an emoji-only message
  if (!cleanMessage) return false;
  
  // Common emoji regex pattern - matches most Unicode emoji
  const emojiRegex = /^[\p{Emoji}]+$/u;
  
  // Check if message consists only of emoji characters
  return emojiRegex.test(cleanMessage);
}

// Format message text to wrap emojis in span tags for consistent rendering
export function formatMessageWithEmojis(message) {
  if (!message || typeof message !== 'string') return '';
  
  // If it's emoji-only, wrap the whole message
  if (isEmojiOnlyMessage(message)) {
    return `<div class="emoji-only-message">${message}</div>`;
  }
  
  // Otherwise detect and wrap individual emojis
  // This regex detects single Unicode emoji characters
  const emojiRegex = /([\p{Emoji}])/gu;
  
  return message.replace(emojiRegex, '<span class="emoji">$1</span>');
}

// Get random emoji based on category
export function getRandomEmoji(category = 'happy') {
  const emojiMap = {
    happy: ['😊', '😁', '😄', '😀', '😃', '🙂', '😌'],
    sad: ['😢', '😭', '😔', '😞', '😟', '🙁', '😥'],
    love: ['❤️', '😍', '🥰', '💕', '💖', '💗', '💓'],
    funny: ['😂', '🤣', '😆', '😝', '😜', '😹', '🤪'],
    surprised: ['😮', '😲', '😯', '😦', '😧', '😳', '🤯'],
    angry: ['😠', '😡', '😤', '😾', '👿', '💢', '😒'],
    neutral: ['😐', '😑', '😶', '😏', '🤔', '🤨', '😬'],
    celebration: ['🎉', '🎊', '🎂', '🎁', '🎈', '🥳', '✨'],
    gestures: ['👍', '👌', '🙏', '👏', '🤝', '✌️', '👊'],
    nature: ['🌷', '🌹', '🌺', '🌻', '🌼', '🌸', '💐'],
    food: ['🍕', '🍔', '🍎', '🍓', '🍒', '🍦', '🍩'],
    activities: ['⚽', '🏀', '🏈', '⚾', '🎾', '🎮', '🎯']
  };
  
  // Default to happy if category not found
  const emojis = emojiMap[category] || emojiMap.happy;
  
  // Return random emoji from the category
  return emojis[Math.floor(Math.random() * emojis.length)];
}

// Check if a text contains emojis
export function containsEmoji(text) {
  if (!text || typeof text !== 'string') return false;
  const emojiRegex = /[\p{Emoji}]/u;
  return emojiRegex.test(text);
} 
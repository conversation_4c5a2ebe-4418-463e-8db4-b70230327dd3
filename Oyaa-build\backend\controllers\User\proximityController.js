const ProximityService = require('../../services/User/proximityService');
const logger = require('../../utils/logger');
const { validateCoordinates } = require('../../utils/geoUtils');

class ProximityController {
  async updateLocation(req, res) {
    try {
      const { userId } = req.params;
      const { lat, lng, radius = 5000 } = req.body;

      // Authentication check (you'll need to implement your auth middleware)
      if (req.user.id !== userId) {
        return res.status(403).json({ error: 'Unauthorized location update' });
      }

      // Validate inputs
      if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
        return res.status(400).json({ error: 'Invalid coordinates' });
      }
      
      validateCoordinates({ latitude: lat, longitude: lng });

      await ProximityService.updateUserLocation(userId, parseFloat(lat), parseFloat(lng));
      logger.info(`Location updated for user: ${userId}`);
      res.status(200).json({ success: true });

    } catch (error) {
      logger.error(`ProximityController.updateLocation: ${error.message}`);
      const status = error.message.includes('Invalid') ? 400 : 500;
      res.status(status).json({ error: error.message });
    }
  }

  async getNearbyUsers(req, res) {
    try {
      const { userId } = req.params;
      const { 
        lat, 
        lng, 
        radius = 1000, // Default 1km radius
        limit = 20, 
        offset = 0 
      } = req.query;

      // Validate parameters
      const numericParams = { limit, offset, radius };
      for (const [key, value] of Object.entries(numericParams)) {
        if (isNaN(value) || value < 0) {
          return res.status(400).json({ error: `Invalid ${key} parameter` });
        }
      }

      let coordinates;
      if (lat && lng) {
        validateCoordinates({ latitude: lat, longitude: lng });
        coordinates = { lat: parseFloat(lat), lng: parseFloat(lng) };
      }

      const nearbyUsers = await ProximityService.getNearbyUsers(
        userId,
        parseInt(limit),
        parseInt(offset),
        parseInt(radius),
        coordinates
      );

      res.status(200).json({
        count: nearbyUsers.length,
        radius: parseInt(radius),
        results: nearbyUsers
      });

    } catch (error) {
      logger.error(`ProximityController.getNearbyUsers: ${error.message}`);
      const status = error.message.includes('Invalid') ? 400 : 500;
      res.status(status).json({ 
        error: error.message,
        details: error.stack?.split('\n')[0] 
      });
    }
  }
}

module.exports = new ProximityController();
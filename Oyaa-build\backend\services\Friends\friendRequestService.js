// backend/services/friendRequestService.js
const FriendRequestModel = require('../../models/Friends/friendRequestModel');
const FriendsModel = require('../../models/Friends/friendsModel');
const db = require('../../utils/db'); // Add db import for direct queries

class FriendRequestService {
  async createRequest(senderId, receiverId) {
   
    // Prevent users from sending friend requests to themselves.
    if (senderId === receiverId) {
      
      throw new Error('You cannot send a friend request to yourself.');
    }

    // Check if the users are already friends.
   
    const currentFriends = await FriendsModel.getFriends(senderId);
    

    if (currentFriends.some(friend => friend.id === receiverId)) {
      
      throw new Error('You are already friends with this user.');
    }

    // Check if there is already any friend request (pending, accepted, or rejected) from the sender to the receiver.
   
    try {
      // Query to check if any request exists with this sender and receiver
      const query = `
        SELECT id, status FROM friend_requests
        WHERE sender_id = $1 AND receiver_id = $2
      `;
      const result = await db.query(query, [senderId, receiverId]);
    
      if (result.rows.length > 0) {
        const existingRequest = result.rows[0];
      

        if (existingRequest.status === 'Pending' || existingRequest.status === 'pending') {
         
          throw new Error('Friend request already sent and pending.');
        } else if (existingRequest.status === 'Accepted' || existingRequest.status === 'accepted') {
        
          throw new Error('You are already friends with this user.');
        } else if (existingRequest.status === 'Rejected' || existingRequest.status === 'rejected') {
          // For rejected requests, we could either allow a new request or not
          // Here we'll allow the user to send a new request by updating the existing one
         
          const updateQuery = `
            UPDATE friend_requests
            SET status = 'Pending', created_at = CURRENT_TIMESTAMP
            WHERE id = $1
            RETURNING *;
          `;
          const updateResult = await db.query(updateQuery, [existingRequest.id]);
          
          return updateResult.rows[0];
        }
      }
    } catch (error) {
      if (error.message.includes('already')) {
        throw error; // Re-throw our custom errors
      }
      console.error('Error checking existing requests:', error);
      throw new Error('Failed to check existing friend requests');
    }

    // If no issues are found, create the friend request.
    
    try {
      const result = await FriendRequestModel.createRequest(senderId, receiverId);
     
      return result;
    } catch (error) {
      console.error('Error creating friend request in model:', error);
      console.error('Error stack:', error.stack);
      throw error;
    }
  }

  async getRequests(receiverId) {
  
    try {
      const requests = await FriendRequestModel.getRequests(receiverId);
     
      return requests;
    } catch (error) {
      console.error('Error in FriendRequestService.getRequests:', error);
      throw error;
    }
  }

  async acceptRequest(requestId) {
   
    try {
      const result = await FriendRequestModel.acceptRequest(requestId);
     
      return result;
    } catch (error) {
      console.error('Error in FriendRequestService.acceptRequest:', error);
      console.error('Error stack:', error.stack);
      throw error;
    }
  }

  async rejectRequest(requestId) {
  
    try {
      const result = await FriendRequestModel.rejectRequest(requestId);
     
      return result;
    } catch (error) {
      console.error('Error in FriendRequestService.rejectRequest:', error);
      console.error('Error stack:', error.stack);
      throw error;
    }
  }

  async getRequestById(requestId) {
    return await FriendRequestModel.getRequestById(requestId);
  }

  async getAllSentRequests(senderId) {
   
    try {
      const requests = await FriendRequestModel.getAllSentRequests(senderId);
     
      return requests;
    } catch (error) {
      console.error('Error in FriendRequestService.getAllSentRequests:', error);
      throw error;
    }
  }

  async getSentRequests(senderId, seenRejections = []) {
    const requests = await FriendRequestModel.getSentRequests(senderId, seenRejections);
    return requests.map(request => ({
      id: request.id,
      receiver_id: request.receiver_id,
      receiver_username: request.receiver_username || 'Unknown',
      receiver_avatar: request.receiver_avatar,
      receiver_description: request.receiver_description,
      status: request.status,
      created_at: request.created_at,
    }));
  }
  async getRequestsSince(receiverId, since) {
    return await FriendRequestModel.getRequestsSince(receiverId, since);
  }
}

module.exports = new FriendRequestService();

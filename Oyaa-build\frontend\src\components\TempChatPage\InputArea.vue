<!-- frontend/src/components/TempChatPage/InputArea.vue -->
<template>
    <div class="input-area">
      <input
        type="text"
        v-model="newMessage"
        @keyup.enter="send"
        placeholder="Type your message here..."
      />
      <button @click="send">Send</button>
    </div>
  </template>
  
  <script>
  export default {
    name: "InputArea",
    data() {
      return {
        newMessage: ""
      }
    },
    methods: {
      send() {
        if (this.newMessage.trim()) {
          this.$emit('send-message', this.newMessage.trim());
          this.newMessage = '';
        }
      }
    }
  }
  </script>
  
  <style scoped>
  .input-area {
    display: flex;
    padding: 15px;
    background-color: #fff;
    border-top: 1px solid #ccc;
  }
  .input-area input[type="text"] {
    flex: 1;
    padding: 10px;
    font-size: 1em;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  .input-area button {
    margin-left: 10px;
    padding: 10px 15px;
    font-size: 1em;
    background-color: #007acc;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  .input-area button:hover {
    background-color: #005fa3;
  }
  </style>
const clients = new Map(); // Store connected clients (key: userId, value: response object)
const logger = require('./logger');

function sendSSEUpdate(userId, data) {
  // Convert userId to string to ensure consistent key type in the Map
  const userIdStr = String(userId);

  const res = clients.get(userIdStr);
  if (res) {
    try {
      res.write(`data: ${JSON.stringify(data)}\n\n`);
    } catch (error) {
      logger.error(`Error sending SSE update to user ${userIdStr}:`, error);
      // Remove the client if there was an error sending the update
      clients.delete(userIdStr);
    }
  }
}

function sseHandler(req, res) {
  // Get userId from the authenticated user object or params
  let userId;

  // If using Firebase authentication, get the Firebase UID
  if (req.user && req.user.firebaseUid) {
    userId = String(req.user.firebaseUid);
    logger.info(`SSE handler using Firebase UID: ${userId}`);
  }
  // Fallback to params or query
  else {
    userId = String(req.params.userId || req.query.userId);
    logger.info(`SSE handler using param/query userId: ${userId}`);
  }

  if (!userId) {
    logger.warn('SSE handler: No user ID available');
    return res.status(400).json({ message: 'User ID is required' });
  }

  // Set headers for SSE
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.flushHeaders();

  // Send an initial message
  res.write(`data: ${JSON.stringify({ type: 'connected', message: 'SSE connection established' })}\n\n`);

  // Store the response object
  clients.set(userId, res);
  logger.info(`SSE client connected: ${userId}`);

  // Remove client on disconnect
  req.on('close', () => {
    clients.delete(userId);
    logger.info(`SSE client disconnected: ${userId}`);
  });
}

module.exports = { sendSSEUpdate, sseHandler };
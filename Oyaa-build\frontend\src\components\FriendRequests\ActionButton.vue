<template>
  <button
    :class="['action-btn', type]"
    :disabled="disabled || clicked"
    @click="handleClick"
    type="button"
  >
    <!-- Icon for accept button -->
    <svg v-if="!loading && type === 'accept'" class="btn-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
      <polyline points="22 4 12 14.01 9 11.01"></polyline>
    </svg>

    <!-- Icon for reject button -->
    <svg v-if="!loading && type === 'reject'" class="btn-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="15" y1="9" x2="9" y2="15"></line>
      <line x1="9" y1="9" x2="15" y2="15"></line>
    </svg>

    <span v-if="!loading" class="btn-text">{{ buttonText }}</span>
    <div v-else class="button-spinner"></div>
  </button>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: 'accept',
      validator: (value) => ['accept', 'reject'].includes(value),
    },
    loading: Boolean,
    disabled: Boolean,
  },
  data() {
    return {
      clicked: false, // Track if button has been clicked
    };
  },
  computed: {
    buttonText() {
      return this.type === 'accept' ? 'Accept' : 'Reject';
    },
  },
  methods: {
    handleClick() {
      // Prevent multiple clicks
      if (this.clicked || this.disabled || this.loading) return;

      // Set clicked state to true
      this.clicked = true;

      // Emit click event
      this.$emit('click');
    },
  },
  // Reset clicked state when loading or disabled props change
  watch: {
    loading(newVal) {
      if (!newVal) this.clicked = false;
    },
    disabled(newVal) {
      if (!newVal) this.clicked = false;
    },
  },
};
</script>

<style scoped>
.action-btn {
  --success: #10b981;
  --success-dark: #059669;
  --success-light: rgba(16, 185, 129, 0.1);
  --danger: #ef4444;
  --danger-dark: #dc2626;
  --danger-light: rgba(239, 68, 68, 0.1);
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 6.5rem;
  height: 2.5rem;
  padding: 0 1.25rem;
  border: none;
  border-radius: 0.625rem;
  font-size: 0.9375rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  transform: scale(0);
  opacity: 0;
  transition: transform 0.5s, opacity 0.3s;
}

.action-btn:hover::before,
.action-btn:focus::before {
  transform: scale(2);
  opacity: 1;
}

.btn-icon {
  width: 1.125rem;
  height: 1.125rem;
  flex-shrink: 0;
}

.action-btn.accept {
  background-color: var(--success);
  color: white;
}

.action-btn.accept:hover:not(:disabled),
.action-btn.accept:focus:not(:disabled) {
  background-color: var(--success-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-btn.reject {
  background-color: var(--danger);
  color: white;
}

.action-btn.reject:hover:not(:disabled),
.action-btn.reject:focus:not(:disabled) {
  background-color: var(--danger-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: var(--shadow-sm);
}

.button-spinner {
  width: 1.125rem;
  height: 1.125rem;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Touch-friendly adjustments for mobile */
@media (max-width: 640px) {
  .action-btn {
    height: 2.75rem;
    min-width: 7rem;
    padding: 0 1.25rem;
  }
}

/* Ensure buttons are properly sized on small screens */
@media (max-width: 360px) {
  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
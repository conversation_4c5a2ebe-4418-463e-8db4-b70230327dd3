<template>
  <div class="sidebar-container">
    <aside ref="sidebarRef" class="sidebar" :class="{ 'collapsed': isCollapsed }">
      <div class="sidebar-header" :class="{ 'centered': isCollapsed }">
        <div v-if="!isCollapsed" class="logo-container">
          <div class="logo">
            <img src="/Oyaalogo-DB.svg" alt="Oyaa Logo" />
          </div>
          <h2>Oyaa</h2>
        </div>
        <button class="sidebar-toggle" @click="toggleSidebar" :aria-label="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'">
          <svg v-if="isCollapsed" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </button>
      </div>

      <div class="sidebar-nav">
        <template v-for="(item, index) in navItems" :key="index">
          <div v-if="isCollapsed" class="tooltip-container">
            <button
              class="nav-item-collapsed"
              :class="{ 'active': activeTab === item.tab }"
              @click="setActiveTab(item.tab)"
            >
              <component :is="item.icon" />
              <span v-if="activeTab === item.tab" class="active-indicator"></span>
            </button>
            <div class="tooltip">{{ item.title }}</div>
          </div>
          <button
            v-else
            class="nav-item"
            :class="{ 'active': activeTab === item.tab }"
            @click="setActiveTab(item.tab)"
          >
            <component :is="item.icon" />
            <span class="nav-item-title">{{ item.title }}</span>
          </button>
        </template>
      </div>

      <div class="sidebar-footer" :class="{ 'centered': isCollapsed }">
        <div v-if="isCollapsed" class="tooltip-container">
          <div class="user-profile-collapsed">
            <img :src="avatarSrc" alt="User Avatar" class="user-avatar-img-collapsed" />
          </div>
          <div class="tooltip">Profile</div>
        </div>
        <div v-else class="user-profile">
          <div class="user-avatar">
            <img :src="avatarSrc" alt="User Avatar" class="user-avatar-img" />
          </div>
          <div class="user-info">
            <p class="user-name">{{ username }}</p>
          </div>
        </div>
      </div>
    </aside>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'vuex';

const props = defineProps({
  navItems: { type: Array, required: true },
  activeTab: { type: String, required: true },
  username: { type: String, required: true }
});

const emit = defineEmits(['update:activeTab', 'update:collapsed']);

const store = useStore();
const avatarSrc = computed(() => store.getters['auth/user']?.avatar || '/Avatar/default.svg');

const isCollapsed = ref(false);
const sidebarRef = ref(null);

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value;
  localStorage.setItem('sidebar-collapsed', String(isCollapsed.value));
  emit('update:collapsed', isCollapsed.value);
};

const setActiveTab = (tab) => {
  emit('update:activeTab', tab);
};

onMounted(() => {
  const savedState = localStorage.getItem('sidebar-collapsed');
  if (savedState) {
    isCollapsed.value = savedState === 'true';
    emit('update:collapsed', isCollapsed.value);
  }
});
</script>

<style scoped>
.sidebar {
  --bg-primary: #0f0f13;
  --bg-secondary: #1a1a22;
  --text-primary: #e2e2e2;
  --text-secondary: #a0a0a0;
  --accent: #6366f1;
  --accent-hover: #4f46e5;
  --border: #2e2e3a;
  --transition-speed: 0.3s;
  
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 240px;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  z-index: 10;
  transition: width var(--transition-speed) cubic-bezier(0.16, 1, 0.3, 1);
  font-family: 'Inter', 'Segoe UI', sans-serif;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);

}

.sidebar.collapsed {
  width: 72px;
}

.sidebar-header {
  height: 70px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-primary);
 
}

.sidebar-header.centered {
  justify-content: center;
  padding: 0;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.sidebar-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5px;
}

.sidebar-toggle {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed) ease;
}

.sidebar-toggle:hover {
  background: var(--accent);
  color: white;
  transform: scale(1.05);
}

.sidebar-nav {
  margin-top: 20px;
  flex: 1;
  padding: 0 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  border-radius: 10px;
  color: var(--text-secondary);
  transition: all var(--transition-speed) ease;
  background: none;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
  font-weight: 500;
}

.nav-item:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  transform: translateX(2px);
}

.nav-item.active {
  background: var(--accent);
  color: white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.nav-item svg {
  width: 20px;
  height: 20px;
}

.nav-item.active svg {
  color: white;
}

.nav-item-title {
  font-size: 14px;
  letter-spacing: 0.3px;
}

.nav-item-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  margin: 8px auto;
  border-radius: 12px;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  transition: all var(--transition-speed) ease;
  border: none;
  cursor: pointer;
  position: relative;
}

.nav-item-collapsed:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  transform: scale(1.08);
}

.nav-item-collapsed.active {
  background: var(--accent);
  color: white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.nav-item-collapsed svg {
  width: 20px;
  height: 20px;
}

.nav-item-collapsed.active .active-indicator {
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: white;
  border-radius: 4px;
}

.tooltip-container {
  position: relative;
}

.tooltip-container:hover .tooltip {
  opacity: 1;
  transform: translateX(12px);
  visibility: visible;
}

.tooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 12px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  pointer-events: none;
  z-index: 20;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}

.sidebar-footer {
  margin-top: auto;
  padding: 16px;

}

.sidebar-footer.centered {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  border-radius: 10px;
  background: var(--bg-secondary);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.user-profile:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
}

.user-avatar {
  width: 38px;
  height: 38px;
  border-radius: 10px;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  overflow: hidden;
  border: 2px solid var(--accent);
}

.user-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.user-profile-collapsed {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  cursor: pointer;
  overflow: hidden;
  border: 2px solid var(--accent);
  transition: all var(--transition-speed) ease;
}

.user-profile-collapsed:hover {
  transform: scale(1.08);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.user-avatar-img-collapsed {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .sidebar {
    display: none;
  }
}

.sidebar-container {
  position: relative;
  height: 100%;
}
</style>
const fs = require('fs');
const pg = require('pg');
const url = require('url');

const config = {
    user: "avnadmin",
    password: "AVNS_QpLwav6tQ6vLldqy3Kq",
    host: "oyaav6-oyaa-v6.i.aivencloud.com",
    port: 13427,
    database: "defaultdb",
    ssl: {
        rejectUnauthorized: true,
        ca: `-----BEGIN CERTIFICATE-----
MIIEQTCCAqmgAwIBAgIUec9BKMMOxJGcQJWohfFCE9VcKWMwDQYJKoZIhvcNAQEM
BQAwOjE4MDYGA1UEAwwvOTJmZDBiOGQtMzk1Zi00NzMwLWFlOWEtMmVjNGJjNzJi
YjMwIFByb2plY3QgQ0EwHhcNMjUwMTE4MTMxNTM3WhcNMzUwMTE2MTMxNTM3WjA6
MTgwNgYDVQQDDC85MmZkMGI4ZC0zOTVmLTQ3MzAtYWU5YS0yZWM0YmM3MmJiMzAg
UHJvamVjdCBDQTCCAaIwDQYJKoZIhvcNAQEBBQADggGPADCCAYoCggGBAIJhd11d
mJknSMpZ+9pYHuRaegFtR2kY7HHm77HGDqADqR3BcT19jnj7GY+sD+4nxmYj4wDQ
pfjBHib6WoU+UDCCHbskTBuOp+tLOXzFopml/v3Vf7DfiXKK3hoAFfZnlBVUFw8I
kADPZ9zWVB+R0uOsEllDMDp2iV5Ay45s8BNRGkbRbIIdbPuS86uF7zaIggepBZLZ
ddfKPeb5tJzEw1QygXjECRIbJDVDK+rpgUP901g6DgB3VoBP4NNnA5kaPZYmd4p1
uGqNeA3mtk0PnpB7CNtAVFBmDc0pNhiWXr2HBiWtix5+ndHo5cZp1INoK9DsM49G
dQbVRKWtI2XjPnkSblvOYBXDN3fdOFF2CtYRzRtNnlzOQE0gezhy3DNa3xotaILP
7GZZQJcf7l8E/Rht55/7h3vRt3JhdoN2P8+GKoslL7DnXYuRY9MqPSs/DPUThIYW
PvrUtH4aMuqxPZP12ws4EacLUMceTTwkx80nSN4LhEmt9MCJrxqFSHo+hwIDAQAB
oz8wPTAdBgNVHQ4EFgQUvOrMf98gjtIqHsdBPVToezdawjUwDwYDVR0TBAgwBgEB
/wIBADALBgNVHQ8EBAMCAQYwDQYJKoZIhvcNAQEMBQADggGBAG4YkyQt5qT91V6X
WwTzNnJc4plXpxv5ftTHddcnFjSDWIG1cBQ8EvQ4w4xfyOIzA0Py7TjF8X2Gl9nj
rxxVmjSmQFQQ/Ah9c3Epi47L5o11rEk7EcnzQ2fsRWAiCNmnuQpn25y9GFgAhGgY
fPWaZqWImr+Jndb8qYUQkmZUg+RgxPtwxMkFNCltjt97PmWNSb64jhjyeDM+oNRN
kph1WiFTFHiJvcNZ84p4Ij/wvupow8Zp15hOTKC96iargBBwCyKb8uFVhfBK28n4
VTfCHpezqpHqk5bK3MAhD6KzbMcWdc/r66EzsdE++EGigpFPT2E8yLp98F2aigjl
F+dIy0gqSHSyLoMCw2F23J0SdKzzqylNN5GvnS41OgCV3OzfcwgTGVl3EYuTbW8f
SCSmiKrtQ8ul8mLRe3ZOx7J0ge4uNJxaLdozGYG+Lt4wT20NmYQzL3lcg7dt9bGq
DNpcN9Ni6HhA4tmNwDe2J3ZjsokNCO8eCR3kQP5F7hOciQUspg==
-----END CERTIFICATE-----`,
    },
};

const client = new pg.Client(config);
client.connect(function (err) {
    if (err)
        throw err;
    client.query("SELECT VERSION()", [], function (err, result) {
        if (err)
            throw err;

        console.log(result.rows[0].version);
        client.end(function (err) {
            if (err)
                throw err;
        });
    });
});
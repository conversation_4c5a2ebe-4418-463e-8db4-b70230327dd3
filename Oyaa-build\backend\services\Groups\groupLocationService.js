// backend/services/groupLocationService.js
const db = require('../../utils/db');

class GroupLocationService {
  /**
   * Find groups near a given location within a specified radius.
   * @param {number} lat - Latitude of the user's location.
   * @param {number} lng - Longitude of the user's location.
   * @param {number} radius - Search radius in meters (default: 10000m or 10km).
   * @param {number} limit - Maximum number of results (default: 10).
   * @param {number} offset - Number of results to skip (default: 0).
   * @returns {Promise<Array>} Array of nearby groups with distance.
   */
  async getNearbyGroups(lat, lng, radius = 10000, limit = 10, offset = 0) {
    const query = `
    SELECT 
      id, 
      name, 
      description,
      latitude, 
      longitude,
      ST_Distance(
        location,
        ST_SetSRID(ST_MakePoint($2, $1), 4326)::geography
      ) AS distance
    FROM groups
    WHERE location IS NOT NULL
    AND ST_DWithin(
      location,
      ST_SetSRID(ST_MakePoint($2, $1), 4326)::geography,
      $3
    )
    ORDER BY distance
    LIMIT $4 OFFSET $5;
  `;
  
    const values = [lat, lng, radius, limit, offset];
    const result = await db.query(query, values);
    return result.rows;
  }
}

module.exports = new GroupLocationService();
<template>
  <component
    :is="asChild ? (tag || 'span') : 'button'"
    :class="computedClass"
    v-bind="attrs"
  >
    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed, useAttrs } from 'vue'
import { cva } from 'class-variance-authority'

interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  asChild?: boolean
  tag?: string
}

const props = defineProps<ButtonProps>()
const attrs = useAttrs()

const buttonVariants = cva(
  "btn", // base button class
  {
    variants: {
      variant: {
        default: "btn-default",
        destructive: "btn-destructive",
        outline: "btn-outline",
        secondary: "btn-secondary",
        ghost: "btn-ghost",
        link: "btn-link",
      },
      size: {
        default: "btn-size-default",
        sm: "btn-size-sm",
        lg: "btn-size-lg",
        icon: "btn-size-icon",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const computedClass = computed(() => {
  return buttonVariants({
    variant: props.variant || 'default',
    size: props.size || 'default',
    className: (attrs.class as string) || ''
  })
})
</script>

<style scoped>
/* Base button style */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  white-space: nowrap;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease, color 0.2s ease;
  border: none;
  padding: 0;
  cursor: pointer;
}

/* Variants */
.btn-default {
  background-color: #007bff;
  color: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.btn-default:hover {
  background-color: #0069d9;
}

.btn-destructive {
  background-color: #dc3545;
  color: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.btn-destructive:hover {
  background-color: #c82333;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid #ced4da;
  color: #495057;
}
.btn-outline:hover {
  background-color: #e9ecef;
}

.btn-secondary {
  background-color: #6c757d;
  color: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-ghost {
  background-color: transparent;
  color: #495057;
}
.btn-ghost:hover {
  background-color: #f8f9fa;
}

.btn-link {
  background-color: transparent;
  color: #007bff;
  text-decoration: underline;
}
.btn-link:hover {
  color: #0056b3;
}

/* Sizes */
.btn-size-default {
  height: 36px;
  padding: 0 16px;
}
.btn-size-sm {
  height: 32px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 3px;
}
.btn-size-lg {
  height: 40px;
  padding: 0 24px;
  border-radius: 5px;
}
.btn-size-icon {
  height: 36px;
  width: 36px;
}
</style>

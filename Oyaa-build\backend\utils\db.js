const { Pool } = require('pg');
// Valkey service temporarily disabled
// const { redisClient, pubSubClient } = require('../config/valkey.config');
const logger = require('./logger');
// const { promisify } = require('util');
require('dotenv').config();

// PostgreSQL Pool
const pool = new Pool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  ssl: {
    rejectUnauthorized: false, // Allow self-signed certificates
  },
  // Connection timeout
  connectionTimeoutMillis: 10000,
  // Idle timeout
  idleTimeoutMillis: 30000,
  // Maximum number of clients the pool should contain
  max: 20,
});

// Log connection events
pool.on('connect', (client) => {
  logger.info('✅ PostgreSQL client connected');
  console.log('✅ PostgreSQL client connected');
});

pool.on('acquire', (client) => {
  logger.debug('PostgreSQL client acquired from pool');
});

pool.on('remove', (client) => {
  logger.debug('PostgreSQL client removed from pool');
});

// Handle pool errors
pool.on('error', (err, client) => {
  logger.error('Unexpected error on PostgreSQL client', {
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
      code: err.code,
      detail: err.detail,
      hint: err.hint,
      position: err.position,
      internalPosition: err.internalPosition,
      internalQuery: err.internalQuery,
      where: err.where,
      schema: err.schema,
      table: err.table,
      column: err.column,
      dataType: err.dataType,
      constraint: err.constraint
    }
  });

  console.error('Unexpected error on PostgreSQL client:', err.message);
  if (err.code) {
    console.error('Error code:', err.code);
  }
  if (err.detail) {
    console.error('Error detail:', err.detail);
  }

  // Don't exit immediately, give logger time to write
  setTimeout(() => {
    process.exit(-1);
  }, 1000);
});

// Database connection with health check
const connectDB = async () => {
  try {
    // Verify PostgreSQL connection
    const pgClient = await pool.connect();
    const pgResult = await pgClient.query('SELECT NOW() as now');
    const pgTimestamp = pgResult.rows[0].now;
    logger.info(`✅ PostgreSQL connected successfully at ${pgTimestamp}`);
    console.log(`✅ PostgreSQL connected successfully at ${pgTimestamp}`);
    pgClient.release();

    // Redis/Valkey connection check disabled
    logger.info('⚠️ Redis/Valkey connection check skipped (service disabled)');
    console.log('⚠️ Redis/Valkey connection check skipped (service disabled)');

    return true;
  } catch (err) {
    logger.error('Database connection error', {
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack,
        code: err.code,
        detail: err.detail,
        hint: err.hint
      }
    });

    console.error('Database connection error:', err.message);
    if (err.code) {
      console.error('Error code:', err.code);
    }
    if (err.detail) {
      console.error('Error detail:', err.detail);
    }

    // Don't exit immediately, give logger time to write
    setTimeout(() => {
      process.exit(1);
    }, 1000);
  }
};

// Function to check database health
const checkDatabaseHealth = async () => {
  try {
    // Check PostgreSQL
    const pgClient = await pool.connect();
    const startTime = Date.now();
    const result = await pgClient.query('SELECT 1 as health_check');
    const endTime = Date.now();
    pgClient.release();

    const responseTime = endTime - startTime;

    // Get pool statistics
    const poolStats = {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount,
      responseTime
    };

    logger.debug('PostgreSQL health check', { poolStats });

    return {
      postgres: {
        status: 'up',
        responseTime: `${responseTime}ms`,
        poolStats
      },
      redis: {
        status: 'disabled', // Redis/Valkey service temporarily disabled
        message: 'Redis/Valkey service temporarily disabled'
      }
    };
  } catch (err) {
    logger.error('Database health check failed', {
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack
      }
    });

    return {
      postgres: {
        status: 'down',
        error: err.message
      },
      redis: {
        status: 'disabled', // Redis/Valkey service temporarily disabled
        message: 'Redis/Valkey service temporarily disabled'
      }
    };
  }
};

// Enhanced query function with error logging
const queryWithLogging = async (text, params) => {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;

    // Log slow queries (over 200ms)
    if (duration > 200) {
      logger.warn(`Slow query (${duration}ms)`, {
        query: {
          text,
          params,
          duration,
          rowCount: result.rowCount
        }
      });
    }

    return result;
  } catch (err) {
    // Log query errors
    logger.error('Database query error', {
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack,
        code: err.code,
        detail: err.detail,
        hint: err.hint,
        position: err.position
      },
      query: {
        text,
        params
      }
    });

    // Re-throw the error for the caller to handle
    throw err;
  }
};

// Export connection promise
module.exports = {
  query: queryWithLogging,
  pool,
  // Redis clients temporarily disabled
  // redisClient,
  // pubSubClient,
  connectDB,
  checkDatabaseHealth
};
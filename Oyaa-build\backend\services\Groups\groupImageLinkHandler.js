// backend/services/groupImageLinkHandler.js

// Utility function to extract cloud name from CLOUDINARY_URL
function getCloudName() {
    const url = process.env.CLOUDINARY_URL;
    if (url) {
      const match = url.match(/@([^/]+)/);
      if (match && match[1]) {
        return match[1];
      }
    }
    return 'your-cloud-name';
  }
  
  function processGroupImageMessage(message) {
    if (message.groupMedia) {
      // If mediaUrl is already provided, do nothing.
      if (message.groupMedia.mediaUrl && message.groupMedia.mediaUrl.trim() !== '') {
        return message;
      }
      const publicId = message.groupMedia.publicId || message.groupMedia.public_id;
      if (publicId) {
        const cloudName = getCloudName();
        const mediaType = message.groupMedia.mediaType || message.groupMedia.media_type || 'image';
        message.groupMedia.mediaUrl = `https://res.cloudinary.com/${cloudName}/${mediaType}/upload/${publicId}`;
      }
    }
    return message;
  }
  
  module.exports = { processGroupImageMessage };
  
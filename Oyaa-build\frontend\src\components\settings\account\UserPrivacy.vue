<template>
  <div class="user-privacy">
    <div class="header">
      <button class="back-button" @click="$router.push('/settings')">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>User Privacy</h1>
    </div>
    
    <div class="card">
      <div class="privacy-settings">
        <h2 class="section-title">Privacy Settings</h2>
        
        <div class="toggle-option">
          <div class="toggle-info">
            <span class="toggle-label">Allow others to find me in proximity chats</span>
            <span class="toggle-description">When enabled, other users can discover your profile in nearby chats</span>
          </div>
          <label class="toggle">
            <input type="checkbox" v-model="allowProximityDiscovery" />
            <span class="toggle-slider"></span>
          </label>
        </div>
        
        <div class="toggle-option">
          <div class="toggle-info">
            <span class="toggle-label">Make my profile visible in public groups</span>
            <span class="toggle-description">When enabled, your profile will be visible to all members in public groups</span>
          </div>
          <label class="toggle">
            <input type="checkbox" v-model="publicProfile" />
            <span class="toggle-slider"></span>
          </label>
        </div>
        
        <button @click="savePrivacySettings" :disabled="loading" class="save-button">
          <span v-if="loading" class="loading-spinner"></span>
          <span>{{ loading ? 'Saving...' : 'Save Settings' }}</span>
        </button>
        
        <div v-if="error" class="error-message">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          {{ error }}
        </div>
      </div>
      
      <!-- Blocked Accounts Section -->
      <div class="blocked-accounts">
        <h2 class="section-title">Blocked Accounts</h2>
        
        <div v-if="blockedLoading" class="loading-state">
          <div class="loading-spinner"></div>
          <span>Loading blocked accounts...</span>
        </div>
        
        <div v-else-if="blockedError" class="error-message">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          {{ blockedError }}
        </div>
        
        <ul v-else-if="blockedUsers.length > 0" class="blocked-list">
          <li v-for="user in blockedUsers" :key="user.id" class="blocked-user">
            <div class="user-info">
              <div class="avatar">
                <img :src="user.avatar || '/default-avatar.png'" alt="User avatar" />
              </div>
              <span class="username">{{ user.username }}</span>
            </div>
            <button @click="unblockUser(user.id)" :disabled="blockedLoading" class="unblock-button">
              <span v-if="user.unblocking" class="loading-spinner small"></span>
              <span v-else>Unblock</span>
            </button>
          </li>
        </ul>
        
        <div v-else class="empty-state">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          <p>You haven't blocked any users</p>
          <p>When you block someone, they'll appear here</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import axios from 'axios';

export default {
  name: 'UserPrivacy',
  setup() {
    const store = useStore();
    const router = useRouter();
    const allowProximityDiscovery = ref(true);
    const publicProfile = ref(false);
    const loading = ref(false);
    const error = ref('');

    // State for blocked accounts
    const blockedUsers = ref([]);
    const blockedLoading = ref(false);
    const blockedError = ref('');

    // Fetch the list of blocked users
    const fetchBlockedUsers = async () => {
      blockedLoading.value = true;
      blockedError.value = '';
      try {
        const token = store.getters['auth/token'];
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/users/blocked-users`,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        blockedUsers.value = response.data.blockedUsers.map(user => ({
          ...user,
          unblocking: false
        }));
      } catch (err) {
        blockedError.value =
          err.response?.data?.message || 'Error fetching blocked users';
      } finally {
        blockedLoading.value = false;
      }
    };

    // Unblock a user
    const unblockUser = async (blockedUserId) => {
      // Find the user and set unblocking flag
      const userToUnblock = blockedUsers.value.find(user => user.id === blockedUserId);
      if (userToUnblock) {
        userToUnblock.unblocking = true;
      }
      
      blockedError.value = '';
      try {
        const token = store.getters['auth/token'];
        await axios.delete(
          `${import.meta.env.VITE_API_BASE_URL}/api/users/block-user/${blockedUserId}`,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        // Update the list by removing the unblocked user
        blockedUsers.value = blockedUsers.value.filter(
          (user) => user.id !== blockedUserId
        );
      } catch (err) {
        blockedError.value =
          err.response?.data?.message || 'Error unblocking user';
        // Reset unblocking flag if there was an error
        if (userToUnblock) {
          userToUnblock.unblocking = false;
        }
      }
    };

    // Fetch blocked users when the component mounts
    onMounted(() => {
      fetchBlockedUsers();
    });

    // Save privacy settings
    const savePrivacySettings = async () => {
      loading.value = true;
      error.value = '';
      try {
        const userId = store.getters['auth/userId'];
        const token = store.getters['auth/token'];
        await axios.put(
          `${import.meta.env.VITE_API_BASE_URL}/api/users/${userId}/privacy`,
          {
            allowProximityDiscovery: allowProximityDiscovery.value,
            publicProfile: publicProfile.value,
          },
          { headers: { Authorization: `Bearer ${token}` } }
        );
      } catch (err) {
        error.value =
          err.response?.data?.message || 'Error saving privacy settings';
      } finally {
        loading.value = false;
      }
    };

    return {
      allowProximityDiscovery,
      publicProfile,
      loading,
      error,
      savePrivacySettings,
      blockedUsers,
      blockedLoading,
      blockedError,
      unblockUser,
    };
  },
};
</script>

<style scoped>
.user-privacy {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --success: #10b981;
  --success-bg: rgba(16, 185, 129, 0.15);
  --danger: #ef4444;
  --danger-bg: rgba(239, 68, 68, 0.15);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  position: relative;
}

.header h1 {
  flex: 1;
  text-align: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-button {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  cursor: pointer;
  outline: none;
  padding: 8px;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation; /* Improve touch behavior */
}

.back-button:hover {
  background: var(--accent-tertiary);
  color: var(--accent-primary);
  transform: translateX(-2px);
}

.back-button:active {
  transform: scale(0.95);
}

.card {
  background: var(--bg-tertiary);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-primary);
}

.privacy-settings {
  margin-bottom: 32px;
}

.toggle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.toggle-option:last-child {
  border-bottom: none;
}

.toggle-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.toggle-label {
  font-weight: 500;
  font-size: 15px;
  color: var(--text-primary);
}

.toggle-description {
  font-size: 13px;
  color: var(--text-secondary);
  max-width: 300px;
}

.toggle {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
  flex-shrink: 0;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  transition: var(--transition-fast);
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 3px;
  background-color: var(--text-secondary);
  transition: var(--transition-fast);
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent-tertiary);
  border-color: var(--accent-primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(22px);
  background-color: var(--accent-primary);
}

.save-button {
  width: 100%;
  padding: 14px;
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: var(--shadow-sm);
  margin-top: 24px;
  touch-action: manipulation; /* Improve touch behavior */
  -webkit-appearance: none; /* Remove default iOS styling */
  appearance: none;
}

.save-button:hover:not(:disabled) {
  background: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.save-button:active:not(:disabled) {
  transform: scale(0.98);
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

.loading-spinner.small {
  width: 14px;
  height: 14px;
  border-width: 1.5px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  margin-top: 16px;
  padding: 12px 16px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  animation: fadeIn 0.3s ease;
  background: var(--danger-bg);
  color: var(--danger);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.blocked-accounts {
  margin-top: 32px;
  padding-top: 32px;
  border-top: 1px solid var(--border-color);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 32px 0;
  color: var(--text-secondary);
}

.blocked-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.blocked-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 12px;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.blocked-user:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.blocked-user:active {
  transform: scale(0.98);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  overflow: hidden;
  background: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.username {
  font-weight: 500;
  font-size: 15px;
  color: var(--text-primary);
}

.unblock-button {
  background: var(--danger-bg);
  color: var(--danger);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation; /* Improve touch behavior */
  -webkit-appearance: none; /* Remove default iOS styling */
  appearance: none;
}

.unblock-button:hover:not(:disabled) {
  background: var(--danger);
  color: white;
  transform: translateY(-2px);
}

.unblock-button:active:not(:disabled) {
  transform: scale(0.95);
}

.unblock-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
}

.empty-state svg {
  color: var(--accent-primary);
  opacity: 0.7;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 4px 0;
  font-size: 15px;
}

.empty-state p:first-of-type {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .user-privacy {
    padding: 16px;
  }
  
  .header {
    margin-bottom: 24px;
  }
  
  .header h1 {
    font-size: 18px;
  }
  
  .back-button {
    padding: 6px;
  }
  
  .back-button svg {
    width: 22px;
    height: 22px;
  }
  
  .card {
    padding: 20px;
    border-radius: 14px;
  }
  
  .section-title {
    font-size: 16px;
    margin-bottom: 16px;
  }
  
  .toggle-option {
    margin-bottom: 16px;
    padding-bottom: 16px;
  }
  
  .toggle-label {
    font-size: 14px;
  }
  
  .toggle-description {
    font-size: 12px;
    max-width: 220px;
  }
  
  .toggle {
    width: 46px;
    height: 24px;
  }
  
  .toggle-slider:before {
    height: 16px;
    width: 16px;
    left: 3px;
    bottom: 3px;
  }
  
  input:checked + .toggle-slider:before {
    transform: translateX(20px);
  }
  
  .save-button {
    padding: 12px;
    font-size: 15px;
    border-radius: 8px;
    margin-top: 20px;
  }
  
  .blocked-user {
    padding: 10px 14px;
    margin-bottom: 10px;
    border-radius: 10px;
  }
  
  .avatar {
    width: 36px;
    height: 36px;
    border-radius: 8px;
  }
  
  .username {
    font-size: 14px;
  }
  
  .unblock-button {
    padding: 6px 12px;
    font-size: 13px;
    min-width: 70px;
  }
  
  .empty-state {
    padding: 30px 16px;
  }
  
  .empty-state svg {
    width: 40px;
    height: 40px;
  }
  
  .empty-state p {
    font-size: 14px;
  }
  
  .empty-state p:first-of-type {
    font-size: 15px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .user-privacy {
    padding: 12px;
  }
  
  .header h1 {
    font-size: 16px;
  }
  
  .card {
    padding: 16px;
  }
  
  .toggle-description {
    max-width: 180px;
  }
  
  .toggle {
    width: 40px;
    height: 22px;
  }
  
  .toggle-slider:before {
    height: 14px;
    width: 14px;
  }
  
  input:checked + .toggle-slider:before {
    transform: translateX(16px);
  }
  
  .avatar {
    width: 32px;
    height: 32px;
  }
  
  .username {
    font-size: 13px;
  }
  
  .unblock-button {
    padding: 5px 10px;
    font-size: 12px;
    min-width: 60px;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .back-button,
  .toggle-slider,
  .toggle-slider:before,
  .save-button,
  .blocked-user,
  .unblock-button {
    transition: none;
  }
  
  .loading-spinner,
  .loading-spinner.small {
    animation: none;
  }
  
  .error-message {
    animation: none;
  }
}
</style>
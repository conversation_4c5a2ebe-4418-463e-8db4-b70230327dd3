<template>
  <li :class="['request-item', {
    'processing': request.processing,
    'accepted': request.status === 'accepted',
    'rejected': request.status === 'rejected'
  }]">
    <div class="request-content">
      <div class="avatar">
        <img v-if="request.sender_avatar" :src="request.sender_avatar" alt="Avatar" class="avatar-img" />
        <div v-else class="avatar-placeholder">{{ request.sender_username.charAt(0).toUpperCase() }}</div>
      </div>

      <div class="user-details">
        <span class="username">{{ request.sender_username }}</span>
        <p v-if="request.sender_description" class="description">{{ request.sender_description }}</p>
        <span v-if="request.status" class="status-message">
          <svg v-if="request.status === 'accepted'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          {{ statusText }}
        </span>
      </div>
    </div>

    <!-- Action buttons with improved UI -->
    <div v-if="!request.status" class="action-section">
      <div class="action-prompt">Would you like to accept this friend request?</div>
      <div class="action-buttons">
        <ActionButton
          @click="$emit('accept', request)"
          :disabled="request.processing"
          type="accept"
          :loading="request.processing"
        />
        <ActionButton
          @click="$emit('reject', request)"
          :disabled="request.processing"
          type="reject"
          :loading="request.processing"
        />
      </div>
    </div>

    <!-- Status message for accepted/rejected requests -->
    <div v-else class="status-section">
      <div class="status-message">
        <svg v-if="request.status === 'accepted'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>
        {{ statusText }}
      </div>
    </div>
  </li>
</template>

<script>
import ActionButton from './ActionButton.vue';

export default {
  components: { ActionButton },
  props: {
    request: {
      type: Object,
      required: true,
    },
  },
  computed: {
    statusText() {
      return {
        accepted: 'Accepted!',
        rejected: 'Rejected',
      }[this.request.status];
    },
  },
};
</script>

<style scoped>
.request-item {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --success: #10b981;
  --success-dark: #059669;
  --success-bg: rgba(16, 185, 129, 0.15);
  --danger: #ef4444;
  --danger-dark: #dc2626;
  --danger-bg: rgba(239, 68, 68, 0.15);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.25rem;
  margin-bottom: 1rem;
  border-radius: 0.75rem;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.request-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  flex-shrink: 0;
  overflow: hidden; /* Ensure the image doesn't overflow */
  box-shadow: var(--shadow-sm);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.75rem; /* Matches the avatar container */
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  font-weight: 600;
  font-size: 1.5rem;
  border-radius: 0.75rem;
}

.user-details {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.username {
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Limit to 2 lines */
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.status-message {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  animation: fadeIn 0.3s ease;
  margin-top: 0.5rem;
}

.status-message svg {
  width: 1rem;
  height: 1rem;
}

/* Action section styles */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.action-prompt {
  font-size: 0.9375rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 0.25rem;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

/* Status section styles */
.status-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.status-section .status-message {
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  animation: fadeIn 0.3s ease;
}

.status-section .status-message svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* States */
.processing {
  opacity: 0.7;
  pointer-events: none;
}

.accepted {
  background-color: var(--success-bg);
  border-left: 3px solid var(--success);
}

.accepted .status-message svg {
  color: var(--success);
}

.rejected {
  background-color: var(--danger-bg);
  border-left: 3px solid var(--danger);
}

.rejected .status-message svg {
  color: var(--danger);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Media queries for responsive design */
@media (min-width: 768px) {
  .action-section {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .action-prompt {
    text-align: left;
    margin-bottom: 0;
  }

  .action-buttons {
    justify-content: flex-end;
  }
}

/* Ensure buttons are properly sized on small screens */
@media (max-width: 360px) {
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
}
</style>
// frontend/src/store/LocalChat.js
import axios from 'axios';
import tempChatSocket from '@/tempChatSocket';

export default {
  namespaced: true,
  state: {
    lastChats: {}, // Object with localId as keys and last message objects as values
  },
  mutations: {
    // Set initial last chats from server fetch
    SET_LAST_CHATS(state, chats) {
      state.lastChats = chats.reduce((acc, chat) => {
        acc[chat.local_id] = {
          message: chat.last_message,
          timestamp: chat.last_message_time,
          senderId: chat.last_message_sender_id,
        };
        return acc;
      }, {});
    },
    // Update last chat for a specific local
    UPDATE_LAST_CHAT(state, { localId, message, timestamp, senderId }) {
      state.lastChats = {
        ...state.lastChats,
        [localId]: { message, timestamp, senderId },
      };
    },
  },
  actions: {
    // Fetch last chats for all locals
    async fetchLastChats({ commit, rootGetters }) {
      const userId = rootGetters['auth/userId'];
      if (!userId) return;
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/locals/${userId}`,
          {
            headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
          }
        );
        const locals = response.data.locals || [];
        commit('SET_LAST_CHATS', locals);
      } catch (error) {
        console.error('Error fetching last chats for locals:', error);
      }
    },
    // Update last chat from WebSocket or manual dispatch
    updateLastChat({ commit }, { localId, message, timestamp, senderId }) {
      commit('UPDATE_LAST_CHAT', { localId, message, timestamp, senderId });
    },
    // Initialize WebSocket listeners
    initializeSocket({ dispatch, rootGetters }) {
      const userId = rootGetters['auth/userId'];
      if (!userId) return;

      tempChatSocket.connect();
      tempChatSocket.emit('joinPersonalRoom', userId);
      tempChatSocket.on('lastTempMessageUpdate', (data) => {
        dispatch('updateLastChat', {
          localId: data.friendId,
          message: data.message,
          timestamp: data.timestamp,
          senderId: data.senderId,
        });
      });
    },
    // Cleanup WebSocket listeners
    disconnectSocket() {
      tempChatSocket.off('lastTempMessageUpdate');
      tempChatSocket.disconnect();
    },
  },
};
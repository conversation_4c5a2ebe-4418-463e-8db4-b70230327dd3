<template>
  <li class="friend-item" @click="openChat">
    <img class="avatar" :src="getFriendAvatar(friend)" alt="Friend Avatar" />
    <div class="friend-info">
      <span class="friend-username">{{ friend.username }}</span>
      <span class="last-message" :class="{ typing: friend.isTyping }">
        {{ friend.lastMessage }}
      </span>
    </div>
    <div class="meta">
      <div class="timestamp" v-if="friend.lastMessageTime">
        {{ formatTime(friend.lastMessageTime) }}
      </div>
      <div class="unread-count" v-if="friend.unreadCount">
        {{ friend.unreadCount }}
      </div>
    </div>
  </li>
</template>

<script>
export default {
  name: "FriendItem",
  props: {
    friend: { type: Object, required: true },
  },
  methods: {
    openChat() {
      this.$emit('openChat', this.friend.id);
    },
    formatTime(isoString) {
      const date = new Date(isoString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    },
    getFriendAvatar(friend) {
      return friend.avatar ? friend.avatar : '/Avatar/defaultFriend.svg';
    },
  },
};
</script>

<style scoped>
.friend-item {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: 12px;
  margin: 4px 8px;
  touch-action: manipulation; /* Improve touch behavior */
}

.friend-item:hover {
  background-color: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.friend-item:active {
  transform: scale(0.98); /* Add feedback for touch */
  background-color: var(--bg-tertiary);
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  object-fit: cover;
  margin-right: 12px;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
  background-color: var(--bg-tertiary);
  transition: all var(--transition-fast);
}

.friend-item:hover .avatar {
  border-color: var(--accent-primary);
}

.friend-info {
  flex: 1;
  min-width: 0;
  margin-right: 12px;
}

.friend-username {
  display: block;
  color: var(--text-primary);
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 4px;
}

.last-message {
  display: block;
  color: var(--text-secondary);
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-message.typing {
  color: var(--accent-primary);
  font-style: italic;
}

.meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  flex-shrink: 0;
}

.timestamp {
  color: var(--text-secondary);
  font-size: 12px;
}

.unread-count {
  background-color: var(--accent-primary);
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
  font-weight: 500;
  box-shadow: var(--shadow-sm);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .friend-item {
    padding: 10px 12px;
    margin: 3px 6px;
  }
  
  .avatar {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }
  
  .friend-username {
    font-size: 14px;
  }
  
  .last-message {
    font-size: 12px;
  }
  
  .timestamp {
    font-size: 11px;
  }
  
  .unread-count {
    font-size: 11px;
    min-width: 18px;
    padding: 1px 5px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  .friend-item {
    padding: 8px 10px;
    margin: 2px 4px;
  }
  
  .avatar {
    width: 36px;
    height: 36px;
    margin-right: 8px;
  }
  
  .friend-username {
    font-size: 13px;
  }
  
  .last-message {
    font-size: 11px;
  }
}
</style>
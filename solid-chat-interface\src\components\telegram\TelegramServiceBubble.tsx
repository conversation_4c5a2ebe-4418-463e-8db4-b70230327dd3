import { Component } from 'solid-js';
import '../../styles/telegram-bubbles.css';

interface TelegramServiceBubbleProps {
  text: string;
  type?: 'date' | 'system' | 'unread';
  date?: Date | string;
}

const TelegramServiceBubble: Component<TelegramServiceBubbleProps> = (props) => {
  // Format date like Telegram
  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // Check if it's today
    if (d.toDateString() === today.toDateString()) {
      return 'Today';
    }
    
    // Check if it's yesterday
    if (d.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }
    
    // Check if it's this year
    if (d.getFullYear() === today.getFullYear()) {
      return d.toLocaleDateString('en-US', { 
        month: 'long', 
        day: 'numeric' 
      });
    }
    
    // Different year
    return d.toLocaleDateString('en-US', { 
      year: 'numeric',
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getServiceText = () => {
    if (props.type === 'date' && props.date) {
      return formatDate(props.date);
    }
    
    if (props.type === 'unread') {
      return 'Unread messages';
    }
    
    return props.text;
  };

  return (
    <div class="bubble service">
      <div class="service-msg">
        {getServiceText()}
      </div>
    </div>
  );
};

export default TelegramServiceBubble;

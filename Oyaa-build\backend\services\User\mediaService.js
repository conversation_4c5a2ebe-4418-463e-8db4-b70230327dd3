// backend/services/mediaService.js
const Media = require('../models/Media');

class MediaService {
  /**
   * Creates a new media record.
   */
  async uploadMedia({ userId, chatId, mediaUrl, publicId, mediaType }) {
    return await Media.createMedia({ userId, chatId, mediaUrl, publicId, mediaType });
  }
  

  /**
   * Retrieves media items for a given chat.
   */
  async getMediaByChatId(chatId) {
    return await Media.getMediaByChatId(chatId);
  }

  /**
   * Retrieves media items for a given user.
   */
  async getMediaByUserId(userId) {
    return await Media.getMediaByUserId(userId);
  }

  /**
   * Deletes a media record.
   */
  async deleteMedia(mediaId) {
    return await Media.deleteMedia(mediaId);
  }
}

module.exports = new MediaService();

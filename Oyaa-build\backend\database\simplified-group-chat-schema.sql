-- Simplified Group Chat Schema for AlloyDB Omni
-- This script creates the tables needed for the simplified group chat functionality

-- Create group_messages table
CREATE TABLE IF NOT EXISTS group_messages (
  id SERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL,
  sender_id INTEGER,
  message TEXT,
  sent_at TIMESTAMP DEFAULT NOW(),
  reply_id INTEGER,
  system_message BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create group_message_media table
CREATE TABLE IF NOT EXISTS group_message_media (
  id SERIAL PRIMARY KEY,
  message_id INTEGER REFERENCES group_messages(id) ON DELETE CASCADE,
  media_url TEXT NOT NULL,
  media_type VARCHAR(50) NOT NULL,
  public_id VARCHAR(255),
  thumbnail_url TEXT,
  duration INTEGER,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_group_messages_group_id ON group_messages(group_id);
CREATE INDEX IF NOT EXISTS idx_group_messages_sender_id ON group_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_group_messages_sent_at ON group_messages(sent_at);
CREATE INDEX IF NOT EXISTS idx_group_messages_group_sent ON group_messages(group_id, sent_at DESC);
CREATE INDEX IF NOT EXISTS idx_group_messages_reply_id ON group_messages(reply_id);
CREATE INDEX IF NOT EXISTS idx_group_message_media_message_id ON group_message_media(message_id);

-- Add comments to tables and columns for documentation
COMMENT ON TABLE group_messages IS 'Stores all group chat messages';
COMMENT ON COLUMN group_messages.id IS 'Unique identifier for the message';
COMMENT ON COLUMN group_messages.group_id IS 'Reference to the group ID in the main PostgreSQL database';
COMMENT ON COLUMN group_messages.sender_id IS 'User ID of the message sender, NULL for system messages';
COMMENT ON COLUMN group_messages.message IS 'Text content of the message';
COMMENT ON COLUMN group_messages.sent_at IS 'Timestamp when the message was sent';
COMMENT ON COLUMN group_messages.reply_id IS 'Reference to another message if this is a reply';
COMMENT ON COLUMN group_messages.system_message IS 'Flag indicating if this is a system-generated message';

COMMENT ON TABLE group_message_media IS 'Stores media attachments for group chat messages';
COMMENT ON COLUMN group_message_media.message_id IS 'Reference to the associated message';
COMMENT ON COLUMN group_message_media.media_url IS 'URL to the media file';
COMMENT ON COLUMN group_message_media.media_type IS 'Type of media (image, video, audio, etc.)';
COMMENT ON COLUMN group_message_media.public_id IS 'Public ID for cloud storage reference';
COMMENT ON COLUMN group_message_media.thumbnail_url IS 'URL to the thumbnail image for videos';
COMMENT ON COLUMN group_message_media.duration IS 'Duration in seconds for audio/video files';

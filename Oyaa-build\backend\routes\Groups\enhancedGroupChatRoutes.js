// backend/routes/Groups/enhancedGroupChatRoutes.js
const express = require('express');
const enhancedGroupChatController = require('../../controllers/Group/enhancedGroupChatController');
const authMiddleware = require('../../middleware/authMiddleware');
const router = express.Router();

// Route to send a new group chat message, protected by authMiddleware
router.post('/send', authMiddleware, enhancedGroupChatController.sendMessage);

// Route to get messages for a group chat
router.get('/:groupId/messages', authMiddleware, enhancedGroupChatController.getMessages);

// Route to get last chats for a user
router.get('/last-chats', authMiddleware, enhancedGroupChatController.getLastChats);



// Route to send a system message (admin only)
router.post('/system-message', authMiddleware, enhancedGroupChatController.sendSystemMessage);

// Route to synchronize the cache with the database for a group chat (admin only)
router.post('/:groupId/sync-cache', authMiddleware, enhancedGroupChatController.syncGroupCache);

// Message editing and deletion routes
router.put('/messages/:messageId', authMiddleware, enhancedGroupChatController.editMessage);
router.delete('/messages/:messageId', authMiddleware, enhancedGroupChatController.deleteMessage);

module.exports = router;

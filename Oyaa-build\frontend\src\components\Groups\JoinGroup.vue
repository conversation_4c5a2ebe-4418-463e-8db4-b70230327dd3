<template>
  <div class="join-group">
    <div class="header">
      <h2>Join a Group</h2>
      <button class="back-button" @click="$router.push('/dashboard')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
        Back
      </button>
    </div>
    
    <!-- Search Section -->
    <div class="search-container">
      <div class="search-wrapper">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
        </svg>
        <input
          v-model="searchQuery"
          placeholder="Search for a group by name"
          class="search-input"
        />
      </div>
      <button @click="searchGroups" class="search-button">Search</button>
    </div>
    
    <!-- Search Results -->
    <div v-if="groups.length" class="results-container">
      <h3>Search Results</h3>
      <ul class="group-list">
        <li v-for="group in groups" :key="group.id" class="group-item">
          <div class="group-info">
            <div class="group-avatar">
              <img :src="group.avatar || '/Avatar/groups-Avatar/Basketball.svg'" alt="Group Avatar" />
            </div>
            <div class="group-details">
              <span class="group-name">{{ group.name }}</span>
              <span class="group-meta">{{ group.member_count || 0 }} members</span>
            </div>
          </div>
          <button @click="joinGroup(group.id)" class="join-button">Join Group</button>
        </li>
      </ul>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="searchPerformed" class="empty-state">
      <div class="empty-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      </div>
      <p>No groups found matching your search.</p>
      <p>Try a different search term or create a new group.</p>
    </div>
    
    <!-- Initial State -->
    <div v-else class="initial-state">
      <div class="initial-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
        </svg>
      </div>
      <p>Search for groups to join or create your own.</p>
      <button @click="$router.push('/create-group')" class="create-button">Create a Group</button>
    </div>
    
    <!-- Error Message -->
    <p v-if="errorMessage" class="error">{{ errorMessage }}</p>
  </div>
</template>

<script>
import axios from 'axios';
import groupSocket from '@/groupSocket';

export default {
  name: 'JoinGroup',
  data() {
    return {
      searchQuery: '',
      groups: [],
      errorMessage: '',
      searchPerformed: false,
    };
  },
  computed: {
    // Expose the socket instance to use its id when joining
    $groupSocket: () => groupSocket,
  },
  methods: {
    async searchGroups() {
      try {
        this.errorMessage = '';
        this.searchPerformed = true;
        
        if (!this.searchQuery.trim()) {
          this.errorMessage = 'Please enter a search term';
          return;
        }
        
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/groups/search?name=${encodeURIComponent(this.searchQuery)}`
        );
        this.groups = response.data.groups;
      } catch (error) {
        this.errorMessage = error.response?.data?.message || 'Error searching for groups';
      }
    },
    async joinGroup(groupId) {
      try {
        const userId = this.$store.state.auth.user.id;
        // Get the socket ID to ensure the backend can send the join message to the correct socket
        const socketId = this.$groupSocket.id;
        await axios.post(
          `${import.meta.env.VITE_API_BASE_URL}/api/group-members/add`,
          { groupId, userId, socketId }
        );
        // Navigate to the Group Chat page; ensure that page fetches messages on mount for updated history.
        this.$router.push({ name: 'GroupChatPage', params: { groupId } });
      } catch (error) {
        this.errorMessage = error.response?.data?.message || 'Error joining group';
      }
    },
  },
};
</script>

<style scoped>
.join-group {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  max-width: 800px;
  margin: 20px auto;
  padding: 24px;
  background: var(--bg-tertiary);
  border-radius: 16px;
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-button:hover {
  background: var(--accent-tertiary);
  color: var(--accent-primary);
  transform: translateX(-2px);
}

.search-container {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.search-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 0 16px;
  transition: all var(--transition-fast);
}

.search-wrapper:focus-within {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}

.search-wrapper svg {
  color: var(--text-secondary);
  margin-right: 12px;
}

.search-input {
  flex: 1;
  background: none;
  border: none;
  color: var(--text-primary);
  padding: 12px 0;
  font-size: 15px;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.search-button {
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.search-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.search-button:active {
  transform: scale(0.98);
}

.results-container h3 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  color: var(--text-primary);
}

.group-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.group-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-tertiary);
}

.group-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.group-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  background: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
}

.group-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.group-details {
  display: flex;
  flex-direction: column;
}

.group-name {
  font-weight: 500;
  font-size: 16px;
  color: var(--text-primary);
}

.group-meta {
  font-size: 14px;
  color: var(--text-secondary);
}

.join-button {
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.join-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-2px);
}

.join-button:active {
  transform: scale(0.98);
}

.empty-state, .initial-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
  min-height: 300px;
}

.empty-icon, .initial-icon {
  margin-bottom: 16px;
  color: var(--accent-primary);
  opacity: 0.7;
}

.empty-state p, .initial-state p {
  margin: 4px 0;
  font-size: 15px;
}

.empty-state p:first-of-type, .initial-state p:first-of-type {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
}

.create-button {
  margin-top: 16px;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.create-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-2px);
}

.error {
  margin-top: 16px;
  color: #ef4444;
  text-align: center;
  font-size: 14px;
  padding: 8px 16px;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(239, 68, 68, 0.3);
}
</style>
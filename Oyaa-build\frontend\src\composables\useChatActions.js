// frontend/src/composables/useChatActions.js
import { useMessageHandlers } from './useChatActions/messageHandlers';
import { useChatInitialization } from './useChatActions/initialization';
import { useLoadMessages } from './useChatActions/loadMessages';
import { useMessageSending } from './useChatActions/messageSending';
import { useUIHandlers } from './useChatActions/uiHandlers';
import { useMediaHandlers } from './useChatActions/mediaHandlers';
import { useReactions } from './useChatActions/reactions';

export default function useChatActions(state, api, socket, pagination, store, router) {
  // Group the message-related handlers.
  const messageHandlers = useMessageHandlers(state, store);

  // Chat initialization (fetch friend & initial messages, setup socket)
  const init = useChatInitialization(state, api, socket, pagination, store, router, messageHandlers);

  // Loading older messages
  const { loadOlderMessages } = useLoadMessages(state, api, pagination);

  // Sending messages & typing events.
  const messageSending = useMessageSending(state, socket, store);

  // UI-related handlers (search, reply, etc.)
  const uiHandlers = useUIHandlers(state);

  // Media file upload handling.
  const mediaHandlers = useMediaHandlers(state, messageSending.handleSendMessage);

  // Reactions (e.g., message "like" or other emoji reactions)
  const reactions = useReactions();

  return {
    // Initialization returns our initializeChat function.
    ...init,
    // Expose the loadOlderMessages (can be debounced in useChat.js)
    loadOlderMessages,
    // Spread message sending, UI, media and reaction handlers.
    ...messageSending,
    ...uiHandlers,
    ...mediaHandlers,
    ...reactions,
  };
}

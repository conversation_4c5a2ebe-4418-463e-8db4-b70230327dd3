import { io } from 'socket.io-client';
import store from './store';

const groupSocket = io(`${import.meta.env.VITE_API_BASE_URL}/groups`, {
  autoConnect: true,
  withCredentials: true,
  transports: ['websocket'], // Force WebSocket instead of polling
  reconnection: true,        // Enable reconnection
  reconnectionAttempts: 5,   // Number of reconnection attempts
  reconnectionDelay: 1000,   // Initial delay before reconnection
  timeout: 10000            // Connection timeout
});

groupSocket.on('connect', () => {
  console.log('Connected to groups namespace, socket ID:', groupSocket.id);

  // Dispatch an event to the store to notify components that the socket has reconnected
  store.dispatch('groupChat/socketReconnected');
});

groupSocket.on('disconnect', (reason) => {
  console.log('Disconnected from groups namespace, reason:', reason);

  // Set up automatic reconnection with exponential backoff
  let reconnectAttempt = 0;
  const maxReconnectAttempts = 10;

  const attemptReconnect = () => {
    if (reconnectAttempt < maxReconnectAttempts) {
      reconnectAttempt++;
      const delay = Math.min(1000 * Math.pow(2, reconnectAttempt), 30000); // Exponential backoff, max 30 seconds
      console.log(`Scheduling manual reconnect in ${delay}ms (attempt ${reconnectAttempt})`);

      setTimeout(() => {
        console.log(`Attempting manual reconnect (attempt ${reconnectAttempt})`);
        if (!groupSocket.connected) {
          groupSocket.connect();
        }
      }, delay);
    }
  };

  // Start reconnection process
  attemptReconnect();
});

groupSocket.on('connect_error', (error) => {
  console.error('Groups namespace connection error:', error);
});

// For debugging - log all incoming events
groupSocket.onAny((event, ...args) => {
  // Only log certain events to reduce console noise
  if (event !== 'ping' && event !== 'pong') {
    console.log(`Socket event received: ${event}`, args);
  }
});

groupSocket.on('systemMessage', (data) => {
  console.log('System message:', data);
});

groupSocket.on('groupUpdated', (data) => {
  console.log('Group updated:', data);
  store.dispatch('groupChat/updateGroup', data);
});

// Add reconnection method
const reconnect = () => {
  if (!groupSocket.connected) {
    console.log('Attempting to reconnect to group socket');
    groupSocket.connect();
  }
};

// Expose reconnect method
groupSocket.reconnect = reconnect;

export default groupSocket;
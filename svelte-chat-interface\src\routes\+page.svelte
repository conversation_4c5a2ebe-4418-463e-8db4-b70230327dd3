<script lang="ts">
	import ChatInterface from '$lib/components/ChatInterface.svelte';
	import { onMount } from 'svelte';
	import type { Message } from '$lib/types/chat';

	let messages: Message[] = [];

	// Generate sample messages for testing performance
	onMount(() => {
		generateSampleMessages();
	});

	function generateSampleMessages() {
		const messageTypes = ['text', 'image', 'audio', 'video', 'file'];
		const sampleTexts = [
			'Hello there! How are you doing today?',
			'This is a sample message to test the chat interface performance.',
			'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
			'Check out this amazing feature!',
			'The weather is really nice today, don\'t you think?',
			'I just finished working on this project.',
			'What do you think about the new design?',
			'This message contains some emojis 😀 🎉 🚀',
			'Long message: ' + 'A'.repeat(200),
			'Short msg'
		];

		const sampleMedia = [
			{ type: 'image', url: 'https://picsum.photos/400/300?random=', name: 'sample.jpg' },
			{ type: 'audio', url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', name: 'audio.wav' },
			{ type: 'video', url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', name: 'video.mp4' },
			{ type: 'file', url: '#', name: 'document.pdf' }
		];

		// Generate 500 messages for performance testing
		for (let i = 0; i < 500; i++) {
			const isMedia = Math.random() < 0.3; // 30% chance of media
			const messageType = isMedia ?
				messageTypes[Math.floor(Math.random() * (messageTypes.length - 1)) + 1] :
				'text';

			let message: Message = {
				id: `msg-${i}`,
				text: sampleTexts[Math.floor(Math.random() * sampleTexts.length)],
				timestamp: new Date(Date.now() - (500 - i) * 60000), // Messages from 500 minutes ago to now
				sender: {
					id: Math.random() < 0.5 ? 'user1' : 'user2',
					name: Math.random() < 0.5 ? 'Alice' : 'Bob',
					avatar: `https://i.pravatar.cc/40?img=${Math.floor(Math.random() * 20) + 1}`
				},
				type: messageType as any
			};

			if (isMedia && messageType !== 'text') {
				const media = sampleMedia.find(m => m.type === messageType);
				if (media) {
					message.media = {
						url: messageType === 'image' ? media.url + i : media.url,
						type: messageType as any,
						name: media.name,
						size: Math.floor(Math.random() * 1000000) + 100000 // Random size
					};
				}
			}

			messages.push(message);
		}

		messages = messages; // Trigger reactivity
	}

	function handleSendMessage(event: CustomEvent<{text: string, media?: any}>) {
		const { text, media } = event.detail;

		const newMessage: Message = {
			id: `msg-${Date.now()}`,
			text,
			timestamp: new Date(),
			sender: {
				id: 'current-user',
				name: 'You',
				avatar: 'https://i.pravatar.cc/40?img=1'
			},
			type: media ? media.type : 'text',
			media
		};

		messages = [...messages, newMessage];
	}
</script>

<main class="app">
	<header class="app-header">
		<h1>High-Performance Chat Interface</h1>
		<p>Testing with {messages.length} messages</p>
	</header>

	<ChatInterface
		{messages}
		on:sendMessage={handleSendMessage}
	/>
</main>

<style>
	.app {
		height: 100vh;
		display: flex;
		flex-direction: column;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	.app-header {
		padding: 1rem;
		background: #f8f9fa;
		border-bottom: 1px solid #e9ecef;
		text-align: center;
	}

	.app-header h1 {
		margin: 0 0 0.5rem 0;
		font-size: 1.5rem;
		color: #333;
	}

	.app-header p {
		margin: 0;
		color: #666;
		font-size: 0.9rem;
	}

	:global(body) {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	:global(*, *::before, *::after) {
		box-sizing: inherit;
	}
</style>

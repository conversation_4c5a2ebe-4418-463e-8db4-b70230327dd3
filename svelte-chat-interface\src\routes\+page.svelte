<script lang="ts">
	import ChatInterface from '$lib/components/ChatInterface.svelte';
	import DebugMenu from '$lib/components/DebugMenu.svelte';
	import { onMount } from 'svelte';
	import type { Message } from '$lib/types/chat';

	let messages: Message[] = [];
	let chatInterface: any;

	// Debug metrics
	let visibleItems: any[] = [];
	let totalHeight: number = 0;
	let containerHeight: number = 0;
	let scrollTop: number = 0;
	let virtualScrollManager: any = null;

	// Generate sample messages for testing performance
	onMount(() => {
		generateSampleMessages(500); // Start with 500 messages
	});

	function generateSampleMessages(count: number = 500) {
		const messageTypes = ['text', 'image', 'audio', 'video', 'file'];
		const sampleTexts = [
			'Hello there! How are you doing today?',
			'This is a sample message to test the chat interface performance.',
			'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
			'Check out this amazing feature!',
			'The weather is really nice today, don\'t you think?',
			'I just finished working on this project.',
			'What do you think about the new design?',
			'This message contains some emojis 😀 🎉 🚀 💻 🔥 ⚡ 🎯 🚀',
			'Short msg',
			'Medium length message that spans multiple lines and contains various types of content to test rendering performance.',
			'Very long message: ' + 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. '.repeat(10),
			'Super long message: ' + 'A'.repeat(500),
			'Code snippet:\n```javascript\nfunction test() {\n  console.log("Hello World!");\n}\n```',
			'Multi-line\nmessage\nwith\nline\nbreaks',
			'Message with special characters: !@#$%^&*()_+-=[]{}|;:,.<>?',
			'Unicode test: 🌍🌎🌏 🇺🇸🇬🇧🇫🇷 αβγδε 中文测试 العربية',
			'Numbers and dates: 123456789 2024-01-01 12:34:56',
			'URL test: https://example.com/very/long/path/to/resource?param1=value1&param2=value2',
			'Markdown-like: **bold** *italic* `code` [link](url)',
			'Just a single character: A',
			'Empty-ish:   ',
			'Repeated pattern: ' + 'ABC123 '.repeat(20)
		];

		const sampleMedia = [
			{ type: 'image', url: 'https://picsum.photos/400/300?random=', name: 'sample.jpg' },
			{ type: 'audio', url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', name: 'audio.wav' },
			{ type: 'video', url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', name: 'video.mp4' },
			{ type: 'file', url: '#', name: 'document.pdf' }
		];

		// Generate messages for performance testing
		const startIndex = messages.length;
		for (let i = 0; i < count; i++) {
			const isMedia = Math.random() < 0.3; // 30% chance of media
			const messageType = isMedia ?
				messageTypes[Math.floor(Math.random() * (messageTypes.length - 1)) + 1] :
				'text';

			let message: Message = {
				id: `msg-${startIndex + i}-${Date.now()}`,
				text: sampleTexts[Math.floor(Math.random() * sampleTexts.length)],
				timestamp: new Date(Date.now() - (count - i) * 60000), // Messages from count minutes ago to now
				sender: {
					id: Math.random() < 0.5 ? 'user1' : 'user2',
					name: Math.random() < 0.5 ? 'Alice' : 'Bob',
					avatar: `https://i.pravatar.cc/40?img=${Math.floor(Math.random() * 20) + 1}`
				},
				type: messageType as any
			};

			if (isMedia && messageType !== 'text') {
				const media = sampleMedia.find(m => m.type === messageType);
				if (media) {
					message.media = {
						url: messageType === 'image' ? media.url + (startIndex + i) : media.url,
						type: messageType as any,
						name: media.name,
						size: Math.floor(Math.random() * 1000000) + 100000 // Random size
					};
				}
			}

			messages.push(message);
		}

		messages = messages; // Trigger reactivity
	}

	function handleSendMessage(event: CustomEvent<{text: string, media?: any}>) {
		const { text, media } = event.detail;

		const newMessage: Message = {
			id: `msg-${Date.now()}`,
			text,
			timestamp: new Date(),
			sender: {
				id: 'current-user',
				name: 'You',
				avatar: 'https://i.pravatar.cc/40?img=1'
			},
			type: media ? media.type : 'text',
			media
		};

		messages = [...messages, newMessage];
	}

	function handleGenerateMessages(event: CustomEvent<{ count: number }>) {
		const { count } = event.detail;
		generateSampleMessages(count);
	}

	function handleClearMessages() {
		messages = [];
	}

	function updateDebugMetrics(event: CustomEvent<any>) {
		const { visibleItems: vi, totalHeight: th, containerHeight: ch, scrollTop: st, virtualScrollManager: vsm } = event.detail;
		visibleItems = vi;
		totalHeight = th;
		containerHeight = ch;
		scrollTop = st;
		virtualScrollManager = vsm;
	}
</script>

<main class="app">
	<header class="app-header">
		<h1>High-Performance Chat Interface</h1>
		<p>Testing with {messages.length} messages</p>
	</header>

	<ChatInterface
		bind:this={chatInterface}
		{messages}
		on:sendMessage={handleSendMessage}
		on:debugMetrics={updateDebugMetrics}
	/>

	<DebugMenu
		{messages}
		{visibleItems}
		{totalHeight}
		{containerHeight}
		{scrollTop}
		{virtualScrollManager}
		on:generateMessages={handleGenerateMessages}
		on:clearMessages={handleClearMessages}
	/>
</main>

<style>
	.app {
		height: 100vh;
		display: flex;
		flex-direction: column;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	.app-header {
		padding: 1rem;
		background: #f8f9fa;
		border-bottom: 1px solid #e9ecef;
		text-align: center;
	}

	.app-header h1 {
		margin: 0 0 0.5rem 0;
		font-size: 1.5rem;
		color: #333;
	}

	.app-header p {
		margin: 0;
		color: #666;
		font-size: 0.9rem;
	}

	:global(body) {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	:global(*, *::before, *::after) {
		box-sizing: inherit;
	}
</style>

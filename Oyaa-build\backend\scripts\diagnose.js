#!/usr/bin/env node

/**
 * Diagnostic script to check for common issues in the codebase
 * Run with: node scripts/diagnose.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

// Print header
console.log(`${colors.bold}${colors.cyan}=== Oyaa Backend Diagnostic Tool ===${colors.reset}\n`);

// Check Node.js version
console.log(`${colors.bold}Node.js Version:${colors.reset}`);
const nodeVersion = process.version;
console.log(`  ${nodeVersion}`);
const recommendedNodeVersion = 'v18.0.0';
if (nodeVersion.localeCompare(recommendedNodeVersion, undefined, { numeric: true, sensitivity: 'base' }) < 0) {
  console.log(`  ${colors.yellow}⚠️ Warning: Node.js version is older than recommended (${recommendedNodeVersion})${colors.reset}`);
} else {
  console.log(`  ${colors.green}✓ Node.js version is up to date${colors.reset}`);
}
console.log();

// Check npm version
console.log(`${colors.bold}npm Version:${colors.reset}`);
try {
  const npmVersion = execSync('npm -v').toString().trim();
  console.log(`  ${npmVersion}`);
  console.log(`  ${colors.green}✓ npm is installed${colors.reset}`);
} catch (error) {
  console.log(`  ${colors.red}✗ Error checking npm version: ${error.message}${colors.reset}`);
}
console.log();

// Check system info
console.log(`${colors.bold}System Information:${colors.reset}`);
console.log(`  Platform: ${os.platform()}`);
console.log(`  Architecture: ${os.arch()}`);
console.log(`  CPU Cores: ${os.cpus().length}`);
console.log(`  Total Memory: ${Math.round(os.totalmem() / (1024 * 1024 * 1024))} GB`);
console.log(`  Free Memory: ${Math.round(os.freemem() / (1024 * 1024 * 1024))} GB`);
console.log();

// Check for required files
console.log(`${colors.bold}Required Files:${colors.reset}`);
const requiredFiles = [
  '.env',
  'server.key',
  'server.cert',
  'app.js',
  'server.js'
];

requiredFiles.forEach(file => {
  if (fs.existsSync(path.join(__dirname, '..', file))) {
    console.log(`  ${colors.green}✓ ${file} exists${colors.reset}`);
  } else {
    console.log(`  ${colors.red}✗ ${file} is missing${colors.reset}`);
  }
});
console.log();

// Check environment variables
console.log(`${colors.bold}Environment Variables:${colors.reset}`);
try {
  require('dotenv').config({ path: path.join(__dirname, '..', '.env') });
  
  const requiredEnvVars = [
    'DB_USER',
    'DB_PASSWORD',
    'DB_HOST',
    'DB_PORT',
    'DB_NAME',
    'JWT_SECRET',
    'VALKEY_URI'
  ];
  
  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      console.log(`  ${colors.green}✓ ${envVar} is set${colors.reset}`);
    } else {
      console.log(`  ${colors.red}✗ ${envVar} is missing${colors.reset}`);
    }
  });
} catch (error) {
  console.log(`  ${colors.red}✗ Error loading .env file: ${error.message}${colors.reset}`);
}
console.log();

// Check for SSL certificates
console.log(`${colors.bold}SSL Certificates:${colors.reset}`);
try {
  const keyStats = fs.statSync(path.join(__dirname, '..', 'server.key'));
  const certStats = fs.statSync(path.join(__dirname, '..', 'server.cert'));
  
  console.log(`  ${colors.green}✓ server.key exists (${keyStats.size} bytes)${colors.reset}`);
  console.log(`  ${colors.green}✓ server.cert exists (${certStats.size} bytes)${colors.reset}`);
  
  // Check if they're valid files (not empty)
  if (keyStats.size < 100) {
    console.log(`  ${colors.yellow}⚠️ Warning: server.key seems too small (${keyStats.size} bytes)${colors.reset}`);
  }
  
  if (certStats.size < 100) {
    console.log(`  ${colors.yellow}⚠️ Warning: server.cert seems too small (${certStats.size} bytes)${colors.reset}`);
  }
} catch (error) {
  console.log(`  ${colors.red}✗ Error checking SSL certificates: ${error.message}${colors.reset}`);
}
console.log();

// Check for logs directory
console.log(`${colors.bold}Logs Directory:${colors.reset}`);
const logsDir = path.join(__dirname, '..', 'logs');
if (fs.existsSync(logsDir)) {
  console.log(`  ${colors.green}✓ logs directory exists${colors.reset}`);
  
  // Check log files
  try {
    const logFiles = fs.readdirSync(logsDir);
    console.log(`  Found ${logFiles.length} log files:`);
    logFiles.forEach(file => {
      try {
        const stats = fs.statSync(path.join(logsDir, file));
        const fileSizeInMB = stats.size / (1024 * 1024);
        console.log(`    - ${file} (${fileSizeInMB.toFixed(2)} MB)`);
      } catch (error) {
        console.log(`    - ${file} (error getting size: ${error.message})`);
      }
    });
  } catch (error) {
    console.log(`  ${colors.red}✗ Error reading logs directory: ${error.message}${colors.reset}`);
  }
} else {
  console.log(`  ${colors.yellow}⚠️ logs directory doesn't exist yet (will be created on first run)${colors.reset}`);
}
console.log();

// Check database connection
console.log(`${colors.bold}Database Connection:${colors.reset}`);
try {
  // Try to require the database module
  const db = require('../utils/db');
  console.log(`  ${colors.green}✓ Database module loaded${colors.reset}`);
  
  // Try to connect to the database
  console.log(`  Attempting to connect to database...`);
  
  // This is an async operation, but we're in a script, so we'll use a simple approach
  db.pool.connect((err, client, done) => {
    if (err) {
      console.log(`  ${colors.red}✗ Failed to connect to database: ${err.message}${colors.reset}`);
      if (err.code) {
        console.log(`    Error code: ${err.code}`);
      }
      if (err.detail) {
        console.log(`    Error detail: ${err.detail}`);
      }
    } else {
      console.log(`  ${colors.green}✓ Successfully connected to database${colors.reset}`);
      client.query('SELECT NOW() as now', (err, result) => {
        done();
        if (err) {
          console.log(`  ${colors.red}✗ Failed to query database: ${err.message}${colors.reset}`);
        } else {
          console.log(`  ${colors.green}✓ Database query successful: ${result.rows[0].now}${colors.reset}`);
        }
        
        // Exit after database check
        process.exit(0);
      });
    }
  });
} catch (error) {
  console.log(`  ${colors.red}✗ Error loading database module: ${error.message}${colors.reset}`);
  process.exit(1);
}

// frontend/src/composables/useChatState.js
import { ref, computed } from 'vue';

export default function useChatState(route, store) {
  const friend = ref(null);
  const messages = ref([]);
  const isFriendTyping = ref(false);
  const messagesLoaded = ref(false);
  const searchQuery = ref('');
  const replyingMessage = ref(null);

  const currentUser = computed(() => store.getters['auth/user']);
  const friendId = computed(() => route.params.friendId);

  return {
    friend,
    messages,
    isFriendTyping,
    messagesLoaded,
    searchQuery,
    replyingMessage,
    currentUser,
    friendId,
  };
}

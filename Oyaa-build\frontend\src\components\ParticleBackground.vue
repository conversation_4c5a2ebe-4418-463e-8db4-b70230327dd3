<!-- frontend/src/components/ParticleBackground.vue -->
<template>
    <canvas ref="canvasRef" class="particle-canvas"></canvas>
  </template>
  
  <script setup>
  import { ref, onMounted, onUnmounted, watch } from 'vue';
  
  const props = defineProps({
    theme: <PERSON>olean, // true for dark mode, false for light mode
  });
  
  const canvasRef = ref(null);
  let particles = [];
  const particleCount = 30;
  
  class Particle {
    constructor(canvas, isDarkMode) {
      this.x = Math.random() * canvas.width;
      this.y = Math.random() * canvas.height;
      this.size = Math.random() * 3 + 1;
      this.speedX = Math.random() * 0.5 - 0.25;
      this.speedY = Math.random() * 0.5 - 0.25;
      this.color = isDarkMode
        ? `rgba(255, 255, 255, ${Math.random() * 0.2})`
        : `rgba(0, 0, 0, ${Math.random() * 0.15})`;
    }
  
    update(canvas) {
      this.x += this.speedX;
      this.y += this.speedY;
      if (this.x > canvas.width) this.x = 0;
      if (this.x < 0) this.x = canvas.width;
      if (this.y > canvas.height) this.y = 0;
      if (this.y < 0) this.y = canvas.height;
    }
  
    draw(ctx) {
      ctx.fillStyle = this.color;
      ctx.beginPath();
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
      ctx.fill();
    }
  }
  
  const init = (canvas, isDarkMode) => {
    particles = [];
    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle(canvas, isDarkMode));
    }
  };
  
  const animate = (ctx, canvas) => {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    for (const particle of particles) {
      particle.update(canvas);
      particle.draw(ctx);
    }
    requestAnimationFrame(() => animate(ctx, canvas));
  };
  
  onMounted(() => {
    const canvas = canvasRef.value;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
  
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  
    init(canvas, props.theme);
    animate(ctx, canvas);
  
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    window.addEventListener('resize', handleResize);
  
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize);
    });
  });
  
  watch(() => props.theme, (newTheme) => {
    const canvas = canvasRef.value;
    if (canvas) init(canvas, newTheme);
  });
  </script>
  
  <style scoped>
  .particle-canvas {
    position: absolute;
    inset: 0;
    z-index: 0;
    opacity: 0.7;
  }
  </style>
<!-- frontend/src/components/chatPage/MessageInputComponents/ActionButtons.vue -->
<template>
  <div class="action-buttons">
    <!-- If no text and no attachment, show the regular buttons -->
    <template v-if="!message.trim() && !hasAttachment">
      <button class="add-button" @click="triggerAttachmentInput">
        <PlusIcon class="icon" />
      </button>
      <button class="action-button">
        <ImageIcon class="icon" />
      </button>
    </template>
    <!-- Otherwise, show the send button -->
    <button
      v-else
      class="action-button send-button"
      @click="sendMessage"
      :disabled="isOverCharLimit"
    >
      <SendIcon class="icon send-icon" />
    </button>
  </div>
</template>

<script setup>
import { Plus as PlusIcon, Image as ImageIcon, Send as SendIcon } from 'lucide-vue-next'

const props = defineProps({
  message: {
    type: String,
    default: ''
  },
  isOverCharLimit: {
    type: Boolean,
    default: false
  },
  hasAttachment: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['triggerAttachmentInput', 'sendMessage'])

const triggerAttachmentInput = () => {
  emit('triggerAttachmentInput')
}
const sendMessage = () => {
  emit('sendMessage')
}
</script>

<style scoped>
.action-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
  padding-bottom: 10px;
}
.add-button,
.action-button {
  background: transparent;
  border: none;
  padding: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}
.add-button:hover,
.action-button:hover {
  transform: scale(1.1);
}
.send-button {
  padding: 6px;
}
.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.icon {
  width: 20px;
  height: 20px;
  color: #949ba4;
}
.send-icon {
  color: #00a8fc;
}
</style>
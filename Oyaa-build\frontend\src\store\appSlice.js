import axios from '@/api/axios';

export default {
  namespaced: true,
  state: {
    lastSeenRequests: 0,
    pendingNotifications: [],
    notifiedRequests: new Set(),
    notifiedGroupRequests: new Set(),
    activeChatFriend: null,
    messages: [],
    blockedUsers: [], // Store blocked users
    toasts: [] // Store toast notifications
  },
  mutations: {
    MARK_AS_SEEN(state) {
      state.lastSeenRequests = Date.now();
      state.pendingNotifications = [];
      localStorage.setItem('lastSeenRequests', state.lastSeenRequests);
    },
    ADD_NOTIFICATION(state, request) {
      if (!state.pendingNotifications.some((r) => r.id === request.id)) {
        state.pendingNotifications.push(request);
      }
    },
    REMOVE_NOTIFICATION(state, id) {
      state.pendingNotifications = state.pendingNotifications.filter(
        (notif) => notif.id !== id
      );
    },
    SET_LAST_SEEN_FRIEND_REQUESTS(state, timestamp) {
      state.lastSeenRequests = timestamp;
    },
    CLEAR_NOTIFIED_REQUESTS(state) {
      state.pendingNotifications = [];
    },
    ADD_SYSTEM_MESSAGE(state, message) {
      state.messages.push(message);
    },
    SET_ACTIVE_CHAT_FRIEND(state, friend) {
      state.activeChatFriend = friend;
    },
    SET_BLOCKED_USERS(state, users) {
      state.blockedUsers = users;
    },
    ADD_BLOCKED_USER(state, user) {
      if (!state.blockedUsers.some((u) => u.id === user.id)) {
        state.blockedUsers.push(user);
      }
    },
    REMOVE_BLOCKED_USER(state, userId) {
      state.blockedUsers = state.blockedUsers.filter((u) => u.id !== userId);
    },
    FILTER_BLOCKED_MESSAGES(state) {
      state.messages = state.messages.filter(
        (message) => !state.blockedUsers.some((blocked) => blocked.id === message.sender_id)
      );
    },
    // Toast notifications mutations
    ADD_TOAST(state, toast) {
      state.toasts.push(toast);
    },
    REMOVE_TOAST(state, id) {
      state.toasts = state.toasts.filter(toast => toast.id !== id);
    }
  },
  actions: {
    handleNewRequest({ commit }, request) {
      const lastSeen = localStorage.getItem('lastSeenRequests') || 0;
      if (new Date(request.createdAt) > new Date(lastSeen)) {
        commit('ADD_NOTIFICATION', request);
      }
    },
    addNotifiedRequest({ state }, requestId) {
      if (!state.notifiedRequests.has(requestId)) {
        state.notifiedRequests.add(requestId);
      }
    },
    addNotifiedGroupRequest({ state }, requestId) {
      if (!state.notifiedGroupRequests.has(requestId)) {
        state.notifiedGroupRequests.add(requestId);
      }
    },
    async fetchRequests({ commit, rootState }) {
      if (!rootState.auth.token) {
        console.log('User not authenticated, skipping fetchRequests');
        return;
      }
      try {
        const lastSeen = localStorage.getItem('lastSeenRequests') || 0;
        const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

        // Check if we have a numeric user ID
        const userId = rootState.auth.user?.id;
        if (!userId) {
          console.log('No numeric user ID available, skipping fetchRequests');
          return;
        }

        const response = await axios.get(`${API_BASE_URL}/api/friend-requests`, {
          params: { since: lastSeen },
          headers: { Authorization: `Bearer ${rootState.auth.token}` },
        });
        if (Array.isArray(response.data?.data)) {
          response.data.data.forEach((request) => {
            commit('ADD_NOTIFICATION', request);
          });
        }
      } catch (error) {
        console.error('Error fetching new requests:', error);
      }
    },
    updateLastSeenFriendRequests({ commit }, timestamp) {
      commit('SET_LAST_SEEN_FRIEND_REQUESTS', timestamp);
    },
    clearNotifiedRequests({ commit }) {
      commit('CLEAR_NOTIFIED_REQUESTS');
    },
    handleSystemMessage({ commit }, message) {
      commit('ADD_SYSTEM_MESSAGE', message);
    },
    openTempChat({ commit }, friend) {
      commit('SET_ACTIVE_CHAT_FRIEND', friend);
      return Promise.resolve();
    },
    async blockUser({ commit }, user) {
      try {
        await axios.post('/api/users/block-user', { blockedId: user.id });
        commit('ADD_BLOCKED_USER', user);
        commit('FILTER_BLOCKED_MESSAGES'); // Immediately filter existing messages
      } catch (error) {
        console.error('Error blocking user:', error);
      }
    },
    async unblockUser({ commit }, userId) {
      try {
        await axios.delete(`/api/users/block-user/${userId}`);
        commit('REMOVE_BLOCKED_USER', userId);
        // Optionally refetch messages if needed
      } catch (error) {
        console.error('Error unblocking user:', error);
      }
    },
    async fetchBlockedUsers({ commit }) {
      try {
        const response = await axios.get('/api/users/blocked-users');
        commit('SET_BLOCKED_USERS', response.data.blockedUsers || []);
      } catch (error) {
        console.error('Error fetching blocked users:', error);
      }
    },
    // Toast notification actions
    showToast({ commit }, { message, type = 'info', duration = 3000 }) {
      const id = Date.now();
      commit('ADD_TOAST', { id, message, type });

      // Auto-remove toast after duration
      setTimeout(() => {
        commit('REMOVE_TOAST', id);
      }, duration);

      return id;
    },
    removeToast({ commit }, id) {
      commit('REMOVE_TOAST', id);
    }
  }
};
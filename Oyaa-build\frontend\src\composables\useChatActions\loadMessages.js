// frontend/src/composables/useChatActions/loadMessages.js
import { formatTimestamp, formatReply } from '../messageFormatter';

export function useLoadMessages(state, api, pagination) {
  const loadOlderMessages = async () => {
    if (!pagination.hasMoreMessages.value || pagination.isLoadingOlder.value) return;
    pagination.isLoadingOlder.value = true;
    try {
      const nextPage = pagination.currentPage.value + 1;
      const olderMessages = await api.fetchMessages(
        state.currentUser.value.id,
        state.friendId.value,
        nextPage,
        pagination.pageSize,
        localStorage.getItem('token')
      );
      const formattedMessages = olderMessages.map((message) => ({
        ...message,
        rawSentAt: message.sent_at,
        sent_at: formatTimestamp(message.sent_at),
        reply: message.reply_id ? formatReply(message) : null,
        media: message.media_url
          ? {
              mediaUrl: message.media_url,
              mediaType: message.media_type,
              publicId: message.public_id,
            }
          : null,
      }));

      // Filter out duplicate messages and prepend.
      const newMessages = formattedMessages.filter(
        (msg) => !state.messages.value.some((existing) => existing.id === msg.id)
      );
      if (newMessages.length < pagination.pageSize) pagination.hasMoreMessages.value = false;
      state.messages.value = [...newMessages, ...state.messages.value];
      pagination.incrementPage();
    } catch (error) {
      console.error('Error loading older messages:', error);
    } finally {
      pagination.isLoadingOlder.value = false;
    }
  };

  return { loadOlderMessages };
}

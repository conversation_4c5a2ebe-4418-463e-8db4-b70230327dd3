<template>
  <div class="chat-input">
    <textarea
      ref="messageInput"
      v-model="newMessage"
      placeholder="Type a message..."
      @keydown.enter.prevent="sendMessage"
      @input="handleInput"
    ></textarea>
    <button class="send-button" @click="sendMessage" :disabled="!newMessage.trim()">
      <span class="send-icon">➤</span>
    </button>
  </div>
</template>

<script>
import { ref, nextTick, onUnmounted } from "vue"

export default {
  name: "ChatInputArea",

  emits: ["send-message"],

  setup(props, { emit }) {
    // References
    const messageInput = ref(null)

    // State
    const newMessage = ref("")
    const isTyping = ref(false)
    const typingTimeout = ref(null)

    // Handle input
    const handleInput = () => {
      // Clear previous timeout
      if (typingTimeout.value) {
        clearTimeout(typingTimeout.value)
      }

      // Set typing state
      if (!isTyping.value) {
        isTyping.value = true
        // Emit typing started event (if needed)
      }

      // Set timeout to clear typing state
      typingTimeout.value = setTimeout(() => {
        isTyping.value = false
        // Emit typing stopped event (if needed)
      }, 2000)

      // Auto-resize textarea
      nextTick(() => {
        if (messageInput.value) {
          messageInput.value.style.height = "auto"
          messageInput.value.style.height = `${messageInput.value.scrollHeight}px`
        }
      })
    }

    // Send message
    const sendMessage = () => {
      const content = newMessage.value.trim()
      if (!content) return

      // Emit send message event
      emit("send-message", content)

      // Clear input
      newMessage.value = ""

      // Reset textarea height
      if (messageInput.value) {
        messageInput.value.style.height = "auto"
      }

      // Focus input
      nextTick(() => {
        if (messageInput.value) {
          messageInput.value.focus()
        }
      })
    }

    // Lifecycle hooks
    onUnmounted(() => {
      // Clear typing timeout
      if (typingTimeout.value) {
        clearTimeout(typingTimeout.value)
      }
    })

    // Focus the input when component is mounted
    const focusInput = () => {
      nextTick(() => {
        if (messageInput.value) {
          messageInput.value.focus()
        }
      })
    }

    // Expose methods to parent component
    return {
      messageInput,
      newMessage,
      handleInput,
      sendMessage,
      focusInput
    }
  },
}
</script>

<style scoped>
.chat-input {
  display: flex;
  align-items: flex-end;
  padding: 10px 15px;
  border-top: 1px solid #333;
  background-color: #1E1E1E;
  flex-shrink: 0;
}

textarea {
  flex-grow: 1;
  background-color: #333;
  border: none;
  border-radius: 20px;
  padding: 10px 15px;
  color: white;
  resize: none;
  max-height: 120px;
  min-height: 40px;
  margin-right: 10px;
  outline: none;
  font-family: inherit;
  font-size: 14px;
}

.send-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #0084FF;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: #0078E7;
}

.send-button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.send-icon {
  font-size: 16px;
  transform: rotate(90deg);
}
</style>

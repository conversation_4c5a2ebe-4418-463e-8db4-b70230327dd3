// frontend/src/services/sseService.js
import store from '@/store';
import { getIdToken } from '@/services/firebaseService';

let eventSource = null;
let reconnectTimer = null;
const RECONNECT_DELAY = 5000; // 5 seconds

/**
 * Initialize the SSE connection
 */
export async function initSSE() {
  // Close any existing connection
  closeSSE();

  try {
    // Try to get the Firebase UID from the store
    const firebaseUid = store.getters['auth/firebaseUid'];

    // If no Firebase UID is available, try to use the user ID from the store
    if (!firebaseUid) {
      console.log('No Firebase UID available, checking for user ID');

      // Get the user ID from the store
      const userId = store.getters['auth/userId'];

      if (!userId) {
        console.error('Cannot initialize SSE: No Firebase UID or user ID available');
        return;
      }

      // Use the user ID as a fallback
      console.log('Using user ID as fallback for SSE connection:', userId);
      return initSSEWithUserId(userId);
    }

    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

    // Get a fresh Firebase ID token
    let token;
    try {
      token = await getIdToken(true); // Force refresh the token
    } catch (tokenError) {
      console.error('Failed to get Firebase token:', tokenError);
      token = localStorage.getItem('token'); // Fallback to stored token
    }

    if (!token) {
      console.error('Cannot initialize SSE: No authentication token available');
      return;
    }

    const url = `${baseUrl}/api/sse/connect/${firebaseUid}?token=${token}`;
    console.log('Initializing SSE connection to:', url);

    eventSource = new EventSource(url);

    // Connection opened
    eventSource.onopen = () => {
      store.dispatch('app/showToast', {
        message: 'Real-time updates connected',
        type: 'success',
        duration: 3000
      });
    };

    // Add error event listener
    eventSource.addEventListener('error', () => {
      console.error('SSE connection error event');
    });

    // Listen for messages
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // Handle different types of updates
        handleSSEUpdate(data);
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };

    // Handle errors
    eventSource.onerror = () => {
      // Close the connection
      closeSSE();

      // Try to reconnect
      if (!reconnectTimer) {
        reconnectTimer = setTimeout(() => {
          initSSE();
          reconnectTimer = null;
        }, RECONNECT_DELAY);
      }
    };
  } catch (error) {
    console.error('Error initializing SSE:', error);
  }
}

/**
 * Close the SSE connection
 */
export function closeSSE() {
  if (eventSource) {
    eventSource.close();
    eventSource = null;
  }

  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
}

/**
 * Handle SSE updates
 * @param {Object} data - The update data
 */
function handleSSEUpdate(data) {
  if (!data || !data.type) {
    console.error('Invalid SSE update data');
    return;
  }

  switch (data.type) {
    case 'friendRequestUpdate':
      handleFriendRequestUpdate(data);
      break;

    case 'notification':
      handleNotification(data);
      break;
  }
}

/**
 * Handle friend request updates
 * @param {Object} data - The update data
 */
function handleFriendRequestUpdate(data) {
  // Refresh the appropriate lists based on the action
  switch (data.action) {
    case 'new':
      // Refresh received requests when a new request is received
      store.dispatch('friendRequests/fetchReceivedRequests');
      break;

    case 'accepted':
      // Refresh both lists when a request is accepted
      store.dispatch('friendRequests/fetchReceivedRequests');
      store.dispatch('friendRequests/fetchSentRequests');
      break;

    case 'rejected':
      // Refresh both lists when a request is rejected
      store.dispatch('friendRequests/fetchReceivedRequests');
      store.dispatch('friendRequests/fetchSentRequests');
      break;

    case 'test':
      // Test update - only show toast for test messages
      store.dispatch('friendRequests/fetchReceivedRequests');
      store.dispatch('friendRequests/fetchSentRequests');

      // Show a toast notification only for test messages
      store.dispatch('app/showToast', {
        message: data.message || 'Test SSE update received',
        type: 'info',
        duration: 5000
      });
      break;

    default:
      // Default to refreshing both lists
      store.dispatch('friendRequests/fetchReceivedRequests');
      store.dispatch('friendRequests/fetchSentRequests');
  }
}

/**
 * Handle notification updates
 * @param {Object} data - The notification data
 */
function handleNotification(data) {
  // Add the notification to the store
  store.dispatch('notifications/addNotification', data);
}

/**
 * Initialize the SSE connection using a user ID instead of Firebase UID
 * @param {string|number} userId - The user ID to use for the connection
 */
async function initSSEWithUserId(userId) {
  // Close any existing connection
  closeSSE();

  try {
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

    // Get the token from localStorage
    const token = localStorage.getItem('token');

    if (!token) {
      console.error('Cannot initialize SSE with user ID: No authentication token available');
      return;
    }

    const url = `${baseUrl}/api/sse/connect/user/${userId}?token=${token}`;
    console.log('Initializing SSE connection with user ID to:', url);

    eventSource = new EventSource(url);

    // Connection opened
    eventSource.onopen = () => {
      store.dispatch('app/showToast', {
        message: 'Real-time updates connected',
        type: 'success',
        duration: 3000
      });
    };

    // Add error event listener
    eventSource.addEventListener('error', () => {
      console.error('SSE connection error event');
    });

    // Listen for messages
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // Handle different types of updates
        handleSSEUpdate(data);
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };

    // Handle errors
    eventSource.onerror = () => {
      // Close the connection
      closeSSE();

      // Try to reconnect
      if (!reconnectTimer) {
        reconnectTimer = setTimeout(() => {
          initSSEWithUserId(userId);
          reconnectTimer = null;
        }, RECONNECT_DELAY);
      }
    };
  } catch (error) {
    console.error('Error initializing SSE with user ID:', error);
  }
}

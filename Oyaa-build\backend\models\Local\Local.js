// backend/models/Local.js
const { Client } = require('pg');
require('dotenv').config();

class Local {
  constructor() {
    this.client = new Client({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      database: process.env.DB_NAME,
      ssl: { rejectUnauthorized: false }, // Allow self-signed certificates
    });
  }

  async connect() {
    await this.client.connect();
    console.log('Locals DB connected successfully');
  }

  async addLocal(userId, localId, username, data) {
    const query = `
      INSERT INTO locals (user_id, local_id, local_username, data)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (user_id, local_id)
      DO UPDATE SET local_username = EXCLUDED.local_username,
                    data = EXCLUDED.data
      RETURNING *;
    `;
    const values = [userId, localId, username, data];
    const result = await this.client.query(query, values);
    return result.rows[0];
  }

  async getLocals(userId) {
    const query = `
      SELECT l.user_id, l.local_id, u.username AS local_username, u.avatar, l.data,
             tc.message AS last_message, tc.sent_at AS last_message_time, tc.sender_id AS last_message_sender_id
      FROM locals l
      LEFT JOIN users u ON l.local_id = u.id::varchar
      LEFT JOIN LATERAL (
        SELECT message, sent_at, sender_id
        FROM temp_chats
        WHERE (sender_id = l.user_id AND receiver_id = l.local_id)
           OR (sender_id::varchar = l.local_id AND receiver_id = l.user_id::varchar)
        ORDER BY sent_at DESC
        LIMIT 1
      ) tc ON true
      WHERE l.user_id = $1;
    `;
    const result = await this.client.query(query, [userId]);
    return result.rows;
  }
}

module.exports = Local;

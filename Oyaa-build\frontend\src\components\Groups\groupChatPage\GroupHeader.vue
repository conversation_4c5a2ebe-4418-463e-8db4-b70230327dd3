<template>
  <div class="group-header" :class="{ 'search-active': isSearchActive }">
    <transition name="fade" mode="out-in">
      <div class="header-content" :key="isSearchActive ? 'search' : 'header'">
        <GroupInfo v-if="!isSearchActive" :group="group" :typingUsers="typingUsers" />
        <SearchBar
          v-else
          :searchQuery="searchQuery"
          @update:searchQuery="searchQuery = $event"
          @search="handleSearch"
          @clear="clearSearch"
          @cancel="cancelSearch"
        />
        <ActionButtons
          :isSearchActive="isSearchActive"
          @start-search="startSearch"
          @cancel-search="cancelSearch"
          @leave-group="handleLeaveGroup"
          @show-group-info="showGroupInfoModal = true"
          @show-report-modal="showReportModal = true"
        />
      </div>
    </transition>
    <GroupInfoModal :show="showGroupInfoModal" :group="group" @close="showGroupInfoModal = false" />
    <ReportGroupModal
      :show="showReportModal"
      :group="group"
      @close="showReportModal = false"
      @submit-report="handleSubmitReport"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import GroupInfo from './HeaderComponents/GroupHeaderInfo.vue';
import SearchBar from './HeaderComponents/SearchBar.vue';
import ActionButtons from './HeaderComponents/ActionButtons.vue';
import GroupInfoModal from './Modals/GroupInfoModal.vue';
import ReportGroupModal from './Modals/ReportGroupModal.vue';

const props = defineProps({
  group: {
    type: Object,
    required: true,
  },
  typingUsers: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['search', 'leave-group']);

const isSearchActive = ref(false);
const searchQuery = ref('');
const showGroupInfoModal = ref(false);
const showReportModal = ref(false);

const startSearch = () => {
  isSearchActive.value = true;
};

const handleSearch = (query) => {
  emit('search', query);
};

const clearSearch = () => {
  searchQuery.value = '';
  emit('search', '');
};

const cancelSearch = () => {
  isSearchActive.value = false;
  searchQuery.value = '';
  emit('search', '');
};

const handleLeaveGroup = () => {
  emit('leave-group');
};

const handleSubmitReport = (reason) => {
  console.log(`Reporting group for reason: ${reason}`);
  // Future: Add API call to submit the report
};
</script>

<style scoped>
.group-header {
  background: linear-gradient(to right, #2c2d35, #23242b);
  padding: 10px 16px;
  color: #ffffff;
  display: flex;
  align-items: center;
  height: 64px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
}

.group-header.search-active {
  background: #23242b;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .group-header {
    height: 56px;
    padding: 8px 12px;
  }
}

/* Safe area insets for notched phones */
@supports (padding: max(0px)) {
  .group-header {
    padding-left: max(16px, env(safe-area-inset-left, 16px));
    padding-right: max(16px, env(safe-area-inset-right, 16px));
    padding-top: max(10px, env(safe-area-inset-top, 10px));
  }
}
</style>
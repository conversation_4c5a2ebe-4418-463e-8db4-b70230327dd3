// frontend/src/composables/useChatNotifications.js
import { io } from 'socket.io-client';

export default function useChatNotifications() {
  const setupChatWebSocket = (callback) => {
    const socket = io(`${import.meta.env.VITE_WS_URL}/chat`, {
      transports: ['websocket'], // Force WebSocket transport
      withCredentials: true,
    });

    socket.on('connect', () => {
      console.log('Chat WebSocket connection established');
    });

    socket.on('NEW_MESSAGE', (data) => {
      console.log('New chat message received:', data);
      callback(data); // Pass the payload to the callback
    });

    socket.on('disconnect', () => {
      console.log('Chat WebSocket disconnected');
    });

    socket.on('error', (error) => {
      console.error('Chat WebSocket error:', error);
    });

    return () => {
      console.log('Cleaning up Chat WebSocket connection...');
      socket.disconnect();
    };
  };

  return { setupChatWebSocket };
}

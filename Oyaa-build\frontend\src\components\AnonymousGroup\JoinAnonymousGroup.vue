<template>
  <div class="join-page">
    <div class="join-card">
      <div class="card-header">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="card-icon">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
        <h2>Join Anonymous Group</h2>
      </div>
      
      <p class="join-description">
        You're about to join an anonymous group chat. Choose a temporary username to participate.
      </p>
      
      <form @submit.prevent="joinGroup" class="join-form">
        <div class="form-group">
          <label for="tempUsername">Choose a temporary username</label>
          <div class="input-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="input-icon">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            <input 
              v-model="tempUsername" 
              id="tempUsername" 
              required 
              placeholder="e.g., Anon123" 
              autocomplete="off"
              minlength="3"
              maxlength="20"
            />
          </div>
          <p class="input-hint">This name will only be used for this session</p>
        </div>
        
        <div class="privacy-notice">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
          </svg>
          <p>Your identity will remain anonymous. No personal data is stored.</p>
        </div>
        
        <button type="submit" class="submit-btn">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
            <polyline points="10 17 15 12 10 7"></polyline>
            <line x1="15" y1="12" x2="3" y2="12"></line>
          </svg>
          Join Anonymous Chat
        </button>
      </form>
      
      <div class="back-link">
        <a @click.prevent="goBack" href="#">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Back to Anonymous Groups
        </a>
      </div>
    </div>
  </div>
</template>
  
<script>
import axios from '@/api/axios';
  
export default {
  data() {
    return {
      tempUsername: '',
      linkToken: this.$route.params.linkToken,
    };
  },
  methods: {
    async joinGroup() {
      try {
        const response = await axios.post('/api/temp-groups/join', {
          linkToken: this.linkToken,
          tempUsername: this.tempUsername,
        });
        const { sessionToken, groupId, groupName } = response.data;
        localStorage.setItem('tempGroupSessionToken', sessionToken);
        
        // Show success message
        this.$store.dispatch('app/showToast', {
          message: `Joined '${groupName}' successfully!`,
          type: 'success'
        });
        
        this.$router.push(`/anonymous/chat/${groupId}`);
      } catch (err) {
        console.error('Failed to join group:', err);
        
        // Show error message
        this.$store.dispatch('app/showToast', {
          message: 'Failed to join group: ' + (err.response?.data?.message || 'Unknown error'),
          type: 'error'
        });
      }
    },
    goBack() {
      this.$router.push('/anonymous');
    }
  },
};
</script>
  
<style scoped>
.join-page {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 1.5rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

.join-card {
  width: 100%;
  max-width: 450px;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  text-align: center;
}

.card-icon {
  color: var(--accent-primary);
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.join-description {
  padding: 1.25rem 1.5rem 0;
  color: var(--text-secondary);
  font-size: 0.9375rem;
  text-align: center;
  margin: 0;
}

.join-form {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

label {
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--text-primary);
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.9375rem;
  transition: all var(--transition-fast);
}

input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}

input::placeholder {
  color: var(--text-secondary);
}

.input-hint {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

.privacy-notice {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: var(--accent-tertiary);
  border-radius: 8px;
}

.privacy-notice svg {
  color: var(--accent-primary);
  flex-shrink: 0;
}

.privacy-notice p {
  font-size: 0.8125rem;
  color: var(--text-primary);
  margin: 0;
}

.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: var(--accent-primary);
  color: white;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.submit-btn:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.submit-btn:active {
  transform: translateY(0);
}

.back-link {
  padding: 1rem 1.5rem 1.5rem;
  text-align: center;
}

.back-link a {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.back-link a:hover {
  color: var(--accent-primary);
}

@media (max-width: 480px) {
  .join-page {
    padding: 1rem;
  }
  
  .card-header {
    padding: 1.25rem;
  }
  
  h2 {
    font-size: 1.25rem;
  }
  
  .join-form {
    padding: 1.25rem;
  }
}
</style>
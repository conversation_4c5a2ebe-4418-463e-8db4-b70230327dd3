import { createApp } from 'vue';
import store from './store';
import App from './App.vue';
import router from './router';
import vClickOutside from 'v-click-outside';
import './style.css';
import './assets/emoji-style.css';
import useChatSocket from '@/composables/useChatSocket';
import tempChatSocket from './tempChatSocket';
import './config/firebase.config'; // Initialize Firebase
import { initAuthHandler } from './utils/authHandler';

const app = createApp(App);
app.use(store);
app.use(router);
app.use(vClickOutside);
// Make socket globally available
app.config.globalProperties.$tempChatSocket = tempChatSocket;

// Initialize the auth handler
initAuthHandler();

// Mount the app immediately
app.mount('#app');

const { onTyping, onStopTyping } = useChatSocket();
onTyping((data) => {
  store.dispatch('chat/updateTypingStatus', { friendId: data.userId, status: true });
  setTimeout(() => {
    store.dispatch('chat/updateTypingStatus', { friendId: data.userId, status: false });
  }, 2000);
});
onStopTyping((data) => {
  store.dispatch('chat/updateTypingStatus', { friendId: data.userId, status: false });
});

// Global properties
app.config.globalProperties.$socket = {
  isConnected: false
}

// Global error handling
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err);
  console.error('Component:', vm);
  console.error('Info:', info);

  // Show error toast for user-friendly error messages
  if (store && typeof store.dispatch === 'function') {
    store.dispatch('app/showToast', {
      message: `Error: ${err.message || 'An unexpected error occurred'}`,
      type: 'error',
      duration: 5000
    });
  }
}
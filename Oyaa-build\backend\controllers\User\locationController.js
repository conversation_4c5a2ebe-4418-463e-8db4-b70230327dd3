// controllers/locationController.js
const LocationService = require('../../services/User/locationService');

class LocationController {
  async update(req, res) {
    const { userId, latitude, longitude } = req.body;
    console.log(`Received update for user ${userId}: lat=${latitude}, lng=${longitude}`);
    try {
      const updatedUser = await LocationService.updateUserLocation(userId, latitude, longitude);
      res.status(200).json({ message: 'Location updated successfully', user: updatedUser });
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: 'Failed to update location' });
    }
  }
  

  async getNearbyUsers(req, res) {
    const { userId, limit = 10, offset = 0 } = req.query;

    try {
      const nearbyUsers = await LocationService.getNearbyUsers(userId, limit, offset);
      res.status(200).json(nearbyUsers);
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: 'Failed to fetch nearby users' });
    }
  }
}

module.exports = new LocationController();
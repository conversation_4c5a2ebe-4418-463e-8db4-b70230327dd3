// backend/routes/auth/checkUsername.js
const express = require('express');
const router = express.Router();
const pool = require('../../db');

// Import logger safely
let logger;
try {
  const loggerModule = require('../../utils/logger');
  logger = loggerModule.logger || console;
} catch (err) {
  // Fallback to console if logger module is not available
  logger = console;
}

/**
 * Check if a username is available
 * @route POST /api/auth/check-username
 * @param {string} username - Username to check
 * @returns {Object} - Response object with availability status
 */
router.post('/check-username', async (req, res) => {
  try {
    const { username } = req.body;

    // Validate input
    if (!username) {
      return res.status(400).json({
        available: false,
        message: 'Username is required'
      });
    }

    // Check username format
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      return res.status(400).json({
        available: false,
        message: 'Username must be 3-20 characters and contain only letters, numbers, and underscores'
      });
    }

    // Check if username exists in database (case insensitive)
    const result = await pool.query(
      'SELECT id FROM users WHERE LOWER(username) = LOWER($1)',
      [username]
    );

    // Return availability status
    const isAvailable = result.rows.length === 0;

    logger.info(`Username availability check for "${username}": ${isAvailable ? 'Available' : 'Taken'}`);

    return res.status(200).json({
      available: isAvailable,
      message: isAvailable ? 'Username is available' : 'Username is already taken'
    });
  } catch (error) {
    // Safe error logging
    try {
      if (error) {
        logger.error('Error checking username availability:',
          typeof error === 'object' ?
            (error.message || error.toString()) :
            String(error)
        );
      } else {
        logger.error('Unknown error checking username availability');
      }
    } catch (loggingError) {
      // If even logging fails, use console as fallback
      console.error('Error during error logging:', loggingError);
      console.error('Original error:', error);
    }

    // Always return a valid response
    return res.status(500).json({
      available: false,
      message: 'Server error checking username availability'
    });
  }
});

/**
 * Check if a username is available (GET method for backward compatibility)
 * @route GET /api/auth/check-username/:username
 * @param {string} username - Username to check
 * @returns {Object} - Response object with availability status
 */
router.get('/check-username/:username', async (req, res) => {
  try {
    const { username } = req.params;

    // Validate input
    if (!username) {
      return res.status(400).json({
        available: false,
        message: 'Username is required'
      });
    }

    // Check username format
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      return res.status(400).json({
        available: false,
        message: 'Username must be 3-20 characters and contain only letters, numbers, and underscores'
      });
    }

    // Check if username exists in database (case insensitive)
    const result = await pool.query(
      'SELECT id FROM users WHERE LOWER(username) = LOWER($1)',
      [username]
    );

    // Return availability status
    const isAvailable = result.rows.length === 0;

    logger.info(`Username availability check (GET) for "${username}": ${isAvailable ? 'Available' : 'Taken'}`);

    return res.status(200).json({
      available: isAvailable,
      message: isAvailable ? 'Username is available' : 'Username is already taken'
    });
  } catch (error) {
    // Safe error logging
    try {
      if (error) {
        logger.error('Error checking username availability (GET):',
          typeof error === 'object' ?
            (error.message || error.toString()) :
            String(error)
        );
      } else {
        logger.error('Unknown error checking username availability (GET)');
      }
    } catch (loggingError) {
      // If even logging fails, use console as fallback
      console.error('Error during error logging (GET):', loggingError);
      console.error('Original error (GET):', error);
    }

    // Always return a valid response
    return res.status(500).json({
      available: false,
      message: 'Server error checking username availability'
    });
  }
});

module.exports = router;

// frontend/src/services/friendRequestsSocket.js
import { io } from 'socket.io-client';
import store from '@/store';

let socket = null;
let reconnectTimer = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10;
const RECONNECT_DELAY = 2000; // 2 seconds

// Function to play notification sound
function playNotificationSound() {
  const audio = new Audio('/sounds/mixkit-software-interface-start-2574.mp3');
  audio.preload = 'auto';
  audio.load();
  audio.play().catch((error) => {
    console.error('Error playing notification sound:', error);
  });
}

/**
 * Attempt to reconnect the WebSocket
 */
function attemptReconnect() {
  if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
    console.error(`Maximum reconnection attempts (${MAX_RECONNECT_ATTEMPTS}) reached. Please refresh the page.`);
    store.dispatch('app/showToast', {
      message: 'Connection lost. Please refresh the page.',
      type: 'error',
      duration: 10000
    });
    return;
  }

  reconnectAttempts++;
  console.log(`Attempting to reconnect (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})...`);

  // Clear any existing timer
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
  }

  // Try to reconnect after delay
  reconnectTimer = setTimeout(() => {
    console.log('Reconnecting to WebSocket...');
    initFriendRequestsSocket();
  }, RECONNECT_DELAY);
}

/**
 * Initialize the friend requests WebSocket connection
 */
export function initFriendRequestsSocket() {
  // If socket already exists, disconnect it first
  if (socket) {
    socket.disconnect();
  }

  // Reset reconnect attempts if this is a manual initialization
  reconnectAttempts = 0;

  // Create a new socket connection with improved settings
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
  console.log(`Connecting to WebSocket at ${baseUrl}/friend-requests`);

  socket = io(`${baseUrl}/friend-requests`, {
    auth: {
      token: localStorage.getItem('token')
    },
    // Improved connection settings
    transports: ['websocket', 'polling'],
    reconnection: true,
    reconnectionAttempts: 10,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    timeout: 20000,
    autoConnect: true,
    forceNew: true,
    // Debug mode in development
    debug: import.meta.env.DEV
  });

  // Socket connection events
  socket.on('connect', () => {
    console.log('Friend requests WebSocket connected successfully');

    // Reset reconnect attempts on successful connection
    reconnectAttempts = 0;

    // Join the user's room
    const userId = store.getters['auth/userId'];
    if (userId) {
      console.log(`Joining room for user ID: ${userId}`);
      socket.emit('join', userId);

      // Request initial data
      console.log('Requesting initial friend requests data');
      fetchInitialData();
    } else {
      console.warn('No user ID available, cannot join room');
    }

    // Show success toast
    store.dispatch('app/showToast', {
      message: 'Friend requests connection established',
      type: 'success',
      duration: 3000
    });

    // Dispatch connection event
    window.dispatchEvent(new CustomEvent('friendsocket:connected'));
  });

  socket.on('disconnect', () => {
    console.log('Friend requests WebSocket disconnected');
    window.dispatchEvent(new CustomEvent('friendsocket:disconnected'));

    // Attempt to reconnect
    attemptReconnect();
  });

  socket.on('connect_error', (error) => {
    console.error('Friend requests WebSocket connection error:', error);
    window.dispatchEvent(new CustomEvent('friendsocket:error', { detail: error }));

    // Attempt to reconnect
    attemptReconnect();
  });

  socket.on('error', (error) => {
    console.error('Friend requests WebSocket error:', error);
    window.dispatchEvent(new CustomEvent('friendsocket:error', { detail: error }));

    // Attempt to reconnect
    attemptReconnect();
  });

  // Handle incoming events
  socket.on('NEW_FRIEND_REQUEST', (data) => {
    console.log('Received new friend request:', data);

    // Update the store
    store.dispatch('friendRequests/handleWebSocketUpdate', data);

    // Play sound if the current user is the receiver
    if (data.receiverId === store.getters['auth/userId']) {
      playNotificationSound();

      // Add to notifications
      store.commit('app/ADD_NOTIFICATION', {
        id: `fr_${data.request.id}`,
        type: 'friendRequest',
        message: `${data.request.sender_username} sent you a friend request`,
        timestamp: new Date().toISOString(),
        avatar: data.request.sender_avatar,
        sender: {
          id: data.request.sender_id,
          name: data.request.sender_username
        },
        data: data.request
      });
    }
  });

  socket.on('FRIEND_REQUEST_UPDATE', (data) => {
    console.log('Received friend request update:', data);

    // Update the store
    store.dispatch('friendRequests/handleWebSocketUpdate', data);

    // Play sound for status updates
    const currentUserId = store.getters['auth/userId'];

    // If the current user is the sender and the request was accepted/rejected
    if (data.senderId === currentUserId &&
        (data.request.status.toLowerCase() === 'accepted' ||
         data.request.status.toLowerCase() === 'rejected')) {
      playNotificationSound();

      // Show toast notification
      const statusText = data.request.status.toLowerCase() === 'accepted' ? 'accepted' : 'rejected';
      const toastType = data.request.status.toLowerCase() === 'accepted' ? 'success' : 'info';

      store.dispatch('app/showToast', {
        message: `Your friend request was ${statusText}`,
        type: toastType,
        duration: 5000
      });
    }

    // If the current user is the receiver and they just accepted/rejected
    if (data.receiverId === currentUserId &&
        (data.request.status.toLowerCase() === 'accepted' ||
         data.request.status.toLowerCase() === 'rejected')) {
      // No need to play sound or show toast as the user initiated this action
    }
  });

  socket.on('FRIEND_REQUESTS_LIST', (data) => {
    console.log('Received friend requests list:', data);
    store.dispatch('friendRequests/handleWebSocketUpdate', data);
  });

  return socket;
}

/**
 * Fetch initial friend requests data
 */
function fetchInitialData() {
  const userId = store.getters['auth/userId'];
  console.log('Fetching initial friend requests data for user ID:', userId);

  // Request received requests via WebSocket
  socket.emit('getRequests', { userId }, (response) => {
    if (response && response.success) {
      console.log('Received initial friend requests via WebSocket:', response);

      // Update the store with the received requests
      if (response.requests) {
        store.dispatch('friendRequests/handleWebSocketUpdate', {
          type: 'FRIEND_REQUESTS_LIST',
          receivedRequests: response.requests
        });
      }
    } else {
      console.error('Error fetching received requests via WebSocket:', response?.error || 'Unknown error');
      // Don't use HTTP fallback to avoid polling
      console.log('Not using HTTP fallback to avoid polling');
    }
  });

  // Request sent requests via WebSocket
  socket.emit('getSentRequests', { userId }, (response) => {
    if (response && response.success) {
      console.log('Received initial sent requests via WebSocket:', response);

      // Update the store with the sent requests
      if (response.requests) {
        store.dispatch('friendRequests/handleWebSocketUpdate', {
          type: 'FRIEND_REQUESTS_LIST',
          sentRequests: response.requests
        });
      }
    } else {
      console.error('Error fetching sent requests via WebSocket:', response?.error || 'Unknown error');
      // Don't use HTTP fallback to avoid polling
      console.log('Not using HTTP fallback to avoid polling');
    }
  });
}

/**
 * Send a friend request
 * @param {number} receiverId - The ID of the user to send the request to
 * @returns {Promise} - A promise that resolves with the response
 */
export function sendFriendRequest(receiverId) {
  return new Promise((resolve, reject) => {
    const senderId = store.getters['auth/userId'];

    console.log(`Sending friend request from ${senderId} to ${receiverId}`);

    socket.emit('sendFriendRequest', { senderId, receiverId }, (response) => {
      if (response.success) {
        console.log('Friend request sent successfully:', response);
        resolve(response);
      } else {
        console.error('Error sending friend request:', response.error);
        reject(new Error(response.error));
      }
    });
  });
}

/**
 * Accept a friend request
 * @param {number} requestId - The ID of the request to accept
 * @returns {Promise} - A promise that resolves with the response
 */
export function acceptFriendRequest(requestId) {
  return new Promise((resolve, reject) => {
    console.log(`Accepting friend request ${requestId}`);

    // Check if socket is connected
    if (!socket || !socket.connected) {
      console.error('Socket not connected when trying to accept friend request');

      // Try to reconnect
      console.log('Attempting to reconnect before accepting friend request...');
      initFriendRequestsSocket();

      // Wait a bit for the connection to establish
      setTimeout(() => {
        if (socket && socket.connected) {
          console.log('Reconnected successfully, proceeding with accept request');
          // Continue with the request after reconnection
          acceptFriendRequest(requestId).then(resolve).catch(reject);
        } else {
          console.error('Failed to reconnect, cannot accept friend request');
          reject(new Error('WebSocket not connected. Please refresh the page.'));
        }
      }, 1000);

      return;
    }

    // Add a timeout to prevent hanging
    const timeout = setTimeout(() => {
      console.error(`Accept friend request timed out for request ${requestId}`);
      reject(new Error('Request timed out. Please try again.'));
    }, 10000); // 10 second timeout

    socket.emit('acceptFriendRequest', { requestId }, (response) => {
      clearTimeout(timeout); // Clear the timeout

      if (response && response.success) {
        console.log('Friend request accepted successfully:', response);

        // Manually update the store
        store.dispatch('friendRequests/fetchReceivedRequests');

        resolve(response);
      } else {
        const errorMsg = response?.error || 'Unknown error accepting friend request';
        console.error('Error accepting friend request:', errorMsg);
        reject(new Error(errorMsg));
      }
    });
  });
}

/**
 * Reject a friend request
 * @param {number} requestId - The ID of the request to reject
 * @returns {Promise} - A promise that resolves with the response
 */
export function rejectFriendRequest(requestId) {
  return new Promise((resolve, reject) => {
    console.log(`Rejecting friend request ${requestId}`);

    // Check if socket is connected
    if (!socket || !socket.connected) {
      console.error('Socket not connected when trying to reject friend request');

      // Try to reconnect
      console.log('Attempting to reconnect before rejecting friend request...');
      initFriendRequestsSocket();

      // Wait a bit for the connection to establish
      setTimeout(() => {
        if (socket && socket.connected) {
          console.log('Reconnected successfully, proceeding with reject request');
          // Continue with the request after reconnection
          rejectFriendRequest(requestId).then(resolve).catch(reject);
        } else {
          console.error('Failed to reconnect, cannot reject friend request');
          reject(new Error('WebSocket not connected. Please refresh the page.'));
        }
      }, 1000);

      return;
    }

    // Add a timeout to prevent hanging
    const timeout = setTimeout(() => {
      console.error(`Reject friend request timed out for request ${requestId}`);
      reject(new Error('Request timed out. Please try again.'));
    }, 10000); // 10 second timeout

    socket.emit('rejectFriendRequest', { requestId }, (response) => {
      clearTimeout(timeout); // Clear the timeout

      if (response && response.success) {
        console.log('Friend request rejected successfully:', response);

        // Manually update the store
        store.dispatch('friendRequests/fetchReceivedRequests');

        resolve(response);
      } else {
        const errorMsg = response?.error || 'Unknown error rejecting friend request';
        console.error('Error rejecting friend request:', errorMsg);
        reject(new Error(errorMsg));
      }
    });
  });
}

/**
 * Get the socket instance
 * @returns {Object} - The socket instance
 */
export function getSocket() {
  return socket;
}

/**
 * Check if the socket is connected and reconnect if needed
 * @returns {boolean} - Whether the socket is connected
 */
export function ensureSocketConnected() {
  if (!socket || !socket.connected) {
    console.log('Socket not connected, attempting to reconnect...');
    initFriendRequestsSocket();
    return false;
  }
  return true;
}

/**
 * Disconnect the socket
 */
export function disconnect() {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
}

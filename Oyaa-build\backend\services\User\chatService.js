// backend/services/chatService.js
// Valkey service temporarily disabled
// const { valkeyGet, valkeySet, valkeyDel } = require('../../config/valkey.config');
const db = require('../../db');
const Media = require('../../models/User/Media');

class ChatService {
  async getLastChats(userId) {
    try {
      // Cache check disabled (Valkey service temporarily disabled)
      console.log('Cache disabled, fetching last chats from database');

      const query = `
        SELECT DISTINCT ON (
          CASE
            WHEN sender_id = $1 THEN receiver_id
            ELSE sender_id
          END
        )
          chats.*,
          CASE
            WHEN sender_id = $1 THEN receiver_id
            ELSE sender_id
          END AS friend_id,
          u.username AS friend_username
        FROM chats
        LEFT JOIN users u
          ON u.id = (CASE WHEN sender_id = $1 THEN receiver_id ELSE sender_id END)
        WHERE sender_id = $1 OR receiver_id = $1
        ORDER BY
          CASE WHEN sender_id = $1 THEN receiver_id ELSE sender_id END,
          sent_at DESC;
      `;
      const result = await db.query(query, [userId]);
      const chats = result.rows;

      // Cache update disabled (Valkey service temporarily disabled)
      console.log('Cache disabled, not caching last chats');
      return chats;
    } catch (err) {
      throw new Error(`Failed to get last chats: ${err.message}`);
    }
  }

  async sendMessage(senderId, receiverId, message, replyId = null, media = null) {
    try {
      if ((!message || message.trim() === '') && !media) {
        throw new Error('Either message text or media must be provided.');
      }

      const query = `
        WITH inserted AS (
          INSERT INTO chats (sender_id, receiver_id, message, sent_at, reply_id)
          VALUES ($1, $2, $3, NOW(), $4)
          RETURNING *
        )
        SELECT inserted.*,
               r.message AS reply_message,
               ru.username AS reply_sender_name
        FROM inserted
        LEFT JOIN chats r ON inserted.reply_id = r.id
        LEFT JOIN users ru ON r.sender_id = ru.id;
      `;
      const values = [senderId, receiverId, message, replyId];
      const result = await db.query(query, values);
      console.log('Message inserted:', result.rows[0]);
      let messageData = result.rows[0];

      if (media) {
        const normalizedMedia = {
          mediaUrl: media.media_url || media.mediaUrl,
          mediaType: media.media_type || media.mediaType,
          publicId: media.public_id || media.publicId,
          thumbnailUrl: media.thumbnail_url || media.thumbnailUrl,
          duration: media.duration,
        };
        if (!normalizedMedia.mediaUrl) {
          throw new Error('Media object must contain a valid mediaUrl');
        }
        const mediaRecord = await Media.createMedia({
          userId: senderId,
          chatId: messageData.id,
          mediaUrl: normalizedMedia.mediaUrl,
          mediaType: normalizedMedia.mediaType,
          publicId: normalizedMedia.publicId,
          thumbnailUrl: normalizedMedia.thumbnailUrl,
          duration: normalizedMedia.duration,
        });
        messageData.media = {
          mediaUrl: mediaRecord.media_url,
          mediaType: mediaRecord.media_type,
          publicId: mediaRecord.public_id,
          thumbnailUrl: mediaRecord.thumbnail_url,
          duration: mediaRecord.duration,
        };
      }

      // Cache deletion disabled (Valkey service temporarily disabled)
      console.log('Cache disabled, not clearing cache keys for sender and receiver');

      const userRes = await db.query('SELECT username, avatar FROM users WHERE id = $1', [senderId]);
      if (userRes.rows[0]) {
        messageData.sender_name = userRes.rows[0].username;
        messageData.sender_avatar = userRes.rows[0].avatar;
      }

      return messageData;
    } catch (err) {
      console.error(`Failed to send message: ${err.message}`);
      throw new Error(`Failed to send message: ${err.message}`);
    }
  }

  async getMessages(userId, targetUserId, page = 1, pageSize = 50) {
    try {
      const offset = (page - 1) * pageSize;
      console.log(
        `getMessages called: userId=${userId}, targetUserId=${targetUserId}, page=${page}, pageSize=${pageSize}, offset=${offset}`
      );
      const query = `
        SELECT
          c.id,
          c.message,
          c.sender_id,
          c.receiver_id,
          c.sent_at,
          c.reply_id,
          u.username AS sender_name,
          u.avatar AS sender_avatar,
          m.media_url,
          m.media_type,
          m.public_id,
          m.thumbnail_url,
          m.duration,
          r.message AS reply_message,
          ru.username AS reply_sender_name
        FROM chats c
        JOIN users u ON c.sender_id = u.id
        LEFT JOIN media m ON m.chat_id = c.id
        LEFT JOIN chats r ON c.reply_id = r.id
        LEFT JOIN users ru ON r.sender_id = ru.id
        WHERE (c.sender_id = $1 AND c.receiver_id = $2)
           OR (c.sender_id = $2 AND c.receiver_id = $1)
        ORDER BY c.sent_at DESC
        LIMIT $3 OFFSET $4;
      `;
      const result = await db.query(query, [userId, targetUserId, pageSize, offset]);
      const messages = result.rows.map(row => {
        const message = {
          id: row.id,
          message: row.message,
          sender_id: row.sender_id,
          receiver_id: row.receiver_id,
          sent_at: row.sent_at,
          sender_name: row.sender_name,
          sender_avatar: row.sender_avatar,
          reply_message: row.reply_message,
          reply_sender_name: row.reply_sender_name,
        };
        if (row.media_url) {
          message.media = {
            mediaUrl: row.media_url,
            mediaType: row.media_type,
            publicId: row.public_id,
            thumbnailUrl: row.thumbnail_url,
            duration: row.duration,
          };
        }
        return message;
      });
      return messages.reverse(); // Oldest to newest
    } catch (err) {
      console.error('Error in getMessages:', err);
      throw new Error(`Failed to retrieve messages: ${err.message}`);
    }
  }
}

module.exports = new ChatService();
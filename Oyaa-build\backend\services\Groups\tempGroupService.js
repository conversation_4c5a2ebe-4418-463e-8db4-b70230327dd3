// backend/services/Groups/tempGroupService.js
const TempGroupModel = require('../../models/Groups/tempGroupModel');
const jwt = require('jsonwebtoken');

class TempGroupService {
  async createTempGroup(creatorId, groupName, category, description, expiresInHours = 24) {
    const group = await TempGroupModel.createTempGroup(creatorId, groupName, category, description, expiresInHours);
    return group;
  }

  async joinTempGroup(linkToken, tempUsername) {
    const group = await TempGroupModel.getTempGroupByLinkToken(linkToken);
    if (!group) {
      throw new Error('Invalid or expired group link');
    }
    const participant = await TempGroupModel.addParticipant(group.id, tempUsername);
    const tokenPayload = {
      groupId: group.id,
      participantId: participant.id,
      tempUsername: participant.temp_username,
      groupName: group.group_name,      // Include group name in token
      description: group.description    // Include description in token
    };
    const sessionToken = jwt.sign(tokenPayload, process.env.JWT_SECRET, { expiresIn: '24h' });
    return { sessionToken, groupId: group.id, groupName: group.group_name };
  }

  async sendMessage(sessionToken, message) {
    const decoded = jwt.verify(sessionToken, process.env.JWT_SECRET);
    const { groupId, participantId } = decoded;
    const chatMessage = await TempGroupModel.sendMessage(groupId, participantId, message);
    return chatMessage;
  }

  async getMessages(groupId, limit = 50, offset = 0) {
    return await TempGroupModel.getMessages(groupId, limit, offset);
  }
}

module.exports = new TempGroupService();
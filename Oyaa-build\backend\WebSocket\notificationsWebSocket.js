// backend/notificationsWebSocket.js
module.exports = (io) => {
    const notifications = io.of('/notifications');
  
    console.log('Notifications WebSocket namespace initialized.');
  
    notifications.on('connection', (socket) => {
      console.log('New client connected to notifications namespace:', socket.id);
  
      // Listen for joinRoom events so the client can join its personal room
      socket.on('joinRoom', (room, callback) => {
        console.log(`Socket ${socket.id} requested to join room: ${room}`);
        socket.join(room);
        if (callback) callback(`Joined room: ${room}`);
      });
  
      socket.on('disconnect', () => {
        console.log('Client disconnected from notifications namespace:', socket.id);
      });
    });
  };
  
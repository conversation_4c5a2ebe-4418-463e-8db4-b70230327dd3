// frontend/src/composables/useChat.js
import useChatState from './useChatState';
import useChatActions from './useChatActions';
import useChatApi from './useChatApi';
import useChatSocket from './useChatSocket';
import useDebounce from './useDebounce';
import usePagination from './usePagination';

export default function useChat(route, router, store) {
  const state = useChatState(route, store);
  const api = useChatApi();
  const socket = useChatSocket();
  const pagination = usePagination();

  const actions = useChatActions(state, api, socket, pagination, store, router);

  // Debounce the loading of older messages if needed.
  const debouncedLoadOlderMessages = useDebounce(actions.loadOlderMessages, 300);

  return {
    ...state,
    ...actions,
    loadOlderMessages: debouncedLoadOlderMessages,
  };
}

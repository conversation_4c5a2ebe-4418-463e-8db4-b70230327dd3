<template>
  <div class="group-audio-player" :class="{ 'sender-me': props.isSender }">
    <button @click="togglePlayPause" class="play-pause-button">
      <PlayIcon v-if="!isPlaying" class="icon" />
      <PauseIcon v-else class="icon" />
    </button>
    <div class="waveform-container" ref="waveform">
      <div class="playhead" :style="{ left: playheadPosition + '%' }"></div>
    </div>
    <span class="duration">{{ displayTime }}</span>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import WaveSurfer from 'wavesurfer.js';
import { Play as PlayIcon, Pause as PauseIcon } from 'lucide-vue-next';

const props = defineProps({
  mediaUrl: { type: String, required: true },
  isSender: { type: Boolean, default: false },
  duration: { type: Number, default: 0 },
  lazyLoad: { type: Boolean, default: false },
});

const wavesurfer = ref(null);
const waveform = ref(null);
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(props.duration || 0);
const isLoaded = ref(!props.lazyLoad);

const playheadPosition = computed(() => {
  if (duration.value > 0) {
    return (currentTime.value / duration.value) * 100;
  }
  return 0;
});

const loadAudio = () => {
  // Determine if we're on a small screen
  const isSmallScreen = window.innerWidth <= 480;
  const isTinyScreen = window.innerWidth <= 360;

  // Adjust parameters based on screen size
  const barWidth = isTinyScreen ? 1 : (isSmallScreen ? 1.5 : 2);
  const barGap = isTinyScreen ? 0.5 : 1;
  const height = isTinyScreen ? 28 : (isSmallScreen ? 32 : 36);

  // Colors based on sender status
  const waveColor = props.isSender ? 'rgba(140, 142, 255, 0.7)' : 'rgba(120, 120, 120, 0.7)';
  const progressColor = props.isSender ? '#ffffff' : '#ffffff';

  wavesurfer.value = WaveSurfer.create({
    container: waveform.value,
    waveColor: waveColor,
    progressColor: progressColor,
    cursorWidth: 0,
    barWidth: barWidth,
    barGap: barGap,
    barRadius: 3,
    responsive: true,
    height: height,
    normalize: true,
    partialRender: true,
    barHeight: 0.9, // Slightly shorter bars for better aesthetics
    interact: true,
  });

  wavesurfer.value.load(props.mediaUrl);

  wavesurfer.value.on('ready', () => {
    duration.value = wavesurfer.value.getDuration();
  });

  wavesurfer.value.on('audioprocess', () => {
    currentTime.value = wavesurfer.value.getCurrentTime();
  });

  wavesurfer.value.on('seek', (progress) => {
    const seekTime = progress * duration.value;
    currentTime.value = seekTime;
    wavesurfer.value.seekTo(progress);
  });

  wavesurfer.value.on('finish', () => {
    isPlaying.value = false;
    currentTime.value = 0;
  });

  wavesurfer.value.on('play', () => {
    isPlaying.value = true;
  });

  wavesurfer.value.on('pause', () => {
    isPlaying.value = false;
  });
};

onMounted(() => {
  if (isLoaded.value) {
    loadAudio();
  }
});

const togglePlayPause = () => {
  if (!isLoaded.value) {
    isLoaded.value = true;
    loadAudio();
    // Wait for audio to load before playing
    wavesurfer.value.on('ready', () => {
      wavesurfer.value.play();
    });
  } else {
    wavesurfer.value.playPause();
  }
};

const formatTime = (seconds) => {
  if (isNaN(seconds) || !isFinite(seconds)) return '0:00';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
};

const displayTime = computed(() => {
  if (isPlaying.value) {
    const remaining = duration.value - currentTime.value;
    return `-${formatTime(remaining)}`;
  } else {
    return formatTime(duration.value);
  }
});

onBeforeUnmount(() => {
  if (wavesurfer.value) {
    wavesurfer.value.destroy();
  }
});
</script>

<style scoped>
.group-audio-player {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  border-radius: 14px;
  background-color: #1f2937;
  background-image: linear-gradient(135deg, #1f2937, #2d3748);
  max-width: 300px;
  width: 100%;
  box-sizing: border-box;
  transition: transform 0.2s ease;
}

.group-audio-player.sender-me {
  background-color: #4338ca;
  background-image: linear-gradient(135deg, #4338ca, #4f46e5);
}

.group-audio-player:hover {
  transform: scale(1.01);
}

/* Responsive styles for smaller screens */
@media (max-width: 480px) {
  .group-audio-player {
    padding: 8px 10px;
    gap: 6px;
    border-radius: 12px;
  }
}

@media (max-width: 360px) {
  .group-audio-player {
    padding: 6px 8px;
    gap: 4px;
    border-radius: 10px;
  }
}

.play-pause-button {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #374151;
  background-image: linear-gradient(135deg, #374151, #4b5563);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
}

.play-pause-button:hover {
  transform: scale(1.08);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.play-pause-button:active {
  transform: scale(0.95);
}

.group-audio-player.sender-me .play-pause-button {
  background-color: #3730a3;
  background-image: linear-gradient(135deg, #3730a3, #4338ca);
  box-shadow: 0 2px 6px rgba(67, 56, 202, 0.3);
}

.group-audio-player.sender-me .play-pause-button:hover {
  box-shadow: 0 3px 8px rgba(67, 56, 202, 0.4);
}

/* Responsive styles for smaller screens */
@media (max-width: 480px) {
  .play-pause-button {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 360px) {
  .play-pause-button {
    width: 28px;
    height: 28px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  }
}

.icon {
  width: 16px;
  height: 16px;
  color: white;
}

.waveform-container {
  position: relative;
  flex: 1;
  height: 36px;
  min-width: 140px;
  cursor: pointer;
  margin: 0 4px;
  border-radius: 4px;
  overflow: hidden;
}

.waveform-container:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.05);
  pointer-events: none;
}

/* Responsive styles for smaller screens */
@media (max-width: 480px) {
  .icon {
    width: 14px;
    height: 14px;
  }

  .waveform-container {
    height: 32px;
    min-width: 100px;
    margin: 0 3px;
  }
}

@media (max-width: 360px) {
  .icon {
    width: 12px;
    height: 12px;
  }

  .waveform-container {
    height: 28px;
    min-width: 80px;
    margin: 0 2px;
  }
}

.waveform-container :deep(canvas) {
  z-index: 1;
}

.playhead {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background-color: white;
  border-radius: 50%;
  pointer-events: none;
  z-index: 5;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.4);
  transition: transform 0.1s ease;
}

.group-audio-player:hover .playhead {
  transform: translateY(-50%) scale(1.1);
}

.duration {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.85);
  min-width: 46px;
  text-align: right;
  font-weight: 500;
  letter-spacing: 0.02em;
}

/* Responsive styles for smaller screens */
@media (max-width: 480px) {
  .playhead {
    width: 10px;
    height: 10px;
  }

  .duration {
    font-size: 11px;
    min-width: 42px;
    letter-spacing: 0.01em;
  }
}

@media (max-width: 360px) {
  .playhead {
    width: 8px;
    height: 8px;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  }

  .duration {
    font-size: 10px;
    min-width: 38px;
    letter-spacing: 0;
  }
}
</style>
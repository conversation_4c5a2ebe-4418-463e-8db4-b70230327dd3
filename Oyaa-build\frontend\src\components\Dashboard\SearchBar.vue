<template>
  <div class="search-container">
    <div class="search-wrapper" :class="{ 'focused': isFocused }">
      <span class="search-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
        </svg>
      </span>
      <input 
        type="text" 
        placeholder="Search..." 
        v-model="searchQuery"
        @input="onSearch"
        @focus="isFocused = true"
        @blur="isFocused = false"
      >
      <button 
        v-if="searchQuery" 
        @click="clearSearch" 
        class="clear-button"
        aria-label="Clear search"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
  </div>
</template>
  
<script>
export default {
  data() {
    return {
      searchQuery: '',
      isFocused: false
    }
  },
  methods: {
    onSearch() {
      this.$emit('search', this.searchQuery)
    },
    clearSearch() {
      this.searchQuery = ''
      this.$emit('search', '')
    }
  }
}
</script>
  
<style scoped>
.search-container {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  padding: 8px 16px;
  margin-bottom: 8px;
  width: 100%;
  box-sizing: border-box;
}
  
.search-wrapper {
  display: flex;
  align-items: center;
  background-color: var(--bg-tertiary);
  border-radius: 12px;
  padding: 10px 16px;
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  width: 100%;
  box-sizing: border-box;
}

.search-wrapper.focused {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}
  
.search-icon {
  color: var(--text-secondary);
  margin-right: 12px;
  display: flex;
  align-items: center;
  transition: color var(--transition-fast);
  flex-shrink: 0;
}

.search-wrapper.focused .search-icon {
  color: var(--accent-primary);
}
  
input {
  background: none;
  border: none;
  color: var(--text-primary);
  width: 100%;
  font-size: 16px; /* Prevent zoom on iOS */
  outline: none;
 
  padding: 0;
  margin: 0;
}
  
input::placeholder {
  color: var(--text-secondary);
}

.clear-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
  transition: all var(--transition-fast);
  flex-shrink: 0;
  margin-left: 8px;
  min-width: 24px;
  min-height: 24px;
}

.clear-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
}

/* Mobile optimizations */
@media (max-width: 768px) {

  .search-wrapper {
    padding: 8px 12px;
  }
  
  .search-icon svg {
    width: 16px;
    height: 16px;
  }
  
  .search-icon {
    margin-right: 8px;
  }
  
  input {
    font-size: 14px;
  }
  
  .clear-button svg {
    width: 14px;
    height: 14px;
  }
}

/* Small mobile screens */
@media (max-width: 320px) {
  
  
  .search-wrapper {
    padding: 6px 10px;
  }
  
  .search-icon svg {
    width: 14px;
    height: 14px;
  }
  
  .search-icon {
    margin-right: 6px;
  }
  
  input {
    font-size: 13px;
  }
}
</style>
<template>
  <div class="dashboard" :style="{ '--sidebar-width': isSidebarCollapsed ? '72px' : '240px' }">
    <SidePanel
      :navItems="navItems"
      :activeTab="activeTab"
      :username="username"
      @update:activeTab="setActiveTab"
      @update:collapsed="handleSidebarCollapse"
    />
    <div class="main-content-Dash">
      <Header
        :friendRequestsCount="friendRequestsCount"
        :groupRequestsCount="groupRequestsCount"
        :username="username"
      />
      <div class="content-wrapper">
        <DynamicContent
          :activeTab="activeTab"
          :friends="friends"
          :groups="groups"
          :friendRequestsCount="friendRequestsCount"
          :groupRequestsCount="groupRequestsCount"
        />
      </div>
      <GlobalNotificationCenter />
    </div>
    <BottomNavigation
      :navItems="navItems"
      :activeTab="activeTab"
      @update:activeTab="setActiveTab"
    />
    <CenterAction :active-tab="activeTab" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import axios from 'axios';
import Header from './Header.vue';
import DynamicContent from './DynamicContent.vue';
import BottomNavigation from './BottomNavigation.vue';
import SidePanel from './SidePanel.vue';
import GlobalNotificationCenter from '@/components/GlobalNotificationCenter.vue';
import CenterAction from './ActionArea/CenterAction.vue';
import { MessageSquare, MapPin, Users, User } from 'lucide-vue-next';

const store = useStore();
const route = useRoute();
const activeTab = ref('friends'); // Default tab
const friendRequestsCount = ref(0);
const groupRequestsCount = ref(0);
const pollInterval = ref(null);
const lastUpdated = ref(0);
const friends = ref([]);
const groups = ref([]);
const isSidebarCollapsed = ref(false);

const user = computed(() => store.getters['auth/user']);
const userId = computed(() => user.value?.id);
const username = computed(() => user.value?.username || 'Guest');

const navItems = [
  { title: 'Chats', icon: MessageSquare, tab: 'friends' },
  { title: 'Locals', icon: MapPin, tab: 'locals' },
  { title: 'Groups', icon: Users, tab: 'groups' },
  { title: 'Profile', icon: User, tab: 'profile' },
];

const setActiveTab = (tab) => {
  activeTab.value = tab;
};

const handleSidebarCollapse = (collapsed) => {
  isSidebarCollapsed.value = collapsed;
};

// Existing fetch functions remain unchanged
const fetchFriendRequestsCount = async () => {
  if (userId.value) {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/${userId.value}`,
        {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
          params: { lastUpdated: lastUpdated.value },
        }
      );
      if (response.data.lastUpdated > lastUpdated.value) {
        friendRequestsCount.value = response.data.requests.length;
        lastUpdated.value = response.data.lastUpdated;
        const notifiedRequests = store.state.app.notifiedRequests;
        const newRequests = response.data.requests.filter(
          (request) => !notifiedRequests.has(request.id)
        );
        if (newRequests.length > 0) {
          newRequests.forEach((request) => {
            store.dispatch('app/addNotifiedRequest', request.id);
          });
        }
      }
    } catch (error) {
      console.error('Error fetching friend requests:', error);
    }
  }
};

const fetchGroupRequestsCount = async () => {
  if (userId.value) {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_BASE_URL}/api/groups/group-requests/${userId.value}`,
        {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
          params: { lastUpdated: lastUpdated.value },
        }
      );
      if (response.data.lastUpdated > lastUpdated.value) {
        groupRequestsCount.value = response.data.requests.length;
        lastUpdated.value = response.data.lastUpdated;
        const notifiedRequests = store.state.app.notifiedGroupRequests;
        const newRequests = response.data.requests.filter(
          (request) => !notifiedRequests.has(request.id)
        );
        if (newRequests.length > 0) {
          newRequests.forEach((request) => {
            store.dispatch('app/addNotifiedGroupRequest', request.id);
          });
        }
      }
    } catch (error) {
      console.error('Error fetching group requests:', error);
    }
  }
};

onMounted(async () => {
  // Set activeTab based on query parameter if it exists
  if (route.query.tab) {
    activeTab.value = route.query.tab;
  }
  await fetchFriendRequestsCount();
  await fetchGroupRequestsCount();
  pollInterval.value = setInterval(() => {
    fetchFriendRequestsCount();
    fetchGroupRequestsCount();
  }, 3000);
});

onUnmounted(() => {
  clearInterval(pollInterval.value);
});
</script>

<style scoped>
.dashboard {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --header-height: 73px;
  --bottom-nav-height: 65px;
  --safe-area-bottom: env(safe-area-inset-bottom, 0px);
  
  background: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  font-family: 'Inter', 'Segoe UI', sans-serif;
  width: 100%;
  box-sizing: border-box;
}

.main-content-Dash {
  flex: 1;
  position: relative;
  z-index: 1;
  padding-left: var(--sidebar-width);
  
  will-change: padding-left;
  width: 100%;
  box-sizing: border-box;
  height: 100vh;
  overflow: hidden;
}

.content-wrapper {
  padding: 20px;
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  scrollbar-width: thin;
  scrollbar-color: var(--accent-tertiary) transparent;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.content-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.content-wrapper::-webkit-scrollbar-thumb {
  background-color: var(--accent-tertiary);
  border-radius: 6px;
}

@media (max-width: 768px) {
  .main-content-Dash {
    padding-left: 0;
    padding-bottom: calc(var(--bottom-nav-height) + var(--safe-area-bottom)); /* Space for bottom navigation + safe area */
  }
  
  .content-wrapper {
    padding: 12px;
    height: calc(100vh - var(--header-height) - var(--bottom-nav-height) - var(--safe-area-bottom));
  }
}

/* iPhone X and newer specific adjustments */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .dashboard {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Prevent text size adjustment on orientation change */
html {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* Prevent element overflow */
* {
  max-width: 100%;
}
</style>
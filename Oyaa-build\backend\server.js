// backend/server.js
// Initialize logger first to capture all startup errors
const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Create logs directory if it doesn't exist
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
  try {
    fs.mkdirSync(logDir, { recursive: true });
    console.log('Created logs directory:', logDir);
  } catch (err) {
    console.error('Failed to create logs directory:', err);
  }
}

// Create a basic logger immediately for startup errors
const startupLogger = winston.createLogger({
  level: 'debug',
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'server-startup' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.printf(({ level, message, timestamp, stack }) => {
          return `${timestamp} ${level}: ${message}${stack ? '\n' + stack : ''}`;
        })
      )
    }),
    new winston.transports.File({
      filename: path.join(logDir, 'startup.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
});

// Log startup beginning
startupLogger.info('Server starting up...');

// Define variables at the top level so they're available throughout the file
let https, app, setIO, WorldChatModel;

try {
  // Load environment variables
  require('dotenv').config();
  startupLogger.info('Environment variables loaded');

  // Import required modules
  https = require('https');
  startupLogger.info('Core modules loaded');

  // Import application
  startupLogger.info('Loading application...');
  app = require('./app');
  startupLogger.info('Application loaded successfully');

  // Import WebSocket setup
  startupLogger.info('Loading WebSocket setup...');
  const socketInstance = require('./WebSocket/socketInstance');
  setIO = socketInstance.setIO;
  startupLogger.info('WebSocket setup loaded');

  // Import models
  startupLogger.info('Loading models...');
  WorldChatModel = require('./models/Groups/worldChatModel');
  startupLogger.info('Models loaded');

  // Import logger (but don't use it directly in this file)
  require('./utils/logger');
  startupLogger.info('Logger loaded');

  // Import database configurations
  require('./config/alloydb.config');
  startupLogger.info('AlloyDB Omni configuration loaded');

  // Import Group Chat Valkey configuration
  require('./config/groupChatValkey.config');
  startupLogger.info('Group Chat Valkey configuration loaded');
} catch (err) {
  startupLogger.error('Error during server initialization', { error: err });
  console.error('Error during server initialization:', err);
  process.exit(1);
}

// Set up global uncaught exception handler
process.on('uncaughtException', (error) => {
  // Use startupLogger which is already defined
  startupLogger.error('UNCAUGHT EXCEPTION! 💥 Shutting down...', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    }
  });
  console.error('UNCAUGHT EXCEPTION! 💥', error.name, error.message);
  console.error('Error Stack:', error.stack);

  // Give the logger time to write to file before exiting
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// Set up global unhandled rejection handler
process.on('unhandledRejection', (reason, promise) => {
  // Use startupLogger which is already defined
  startupLogger.error('UNHANDLED REJECTION! 💥 Shutting down...', {
    reason: reason instanceof Error ? {
      name: reason.name,
      message: reason.message,
      stack: reason.stack
    } : reason,
    promise
  });
  console.error('UNHANDLED REJECTION! 💥', reason);

  // Give the logger time to write to file before exiting
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// Read your SSL/TLS certificate and key files
let options;
try {
  options = {
    key: fs.readFileSync('server.key'),
    cert: fs.readFileSync('server.cert'),
  };
  startupLogger.info('SSL certificates loaded successfully');
} catch (error) {
  startupLogger.error('Failed to load SSL certificates', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    }
  });
  console.error('Failed to load SSL certificates:', error.message);
  process.exit(1);
}

const port = 5000;

// Create an HTTPS server
const server = https.createServer(options, app);

// Initialize Socket.IO with improved connection settings
const io = require('socket.io')(server, {
  cors: {
    origin: ['https://localhost:5175', 'https://***************:5175'],
    credentials: true,
  },
  // Improve connection reliability
  pingInterval: 10000, // Send a ping every 10 seconds
  pingTimeout: 5000,   // Consider connection closed if no pong within 5 seconds
  connectTimeout: 10000, // Connection timeout
  transports: ['websocket', 'polling'], // Allow both WebSocket and polling
  allowUpgrades: true, // Allow transport upgrades
  maxHttpBufferSize: 1e8, // 100 MB
  path: '/socket.io', // Default path
});

// Define the temporary groups namespace *after* io is initialized
const tempGroupNamespace = io.of('/temp-groups');

// Attach io to the Express app
app.set('io', io);
setIO(io);

// Fetch or initialize the world chat ID
let worldChatId;
(async () => {
  try {
    // Try to get the World Chat
    let worldChat;
    try {
      worldChat = await WorldChatModel.getWorldChat();
    } catch (error) {
      // If World Chat doesn't exist, initialize it
      startupLogger.info('World Chat not found, initializing it...');
      console.log('World Chat not found, initializing it...');

      // Import GroupModel to initialize World Chat
      const GroupModel = require('./models/Groups/groupModel');
      worldChat = await GroupModel.initializeWorldChat();

      startupLogger.info('World Chat initialized successfully');
      console.log('World Chat initialized successfully');
    }

    worldChatId = worldChat.id;
    startupLogger.info(`World Chat ID set to: ${worldChatId}`);
    console.log(`World Chat ID set to: ${worldChatId}`);
  } catch (err) {
    startupLogger.error('Failed to fetch or initialize world chat ID', {
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack,
        code: err.code,
        sqlMessage: err.sqlMessage
      }
    });
    console.error('Failed to fetch or initialize world chat ID:', err.message);
    if (err.stack) {
      console.error('Error Stack:', err.stack);
    }
    if (err.code) {
      console.error('Error Code:', err.code);
    }
    if (err.sqlMessage) {
      console.error('SQL Error:', err.sqlMessage);
    }
  }
})();

// Setup WebSocket handlers, passing worldChatId to groupwebsocket
require('./WebSocket/friendRequestWebSocket')(io);
require('./WebSocket/chatwebsockets')(io);
require('./WebSocket/groupwebsocket')(io, worldChatId);
require('./WebSocket/tempChatWebSockets')(io);
require('./WebSocket/notificationsWebSocket')(io);
require('./WebSocket/tempGroupWebSocket')(tempGroupNamespace);

// Start the server with error handling
server.listen(port, '0.0.0.0', () => {
  startupLogger.info(`🚀 Server running on https://0.0.0.0:${port}`);
  console.log(`🚀 Server running on https://0.0.0.0:${port}`);
});

// Handle server errors
server.on('error', (error) => {
  startupLogger.error('Server error occurred', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code,
      syscall: error.syscall,
      address: error.address,
      port: error.port
    }
  });

  console.error('Server error occurred:', error.message);

  if (error.code === 'EADDRINUSE') {
    console.error(`Port ${port} is already in use. Please use a different port.`);
  } else if (error.code === 'EACCES') {
    console.error(`Port ${port} requires elevated privileges. Please run with appropriate permissions.`);
  }

  // Exit with error
  process.exit(1);
});
import { Component, createSignal, onMount } from 'solid-js';
import ChatInterface from './components/ChatInterface';
import DebugMenu from './components/DebugMenu';
import { Message } from './types/chat';
import { generateSampleMessages } from './utils/messageGenerator';
import styles from './App.module.css';

const App: Component = () => {
  const [messages, setMessages] = createSignal<Message[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = createSignal({
    fps: 0,
    renderTime: 0,
    memoryUsage: { used: 0, total: 0 },
    scrollJank: 0,
    domNodes: 0,
    virtualization: {
      totalMessages: 0,
      renderedMessages: 0,
      virtualizationRatio: 0,
      visibleRange: { start: 0, end: 0 }
    }
  });

  onMount(() => {
    // Start with 500 messages for testing
    setMessages(generateSampleMessages(500));
  });

  const handleSendMessage = (text: string, media?: any) => {
    const newMessage: Message = {
      id: `msg-${Date.now()}`,
      text,
      timestamp: new Date(),
      sender: {
        id: 'current-user',
        name: 'You',
        avatar: 'https://i.pravatar.cc/40?img=1'
      },
      type: media ? media.type : 'text',
      media
    };

    setMessages(prev => [...prev, newMessage]);
  };

  const handleGenerateMessages = (count: number) => {
    const newMessages = generateSampleMessages(count);
    setMessages(prev => [...prev, ...newMessages]);
  };

  const handleClearMessages = () => {
    setMessages([]);
  };

  return (
    <div class={styles.App}>
      <header class={styles.header}>
        <h1>High-Performance SolidJS Chat Interface</h1>
        <p>Testing with {messages().length} messages</p>
      </header>

      <ChatInterface
        messages={messages()}
        onSendMessage={handleSendMessage}
        onMetricsUpdate={setPerformanceMetrics}
      />

      <DebugMenu
        messages={messages()}
        metrics={performanceMetrics()}
        onGenerateMessages={handleGenerateMessages}
        onClearMessages={handleClearMessages}
      />
    </div>
  );
};

export default App;

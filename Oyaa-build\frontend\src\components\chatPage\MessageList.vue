<template>
  <div class="message-list" ref="messageContainer">
    <!-- Sentinel element for IntersectionObserver -->
    <div ref="sentinel" class="sentinel"></div>

    <div v-if="messages.length === 0" class="welcome-message">
      <p>Start your conversation with {{ friend?.username }}</p>
    </div>
    <MessageChatList
      v-if="messages.length"
      :messages="messages"
      :current-user="currentUser"
      @reply="replyToMessage"
      @react="reactToMessage"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, onBeforeUpdate, onUpdated, nextTick } from 'vue';
import { useStore } from 'vuex';
import MessageChatList from './MessageComponents/MessageChatList.vue';

const store = useStore();
const messageContainer = ref(null);
const sentinel = ref(null);
const osInstance = ref(null); // Reference to OverlayScrollbars instance
const currentUser = computed(() => store.getters['auth/user']);

const props = defineProps({
  messages: { type: Array, required: true },
  friend: { type: Object, required: true },
});

const emit = defineEmits(['reply', 'react', 'load-older']);

const replyToMessage = (message) => {
  console.log('Replying to:', message);
  emit('reply', message);
};

const reactToMessage = (message) => {
  console.log('Reacting to:', message);
  emit('react', message);
};

// Updated scrollToBottom to use OverlayScrollbars API if available
const scrollToBottom = async (behavior = 'auto') => {
  await nextTick(); // Ensure DOM is updated
  if (osInstance.value) {
    osInstance.value.scroll({ y: '100%' }, behavior === 'smooth' ? 300 : 0);
  } else if (messageContainer.value) {
    messageContainer.value.scrollTo({
      top: messageContainer.value.scrollHeight,
      behavior,
    });
  }
};

let observer;
onMounted(async () => {
  // Set up IntersectionObserver to fetch older messages
  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          console.log('Sentinel near viewport: requesting older messages.');
          emit('load-older');
        }
      });
    },
    {
      root: messageContainer.value,
      rootMargin: '600px 0px 0px 0px',
      threshold: 0,
    }
  );
  if (sentinel.value) {
    observer.observe(sentinel.value);
  }

  // Initialize OverlayScrollbars if available
  if (messageContainer.value && window.OverlayScrollbars) {
    osInstance.value = window.OverlayScrollbars(messageContainer.value, {
      scrollbars: {
        autoHide: 'move',
      },
    });
  }

  // Scroll to bottom on initial load
  await scrollToBottom('auto');
  initialScrollDone.value = true;
});

onBeforeUnmount(() => {
  if (observer && sentinel.value) {
    observer.unobserve(sentinel.value);
  }
});

let previousScrollHeight = 0;
const initialScrollDone = ref(false);
const previousMessagesCount = ref(props.messages.length);
const previousFirstMessageId = ref(props.messages.length ? props.messages[0].id : null);

onBeforeUpdate(() => {
  if (messageContainer.value) {
    previousScrollHeight = messageContainer.value.scrollHeight;
  }
});

onUpdated(async () => {
  if (!messageContainer.value) return;

  if (!initialScrollDone.value) {
    await scrollToBottom('auto');
    initialScrollDone.value = true;
  } else if (props.messages.length > previousMessagesCount.value) {
    if (props.messages[0].id === previousFirstMessageId.value) {
      // New messages added at the end
      await scrollToBottom('smooth');
    } else {
      // Older messages loaded at the top, maintain scroll position
      const newScrollHeight = messageContainer.value.scrollHeight;
      const scrollDifference = newScrollHeight - previousScrollHeight;
      if (osInstance.value) {
        const currentScroll = osInstance.value.scroll().position.y;
        osInstance.value.scroll({ y: currentScroll + scrollDifference }, 0);
      } else {
        messageContainer.value.scrollTo({
          top: messageContainer.value.scrollTop + scrollDifference,
          behavior: 'auto',
        });
      }
    }
  }

  previousMessagesCount.value = props.messages.length;
  previousFirstMessageId.value = props.messages.length ? props.messages[0].id : null;
});
</script>

<style scoped>
.message-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: #36393f;
  color: #dcddde;
  position: relative;
  scroll-behavior: smooth;
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 100, 100, 0.2) transparent;
}

/* WebKit scrollbar styling (Chrome, Safari, Edge) */
.message-list::-webkit-scrollbar {
  width: 4px; /* Very slim scrollbar */
}

.message-list::-webkit-scrollbar-track {
  background: transparent; /* Transparent track */
  margin: 4px 0; /* Add some margin to top and bottom */
}

.message-list::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.2); /* Dark subtle color */
  border-radius: 4px; /* Rounded corners */
}

/* Only show scrollbar on hover for a cleaner look */
.message-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(150, 150, 150, 0.3); /* Slightly lighter on hover */
}

.welcome-message {
  text-align: center;
  padding: 2rem;
  color: #72767d;
}

.sentinel {
  height: 1px;
}
</style>
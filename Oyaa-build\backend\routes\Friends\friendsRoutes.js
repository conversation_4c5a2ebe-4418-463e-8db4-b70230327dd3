const express = require('express');
const friendsController = require('../../controllers/Friends/friendsController');
const router = express.Router();

// Specific route first
router.get('/check', friendsController.checkFriendship);
router.get('/:userId(\\d+)', friendsController.getFriends);
// Parameterized route after specific routes
router.get('/:userId', friendsController.getFriends);

// Other routes
router.post('/add', friendsController.addFriend);
router.post('/remove', friendsController.removeFriend);

module.exports = router;
<template>
  <div class="fab-options">
    <div class="options-list">
      <router-link
        v-for="(item, index) in menuItems"
        :key="item.action"
        :to="getRoute(item.action)"
        class="action-bubble"
        :class="{ 'is-visible': isVisible }"
        :style="{ 'transition-delay': `${index * 50}ms` }"
        :aria-label="item.label"
      >
        <component :is="item.icon" class="action-icon" />
        <span class="action-text">{{ item.label }}</span>
      </router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { Plus, Search, MapPin, TrendingUp, UserPlus, Heart, EyeOff } from 'lucide-vue-next';

const props = defineProps({
  activeTab: { type: String, default: 'friends' },
});

const menuItems = computed(() => {
  return props.activeTab === 'groups'
    ? [
        { action: 'createGroup', label: 'Create Group', icon: Plus },
        { action: 'findGroup', label: 'Find Group', icon: Search },
        { action: 'findGroupsInterest', label: 'Find Groups by Interest', icon: Heart },
        { action: 'groupsNearYou', label: 'Groups Near You', icon: MapPin },
        { action: 'trendingRooms', label: 'Trending Rooms', icon: TrendingUp },
        { action: 'anonymous', label: 'Anonymous', icon: EyeOff },
      ]
    : [
        { action: 'addFriends', label: 'Add Friends', icon: UserPlus },
        { action: 'peopleNearYou', label: 'People Near You', icon: MapPin },
        { action: 'addFriendsInterest', label: 'Add Friends by Interest', icon: Heart },
      ];
});

const isVisible = ref(false);

watch(
  () => props.activeTab,
  () => {
    isVisible.value = false;
    nextTick(() => {
      isVisible.value = true;
    });
  },
  { immediate: true }
);

const getRoute = (action) => {
  const routeMap = {
    createGroup: '/create-group',
    findGroup: '/find-group',
    findGroupsInterest: '/find-groups-interest',
    groupsNearYou: '/groups-near-you',
    trendingRooms: '/trending-rooms',
    addFriends: '/add-friends',
    addFriendsInterest: '/add-friends-interest',
    peopleNearYou: '/near-you',
    anonymous: '/anonymous',
  };
  return routeMap[action] || '/';
};
</script>

<style scoped>
.fab-options {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-100%);
  z-index: 3;
}

.options-list {
  display: flex;
  flex-direction: column-reverse;
  gap: 12px;
  width: max-content;
}

.action-bubble {
  display: flex;
  align-items: center;
  padding: 12px 18px;
  background: rgba(26, 26, 34, 0.95);
  border: 1px solid rgba(46, 46, 58, 0.8);
  border-radius: 14px;
  color: #e2e2e2;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  cursor: pointer;
  min-width: 180px;
  transform: translateY(64px);
  opacity: 0;
  pointer-events: none;
  text-decoration: none;
  backdrop-filter: blur(10px);
  touch-action: manipulation; /* Improve touch behavior */
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
}

.action-bubble.is-visible {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}

.action-bubble:hover {
  background: rgba(46, 46, 58, 0.95);
  transform: translateY(-2px) translateX(2px);
  border-color: rgba(99, 102, 241, 0.5);
}

.action-bubble:active {
  transform: scale(0.98);
}

.action-bubble:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px #0f0f13, 0 0 0 4px #6366f1;
}

.action-icon {
  width: 20px;
  height: 20px;
  margin-right: 14px;
  color: #6366f1;
  flex-shrink: 0;
}

.action-text {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .fab-options {
    bottom: 70px;
    left: auto;
    right: 70px;
    transform: none;
  }
  
  .options-list {
    gap: 10px;
  }
  
  .action-bubble {
    min-width: 160px;
    padding: 10px 14px;
    border-radius: 12px;
  }
  
  .action-text {
    font-size: 13px;
  }
  
  .action-icon {
    width: 18px;
    height: 18px;
    margin-right: 10px;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .fab-options {
    right: 60px;
  }
  
  .action-bubble {
    min-width: 140px;
    padding: 8px 12px;
  }
  
  .action-text {
    font-size: 12px;
  }
  
  .action-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}

/* Very small mobile screens */
@media (max-width: 320px) {
  .fab-options {
    right: 50px;
  }
  
  .options-list {
    gap: 8px;
  }
  
  .action-bubble {
    min-width: 130px;
    padding: 6px 10px;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .action-bubble {
    transition-duration: 0.01ms !important;
    transition-delay: 0ms !important;
  }
}
</style>
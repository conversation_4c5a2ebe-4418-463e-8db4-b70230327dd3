// frontend/src/config/firebase.config.js
import { initializeApp } from 'firebase/app';
import {
  getAuth,
  setPersistence,
  browserLocalPersistence,
  browserSessionPersistence,
  inMemoryPersistence,
  connectAuthEmulator
} from 'firebase/auth';
import { getAnalytics, isSupported } from 'firebase/analytics';

/**
 * Validate Firebase configuration
 * @returns {boolean} - Whether the configuration is valid
 */
const validateFirebaseConfig = () => {
  const requiredVars = [
    'VITE_FIREBASE_API_KEY',
    'VITE_FIREBASE_AUTH_DOMAIN',
    'VITE_FIREBASE_PROJECT_ID'
  ];

  const missingVars = requiredVars.filter(varName => !import.meta.env[varName]);

  if (missingVars.length > 0) {
    console.error(`Missing required Firebase configuration: ${missingVars.join(', ')}`);
    return false;
  }

  return true;
};

/**
 * Get persistence type based on environment
 * @returns {any} - Firebase persistence type
 */
const getPersistenceType = () => {
  // Get persistence preference from environment or localStorage
  const persistencePreference = import.meta.env.VITE_FIREBASE_PERSISTENCE ||
                               localStorage.getItem('auth_persistence') ||
                               'LOCAL';

  switch (persistencePreference.toUpperCase()) {
    case 'SESSION':
      console.log('Using SESSION persistence');
      return browserSessionPersistence;
    case 'NONE':
      console.log('Using IN-MEMORY persistence');
      return inMemoryPersistence;
    case 'LOCAL':
    default:
      console.log('Using LOCAL persistence');
      return browserLocalPersistence;
  }
};

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
let app, auth, analytics;

try {
  if (validateFirebaseConfig()) {
    console.log('Initializing Firebase with config:', {
      projectId: firebaseConfig.projectId,
      authDomain: firebaseConfig.authDomain
    });

    // Initialize Firebase app
    app = initializeApp(firebaseConfig);

    // Initialize Firebase Auth
    auth = getAuth(app);

    // Set persistence based on configuration
    const persistenceType = getPersistenceType();
    setPersistence(auth, persistenceType)
      .then(() => {
        console.log('Firebase persistence set successfully');
      })
      .catch((error) => {
        console.error('Error setting persistence:', error);
      });

    // Initialize analytics only if supported
    isSupported().then(supported => {
      if (supported) {
        analytics = getAnalytics(app);
        console.log('Firebase Analytics initialized');
      }
    }).catch(error => {
      console.warn('Firebase Analytics not supported:', error);
    });

    // Connect to auth emulator if in development mode
    if (import.meta.env.DEV && import.meta.env.VITE_USE_FIREBASE_EMULATOR === 'true') {
      connectAuthEmulator(auth, 'http://localhost:9099');
      console.log('Connected to Firebase Auth Emulator');
    }

    console.log('Firebase initialized successfully');
  } else {
    console.error('Firebase initialization skipped due to missing configuration');
  }
} catch (error) {
  console.error('Error initializing Firebase:', error);
}

export { app, auth, analytics };

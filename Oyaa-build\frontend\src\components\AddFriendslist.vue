<template>
  <div class="add-friends-list">
    <!-- Existing header, toast, loading, error, and empty state unchanged -->
    <div class="header">
      <h2>Friend Requests Sent</h2>
      <div class="live-indicator">
        <span class="live-dot"></span>
        <span class="live-text">Live Updates</span>
      </div>
    </div>

    <transition name="fade">
      <div v-if="toastMessage" class="toast">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="toast-icon">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
        {{ toastMessage }}
      </div>
    </transition>

    <div v-if="loading" class="loading-state">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loader">
        <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
      </svg>
      <span>Loading requests...</span>
    </div>
    <div v-else-if="error" class="error-message">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="error-icon">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
      {{ error }}
    </div>
    <div v-else>
      <div v-if="friendRequests.length === 0" class="empty-state">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
          <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="8.5" cy="7" r="4"></circle>
          <line x1="20" y1="8" x2="20" y2="14"></line>
          <line x1="23" y1="11" x2="17" y2="11"></line>
        </svg>
        <p>No friend requests sent</p>
        <p class="hint">Search for users above to send friend requests</p>
        <div :class="['connection-status', connectionStatus]">
          <span class="status-dot"></span>
          <span v-if="connectionStatus === 'connecting'">Connecting...</span>
          <span v-else-if="connectionStatus === 'connected'">Live updates active</span>
          <span v-else>Connection error</span>
        </div>
      </div>
      <ul v-else class="friend-requests-list">
        <li v-for="request in friendRequests" :key="request.id" class="request-item">
          <div class="request-user">
            <div class="user-avatar">
              <img v-if="request.receiver_avatar" :src="request.receiver_avatar" alt="User Avatar" />
              <span v-else>{{ getInitials(request.receiver_username) }}</span>
            </div>
            <div class="user-info">
              <h3>{{ request.receiver_username }}</h3>
              <p v-if="request.receiver_description">{{ request.receiver_description }}</p>
            </div>
          </div>
          <div :class="['status', request.status.toLowerCase()]">
            {{ request.status || 'Pending' }}
            <span v-if="(request.status || 'Pending') === 'Pending'" class="spinner"></span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { mapGetters } from 'vuex';
import useFriendNotifications from '../composables/useFriendNotifications';

export default {
  data() {
    return {
      friendRequests: [],
      rejectedRequests: [],
      toastMessage: '',
      loading: false,
      error: '',
      friendNotificationsInstance: null, // Store the WebSocket instance
      connectionStatus: 'connecting', // Track WebSocket connection status
    };
  },
  computed: {
    ...mapGetters('auth', ['userId']),
  },
  async created() {
    this.loading = true;

    // Set up event listeners for WebSocket connection status
    window.addEventListener('friendsocket:connected', this.handleSocketConnected);
    window.addEventListener('friendsocket:disconnected', this.handleSocketDisconnected);
    window.addEventListener('friendsocket:error', this.handleSocketError);

    // Set up WebSocket first for real-time updates
    this.setupWebSocket();

    // Wait a moment for WebSocket to connect and fetch data
    await new Promise(resolve => setTimeout(resolve, 1000));

    // If we still don't have any data, fall back to HTTP
    if (this.friendRequests.length === 0 && !this.error) {
      console.log('No requests received via WebSocket, falling back to HTTP');
      await this.fetchFriendRequests();
    }

    this.loading = false;
  },

  beforeDestroy() {
    // Clean up event listeners
    window.removeEventListener('friendsocket:connected', this.handleSocketConnected);
    window.removeEventListener('friendsocket:disconnected', this.handleSocketDisconnected);
    window.removeEventListener('friendsocket:error', this.handleSocketError);
  },
  methods: {
    getInitials(name) {
  if (!name) return '';
  const parts = name.split(' ');
  if (parts.length >= 2) {
    return (parts[0][0] + parts[1][0]).toUpperCase();
  } else {
    return name.substring(0, 2).toUpperCase();
  }
},
    displayToast(message, duration = 5000) {
      this.toastMessage = message;
      setTimeout(() => {
        this.toastMessage = '';
      }, duration);
    },

    async fetchFriendRequests() {
      this.loading = true;
      this.error = '';
      let seenRejections = [];
      try {
        seenRejections = JSON.parse(localStorage.getItem('seenRejections'));
        if (!Array.isArray(seenRejections)) {
          seenRejections = [];
        }
      } catch (e) {
        seenRejections = [];
      }
      const seenParam = seenRejections.join(',');
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/friend-requests/sent/${this.userId}?seen=${seenParam}`,
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        );
        console.log('Full API Response:', response.data);
        const requests = response.data.requests || response.data;
        this.friendRequests = requests.filter((req) => req.status === 'Pending');
        this.rejectedRequests = requests.filter((req) => req.status === 'Rejected');

        if (this.rejectedRequests.length > 0) {
          const names = this.rejectedRequests
            .map((r) => r.receiver_username)
            .join(', ');
          this.displayToast(
            `Your friend request${
              this.rejectedRequests.length > 1 ? 's' : ''
            } to ${names} ha${
              this.rejectedRequests.length > 1 ? 've' : 's'
            } been rejected.`
          );
          const updatedSeen = [
            ...seenRejections,
            ...this.rejectedRequests.map((r) => r.id),
          ];
          localStorage.setItem('seenRejections', JSON.stringify(updatedSeen));
        }
      } catch (error) {
        console.error('Fetch Error:', error);
        this.error = 'Failed to fetch friend requests.';
      } finally {
        this.loading = false;
      }
    },

    // Socket connection event handlers
    handleSocketConnected() {
      console.log('Friend requests socket connected');
      this.connectionStatus = 'connected';

      // Request the list of sent requests again
      if (this.friendNotificationsInstance && this.friendNotificationsInstance.socket) {
        this.friendNotificationsInstance.socket.emit('getSentRequests', {
          userId: this.userId
        });
      }
    },

    handleSocketDisconnected() {
      console.log('Friend requests socket disconnected');
      this.connectionStatus = 'connecting';
    },

    handleSocketError(event) {
      console.error('Friend requests socket error:', event.detail);
      this.connectionStatus = 'error';
      this.error = 'Connection error. Please refresh the page.';
    },

    // Handle friend requests list data
    handleFriendRequestsList(data) {
      console.log('Processing friend requests list data:', data);

      if (data.sentRequests && Array.isArray(data.sentRequests)) {
        // Filter to only show pending requests (case insensitive)
        const pendingRequests = data.sentRequests.filter(req =>
          req.status && req.status.toLowerCase() === 'pending'
        );
        console.log('Filtered pending sent requests:', pendingRequests);

        // Update the list
        this.friendRequests = pendingRequests;

        // Update the UI to show we have the latest data
        this.connectionStatus = 'connected';

        // If we have requests, show a toast
        if (pendingRequests.length > 0) {
          this.displayToast(`You have ${pendingRequests.length} pending friend request${pendingRequests.length !== 1 ? 's' : ''}`, 3000);
        }
      } else {
        console.warn('Received invalid friend requests list data:', data);
      }
    },

    setupWebSocket() {
      // Get the full WebSocket composable with all methods
      const friendNotifications = useFriendNotifications();
      this.friendNotificationsInstance = friendNotifications;

      // Set up the WebSocket listener
      friendNotifications.setupFriendWebSocket((data) => {
        console.log('Sent requests - received WebSocket event:', data);

        if (data.type === 'NEW_FRIEND_REQUEST') {
          // If this is a request sent by the current user, add it to the list
          if (data.senderId === this.userId) {
            console.log('Adding new sent request to list:', data.request);
            // Check if it's not already in the list
            if (!this.friendRequests.some(req => req.id === data.request.id)) {
              // Make sure the status is set to 'pending' for consistency with database
              const requestWithStatus = {
                ...data.request,
                status: data.request.status || 'pending'
              };
              this.friendRequests.push(requestWithStatus);

              // Play a sound or show a visual indicator that the request was added
              this.displayToast(`Friend request to ${data.request.receiver_username} added to your sent requests`, 3000);
            }
          }
        } else if (data.type === 'FRIEND_REQUEST_UPDATE') {
          // If this is an update to a request sent by the current user
          if (data.senderId === this.userId) {
            console.log('Updating sent request status:', data.request);

            const requestIndex = this.friendRequests.findIndex(
              (req) => req.id === data.request.id
            );

            if (requestIndex !== -1) {
              // Update the request in the list
              this.$set(this.friendRequests, requestIndex, {
                ...this.friendRequests[requestIndex],
                status: data.request.status
              });

              // Show appropriate notifications
              if (data.request.status === 'Accepted') {
                this.displayToast(
                  `${data.request.receiver_username} accepted your friend request!`,
                  5000
                );

                // Remove from list after a delay
                setTimeout(() => {
                  this.friendRequests = this.friendRequests.filter(
                    req => req.id !== data.request.id
                  );
                }, 5000);
              } else if (data.request.status === 'Rejected') {
                this.displayToast(
                  `${data.request.receiver_username} rejected your friend request.`,
                  5000
                );

                // Remove from list after a delay
                setTimeout(() => {
                  this.friendRequests = this.friendRequests.filter(
                    req => req.id !== data.request.id
                  );
                }, 5000);
              }
            } else if (
              data.request.status === 'Pending' &&
              data.senderId === this.userId
            ) {
              // This is a new request that wasn't in our list
              this.friendRequests.push(data.request);
            }
          }
        } else if (data.type === 'FRIEND_REQUESTS_LIST') {
          // Handle initial list of sent requests
          console.log('Received sent requests list:', data);
          this.handleFriendRequestsList(data);
        }
      });

      // Request the list of sent requests
      console.log('Requesting sent friend requests via WebSocket');
      friendNotifications.socket.emit('getSentRequests', { userId: this.userId });
    },
  },
};
</script>

<style scoped>
.add-friends-list {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --error-color: #f87171;
  --success-color: #34d399;
  --warning-color: #fbbf24;

  margin-top: 2rem;
  padding: 1.5rem;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(52, 211, 153, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(52, 211, 153, 0.2);
  border-radius: 8px;
  font-size: 0.875rem;
}

.live-dot {
  width: 8px;
  height: 8px;
  background-color: var(--success-color);
  border-radius: 50%;
  display: inline-block;
  position: relative;
}

.live-dot::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background-color: rgba(52, 211, 153, 0.3);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% { transform: scale(0.8); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 0.4; }
  100% { transform: scale(0.8); opacity: 0.8; }
}

.live-text {
  font-weight: 500;
}

.toast {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem 1rem;
  background-color: rgba(248, 113, 113, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(248, 113, 113, 0.2);
  border-radius: 8px;
}

.toast-icon {
  color: var(--error-color);
  flex-shrink: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: all var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-secondary);
  gap: 1rem;
}

.loader {
  animation: spin 1.2s linear infinite;
  color: var(--accent-primary);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  background-color: rgba(248, 113, 113, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(248, 113, 113, 0.2);
  border-radius: 8px;
}

.error-icon {
  color: var(--error-color);
  flex-shrink: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem 1rem;
  color: var(--text-secondary);
}

.empty-state svg {
  color: var(--accent-tertiary);
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.empty-state .hint {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  background-color: var(--bg-tertiary);
}

.connection-status.connecting .status-dot {
  background-color: var(--warning-color);
}

.connection-status.connected .status-dot {
  background-color: var(--success-color);
}

.connection-status.error .status-dot {
  background-color: var(--error-color);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: inline-block;
}

.friend-requests-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.request-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.request-item:last-child {
  border-bottom: none;
}

.request-item:hover {
  background-color: var(--bg-tertiary);
}

.request-user {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
  background-color: var(--accent-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  font-weight: 500;
  font-size: 1rem;
  flex-shrink: 0;
  overflow: hidden; /* Clips the image */
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.username {
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--text-primary);
}
.user-info {
  flex: 1;
  min-width: 0;
}

.user-info h3 {
  margin: 0;
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--text-primary);
}

.user-info p {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status.pending {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
}

.status.accepted {
  background-color: rgba(52, 211, 153, 0.15);
  color: var(--success-color);
}

.status.rejected {
  background-color: rgba(248, 113, 113, 0.15);
  color: var(--error-color);
}

.spinner {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid rgba(99, 102, 241, 0.3);
  border-top: 2px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@media (max-width: 640px) {
  .request-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .status {
    align-self: flex-start;
  }
}
</style>
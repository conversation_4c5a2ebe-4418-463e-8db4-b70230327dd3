// backend/friendRequestWebSocket.js
const FriendRequestService = require('../services/Friends/friendRequestService');
const userService = require('../services/User/userService');
const jwt = require('jsonwebtoken');
const db = require('../utils/db'); // Import db for direct queries

module.exports = (io) => {
  const friendRequestNamespace = io.of('/friend-requests');
  const notificationsNamespace = io.of('/notifications');

  console.log('Friend Request WebSocket namespace initialized.');

  // Middleware to authenticate socket connections
  friendRequestNamespace.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;

      if (!token) {
        console.log('No token provided in socket handshake');
        // Try to get token from query params as fallback
        if (socket.handshake.query && socket.handshake.query.token) {
          const queryToken = socket.handshake.query.token;
          console.log('Found token in query params');
          try {
            const decoded = jwt.verify(queryToken, process.env.JWT_SECRET);
            socket.userId = decoded.userId;
            console.log(`Authenticated socket connection for user ${socket.userId} using query token`);
            return next();
          } catch (err) {
            console.error('Invalid token in query params:', err);
          }
        }
        return next(new Error('Authentication error: No token provided'));
      }

      // Verify the token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      socket.userId = decoded.userId;
      console.log(`Authenticated socket connection for user ${socket.userId}`);
      next();
    } catch (error) {
      console.error('Socket authentication error:', error);
      next(new Error('Authentication error: Invalid token'));
    }
  });

  friendRequestNamespace.on('connection', (socket) => {
    console.log(`New WebSocket connection for friend requests: ${socket.id}`);

    // Listen for join event to add socket to a room based on userId
    socket.on('join', (userId) => {
      console.log(`Join event received with userId: ${userId}, socket.userId: ${socket.userId}`);

      // Use provided userId or fall back to the authenticated userId from the token
      const roomId = userId || socket.userId;

      if (roomId) {
        // Convert to string to ensure consistent room naming
        const roomIdStr = roomId.toString();
        socket.join(roomIdStr);
        console.log(`Socket ${socket.id} joined room ${roomIdStr}`);

        // Automatically fetch friend requests after joining
        socket.emit('getRequests', { userId: roomId }, (response) => {
          if (response.success) {
            console.log(`Auto-fetched ${response.requests.length} friend requests for user ${roomId}`);
          } else {
            console.error('Error auto-fetching friend requests:', response.error);
          }
        });
      } else {
        console.error('join event missing userId and no authenticated userId available');
      }
    });

    // Get friend requests for a user
    socket.on('getRequests', async ({ userId }, callback) => {
      try {
        console.log(`Getting friend requests for receiverId: ${userId}, socket.userId: ${socket.userId}`);

        // If userId is not provided, use the authenticated user's ID
        const receiverId = userId || socket.userId;

        if (!receiverId) {
          throw new Error('No user ID provided for getRequests');
        }

        // Convert to integer if it's a string
        const receiverIdInt = parseInt(receiverId);

        console.log(`Fetching requests for receiverId (int): ${receiverIdInt}`);
        const requests = await FriendRequestService.getRequests(receiverIdInt);
        console.log('Found friend requests:', requests);
        console.log('Number of requests:', requests.length);

        // Send the requests back to the client
        console.log('Emitting FRIEND_REQUESTS_LIST with requests:', requests);
        socket.emit('FRIEND_REQUESTS_LIST', { requests });

        // Also broadcast to the receiver's room to ensure all connected clients get the update
        const receiverRoom = receiverIdInt.toString();
        console.log(`Broadcasting to room ${receiverRoom}`);
        friendRequestNamespace.to(receiverRoom).emit('FRIEND_REQUESTS_LIST', { requests });

        if (callback) callback({ success: true, requests });
      } catch (error) {
        console.error('Error getting friend requests:', error);
        if (callback) callback({ success: false, error: error.message });
      }
    });

    // Send a friend request
    socket.on('sendFriendRequest', async ({ senderId, receiverId }, callback) => {
      try {
        console.log(`Sending friend request from ${senderId} to ${receiverId}`);

        // Verify the sender is the authenticated user
        if (socket.userId !== senderId) {
          throw new Error('Unauthorized: You can only send requests as yourself');
        }

        // Create the friend request
        const request = await FriendRequestService.createRequest(senderId, receiverId);
        console.log('Friend request created:', request);

        // Get sender details for the notification
        const sender = await userService.getUserById(senderId);

        // Get receiver details for the notification
        const receiver = await userService.getUserById(receiverId);

        // Prepare the payload
        const payload = {
          type: 'NEW_FRIEND_REQUEST',
          request: {
            id: request.id,
            sender_id: senderId,
            receiver_id: receiverId,
            sender_username: sender.username,
            sender_avatar: sender.avatar,
            sender_description: sender.description,
            receiver_username: receiver.username,
            receiver_avatar: receiver.avatar,
            receiver_description: receiver.description,
            created_at: request.created_at,
            status: request.status
          },
          senderId,
          receiverId,
        };

        // Emit to both the sender and receiver
        friendRequestNamespace.to(senderId.toString()).emit('NEW_FRIEND_REQUEST', payload);
        friendRequestNamespace.to(receiverId.toString()).emit('NEW_FRIEND_REQUEST', payload);

        // Also emit to the notifications namespace for the receiver
        const notificationPayload = {
          id: `fr_${request.id}`,
          type: 'friendRequest',
          message: `${sender.username} sent you a friend request`,
          timestamp: new Date().toISOString(),
          avatar: sender.avatar,
          sender: {
            id: senderId,
            name: sender.username
          },
          data: request
        };

        // Emit to the receiver's room in the notifications namespace
        const receiverRoom = `user_${receiverId}`;
        notificationsNamespace.to(receiverRoom).emit('notification', notificationPayload);

        // Also send an updated list of sent requests to the sender
        try {
          console.log(`Fetching updated sent requests for sender ${senderId}`);

          // Get ALL sent requests (including all statuses)
          const allSentRequests = await FriendRequestService.getAllSentRequests(senderId);
          console.log(`Found ${allSentRequests.length} ALL sent requests for sender ${senderId}`);

          // Emit the updated list to the sender using the new event
          friendRequestNamespace.to(senderId.toString()).emit('SENT_REQUESTS_LIST', { sentRequests: allSentRequests });

          // Also emit the original event for backward compatibility
          const pendingSentRequests = await FriendRequestService.getSentRequests(senderId);
          friendRequestNamespace.to(senderId.toString()).emit('FRIEND_REQUESTS_LIST', { sentRequests: pendingSentRequests });
        } catch (error) {
          console.error('Error fetching sent requests after new request:', error);
        }

        console.log(`Friend request event sent from ${senderId} to ${receiverId}`);
        console.log(`Notification emitted to room ${receiverRoom}`);

        if (callback) callback({ success: true, request });
      } catch (error) {
        console.error('Error sending friend request:', error);
        if (callback) callback({ success: false, error: error.message });
      }
    });

    // Accept a friend request
    socket.on('acceptFriendRequest', async ({ requestId }, callback) => {
      try {
        console.log(`Accepting friend request ${requestId}`);

        // Get the request to verify ownership
        const request = await FriendRequestService.getRequestById(requestId);

        if (!request) {
          throw new Error('Friend request not found');
        }

        // Verify the receiver is the authenticated user
        if (socket.userId !== request.receiver_id) {
          throw new Error('Unauthorized: You can only accept requests sent to you');
        }

        try {
          // Accept the request
          console.log(`Calling FriendRequestService.acceptRequest with requestId: ${requestId}`);
          const result = await FriendRequestService.acceptRequest(requestId);
          console.log('Friend request accepted result:', result);

          // Prepare the payload
          const payload = {
            type: 'FRIEND_REQUEST_UPDATE',
            request: {
              id: requestId,
              status: 'accepted',
              sender_id: request.sender_id,
              receiver_id: request.receiver_id
            },
            senderId: request.sender_id,
            receiverId: request.receiver_id,
          };

          // Emit to both parties
          friendRequestNamespace.to(request.sender_id.toString()).emit('FRIEND_REQUEST_UPDATE', payload);
          friendRequestNamespace.to(request.receiver_id.toString()).emit('FRIEND_REQUEST_UPDATE', payload);

          console.log(`Friend request accepted event sent for request ${requestId}`);

          // Also refresh the lists for both users
          console.log('Refreshing friend requests lists for both users');

          // Get updated received requests for the receiver
          const receiverRequests = await FriendRequestService.getRequests(request.receiver_id);
          friendRequestNamespace.to(request.receiver_id.toString()).emit('FRIEND_REQUESTS_LIST', { requests: receiverRequests });

          // Get updated sent requests for the sender
          const senderSentRequests = await FriendRequestService.getSentRequests(request.sender_id);
          friendRequestNamespace.to(request.sender_id.toString()).emit('FRIEND_REQUESTS_LIST', { sentRequests: senderSentRequests });

          if (callback) callback({ success: true, result });
        } catch (acceptError) {
          console.error('Error in acceptRequest:', acceptError);

          // Check if it's a duplicate key error (already friends)
          if (acceptError.code === '23505' && acceptError.constraint === 'friends_user_id_friend_id_key') {
            console.log('Users are already friends, updating request status anyway');

            // Just update the request status without adding friendship
            const updateQuery = `
              UPDATE friend_requests
              SET status = 'Accepted'
              WHERE id = $1
              RETURNING *;
            `;
            const updateResult = await db.query(updateQuery, [requestId]);

            // Prepare the payload
            const payload = {
              type: 'FRIEND_REQUEST_UPDATE',
              request: {
                id: requestId,
                status: 'accepted',
                sender_id: request.sender_id,
                receiver_id: request.receiver_id
              },
              senderId: request.sender_id,
              receiverId: request.receiver_id,
            };

            // Emit to both parties
            friendRequestNamespace.to(request.sender_id.toString()).emit('FRIEND_REQUEST_UPDATE', payload);
            friendRequestNamespace.to(request.receiver_id.toString()).emit('FRIEND_REQUEST_UPDATE', payload);

            console.log(`Friend request accepted event sent for request ${requestId} (already friends)`);

            if (callback) callback({ success: true, result: updateResult.rows[0], alreadyFriends: true });
          } else {
            // Re-throw other errors
            throw acceptError;
          }
        }
      } catch (error) {
        console.error('Error accepting friend request:', error);
        if (callback) callback({ success: false, error: error.message });
      }
    });

    // Reject a friend request
    socket.on('rejectFriendRequest', async ({ requestId }, callback) => {
      try {
        console.log(`Rejecting friend request ${requestId}`);

        // Get the request to verify ownership
        const request = await FriendRequestService.getRequestById(requestId);

        if (!request) {
          throw new Error('Friend request not found');
        }

        // Verify the receiver is the authenticated user
        if (socket.userId !== request.receiver_id) {
          throw new Error('Unauthorized: You can only reject requests sent to you');
        }

        // Reject the request
        console.log(`Calling FriendRequestService.rejectRequest with requestId: ${requestId}`);
        const result = await FriendRequestService.rejectRequest(requestId);
        console.log('Friend request rejected result:', result);

        // Prepare the payload
        const payload = {
          type: 'FRIEND_REQUEST_UPDATE',
          request: {
            id: requestId,
            status: 'rejected',
            sender_id: request.sender_id,
            receiver_id: request.receiver_id
          },
          senderId: request.sender_id,
          receiverId: request.receiver_id,
        };

        // Emit to both parties
        friendRequestNamespace.to(request.sender_id.toString()).emit('FRIEND_REQUEST_UPDATE', payload);
        friendRequestNamespace.to(request.receiver_id.toString()).emit('FRIEND_REQUEST_UPDATE', payload);

        console.log(`Friend request rejected event sent for request ${requestId}`);

        // Also refresh the lists for both users
        console.log('Refreshing friend requests lists for both users');

        // Get updated received requests for the receiver
        const receiverRequests = await FriendRequestService.getRequests(request.receiver_id);
        friendRequestNamespace.to(request.receiver_id.toString()).emit('FRIEND_REQUESTS_LIST', { requests: receiverRequests });

        // Get updated sent requests for the sender
        const senderSentRequests = await FriendRequestService.getSentRequests(request.sender_id);
        friendRequestNamespace.to(request.sender_id.toString()).emit('FRIEND_REQUESTS_LIST', { sentRequests: senderSentRequests });

        if (callback) callback({ success: true, result });
      } catch (error) {
        console.error('Error rejecting friend request:', error);
        if (callback) callback({ success: false, error: error.message });
      }
    });

    // Get ALL sent friend requests for a user (new method that includes all statuses)
    socket.on('getAllSentRequests', async ({ userId }, callback) => {
      try {
        console.log(`Getting ALL sent friend requests for senderId: ${userId}, socket.userId: ${socket.userId}`);

        // If userId is not provided, use the authenticated user's ID
        const senderId = userId || socket.userId;

        if (!senderId) {
          throw new Error('No user ID provided for getAllSentRequests');
        }

        // Convert to integer if it's a string
        const senderIdInt = parseInt(senderId);

        console.log(`Fetching ALL sent requests for senderId (int): ${senderIdInt}`);
        const requests = await FriendRequestService.getAllSentRequests(senderIdInt);
        console.log('Found ALL sent friend requests:', requests);
        console.log('Number of ALL sent requests:', requests.length);

        // Send the requests back to the client
        console.log('Emitting SENT_REQUESTS_LIST with allSentRequests:', requests);
        socket.emit('SENT_REQUESTS_LIST', { sentRequests: requests });

        // Also broadcast to the sender's room to ensure all connected clients get the update
        const senderRoom = senderIdInt.toString();
        console.log(`Broadcasting to room ${senderRoom}`);
        friendRequestNamespace.to(senderRoom).emit('SENT_REQUESTS_LIST', { sentRequests: requests });

        if (callback) callback({ success: true, requests });
      } catch (error) {
        console.error('Error getting ALL sent friend requests:', error);
        if (callback) callback({ success: false, error: error.message });
      }
    });

    // Get sent friend requests for a user (original method, only pending/rejected)
    socket.on('getSentRequests', async ({ userId }, callback) => {
      try {
        console.log(`Getting sent friend requests for senderId: ${userId}, socket.userId: ${socket.userId}`);

        // If userId is not provided, use the authenticated user's ID
        const senderId = userId || socket.userId;

        if (!senderId) {
          throw new Error('No user ID provided for getSentRequests');
        }

        // Convert to integer if it's a string
        const senderIdInt = parseInt(senderId);

        console.log(`Fetching sent requests for senderId (int): ${senderIdInt}`);
        const requests = await FriendRequestService.getSentRequests(senderIdInt);
        console.log('Found sent friend requests:', requests);
        console.log('Number of sent requests:', requests.length);

        // Send the requests back to the client
        console.log('Emitting FRIEND_REQUESTS_LIST with sentRequests:', requests);
        socket.emit('FRIEND_REQUESTS_LIST', { sentRequests: requests });

        // Also broadcast to the sender's room to ensure all connected clients get the update
        const senderRoom = senderIdInt.toString();
        console.log(`Broadcasting to room ${senderRoom}`);
        friendRequestNamespace.to(senderRoom).emit('FRIEND_REQUESTS_LIST', { sentRequests: requests });

        if (callback) callback({ success: true, requests });
      } catch (error) {
        console.error('Error getting sent friend requests:', error);
        if (callback) callback({ success: false, error: error.message });
      }
    });

    socket.on('disconnect', (reason) => {
      console.log(`WebSocket disconnected for friend requests: ${socket.id}, reason: ${reason}`);

      // Log the rooms this socket was in
      const rooms = Array.from(socket.rooms || []);
      console.log(`Socket ${socket.id} was in rooms:`, rooms);

      // If the socket was authenticated, log the user ID
      if (socket.userId) {
        console.log(`Disconnected socket belonged to user ${socket.userId}`);
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error(`WebSocket error for ${socket.id}:`, error);
    });
  });
};

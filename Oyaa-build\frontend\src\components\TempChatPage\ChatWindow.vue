<!-- frontend/src/components/TempChatPage/ChatWindow.vue -->
<template>
  <div class="chat-window">
    <ChatHeader
      :chatFriend="chatFriend"
      @block-user="handleBlockUser"
      @send-friend-request="handleSendFriendRequest"
    />
    <MessagesList :messages="messages" :currentUserId="currentUserId" />
    <InputArea @send-message="sendMessage" />
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import axios from 'axios';
import tempChatSocket from '@/tempChatSocket';
import ChatHeader from './ChatHeader.vue';
import MessagesList from './MessagesList.vue';
import InputArea from './InputArea.vue';

export default {
  name: 'ChatWindow',
  components: { ChatHeader, MessagesList, InputArea },
  data() {
    return {
      messages: [],
    };
  },
  computed: {
    ...mapGetters('auth', { currentUserId: 'userId' }),
    ...mapState('app', { chatFriend: (state) => state.activeChatFriend }),
  },
  async created() {
    const friendId = this.$route.params.friendId;
    if (friendId && !this.chatFriend) {
      await this.loadChatFriend(friendId);
    }
    this.fetchMessages();
  },
  mounted() {
    if (this.currentUserId && this.chatFriend) {
      tempChatSocket.connect();
      tempChatSocket.emit('joinTempChat', {
        userId: this.currentUserId,
        targetUserId: this.chatFriend.id,
      });
      tempChatSocket.on('newTempMessage', this.handleNewMessage);
    }
  },
  beforeUnmount() {
    tempChatSocket.off('newTempMessage', this.handleNewMessage);
    tempChatSocket.disconnect();
  },
  methods: {
    async loadChatFriend(friendId) {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/locals/${this.currentUserId}`,
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        );
        const local = (response.data.locals || []).find((l) => l.local_id === friendId);
        if (local) {
          this.$store.commit('app/SET_ACTIVE_CHAT_FRIEND', {
            id: local.local_id,
            username: local.local_username || 'Unknown',
          });
        }
      } catch (error) {
        console.error('Error loading chat friend:', error);
      }
    },
    async fetchMessages() {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/temp-chat/retrieve`,
          {
            params: { userId: this.currentUserId, targetUserId: this.chatFriend.id },
            headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
          }
        );
        this.messages = response.data || [];
      } catch (error) {
        console.error('Error fetching messages:', error);
      }
    },
    async sendMessage(messageText) {
      const messageData = {
        sender_id: this.currentUserId,
        receiver_id: this.chatFriend.id,
        message: messageText,
      };
      tempChatSocket.emit('sendTempMessage', messageData);
      this.messages.push({ ...messageData, sent_at: new Date().toISOString() });
      this.updateLocalContact();
    },
    handleNewMessage(message) {
      if (message.sender_id !== this.currentUserId) {
        this.messages.push(message);
        this.updateLocalContact();
      }
    },
    async updateLocalContact() {
      try {
        await axios.post(
          `${import.meta.env.VITE_API_BASE_URL}/api/locals`,
          { userId: this.currentUserId, local: this.chatFriend },
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        );
      } catch (error) {
        console.error('Error updating local:', error);
      }
    },
    async handleBlockUser(userId) {
      // Same as original
    },
    async handleSendFriendRequest(userId) {
      // Same as original
    },
  },
};
</script>


<style scoped>
.chat-window {
  display: flex;
  flex-direction: column;
  max-width: 800px;
  height: 100vh;
  margin: 0 auto;
  border: 1px solid #ccc;
  border-radius: 4px;
}
</style>
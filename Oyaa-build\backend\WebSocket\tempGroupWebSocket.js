// backend/WebSocket/tempGroupWebSocket.js
const jwt = require('jsonwebtoken');
const TempGroupModel = require('../models/Groups/tempGroupModel');

module.exports = (tempGroupNamespace) => {
  tempGroupNamespace.on('connection', (socket) => {
    console.log(`User connected to temp group namespace: ${socket.id}`);
    
    const token = socket.handshake.auth.token;
    console.log('Token received:', token ? 'Token present' : 'No token');
    
    if (!token) {
      console.error('No token provided, disconnecting socket');
      socket.emit('error', { message: 'Authentication failed: No token provided' });
      socket.disconnect();
      return;
    }

    try {
      // Verify the JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log('Token verified successfully:', decoded);
      
      const { groupId, participantId, tempUsername } = decoded;
      
      // Make sure we have all required data
      if (!groupId || !participantId || !tempUsername) {
        console.error('Missing required data in token payload:', decoded);
        socket.emit('error', { message: 'Invalid token data' });
        socket.disconnect();
        return;
      }
      
      // Store data on socket object for later use
      socket.groupId = groupId;
      socket.participantId = participantId;
      socket.tempUsername = tempUsername;
      
      // Join the group room
      const roomName = `temp_group_${groupId}`;
      socket.join(roomName);
      console.log(`User ${tempUsername} (ID: ${participantId}) joined room: ${roomName}`);
      
      // Let the user know they're connected successfully
      socket.emit('connected', { 
        message: 'Connected to anonymous group chat',
        username: tempUsername,
        groupId: groupId
      });

      // Send initial messages with error handling
      TempGroupModel.getMessages(groupId, 50, 0)
        .then(messages => {
          if (messages && Array.isArray(messages)) {
            console.log(`Sending ${messages.length} initial messages to user ${tempUsername}`);
            socket.emit('initialMessages', messages.reverse());
          } else {
            console.warn('No messages found or invalid message format:', messages);
            socket.emit('initialMessages', []);
          }
        })
        .catch(err => {
          console.error('Error fetching initial messages:', err);
          socket.emit('error', { message: 'Failed to load messages' });
          socket.emit('initialMessages', []);
        });
    } catch (err) {
      console.error('Invalid token:', err.message);
      socket.emit('error', { message: 'Authentication failed: ' + err.message });
      socket.disconnect();
      return;
    }

    socket.on('sendMessage', async (data) => {
      console.log(`Received sendMessage event from ${socket.tempUsername}:`, data);
      
      // Validation
      if (!data || !data.message || typeof data.message !== 'string') {
        console.error('Invalid message data:', data);
        socket.emit('error', { message: 'Invalid message format' });
        return;
      }
      
      const { message } = data;
      
      if (!socket.groupId || !socket.participantId) {
        console.error('Missing socket data for message sending', { 
          socketId: socket.id,
          groupId: socket.groupId,
          participantId: socket.participantId
        });
        socket.emit('error', { message: 'Session data missing, please rejoin the group' });
        return;
      }
      
      try {
        // Save message to database
        const chatMessage = await TempGroupModel.sendMessage(socket.groupId, socket.participantId, message);
        console.log('Message saved successfully:', chatMessage);
        
        if (!chatMessage) {
          throw new Error('Failed to save message');
        }
        
        // Add username to message object before broadcasting
        const fullMessage = { 
          ...chatMessage, 
          sender_username: socket.tempUsername 
        };
        
        // Broadcast to all users in the room including sender
        const roomName = `temp_group_${socket.groupId}`;
        tempGroupNamespace.to(roomName).emit('newMessage', fullMessage);
        console.log(`Message broadcast to room ${roomName}`);
        
        // Also send a confirmation back to sender
        socket.emit('messageSent', { id: chatMessage.id });
      } catch (err) {
        console.error('Error sending message:', err);
        socket.emit('error', { message: 'Failed to send message: ' + err.message });
      }
    });

    socket.on('disconnect', (reason) => {
      console.log(`Socket ${socket.id} disconnected: ${reason}`);
    });
  });
};
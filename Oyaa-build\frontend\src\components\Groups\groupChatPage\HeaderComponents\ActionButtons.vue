<template>
  <div class="actions">
    <button
      v-if="!isSearchActive"
      class="icon-button"
      @click="startSearch"
      aria-label="Search"
    >
      <i class="fas fa-search"></i>
    </button>
    <button
      v-if="isSearchActive"
      class="icon-button close-button"
      @click="cancelSearch"
      aria-label="Cancel search"
    >
      <i class="fas fa-times"></i>
    </button>
    <div class="dropdown" ref="dropdownRef">
      <button class="icon-button" @click="toggleMenu" aria-label="More options">
        <i class="fas fa-ellipsis-v"></i>
      </button>
      <transition name="fade">
        <div class="dropdown-menu" v-if="isMenuOpen">
          <button class="dropdown-item" @click="showGroupInfo">
            <i class="fas fa-info-circle fa-fw"></i> Group info
          </button>
          <button class="dropdown-item" @click="showReportModal">
            <i class="fas fa-flag fa-fw"></i> Report group
          </button>
          <div class="dropdown-divider"></div>
          <button class="dropdown-item exit" @click="handleLeaveGroup">
            <i class="fas fa-sign-out-alt fa-fw"></i> Exit group
          </button>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  isSearchActive: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['start-search', 'cancel-search', 'leave-group', 'show-group-info', 'show-report-modal']);

const isMenuOpen = ref(false);
const dropdownRef = ref(null);

const startSearch = () => {
  emit('start-search');
};

const cancelSearch = () => {
  emit('cancel-search');
};

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;
};

const showGroupInfo = () => {
  emit('show-group-info');
  isMenuOpen.value = false;
};

const showReportModal = () => {
  emit('show-report-modal');
  isMenuOpen.value = false;
};

const handleLeaveGroup = () => {
  emit('leave-group');
  isMenuOpen.value = false;
};

const handleClickOutside = (event) => {
  if (isMenuOpen.value && dropdownRef.value && !dropdownRef.value.contains(event.target)) {
    isMenuOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.icon-button {
  background: none;
  border: none;
  color: #a0a0a0;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.icon-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.icon-button:active {
  transform: scale(0.95);
}

.icon-button i {
  font-size: 18px;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: rgba(45, 47, 56, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 8px;
  width: 200px;
  z-index: 100;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
  display: flex;
  align-items: center;
  width: 100%;
  text-align: left;
  padding: 12px 16px;
  background: none;
  border: none;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.15s;
}

.dropdown-item i {
  margin-right: 12px;
  font-size: 16px;
  color: #a0a0a0;
}

.dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-item.exit {
  color: #e74c3c;
}

.dropdown-item.exit i {
  color: #e74c3c;
}

.dropdown-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 4px 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s, transform 0.2s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .actions {
    gap: 8px;
  }
  
  .icon-button {
    width: 36px;
    height: 36px;
  }
}
</style>
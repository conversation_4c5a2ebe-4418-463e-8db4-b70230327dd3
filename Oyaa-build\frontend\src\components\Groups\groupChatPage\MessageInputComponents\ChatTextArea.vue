<template>
  <div class="chat-textarea-container" :class="{ 'disabled': disabled }">
    <div
      ref="editableDiv"
      class="chat-textarea"
      contenteditable="true"
      :placeholder="placeholder"
      @input="handleInput"
      @keydown="handleKeyDown"
      @paste="handlePaste"
      @focus="$emit('focus')"
      @blur="$emit('blur')"
      :aria-label="placeholder"
      :aria-disabled="disabled"
      :tabindex="disabled ? -1 : 0"
      role="textbox"
      aria-multiline="true"
    ></div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Type a message...'
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'input', 'send', 'paste-image', 'focus', 'blur']);

const editableDiv = ref(null);
const isComposing = ref(false);

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (editableDiv.value && editableDiv.value.textContent !== newValue) {
    editableDiv.value.textContent = newValue;
  }
}, { immediate: true });

// Handle input events
const handleInput = (event) => {
  if (props.disabled) return;
  
  const text = event.target.textContent;
  emit('update:modelValue', text);
  emit('input', text);
};

// Handle keydown events
const handleKeyDown = (event) => {
  if (props.disabled) return;
  
  // Track IME composition for languages like Chinese, Japanese, Korean
  if (event.isComposing) {
    isComposing.value = true;
    return;
  } else if (isComposing.value) {
    isComposing.value = false;
  }
  
  // Send message on Enter without Shift
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    emit('send');
  }
};

// Handle paste events
const handlePaste = (event) => {
  if (props.disabled) return;
  
  // Check if paste contains images
  const items = (event.clipboardData || event.originalEvent.clipboardData).items;
  let hasHandledImage = false;
  
  for (const item of items) {
    if (item.type.indexOf('image') === 0) {
      event.preventDefault();
      const blob = item.getAsFile();
      emit('paste-image', blob);
      hasHandledImage = true;
      break;
    }
  }
  
  // If no image was handled, let the paste event continue normally
  if (!hasHandledImage) {
    // Clean pasted text to remove unwanted formatting
    nextTick(() => {
      const text = editableDiv.value.textContent;
      emit('update:modelValue', text);
    });
  }
};

// Set up the component on mount
onMounted(() => {
  if (props.modelValue && editableDiv.value) {
    editableDiv.value.textContent = props.modelValue;
  }
  
  // Fix for iOS focus issues
  if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
    editableDiv.value.addEventListener('touchstart', () => {
      if (!props.disabled) {
        editableDiv.value.focus();
      }
    });
  }
});
</script>

<style scoped>
.chat-textarea-container {
  width: 100%;
  position: relative;
}

.chat-textarea {
  width: 100%;
  min-height: 24px;
  max-height: 120px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
  outline: none;
  color: #e0e0e0;
  font-size: 15px;
  line-height: 1.4;
  padding: 6px 0;
  transition: all 0.2s ease;
  cursor: text;
  -webkit-user-select: text;
  user-select: text;
}

/* Placeholder styling */
.chat-textarea:empty::before {
  content: attr(placeholder);
  color: #8a9aa4;
  pointer-events: none;
  position: absolute;
}

/* Disabled state */
.chat-textarea-container.disabled .chat-textarea {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: rgba(0, 0, 0, 0.05);
}

/* Scrollbar styling */
.chat-textarea::-webkit-scrollbar {
  width: 4px;
}

.chat-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.chat-textarea::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.chat-textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .chat-textarea {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 4px 0;
  }
}

/* Fix for iOS text selection */
@supports (-webkit-touch-callout: none) {
  .chat-textarea {
    -webkit-user-select: text;
    user-select: text;
  }
}

/* Fix for Android browsers */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .chat-textarea {
    font-size: 16px;
  }
}

/* Ensure proper height calculation */
.chat-textarea br {
  display: block;
  content: '';
}
</style>
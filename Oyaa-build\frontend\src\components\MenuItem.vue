<template>
  <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100">
    <div class="flex items-center gap-3">
      <component :is="iconComponent" class="w-5 h-5 text-gray-600" />
      <span class="text-gray-800 font-medium">{{ title }}</span>
    </div>
    <div>
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { CogIcon, LockIcon, HelpCircleIcon, LogOutIcon } from 'lucide-vue-next'

const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
})

const iconComponent = computed(() => {
  switch (props.icon) {
    case 'cog':
      return CogIcon
    case 'lock':
      return LockIcon
    case 'question-circle':
      return HelpCircleIcon
    case 'logout':
      return LogOutIcon
    default:
      return null
  }
})
</script>

<style scoped>
/* Add any MenuItem-specific styles here */
</style>
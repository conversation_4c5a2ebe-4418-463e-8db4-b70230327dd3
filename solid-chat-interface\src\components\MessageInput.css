.message-input-container {
  padding: 16px;
  background: #fff;
  border-top: 1px solid #e9ecef;
}

.file-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 20px;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-name {
  font-size: 0.9rem;
  font-weight: 500;
}

.file-size {
  font-size: 0.75rem;
  color: #666;
}

.remove-file {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.remove-file:hover {
  background: #e9ecef;
}

.input-row {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.attach-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.attach-button:hover {
  background: #f8f9fa;
}

.text-input-container {
  flex: 1;
  position: relative;
}

.message-textarea {
  width: 100%;
  border: 1px solid #dee2e6;
  border-radius: 20px;
  padding: 12px 16px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  outline: none;
  transition: border-color 0.2s;
  min-height: 44px;
  max-height: 120px;
}

.message-textarea:focus {
  border-color: #007bff;
}

.message-textarea::placeholder {
  color: #999;
}

.send-button {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  flex-shrink: 0;
}

.send-button:hover:not(.disabled) {
  background: #0056b3;
  transform: scale(1.05);
}

.send-button.disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .message-input-container {
    padding: 12px;
  }

  .input-row {
    gap: 6px;
  }

  .attach-button {
    padding: 6px;
  }

  .send-button {
    width: 40px;
    height: 40px;
  }
}

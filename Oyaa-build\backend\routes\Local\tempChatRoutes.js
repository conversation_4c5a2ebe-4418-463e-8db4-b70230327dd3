// backend/routes/Local/tempChatRoutes.js
const express = require('express');
const router = express.Router();
const tempChatController = require('../../controllers/Local/tempChatController');

// Existing routes
router.post('/send', tempChatController.send);
router.get('/retrieve', tempChatController.retrieve);

// New route for initiating a chat
router.post('/initiate', tempChatController.initiateChat);

module.exports = router;
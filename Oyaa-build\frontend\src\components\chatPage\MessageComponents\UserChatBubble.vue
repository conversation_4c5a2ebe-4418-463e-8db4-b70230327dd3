<template>
  <div :id="'message-' + message.id" class="message-bubble sent">
    <div
      v-if="message.reply"
      class="reply-preview-bubble"
      @click="scrollToRepliedMessage"
      title="Scroll to replied message"
    >
      <span class="reply-label">Replied {{ message.reply.sender_name }}</span>
      <div class="reply-text">{{ replyPreview }}</div>
      <div v-if="message.reply.media">
        <img
          v-if="message.reply.media.mediaType.startsWith('image')"
          :src="message.reply.media.mediaUrl"
          alt="Replied media"
          class="reply-media-preview"
        />
        <ChatVideoPlayer
          v-else-if="message.reply.media.mediaType.startsWith('video')"
          :media-url="message.reply.media.mediaUrl"
          :media-type="message.reply.media.mediaType"
          :video-id="'reply-' + message.id"
        />
        <audio
          v-else-if="message.reply.media.mediaType.startsWith('audio')"
          :src="message.reply.media.mediaUrl"
          controls
        ></audio>
      </div>
    </div>
    <MediaPreview
      v-if="message.media && !message.media.mediaType.startsWith('audio')"
      :media="message.media"
      :message-id="message.id"
    />
    <div
      v-if="message.media && message.media.mediaType.startsWith('audio')"
      class="voice-note-player"
    >
      <HowlerAudioPlayer :mediaUrl="message.media.mediaUrl" />
    </div>
    <div v-if="message.message" class="message-text">
      {{ displayMessage }}
      <span
        v-if="message.message.length > 400"
        class="see-more"
        @click="isExpanded = !isExpanded"
      >
        {{ isExpanded ? 'See less' : 'See more' }}
      </span>
    </div>
    <MessageActions @reply="handleReply" @react="handleReact" />
    <span class="bubble-timestamp">{{ formattedTime }}</span>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { format } from 'date-fns';
import MessageActions from './MessageActions.vue';
import MediaPreview from './MediaPreview.vue';
import ChatVideoPlayer from './ChatVideoPlayer.vue';
import HowlerAudioPlayer from './HowlerAudioPlayer.vue'; // Updated import
const props = defineProps({
  message: { type: Object, required: true },
});
const emit = defineEmits(['reply', 'react']);

const isExpanded = ref(false);

const displayMessage = computed(() => {
  const fullText = props.message.message || '';
  return fullText.length > 400 && !isExpanded.value
    ? fullText.slice(0, 400) + '...'
    : fullText;
});

const replyPreview = computed(() => {
  if (!props.message.reply || !props.message.reply.message) return '';
  const text = props.message.reply.message;
  return text.length > 100 ? text.slice(0, 100) + '...' : text;
});

const formattedTime = computed(() => {
  return format(new Date(props.message.rawSentAt), 'h:mm a');
});

const handleReply = () => emit('reply', props.message);
const handleReact = () => emit('react', props.message);

const scrollToRepliedMessage = () => {
  if (props.message.reply && props.message.reply.id) {
    const target = document.getElementById('message-' + props.message.reply.id);
    if (target) target.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};
</script>

<style scoped>
.message-bubble {
  max-width: 80vw;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  margin-bottom: 0.25rem;
  margin-right: 3vw;
  position: relative;
  transition: background-color 0.2s ease;
}
.sent {
  align-self: flex-end;
  background-color: #4f70a3;
  color: white;
}
.message-text {
  overflow-wrap: break-word;
  white-space: pre-wrap;
}
.see-more {
  color: #25d366;
  cursor: pointer;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}
.bubble-timestamp {
  font-size: 0.7rem;
  color: #99a4b8;
  margin-top: 0.25rem;
  display: inline-block;
}
.reply-preview-bubble {
  background-color: rgba(0, 0, 0, 0.1);
  border-left: 3px solid #25d366;
  padding-left: 0.5rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
}
.reply-label {
  font-size: 0.75rem;
  color: #555;
}
.reply-text {
  font-size: 0.85rem;
  color: #333;
}
.reply-media-preview {
  max-width: 50px;
  max-height: 50px;
  border-radius: 4px;
  margin-top: 0.25rem;
}
.voice-note-player {
  margin-top: 0.5rem;
  display: flex;
  justify-content: flex-start; /* Align left for received, right for sent if needed */
}
</style>
// frontend/src/composables/useChatActions/messageHandlers.js
import { formatTimestamp, formatReply } from '../messageFormatter';

let typingTimeout = null;

export function useMessageHandlers(state, store) {
  const handleNewMessage = (message) => {
    console.log('New message received in handler:', message);
    
    // Check if this message is relevant to the current chat
    if (
      (message.sender_id !== state.currentUser.value.id && message.sender_id !== state.friendId.value) &&
      (message.receiver_id !== state.currentUser.value.id && message.receiver_id !== state.friendId.value)
    ) {
      console.log('Message not relevant to current chat, skipping');
      return;
    }

    if (!message.id) message.id = Date.now();
    message.rawSentAt = message.sent_at || new Date().toISOString();
    message.sent_at = formatTimestamp(message.rawSentAt);

    if (message.reply_id) {
      message.reply = formatReply(message);
    }

    // Normalize media object if needed.
    if (message.media && message.media.media_url && !message.media.mediaUrl) {
      message.media = {
        mediaUrl: message.media.media_url,
        mediaType: message.media.media_type,
        publicId: message.media.public_id,
      };
    }

    // Check for duplicates
    if (state.messages.value.some((m) => m.id === message.id)) {
      console.log('Duplicate message detected, skipping:', message.id);
      return;
    }
    
    console.log('Adding message to state:', message);
    state.messages.value = [...state.messages.value, message];

    const displayMessage =
      message.message ||
      (message.media && message.media.mediaUrl ? '[Media]' : '');
    
    // Only update last chat if it's from the current friend
    if (message.sender_id === state.friendId.value || message.receiver_id === state.friendId.value) {
      console.log('Updating last chat for friend:', state.friendId.value);
      store.dispatch('chat/updateLastChat', {
        friendId: state.friendId.value,
        message: displayMessage,
        timestamp: message.sent_at,
      });
    }
  };

  const handleTypingEvent = () => {
    state.isFriendTyping.value = true;
    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(() => {
      state.isFriendTyping.value = false;
    }, 1500);
  };

  const handleStopTypingEvent = () => {
    state.isFriendTyping.value = false;
  };

  return { handleNewMessage, handleTypingEvent, handleStopTypingEvent };
}

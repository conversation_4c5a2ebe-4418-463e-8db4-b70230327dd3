import type { Message, VirtualScrollItem, ScrollMetrics } from '../types/chat';

export class VirtualScrollManager {
	private items: Message[] = [];
	private itemHeights: Map<string, number> = new Map();
	private defaultItemHeight = 80;
	private bufferSize = 5;
	private scrollTop = 0;
	private containerHeight = 0;

	constructor(defaultHeight = 80, buffer = 5) {
		this.defaultItemHeight = defaultHeight;
		this.bufferSize = buffer;
	}

	setItems(items: Message[]) {
		this.items = items;
	}

	setItemHeight(id: string, height: number) {
		this.itemHeights.set(id, height);
	}

	getItemHeight(id: string): number {
		return this.itemHeights.get(id) || this.defaultItemHeight;
	}

	updateScrollPosition(scrollTop: number, containerHeight: number) {
		this.scrollTop = scrollTop;
		this.containerHeight = containerHeight;
	}

	getVisibleRange(): { start: number; end: number; offset: number } {
		if (this.items.length === 0) {
			return { start: 0, end: 0, offset: 0 };
		}

		let currentOffset = 0;
		let start = 0;
		let end = this.items.length;

		// Find start index
		for (let i = 0; i < this.items.length; i++) {
			const height = this.getItemHeight(this.items[i].id);
			if (currentOffset + height > this.scrollTop) {
				start = Math.max(0, i - this.bufferSize);
				break;
			}
			currentOffset += height;
		}

		// Calculate offset for start position
		let startOffset = 0;
		for (let i = 0; i < start; i++) {
			startOffset += this.getItemHeight(this.items[i].id);
		}

		// Find end index
		let visibleHeight = 0;
		for (let i = start; i < this.items.length; i++) {
			const height = this.getItemHeight(this.items[i].id);
			visibleHeight += height;
			if (visibleHeight > this.containerHeight + this.bufferSize * this.defaultItemHeight) {
				end = Math.min(this.items.length, i + this.bufferSize);
				break;
			}
		}

		return { start, end, offset: startOffset };
	}

	getTotalHeight(): number {
		let total = 0;
		for (const item of this.items) {
			total += this.getItemHeight(item.id);
		}
		return total;
	}

	getVisibleItems(): VirtualScrollItem[] {
		const { start, end, offset } = this.getVisibleRange();
		const items: VirtualScrollItem[] = [];
		let currentOffset = offset;

		for (let i = start; i < end; i++) {
			const message = this.items[i];
			const height = this.getItemHeight(message.id);
			
			items.push({
				id: message.id,
				height,
				offset: currentOffset,
				data: message
			});

			currentOffset += height;
		}

		return items;
	}

	scrollToBottom() {
		return this.getTotalHeight();
	}

	scrollToMessage(messageId: string): number {
		let offset = 0;
		for (const item of this.items) {
			if (item.id === messageId) {
				return offset;
			}
			offset += this.getItemHeight(item.id);
		}
		return 0;
	}
}

export function createVirtualScrollManager(defaultHeight = 80, buffer = 5) {
	return new VirtualScrollManager(defaultHeight, buffer);
}

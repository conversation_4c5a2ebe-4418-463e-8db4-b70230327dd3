const groupRequestsService = require('../../services/Groups/groupRequestsService');

class GroupRequestsController {
  async createRequest(req, res) {
    try {
      const { groupId, userId } = req.body;
      const request = await groupRequestsService.createRequest(groupId, userId);
      res.status(200).json({ message: 'Request created successfully', request });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async updateRequestStatus(req, res) {
    try {
      const { groupId, userId, status } = req.body;
      const request = await groupRequestsService.updateRequestStatus(groupId, userId, status);
      res.status(200).json({ message: 'Request status updated', request });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async getRequestsByUser(req, res) {
    try {
      const { userId } = req.params;
      const requests = await groupRequestsService.getRequestsByUser(userId);
      res.status(200).json({ requests });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }

  async getRequestsByGroup(req, res) {
    try {
      const { groupId } = req.params;
      const requests = await groupRequestsService.getRequestsByGroup(groupId);
      res.status(200).json({ requests });
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }
}

module.exports = new GroupRequestsController();

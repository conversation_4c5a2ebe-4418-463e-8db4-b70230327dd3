const db = require('../../utils/db');
const WorldChatModel = require('../../models/Groups/worldChatModel');
// Use the enhanced group chat service instead of the original one
const EnhancedGroupChatService = require('./enhancedGroupChatService');
const Filter = require('bad-words');
const filter = new Filter();

class WorldChatService {
  async getWorldChat() {
    return await WorldChatModel.getWorldChat();
  }

  async sendMessage(senderId, message, sent_at, replyId = null, media = null) {
    const userRes = await db.query('SELECT banned FROM users WHERE id = $1', [senderId]);
    if (userRes.rows[0]?.banned) {
      throw new Error('You are banned and cannot send messages');
    }
    if (message && filter.isProfane(message)) {
      throw new Error('Message contains inappropriate content');
    }

    const worldChat = await this.getWorldChat();
    return await EnhancedGroupChatService.sendMessage(senderId, worldChat.id, message, sent_at, replyId, media);
  }

  async getMessages(page = 1, pageSize = 50) {
    const worldChat = await this.getWorldChat();
    return await EnhancedGroupChatService.getMessages(worldChat.id, page, pageSize);
  }

  async deleteMessage(messageId, adminId) {
    const isAdmin = await db.query('SELECT current_admin FROM users WHERE id = $1', [adminId]);
    if (!isAdmin.rows[0]?.current_admin) {
      throw new Error('Only the current admin can delete messages');
    }

    // Use the enhanced group chat service to delete the message
    return await EnhancedGroupChatService.deleteMessage(messageId, adminId);
  }
}

module.exports = new WorldChatService();
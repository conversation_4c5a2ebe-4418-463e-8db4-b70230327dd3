<template>
    <div class="container">
      <!-- Hero Section -->
      <section class="hero">
        <div class="hero-content">
          <h1>Community Guidelines</h1>
          <p><strong>Oyaa</strong>  thrives when everyone feels safe, respected, and valued. These guidelines help ensure a positive experience for all members.</p>
        </div>
        <div class="hero-background"></div>
      </section>
  
      <!-- Main Content -->
      <main class="main-content">
        <!-- Guidelines Content -->
        <div class="guidelines-content">
          <section id="overview" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Info class="icon purple" />
                  <h2 class="card-title">Overview</h2>
                </div>
                <p class="card-description">The foundation of our values</p>
              </div>
              <div class="card-content">
                <p>
                  Our guidelines are designed to foster a positive, inclusive, and respectful environment for all members. By joining our platform, you agree to follow these guidelines in all interactions.
                </p>
                <div class="card-grid">
                  <div class="mini-card">
                    <div class="mini-card-header">
                      <h3 class="mini-card-title">Be Respectful</h3>
                    </div>
                    <div class="mini-card-content">
                      Treat others with kindness and respect, even in disagreement.
                    </div>
                  </div>
                  <div class="mini-card">
                    <div class="mini-card-header">
                      <h3 class="mini-card-title">Stay Safe</h3>
                    </div>
                    <div class="mini-card-content">
                      Protect your privacy and respect others' personal information.
                    </div>
                  </div>
                  <div class="mini-card">
                    <div class="mini-card-header">
                      <h3 class="mini-card-title">Add Value</h3>
                    </div>
                    <div class="mini-card-content">
                      Contribute meaningfully to discussions and the Oyaa.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
  
          <section id="respect" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Users class="icon blue" />
                  <h2 class="card-title">Respect & Inclusion</h2>
                </div>
                <p class="card-description">Creating a welcoming environment for everyone</p>
              </div>
              <div class="card-content">
                <div class="accordion">
                  <div class="accordion-item">
                    <div class="accordion-header" @click="toggleAccordion('harassment')">
                      <span>Harassment & Bullying</span>
                      <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === 'harassment' }]" />
                    </div>
                    <div class="accordion-content" v-show="openAccordion === 'harassment'">
                      <p>We have zero tolerance for harassment, bullying, or intimidation of any kind. This includes:</p>
                      <ul>
                        <li>Personal attacks or offensive comments</li>
                        <li>Deliberate intimidation or threats</li>
                        <li>Unwanted sexual attention or advances</li>
                        <li>Sustained disruption of discussions</li>
                        <li>Encouraging others to target an individual or group</li>
                      </ul>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <div class="accordion-header" @click="toggleAccordion('hate')">
                      <span>Hate Speech & Discrimination</span>
                      <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === 'hate' }]" />
                    </div>
                    <div class="accordion-content" v-show="openAccordion === 'hate'">
                      <p>Content that promotes hatred, discrimination, or violence against individuals or groups based on attributes such as:</p>
                      <ul>
                        <li>Race, ethnicity, or national origin</li>
                        <li>Religion or religious affiliation</li>
                        <li>Gender, gender identity, or sexual orientation</li>
                        <li>Disability or medical condition</li>
                        <li>Age, veteran status, or any other protected characteristic</li>
                      </ul>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <div class="accordion-header" @click="toggleAccordion('language')">
                      <span>Inclusive Language</span>
                      <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === 'language' }]" />
                    </div>
                    <div class="accordion-content" v-show="openAccordion === 'language'">
                      <p>We encourage the use of inclusive language that:</p>
                      <ul>
                        <li>Respects diverse perspectives and experiences</li>
                        <li>Avoids stereotypes and harmful generalizations</li>
                        <li>Uses preferred pronouns when known</li>
                        <li>Considers the global nature of our Oyaa</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
  
          <section id="content" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <MessageSquare class="icon green" />
                  <h2 class="card-title">Content Standards</h2>
                </div>
                <p class="card-description">Guidelines for sharing and creating content</p>
              </div>
              <div class="card-content">
                <div class="tabs">
                  <div class="tabs-list">
                    <button 
                      class="tab-trigger" 
                      :class="{ 'active': activeTab === 'prohibited' }"
                      @click="activeTab = 'prohibited'"
                    >
                      Prohibited
                    </button>
                    <button 
                      class="tab-trigger" 
                      :class="{ 'active': activeTab === 'sensitive' }"
                      @click="activeTab = 'sensitive'"
                    >
                      Sensitive
                    </button>
                    <button 
                      class="tab-trigger" 
                      :class="{ 'active': activeTab === 'encouraged' }"
                      @click="activeTab = 'encouraged'"
                    >
                      Encouraged
                    </button>
                  </div>
                  <div class="tab-content" v-show="activeTab === 'prohibited'">
                    <div class="alert alert-red">
                      <h3>The following content is strictly prohibited:</h3>
                      <ul>
                        <li>
                          <Ban class="alert-icon" />
                          <span>Illegal content or promotion of illegal activities</span>
                        </li>
                        <li>
                          <Ban class="alert-icon" />
                          <span>Sexually explicit or pornographic material</span>
                        </li>
                        <li>
                          <Ban class="alert-icon" />
                          <span>Graphic violence or glorification of suffering</span>
                        </li>
                        <li>
                          <Ban class="alert-icon" />
                          <span>Content that exploits or harms minors</span>
                        </li>
                        <li>
                          <Ban class="alert-icon" />
                          <span>Spam, scams, or misleading content</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="tab-content" v-show="activeTab === 'sensitive'">
                    <div class="alert alert-yellow">
                      <h3>The following content requires content warnings:</h3>
                      <ul>
                        <li>
                          <AlertTriangle class="alert-icon" />
                          <span>Discussion of traumatic events or experiences</span>
                        </li>
                        <li>
                          <AlertTriangle class="alert-icon" />
                          <span>Content related to self-harm or suicide</span>
                        </li>
                        <li>
                          <AlertTriangle class="alert-icon" />
                          <span>Intense political or religious discussions</span>
                        </li>
                        <li>
                          <AlertTriangle class="alert-icon" />
                          <span>Medical procedures or conditions</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="tab-content" v-show="activeTab === 'encouraged'">
                    <div class="alert alert-green">
                      <h3>We encourage the following types of content:</h3>
                      <ul>
                        <li>
                          <Heart class="alert-icon" />
                          <span>Educational and informative resources</span>
                        </li>
                        <li>
                          <Heart class="alert-icon" />
                          <span>Creative works and original content</span>
                        </li>
                        <li>
                          <Heart class="alert-icon" />
                          <span>Constructive feedback and discussions</span>
                        </li>
                        <li>
                          <Heart class="alert-icon" />
                          <span>Oyaa support and collaboration</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
  
          <section id="safety" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <AlertTriangle class="icon yellow" />
                  <h2 class="card-title">Safety & Privacy</h2>
                </div>
                <p class="card-description">Protecting yourself and others online</p>
              </div>
              <div class="card-content">
                <div class="two-column">
                  <div class="column">
                    <h3>Personal Information</h3>
                    <p>
                      Be cautious about sharing personal information. Never share:
                    </p>
                    <ul class="danger-list">
                      <li>Home addresses or exact locations</li>
                      <li>Phone numbers or email addresses</li>
                      <li>Financial information or passwords</li>
                      <li>Government IDs or sensitive documents</li>
                    </ul>
                  </div>
                  <div class="column">
                    <h3>Account Security</h3>
                    <p>
                      Protect your account with these best practices:
                    </p>
                    <ul class="success-list">
                      <li>Use a strong, unique password</li>
                      <li>Enable two-factor authentication</li>
                      <li>Be wary of suspicious links or messages</li>
                      <li>Log out from shared devices</li>
                    </ul>
                  </div>
                </div>
                <div class="alert alert-blue">
                  <h3>Respecting Others' Privacy</h3>
                  <p>
                    Always get permission before sharing content that features other people. Never share someone else's personal information without their explicit consent, even if it's publicly available elsewhere.
                  </p>
                </div>
              </div>
            </div>
          </section>
  
          <section id="violations" class="section">
            <div class="card">
              <div class="card-header">
                <div class="card-title-wrapper">
                  <Flag class="icon red" />
                  <h2 class="card-title">Violations & Reporting</h2>
                </div>
                <p class="card-description">How we handle guideline violations</p>
              </div>
              <div class="card-content">
                <div class="subsection">
                  <h3>Reporting Process</h3>
                  <p>
                    If you encounter content that violates our guidelines, please report it immediately:
                  </p>
                  <ol>
                    <li>Use the "Report" button on the content</li>
                    <li>Select the appropriate violation category</li>
                    <li>Provide any additional context that might help our review</li>
                    <li>Submit your report</li>
                  </ol>
                  <p>
                    Our moderation team reviews all reports and takes appropriate action, usually within 24-48 hours.
                  </p>
                </div>
                
                <div class="subsection">
                  <h3>Consequences of Violations</h3>
                  <div class="table-container">
                    <table>
                      <thead>
                        <tr>
                          <th>Violation Level</th>
                          <th>Examples</th>
                          <th>Consequences</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>Minor</td>
                          <td>Off-topic posting, minor rudeness</td>
                          <td>Warning, content removal</td>
                        </tr>
                        <tr>
                          <td>Moderate</td>
                          <td>Repeated minor violations, spam</td>
                          <td>Temporary restrictions, muting</td>
                        </tr>
                        <tr>
                          <td>Severe</td>
                          <td>Harassment, hate speech, illegal content</td>
                          <td>Account suspension or permanent ban</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                
              </div>
            </div>
          </section>
        </div>
      </main>
  
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  import { 
    Shield, 
    Users, 
    MessageSquare, 
    AlertTriangle, 
    Heart, 
    Ban, 
    Flag, 
    Info, 
    ChevronDown
  } from 'lucide-vue-next';
  
  const openAccordion = ref(null);
  const activeTab = ref('prohibited');
  
  const toggleAccordion = (id) => {
    if (openAccordion.value === id) {
      openAccordion.value = null;
    } else {
      openAccordion.value = id;
    }
  };
  </script>
  
  <style>
  /* Container with Variables and Base Styles */
  .container {
    /* Colors */
    --color-black: #000000;
    --color-white: #ffffff;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    --color-gray-950: #030712;
    --color-purple-300: #c4b5fd;
    --color-purple-400: #a78bfa;
    --color-purple-500: #8b5cf6;
    --color-purple-600: #7c3aed;
    --color-purple-700: #6d28d9;
    --color-purple-800: #5b21b6;
    --color-purple-900: #4c1d95;
    --color-purple-950: #2e1065;
    --color-blue-500: #3b82f6;
    --color-blue-600: #2563eb;
    --color-blue-700: #1d4ed8;
    --color-blue-800: #1e40af;
    --color-blue-900: #1e3a8a;
    --color-blue-950: #172554;
    --color-green-500: #22c55e;
    --color-green-600: #16a34a;
    --color-green-700: #15803d;
    --color-green-800: #166534;
    --color-green-900: #14532d;
    --color-green-950: #052e16;
    --color-yellow-500: #eab308;
    --color-yellow-600: #ca8a04;
    --color-yellow-700: #a16207;
    --color-yellow-800: #854d0e;
    --color-yellow-900: #713f12;
    --color-yellow-950: #422006;
    --color-red-500: #ef4444;
    --color-red-600: #dc2626;
    --color-red-700: #b91c1c;
    --color-red-800: #991b1b;
    --color-red-900: #7f1d1d;
    --color-red-950: #450a0a;
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Original .container styles */
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;

    /* Moved from body */
    font-family: 'Rubik', sans-serif;
    background-color: var(--color-black);
    color: var(--color-white);
    line-height: 1.5;

    /* Replace * reset for this scope */
    box-sizing: border-box;
  }

  /* Scoped Heading Styles */
  .container h1, .container h2, .container h3, .container h4, .container h5, .container h6 {
    font-weight: 600;
    line-height: 1.2;
  }

  /* Scoped Link Styles */
  .container a {
    color: var(--color-gray-400);
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .container a:hover {
    color: var(--color-white);
  }

  /* Scoped List Styles */
  .container ul, .container ol {
    padding-left: var(--space-5);
  }

  /* Hero Section */
  .hero {
    position: relative;
    padding: var(--space-20) var(--space-4);
    border-bottom: 1px solid var(--color-gray-800);
    background: linear-gradient(to bottom right, var(--color-black), var(--color-gray-900), var(--color-purple-950));
    overflow: hidden;
    box-sizing: border-box; /* Added since it has padding and border */
  }

  .hero-content {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
  }

  .hero h1 {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-4);
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .hero p {
    max-width: 36rem;
    margin: 0 auto;
    color: var(--color-gray-400);
    font-size: var(--text-lg);
  }

  .hero-background {
    position: absolute;
    inset: 0;
    z-index: 0;
    opacity: 0.2;
    background: radial-gradient(circle at center, var(--color-purple-500), transparent);
  }

  /* Main Content */
  .main-content {
    flex: 1;
    padding: var(--space-12) var(--space-4);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box; /* Added since it has padding */
  }

  .guidelines-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-10);
  }

  /* Section */
  .section {
    scroll-margin-top: var(--space-20);
  }

  /* Card */
  .card {
    background-color: var(--color-gray-900);
    border: 1px solid var(--color-gray-800);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-sizing: border-box; /* Added since it has border and padding */
  }

  .card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-gray-800);
    box-sizing: border-box; /* Added since it has padding and border */
  }

  .card-title-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-1);
  }

  .card-title {
    font-size: var(--text-xl);
    font-weight: 600;
  }

  .card-description {
    color: var(--color-gray-400);
    font-size: var(--text-sm);
  }

  .card-content {
    padding: var(--space-6);
    box-sizing: border-box; /* Added since it has padding */
  }

  .card-content > p {
    margin-bottom: var(--space-4);
  }

  /* Mini Cards */
  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--space-4);
    margin-top: var(--space-4);
  }

  .mini-card {
    background-color: var(--color-gray-800);
    border: 1px solid var(--color-gray-700);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-sizing: border-box; /* Added since it has border and padding */
  }

  .mini-card-header {
    padding: var(--space-2) var(--space-4);
    box-sizing: border-box; /* Added since it has padding */
  }

  .mini-card-title {
    font-size: var(--text-sm);
    font-weight: 500;
  }

  .mini-card-content {
    padding: var(--space-2) var(--space-4) var(--space-4);
    color: var(--color-gray-400);
    font-size: var(--text-xs);
    box-sizing: border-box; /* Added since it has padding */
  }

  /* Accordion */
  .accordion {
    border: 1px solid var(--color-gray-800);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-sizing: border-box; /* Added since it has border and padding */
  }

  .accordion-item {
    border-bottom: 1px solid var(--color-gray-800);
    box-sizing: border-box; /* Added since it has border */
  }

  .accordion-item:last-child {
    border-bottom: none;
  }

  .accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    cursor: pointer;
    font-weight: 500;
    transition: color 0.2s ease;
    box-sizing: border-box; /* Added since it has padding */
  }

  .accordion-header:hover {
    color: var(--color-white);
  }

  .accordion-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.2s ease;
  }

  .accordion-icon.rotated {
    transform: rotate(180deg);
  }

  .accordion-content {
    padding: var(--space-4) var(--space-6);
    color: var(--color-gray-400);
    box-sizing: border-box; /* Added since it has padding */
  }

  .accordion-content p {
    margin-bottom: var(--space-2);
  }

  .accordion-content ul {
    margin-top: var(--space-2);
  }

  .accordion-content li {
    margin-bottom: var(--space-1);
  }

  /* Tabs */
  .tabs {
    width: 100%;
  }

  .tabs-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    background-color: var(--color-gray-800);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-4);
  }

  .tab-trigger {
    padding: var(--space-3) var(--space-4);
    background: none;
    border: none;
    color: var(--color-gray-400);
    cursor: pointer;
    font-size: var(--text-sm);
    font-weight: 500;
    transition: color 0.2s ease, background-color 0.2s ease;
    box-sizing: border-box; /* Added since it has padding */
  }

  .tab-trigger.active {
    background-color: var(--color-gray-700);
    color: var(--color-white);
  }

  .tab-content {
    padding-top: var(--space-4);
    box-sizing: border-box; /* Added since it has padding */
  }

  /* Alerts */
  .alert {
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
    box-sizing: border-box; /* Added since it has padding and border */
  }

  .alert h3 {
    margin-bottom: var(--space-2);
    font-weight: 500;
    font-size: var(--text-base);
  }

  .alert ul {
    list-style: none;
    padding-left: 0;
  }

  .alert li {
    display: flex;
    align-items: flex-start;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
  }

  .alert-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    margin-top: 4px;
  }

  .alert-red {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--color-red-900);
  }

  .alert-red h3 {
    color: var(--color-red-400);
  }

  .alert-yellow {
    background-color: rgba(234, 179, 8, 0.1);
    border: 1px solid var(--color-yellow-900);
  }

  .alert-yellow h3 {
    color: var(--color-yellow-400);
  }

  .alert-green {
    background-color: rgba(34, 197, 94, 0.1);
    border: 1px solid var(--color-green-900);
  }

  .alert-green h3 {
    color: var(--color-green-400);
  }

  .alert-blue {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid var(--color-blue-900);
    margin-top: var(--space-4);
  }

  .alert-blue h3 {
    color: var(--color-blue-400);
  }

  .alert-purple {
    background-color: rgba(139, 92, 246, 0.1);
    border: 1px solid var(--color-purple-900);
  }

  .alert-purple h3 {
    color: var(--color-purple-400);
  }

  /* Two Column Layout */
  .two-column {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  @media (min-width: 768px) {
    .two-column {
      grid-template-columns: 1fr 1fr;
    }
  }

  .column h3 {
    margin-bottom: var(--space-3);
    font-size: var(--text-lg);
  }

  .column p {
    color: var(--color-gray-400);
    margin-bottom: var(--space-3);
  }

  /* Lists */
  .danger-list li {
    position: relative;
    padding-left: var(--space-4);
    margin-bottom: var(--space-1);
    color: var(--color-gray-400);
    box-sizing: border-box; /* Added since it has padding */
  }

  .danger-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--color-red-500);
  }

  .success-list li {
    position: relative;
    padding-left: var(--space-4);
    margin-bottom: var(--space-1);
    color: var(--color-gray-400);
    box-sizing: border-box; /* Added since it has padding */
  }

  .success-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--color-green-500);
  }

  /* Subsection */
  .subsection {
    margin-bottom: var(--space-6);
  }

  .subsection h3 {
    font-size: var(--text-lg);
    margin-bottom: var(--space-3);
  }

  .subsection p {
    color: var(--color-gray-400);
    margin-bottom: var(--space-3);
  }

  .subsection ol {
    margin-bottom: var(--space-3);
    color: var(--color-gray-400);
  }

  /* Table */
  .table-container {
    overflow-x: auto;
  }

  .container table {
    width: 100%;
    border-collapse: collapse;
  }

  .container th {
    padding: var(--space-2) var(--space-4);
    text-align: left;
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--color-gray-400);
    border-bottom: 1px solid var(--color-gray-800);
    box-sizing: border-box; /* Added since it has padding and border */
  }

  .container td {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
    color: var(--color-gray-400);
    border-bottom: 1px solid var(--color-gray-800);
    box-sizing: border-box; /* Added since it has padding and border */
  }

  /* Button */
  .button-outline {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-2) var(--space-4);
    background: transparent;
    color: var(--color-purple-400);
    border: 1px solid var(--color-purple-700);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
    box-sizing: border-box; /* Added since it has padding and border */
  }

  .button-outline:hover {
    background-color: rgba(109, 40, 217, 0.2);
    color: var(--color-purple-300);
  }

  /* Icons */
  .icon {
    width: 20px;
    height: 20px;
  }

  .icon.purple {
    color: var(--color-purple-500);
  }

  .icon.blue {
    color: var(--color-blue-500);
  }

  .icon.green {
    color: var(--color-green-500);
  }

  .icon.yellow {
    color: var(--color-yellow-500);
  }

  .icon.red {
    color: var(--color-red-500);
  }

  /* Responsive Adjustments */
  @media (min-width: 768px) {
    .hero h1 {
      font-size: var(--text-5xl);
    }

    .main-content {
      padding: var(--space-12) var(--space-8);
    }
  }

  @media (min-width: 1024px) {
    .hero h1 {
      font-size: var(--text-6xl);
    }
  }
</style>
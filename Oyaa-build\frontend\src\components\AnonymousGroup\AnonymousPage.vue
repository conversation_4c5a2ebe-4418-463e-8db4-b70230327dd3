<template>
  <div class="anonymous-page">
    <!-- Added header with back button -->
    <div class="page-header">
      <button class="back-button" @click="$router.push('/dashboard')">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
      </button>
      <h1>Anonymous Groups</h1>
      <div class="header-spacer"></div>
    </div>
    
    <div class="page-content">
      <div class="page-subheader">
        <h2>Anonymous Groups</h2>
        <p class="subtitle">Create or join temporary anonymous groups for private discussions</p>
      </div>
      
      <div class="create-group-card">
        <div class="card-header">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="card-icon">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          <h3>Create New Anonymous Group</h3>
        </div>
        
        <form @submit.prevent="createNewGroup" class="create-group-form">
          <div class="form-group">
            <label for="groupName">Group Name</label>
            <div class="input-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="input-icon">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              <input v-model="groupName" id="groupName" required placeholder="Enter group name" />
            </div>
          </div>
          
          <div class="form-group">
            <label for="category">Category</label>
            <div class="input-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="input-icon">
                <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                <polyline points="2 17 12 22 22 17"></polyline>
                <polyline points="2 12 12 17 22 12"></polyline>
              </svg>
              <input v-model="category" id="category" required placeholder="e.g., Gaming, Study, Support" />
            </div>
          </div>
          
          <div class="form-group">
            <label for="description">Description (optional)</label>
            <textarea 
              v-model="description" 
              id="description" 
              placeholder="Provide a brief description of your group"
            ></textarea>
          </div>
          
          <button type="submit" class="create-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="16"></line>
              <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
            Create Anonymous Group
          </button>
        </form>
      </div>
      
      <div class="groups-section">
        <div class="section-header">
          <h3>Your Anonymous Groups</h3>
          <button @click="fetchGroups" class="refresh-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
              <path d="M3 3v5h5"></path>
            </svg>
            Refresh
          </button>
        </div>
        
        <div v-if="groups.length === 0" class="no-groups">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          <p>You haven't created any anonymous groups yet.</p>
          <p class="hint">Create a group above to get started!</p>
        </div>
        
        <ul v-else class="group-list">
          <li v-for="group in groups" :key="group.id" class="group-item">
            <div class="group-header">
              <h4>{{ group.group_name }}</h4>
              <span class="category-badge">{{ group.category }}</span>
            </div>
            
            <p class="group-description">{{ group.description || 'No description provided' }}</p>
            
            <div class="group-meta">
              <div class="meta-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                <span>Created: {{ formatDate(group.created_at) }}</span>
              </div>
              <div class="meta-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                <span>Expires: {{ formatDate(group.expires_at) }}</span>
              </div>
            </div>
            
            <div class="group-actions">
              <div class="share-link">
                <span>Share Link:</span>
                <div class="link-container">
                  <input type="text" readonly :value="getShareLink(group.link_token)" class="link-input" />
                  <button @click="copyLink(group.link_token)" class="copy-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                  </button>
                </div>
              </div>
              
              <button @click="joinGroup(group.link_token)" class="join-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                  <polyline points="10 17 15 12 10 7"></polyline>
                  <line x1="15" y1="12" x2="3" y2="12"></line>
                </svg>
                Join Chat
              </button>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import axios from '@/api/axios';
import { format } from 'date-fns';

export default {
  data() {
    return {
      groups: [],
      groupName: '',
      category: '',
      description: '',
      loading: false,
    };
  },
  methods: {
    async fetchGroups() {
      this.loading = true;
      try {
        const response = await axios.get('/api/temp-groups/created');
        this.groups = response.data.groups || [];
      } catch (err) {
        console.error('Failed to fetch anonymous groups:', err);
      } finally {
        this.loading = false;
      }
    },
    async createNewGroup() {
      try {
        const response = await axios.post('/api/temp-groups/create', {
          groupName: this.groupName,
          category: this.category,
          description: this.description,
          expiresInHours: 24,
        });
        const { linkToken, groupName } = response.data;
        
        // Show success message
        this.$store.dispatch('app/showToast', {
          message: `Group '${groupName}' created successfully!`,
          type: 'success'
        });
        
        // Reset form
        this.groupName = '';
        this.category = '';
        this.description = '';
        
        // Refresh groups list
        await this.fetchGroups();
      } catch (err) {
        console.error('Failed to create group:', err);
        
        // Show error message
        this.$store.dispatch('app/showToast', {
          message: 'Failed to create group: ' + (err.response?.data?.message || 'Unknown error'),
          type: 'error'
        });
      }
    },
    joinGroup(linkToken) {
      this.$router.push(`/anonymous/join/${linkToken}`);
    },
    getShareLink(linkToken) {
      return `${window.location.origin}/anonymous/join/${linkToken}`;
    },
    formatDate(dateString) {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    },
    copyLink(linkToken) {
      const link = this.getShareLink(linkToken);
      navigator.clipboard.writeText(link).then(() => {
        this.$store.dispatch('app/showToast', {
          message: 'Link copied to clipboard!',
          type: 'success'
        });
      }).catch(err => {
        console.error('Failed to copy link:', err);
      });
    }
  },
  mounted() {
    this.fetchGroups();
  },
};
</script>
  
<style scoped>
.anonymous-page {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --success-color: #34d399;
  
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

/* New header styles */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.page-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.back-button:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.header-spacer {
  width: 36px; /* Same width as back button for balance */
}

.page-content {
  padding: 1.5rem;
}

/* Adjust existing styles */
.page-subheader {
  margin-bottom: 1.5rem;
  text-align: center;
}

h2 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  color: var(--text-secondary);
  font-size: 0.9375rem;
}

.create-group-card {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  margin-bottom: 2rem;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-tertiary);
}

.card-icon {
  color: var(--accent-primary);
}

h3, h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.create-group-form {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

input, textarea {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.9375rem;
  transition: all var(--transition-fast);
}

textarea {
  min-height: 100px;
  resize: vertical;
  padding: 0.75rem;
}

input:focus, textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}

input::placeholder, textarea::placeholder {
  color: var(--text-secondary);
}

.create-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: var(--accent-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.create-btn:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.create-btn:active {
  transform: translateY(0);
}

.groups-section {
  margin-top: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.refresh-btn:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.no-groups {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 3rem 1rem;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.no-groups svg {
  color: var(--accent-tertiary);
  margin-bottom: 1rem;
}

.no-groups p {
  margin: 0.25rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.no-groups .hint {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.group-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.group-item {
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.group-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-tertiary);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.category-badge {
  padding: 0.25rem 0.5rem;
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.group-description {
  color: var(--text-secondary);
  font-size: 0.9375rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.group-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: var(--text-secondary);
  font-size: 0.8125rem;
}

.group-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.share-link {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.share-link span {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.link-container {
  display: flex;
  gap: 0.5rem;
}

.link-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: text;
}

.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.copy-btn:hover {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.join-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: var(--accent-primary);
  color: white;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.join-btn:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.join-btn:active {
  transform: translateY(0);
}

@media (max-width: 640px) {
  .page-content {
    padding: 1rem;
  }
  
  .group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .group-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
/**
 * Message Cache Service
 *
 * This service has been disabled to use backend caching (AlloyDB Omni and Valkey) instead.
 * All methods are now stubs that return empty results or resolve immediately.
 */

class MessageCacheService {
  constructor() {
    console.log('MessageCacheService: Local caching disabled, using backend caching instead');
  }

  /**
   * Initialize the service (stub)
   */
  async init() {
    return Promise.resolve();
  }

  /**
   * Ensure the service is initialized (stub)
   */
  async ensureInitialized() {
    return Promise.resolve(true);
  }

  /**
   * Create a clean, serializable copy of a message object (stub)
   */
  createSerializableMessage(message) {
    return message;
  }

  /**
   * Cache messages for a group (stub)
   */
  async cacheMessages(groupId, messages) {
    console.log(`MessageCacheService: Skipping local cache for ${messages?.length || 0} messages in group ${groupId}`);
    return Promise.resolve();
  }

  /**
   * Get cached messages for a group (stub)
   */
  async getMessages(groupId, limit = 100, beforeMessageId = null) {
    console.log(`MessageCacheService: Local cache disabled, returning empty array`);
    return [];
  }

  /**
   * Get the latest messages for a group (stub)
   */
  async getLatestMessages(groupId, limit = 100) {
    console.log(`MessageCacheService: Local cache disabled, returning empty array`);
    return [];
  }

  /**
   * Get a message by its ID (stub)
   */
  async getMessageById(messageId) {
    console.log(`MessageCacheService: Local cache disabled, returning null`);
    return null;
  }

  /**
   * Clear all cached messages for a group (stub)
   */
  async clearGroupMessages(groupId) {
    console.log(`MessageCacheService: Local cache disabled, no action needed`);
    return Promise.resolve();
  }

  /**
   * Clear all cached data (stub)
   */
  async clearAll() {
    console.log('MessageCacheService: Local cache disabled, no action needed');
    return Promise.resolve();
  }
}

// Create and export a singleton instance
const messageCacheService = new MessageCacheService();
export default messageCacheService;

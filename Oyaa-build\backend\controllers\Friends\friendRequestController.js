// backend/controllers/friendRequestController.js
const FriendRequestService = require('../../services/Friends/friendRequestService');
const userService = require('../../services/User/userService'); // Import userService to get sender details
const { sendSSEUpdate } = require('../../utils/sse');
const notificationService = require('../../services/User/notificationService'); // Import the notification service

class FriendRequestController {
  async createRequest(req, res) {
    try {
   
      const senderId = req.user.userId; // Get from authenticated user
    

      // Check if receiverId exists in the request body
      if (!req.body.receiverId) {
        console.error('Missing receiverId in request body');
        return res.status(400).json({ message: 'receiverId is required' });
      }

      const { receiverId } = req.body; // Get receiverId from request body
     

      if (senderId === receiverId) {
       
        return res.status(400).json({ message: 'You cannot send a friend request to yourself.' });
      }

      // Validate receiverId is a number
      if (isNaN(parseInt(receiverId))) {
        console.error('Invalid receiverId format:', receiverId);
        return res.status(400).json({ message: 'receiverId must be a valid number' });
      }

      const request = await FriendRequestService.createRequest(senderId, parseInt(receiverId));
      const sender = await userService.getUserById(senderId);

      // Send SSE update to receiver to refresh their friend requests list
      sendSSEUpdate(receiverId, {
        type: 'friendRequestUpdate',
        action: 'new',
        requestId: request.id
      });

      // Send notification to receiver
      notificationService.notifyUser(receiverId, 'friendRequest', {
        id: request.id,
        message: 'You have a new friend request',
        sender: { id: sender.id, name: sender.username || sender.name || 'Unknown' }
      });
      res.status(200).json({ message: 'Friend request sent', request });
    } catch (err) {
      console.error('Error in createRequest:', err);
      console.error('Error stack:', err.stack);

      // Handle known error cases with appropriate status codes
      if (err.message === 'You are already friends with this user.' ||
          err.message === 'Friend request already sent and pending.' ||
          err.message === 'receiverId is required' ||
          err.message === 'receiverId must be a valid number') {
       
        return res.status(400).json({ message: err.message });
      }

      // Handle database unique constraint violation
      if (err.code === '23505' && err.constraint === 'friend_requests_sender_id_receiver_id_key') {
        console.error('Duplicate friend request detected');
        const errorMessage = 'Friend request already sent and pending.';
       
        return res.status(400).json({ message: errorMessage });
      }

      // Check for other specific database errors
      if (err.code) {
        console.error('Database error code:', err.code);
        console.error('Database error details:', err.detail);
      }

      // Generic server error for all other cases
      const genericErrorMessage = 'Failed to send friend request. Please try again later.';
     
      res.status(500).json({ message: genericErrorMessage });
    }
  }


  async getRequests(req, res) {
    try {
      const { receiverId } = req.params;
     

      // Validate receiverId
      if (!receiverId || isNaN(parseInt(receiverId))) {
        console.error('Invalid receiverId:', receiverId);
        return res.status(400).json({ message: 'Invalid user ID' });
      }

      const requests = await FriendRequestService.getRequests(receiverId);
     
      res.status(200).json({
        requests,
        lastUpdated: Date.now(), // Add timestamp
      });
    } catch (err) {
      console.error('Error in getRequests:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async acceptRequest(req, res) {
    try {
      const { requestId } = req.params;

      // Get the request details before accepting to know sender and receiver
      const request = await FriendRequestService.getRequestById(requestId);
      if (!request) {
        return res.status(404).json({ message: 'Friend request not found' });
      }

      // Accept the request
      const result = await FriendRequestService.acceptRequest(requestId);

      // Send SSE update to both sender and receiver
      // Update sender
      sendSSEUpdate(request.sender_id, {
        type: 'friendRequestUpdate',
        action: 'accepted',
        requestId: request.id
      });

      // Update receiver
      sendSSEUpdate(request.receiver_id, {
        type: 'friendRequestUpdate',
        action: 'accepted',
        requestId: request.id
      });

      // Send notification to sender that their request was accepted
      notificationService.notifyUser(request.sender_id, 'friendRequestAccepted', {
        id: request.id,
        message: 'Your friend request was accepted',
        receiver: { id: request.receiver_id }
      });

      res.status(200).json({ message: 'Friend request accepted', result });
    } catch (err) {
      console.error('Error in acceptRequest:', err);
      res.status(500).json({ message: err.message });
    }
  }

  async rejectRequest(req, res) {
    try {
      const { requestId } = req.params;

      // Check if request exists before rejecting
      const request = await FriendRequestService.getRequestById(requestId);
      if (!request) {
        return res.status(404).json({ message: 'Friend request not found' });
      }

      // Reject the request
      const result = await FriendRequestService.rejectRequest(requestId);

      // Send SSE update to both sender and receiver
      // Update sender
      sendSSEUpdate(request.sender_id, {
        type: 'friendRequestUpdate',
        action: 'rejected',
        requestId: request.id
      });

      // Update receiver
      sendSSEUpdate(request.receiver_id, {
        type: 'friendRequestUpdate',
        action: 'rejected',
        requestId: request.id
      });

      // Optionally send a notification to the sender that their request was rejected
      // Commented out as this might not be desired behavior
      // notificationService.notifyUser(request.sender_id, 'friendRequestRejected', {
      //   id: request.id,
      //   message: 'Your friend request was declined',
      //   receiver: { id: request.receiver_id }
      // });

      res.status(200).json({ message: 'Friend request rejected', result });
    } catch (err) {
      console.error('Error in rejectRequest:', err);
      res.status(500).json({ message: 'Internal Server Error', error: err.message });
    }
  }

  // Updated getSentRequests method
  async getSentRequests(req, res) {
    try {
      const { senderId } = req.params;

      // Validate senderId
      if (!senderId || isNaN(parseInt(senderId))) {
        console.error('Invalid senderId:', senderId);
        return res.status(400).json({ message: 'Invalid user ID' });
      }

      

      // Read the optional "seen" query parameter (a comma-separated string)
      let seenRejections = req.query.seen || '';
      // Convert it to an array of numbers (filter out any invalid values)
      seenRejections = seenRejections
        .split(',')
        .map(id => parseInt(id, 10))
        .filter(id => !isNaN(id));

      // Get all sent requests
      const requests = await FriendRequestService.getSentRequests(parseInt(senderId), seenRejections);
      
      res.status(200).json({
        requests,
        lastUpdated: Date.now()
      });
    } catch (err) {
      console.error('Error in getSentRequests:', err);
      res.status(500).json({ message: err.message });
    }
  }
  async getMyRequests(req, res) {
    try {
      const receiverId = req.user.userId; // Get authenticated user's ID from authMiddleware
      const { since } = req.query; // Get optional 'since' query parameter
      const sinceTimestamp = since ? parseInt(since, 10) : 0; // Default to 0 if not provided
     
      const requests = await FriendRequestService.getRequestsSince(receiverId, sinceTimestamp);
      res.status(200).json({
        requests,
        lastUpdated: Date.now(),
      });
    } catch (err) {
      console.error('Error in getMyRequests:', err);
      res.status(500).json({ message: err.message });
    }
  }

  // New method for getting received requests (for Vuex store)
  async getReceivedRequests(req, res) {
    try {
      const { userId } = req.params;

      // Validate userId
      if (!userId || isNaN(parseInt(userId))) {
        console.error('Invalid userId:', userId);
        return res.status(400).json({ message: 'Invalid user ID' });
      }

     
      // Use the existing getRequests method from the service
      const requests = await FriendRequestService.getRequests(userId);
     

      res.status(200).json({
        requests,
        lastUpdated: Date.now(),
      });
    } catch (err) {
      console.error('Error in getReceivedRequests:', err);
      res.status(500).json({ message: err.message });
    }
  }
}

module.exports = FriendRequestController;

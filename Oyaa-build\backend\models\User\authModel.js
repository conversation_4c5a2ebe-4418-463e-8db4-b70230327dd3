const { Client } = require('pg');
const bcrypt = require('bcrypt');
const db = require('../../utils/db');
const logger = require('../../utils/logger');

class AuthModel {
  /**
   * Create a new user with password (legacy method)
   * @param {string} username - User's username
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @param {string} avatar - User's avatar URL
   * @returns {Object} - New user object
   */
  async createUser(username, email, password, avatar) {
    const hashedPassword = await bcrypt.hash(password, 10);
    const query = `
      INSERT INTO users (username, email, password_hash, avatar)
      VALUES ($1, $2, $3, $4)
      RETURNING *;
    `;
    const result = await db.query(query, [username, email, hashedPassword, avatar]);
    return result.rows[0];
  }

  /**
   * Create a new user with Firebase authentication
   * @param {string} firebaseUid - Firebase user ID
   * @param {string} username - User's username
   * @param {string} email - User's email
   * @param {string} avatar - User's avatar URL
   * @param {string} handle - User's display name/handle (optional, defaults to username)
   * @returns {Object} - New user object
   */
  async createFirebaseUser(firebaseUid, username, email, avatar, handle = null) {
    try {
      // First, check if we need to alter the users table to add firebase_uid column
      await this.ensureFirebaseUidColumn();

      // If handle is not provided, use username as the default
      const userHandle = handle || username;

      const query = `
        INSERT INTO users (username, email, avatar, firebase_uid, handle)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *;
      `;
      const result = await db.query(query, [username, email, avatar, firebaseUid, userHandle]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error creating Firebase user:', error);
      throw error;
    }
  }

  /**
   * Ensure the firebase_uid column exists in the users table
   */
  async ensureFirebaseUidColumn() {
    try {
      // Check if the column exists
      const checkQuery = `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'firebase_uid';
      `;
      const checkResult = await db.query(checkQuery);

      // If the column doesn't exist, add it
      if (checkResult.rows.length === 0) {
        logger.info('Adding firebase_uid column to users table');
        const alterQuery = `
          ALTER TABLE users
          ADD COLUMN firebase_uid VARCHAR(128) UNIQUE;
        `;
        await db.query(alterQuery);
      }
    } catch (error) {
      logger.error('Error ensuring firebase_uid column:', error);
      throw error;
    }
  }

  /**
   * Find a user by username
   * @param {string} username - Username to search for
   * @returns {Object|null} - User object or null
   */
  async findUserByUsername(username) {
    const query = 'SELECT * FROM users WHERE LOWER(username) = LOWER($1);';
    const result = await db.query(query, [username]);
    return result.rows[0];
  }

  /**
   * Find a user by email
   * @param {string} email - Email to search for
   * @returns {Object|null} - User object or null
   */
  async findUserByEmail(email) {
    const query = 'SELECT * FROM users WHERE LOWER(email) = LOWER($1);';
    const result = await db.query(query, [email]);
    return result.rows[0];
  }

  /**
   * Find a user by Firebase UID
   * @param {string} firebaseUid - Firebase UID to search for
   * @returns {Object|null} - User object or null
   */
  async findUserByFirebaseUid(firebaseUid) {
    try {
      // First, ensure the column exists
      await this.ensureFirebaseUidColumn();

      const query = 'SELECT * FROM users WHERE firebase_uid = $1;';
      const result = await db.query(query, [firebaseUid]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error finding user by Firebase UID:', error);
      throw error;
    }
  }

  /**
   * Compare a password with a hashed password
   * @param {string} password - Plain text password
   * @param {string} hashedPassword - Hashed password
   * @returns {boolean} - True if passwords match
   */
  async comparePassword(password, hashedPassword) {
    return await bcrypt.compare(password, hashedPassword);
  }
}

module.exports = new AuthModel();
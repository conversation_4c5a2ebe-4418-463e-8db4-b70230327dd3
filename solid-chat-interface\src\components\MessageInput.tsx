import { Component, createSignal } from 'solid-js';
import './MessageInput.css';

interface MessageInputProps {
  onSendMessage: (text: string, media?: any) => void;
}

const MessageInput: Component<MessageInputProps> = (props) => {
  const [messageText, setMessageText] = createSignal('');
  const [selectedFile, setSelectedFile] = createSignal<File | null>(null);
  let textAreaRef: HTMLTextAreaElement | undefined;
  let fileInputRef: HTMLInputElement | undefined;

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  };

  const sendMessage = () => {
    const text = messageText().trim();
    if (!text && !selectedFile()) return;

    let media = null;
    if (selectedFile()) {
      const file = selectedFile()!;
      media = {
        url: URL.createObjectURL(file),
        type: getFileType(file),
        name: file.name,
        size: file.size
      };
    }

    props.onSendMessage(text, media);
    
    // Reset form
    setMessageText('');
    setSelectedFile(null);
    if (fileInputRef) fileInputRef.value = '';
    
    // Reset textarea height
    if (textAreaRef) {
      textAreaRef.style.height = 'auto';
    }
  };

  const getFileType = (file: File): 'image' | 'audio' | 'video' | 'file' => {
    if (file.type.startsWith('image/')) return 'image';
    if (file.type.startsWith('audio/')) return 'audio';
    if (file.type.startsWith('video/')) return 'video';
    return 'file';
  };

  const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
      setSelectedFile(target.files[0]);
    }
  };

  const removeSelectedFile = () => {
    setSelectedFile(null);
    if (fileInputRef) fileInputRef.value = '';
  };

  const autoResize = () => {
    if (textAreaRef) {
      textAreaRef.style.height = 'auto';
      textAreaRef.style.height = Math.min(textAreaRef.scrollHeight, 120) + 'px';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div class="message-input-container">
      {selectedFile() && (
        <div class="file-preview">
          <div class="file-info">
            <span class="file-icon">
              {selectedFile()!.type.startsWith('image/') && '🖼️'}
              {selectedFile()!.type.startsWith('audio/') && '🎵'}
              {selectedFile()!.type.startsWith('video/') && '🎬'}
              {!selectedFile()!.type.startsWith('image/') && 
               !selectedFile()!.type.startsWith('audio/') && 
               !selectedFile()!.type.startsWith('video/') && '📄'}
            </span>
            <div class="file-details">
              <span class="file-name">{selectedFile()!.name}</span>
              <span class="file-size">{formatFileSize(selectedFile()!.size)}</span>
            </div>
          </div>
          <button 
            class="remove-file"
            onClick={removeSelectedFile}
            type="button"
          >
            ×
          </button>
        </div>
      )}

      <div class="input-row">
        <button 
          class="attach-button"
          onClick={() => fileInputRef?.click()}
          type="button"
        >
          📎
        </button>

        <div class="text-input-container">
          <textarea
            ref={textAreaRef}
            value={messageText()}
            onInput={(e) => {
              setMessageText(e.currentTarget.value);
              autoResize();
            }}
            onKeyDown={handleKeyDown}
            placeholder="Type a message..."
            rows="1"
            class="message-textarea"
          />
        </div>

        <button 
          class={`send-button ${!messageText().trim() && !selectedFile() ? 'disabled' : ''}`}
          onClick={sendMessage}
          disabled={!messageText().trim() && !selectedFile()}
          type="button"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
          </svg>
        </button>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        accept="image/*,audio/*,video/*,.pdf,.doc,.docx,.txt"
        style="display: none;"
      />
    </div>
  );
};

export default MessageInput;

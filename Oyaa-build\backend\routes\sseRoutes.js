const express = require('express');
const router = express.Router();
const { sse<PERSON><PERSON><PERSON>, sendSSEUpdate } = require('../utils/sse');
const authMiddleware = require('../middleware/authMiddleware');
const { admin, initializeFirebaseAdmin } = require('../config/firebase.config');
const logger = require('../utils/logger');

// Initialize Firebase Admin SDK
const firebaseAdmin = initializeFirebaseAdmin();

// Legacy route
router.get('/', sseHandler);

// Test route to manually send an SSE update
router.get('/test/:userId', (req, res) => {
  const { userId } = req.params;

  sendSSEUpdate(userId, {
    type: 'friendRequestUpdate',
    action: 'test',
    message: 'This is a test SSE update'
  });

  res.status(200).json({ message: `Test SSE update sent to user ${userId}` });
});

// New route with user ID in the path - using Firebase authentication
router.get('/connect/:userId', async (req, res) => {
  try {
    // Set CORS headers for SSE
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');

    // Check for token in query params for SSE connections
    const token = req.query.token;
    if (!token) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    try {
      // Verify Firebase token
      const decodedFirebaseToken = await firebaseAdmin.auth().verifyIdToken(token);

      // Verify that the authenticated user matches the requested userId
      if (decodedFirebaseToken.uid !== req.params.userId) {
        logger.warn(`SSE connection attempt with mismatched UIDs: ${decodedFirebaseToken.uid} vs ${req.params.userId}`);
        return res.status(403).json({
          message: 'Unauthorized: You can only connect to your own SSE stream'
        });
      }

      // Add user info to the request
      req.user = {
        firebaseUid: decodedFirebaseToken.uid,
        email: decodedFirebaseToken.email,
        name: decodedFirebaseToken.name,
        picture: decodedFirebaseToken.picture
      };

      logger.info(`SSE connection established for user: ${decodedFirebaseToken.uid}`);
      sseHandler(req, res);
    } catch (firebaseError) {
      logger.error('Firebase token verification error in SSE route:', firebaseError);
      return res.status(401).json({ message: 'Invalid Firebase token' });
    }
  } catch (error) {
    logger.error('Error in SSE connect route:', error);
    return res.status(401).json({ message: 'Invalid authentication token' });
  }
});

// New route with numeric user ID in the path - using JWT token authentication
router.get('/connect/user/:userId', async (req, res) => {
  try {
    // Set CORS headers for SSE
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');

    // Check for token in query params for SSE connections
    const token = req.query.token;
    if (!token) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    try {
      // For user ID-based connections, we'll use a simpler approach
      // We'll look up the user in the database to verify they exist
      const db = require('../utils/db');
      const userId = req.params.userId;

      // Validate userId is a number
      if (isNaN(parseInt(userId))) {
        logger.warn(`SSE connection attempt with invalid user ID: ${userId}`);
        return res.status(400).json({ message: 'Invalid user ID format' });
      }

      // Look up the user in the database
      const userQuery = 'SELECT id, username, email, firebase_uid FROM users WHERE id = $1';
      const userResult = await db.query(userQuery, [userId]);

      if (userResult.rows.length === 0) {
        logger.warn(`SSE connection attempt with non-existent user ID: ${userId}`);
        return res.status(404).json({ message: 'User not found' });
      }

      const user = userResult.rows[0];

      // Add user info to the request
      req.user = {
        userId: user.id,
        username: user.username,
        email: user.email,
        firebaseUid: user.firebase_uid
      };

      logger.info(`SSE connection established for user ID: ${userId}`);
      sseHandler(req, res);
    } catch (dbError) {
      logger.error('Database error in SSE user route:', dbError);
      return res.status(500).json({ message: 'Server error verifying user' });
    }
  } catch (error) {
    logger.error('Error in SSE connect/user route:', error);
    return res.status(401).json({ message: 'Invalid authentication token' });
  }
});

module.exports = router;

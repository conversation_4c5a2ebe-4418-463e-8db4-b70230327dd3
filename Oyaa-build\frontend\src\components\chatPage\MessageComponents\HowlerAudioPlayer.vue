<template>
  <div class="whatsapp-audio-player">
    <button @click="togglePlayPause" class="play-pause-button">
      <PlayIcon v-if="!isPlaying" class="icon" />
      <PauseIcon v-else class="icon" />
    </button>
    <div class="waveform-container" ref="waveform"></div>
    <div class="time-info">
      <span class="current-time">{{ currentTime }}</span>
      <span class="total-time">{{ totalTime }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { Howl } from 'howler';
import WaveSurfer from 'wavesurfer.js';
import { Play as PlayIcon, Pause as PauseIcon } from 'lucide-vue-next';

const props = defineProps({
  mediaUrl: { type: String, required: true },
});

const sound = ref(null);
const wavesurfer = ref(null);
const isPlaying = ref(false);
const currentTime = ref('0:00');
const totalTime = ref('0:00');
const waveform = ref(null);

onMounted(() => {
  // Initialize Howler for audio playback
  sound.value = new Howl({
    src: [props.mediaUrl],
    html5: true,
    onplay: () => {
      isPlaying.value = true;
      updateCurrentTime();
    },
    onpause: () => {
      isPlaying.value = false;
    },
    onend: () => {
      isPlaying.value = false;
      wavesurfer.value.stop();
      currentTime.value = '0:00'; // Reset to start
    },
  });

  // Initialize WaveSurfer for waveform visualization
  wavesurfer.value = WaveSurfer.create({
    container: waveform.value,
    waveColor: '#A8BFFF', // Light blue for the waveform
    progressColor: '#5865F2', // Darker blue for played portion
    cursorColor: '#FFFFFF', // White cursor for seeking
    barWidth: 2,
    barRadius: 2,
    responsive: true,
    height: 30,
    normalize: true,
    partialRender: true,
  });

  // Load the audio file into WaveSurfer
  wavesurfer.value.load(props.mediaUrl);

  // Sync WaveSurfer playback with Howler
  wavesurfer.value.on('audioprocess', () => {
    if (isPlaying.value) {
      const current = wavesurfer.value.getCurrentTime();
      sound.value.seek(current);
      updateCurrentTime(current);
    }
  });

  // Set total duration when waveform is ready
  wavesurfer.value.on('ready', () => {
    const duration = wavesurfer.value.getDuration();
    totalTime.value = formatTime(duration);
  });

  // Handle seeking when clicking/dragging the waveform
  wavesurfer.value.on('seek', (progress) => {
    const seekTime = progress * wavesurfer.value.getDuration();
    sound.value.seek(seekTime);
    updateCurrentTime(seekTime);
  });
});

// Toggle play/pause functionality
const togglePlayPause = () => {
  if (isPlaying.value) {
    sound.value.pause();
    wavesurfer.value.pause();
  } else {
    sound.value.play();
    wavesurfer.value.play();
  }
};

// Update the current time display
const updateCurrentTime = (time = sound.value.seek()) => {
  currentTime.value = formatTime(time);
};

// Format time as m:ss
const formatTime = (time) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
};

// Clean up resources on unmount
onBeforeUnmount(() => {
  if (sound.value) {
    sound.value.unload();
  }
  if (wavesurfer.value) {
    wavesurfer.value.destroy();
  }
});
</script>

<style scoped>
.whatsapp-audio-player {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #2b2d31; /* Dark background like WhatsApp */
  padding: 8px;
  border-radius: 16px;
  max-width: 300px; /* Compact like WhatsApp */
}

.play-pause-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #25d366; /* WhatsApp green */
  color: white;
}

.icon {
  width: 20px;
  height: 20px;
}

.waveform-container {
  flex: 1;
  height: 30px;
  min-width: 100px; /* Ensures waveform has enough space */
}

.time-info {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #b5bac1; /* Light gray for timestamps */
}

.current-time,
.total-time {
  min-width: 30px;
  text-align: center;
}
</style>
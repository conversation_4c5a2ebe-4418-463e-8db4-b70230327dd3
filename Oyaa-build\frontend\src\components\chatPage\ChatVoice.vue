<template>
    <div class="voice-note">
      <button @click="startRecording" :disabled="isRecording">
        <MicIcon class="icon" />
      </button>
      <button @click="stopRecording" :disabled="!isRecording">
        <StopIcon class="icon" />
      </button>
    </div>
  </template>
  
  <script setup>
  import { ref, onUnmounted } from 'vue';
  import { Mic as MicIcon, Square as StopIcon } from 'lucide-vue-next';
  
  const emit = defineEmits(['sendVoiceNote']);
  
  const isRecording = ref(false);
  const mediaRecorder = ref(null);
  const audioChunks = ref([]);
  
  const startRecording = () => {
    navigator.mediaDevices
      .getUserMedia({ audio: true })
      .then((stream) => {
        mediaRecorder.value = new MediaRecorder(stream);
        mediaRecorder.value.ondataavailable = (event) => {
          audioChunks.value.push(event.data);
        };
        mediaRecorder.value.onstop = () => {
          const audioBlob = new Blob(audioChunks.value, { type: 'audio/webm' });
          const audioFile = new File([audioBlob], 'voice_note.webm', { type: 'audio/webm' });
          emit('sendVoiceNote', audioFile);
          audioChunks.value = [];
        };
        mediaRecorder.value.start();
        isRecording.value = true;
      })
      .catch((error) => {
        console.error('Error accessing microphone:', error);
        alert('Please allow microphone access to record voice notes.');
      });
  };
  
  const stopRecording = () => {
    if (mediaRecorder.value && mediaRecorder.value.state !== 'inactive') {
      mediaRecorder.value.stop();
      mediaRecorder.value.stream.getTracks().forEach((track) => track.stop());
      isRecording.value = false;
    }
  };
  
  onUnmounted(() => {
    if (mediaRecorder.value && mediaRecorder.value.state !== 'inactive') {
      mediaRecorder.value.stop();
      mediaRecorder.value.stream.getTracks().forEach((track) => track.stop());
    }
  });
  </script>
  
  <style scoped>
  .voice-note {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  button {
    background: transparent;
    border: none;
    padding: 6px;
    cursor: pointer;
  }
  
  button:hover {
    transform: scale(1.1);
  }
  
  button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .icon {
    width: 20px;
    height: 20px;
    color: #949ba4;
  }
  </style>
const db = require('../../utils/db');

class GroupModel {


  async initializeWorldChat() {
    const existingWorldChat = await this.getGroupById(1);
    if (!existingWorldChat) {
      // Use NULL for creator_id instead of 0
      const query = `
        INSERT INTO groups (id, name, creator_id, description, avatar, is_world_chat, tags)
        VALUES (1, 'World Chat', NULL, 'Just have fun', NULL, true, '{}')
        ON CONFLICT (id) DO NOTHING
        RETURNING *;
      `;
      const result = await db.query(query);
      return result.rows[0] || { id: 1, name: 'World Chat', description: 'Just have fun', creator_id: null, is_world_chat: true };
    }
    return existingWorldChat;
  }


  async createGroup(name, creatorId, description, avatar, tags = [], isWorldChat = false, latitude = null, longitude = null) {
    let query;
    let values;
    if (latitude !== null && longitude !== null) {
      query = `
        INSERT INTO groups (name, creator_id, description, avatar, is_world_chat, latitude, longitude, location, tags)
        VALUES ($1, $2, $3, $4, $5, $6::double precision, $7::double precision, ST_SetSRID(ST_MakePoint($7::double precision, $6::double precision), 4326)::geography, $8)
        RETURNING *;
      `;
      values = [name, creatorId, description, avatar, isWorldChat, latitude, longitude, tags];
    } else {
      query = `
        INSERT INTO groups (name, creator_id, description, avatar, is_world_chat, tags)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *;
      `;
      values = [name, creatorId, description, avatar, isWorldChat, tags];
    }
    const result = await db.query(query, values);
    return result.rows[0];
  }

  async addMember(groupId, userId) {
    const query = `
      INSERT INTO group_members (group_id, user_id)
      VALUES ($1, $2)
      RETURNING *;
    `;
    const result = await db.query(query, [groupId, userId]);
    return result.rows[0];
  }

  async removeMember(groupId, userId) {
    const query = `
      DELETE FROM group_members
      WHERE group_id = $1 AND user_id = $2
      RETURNING *;
    `;
    const result = await db.query(query, [groupId, userId]);
    return result.rows[0];
  }

// backend/models/Groups/groupModel.js
async getGroups(userId) {
  const query = `
    SELECT
      g.id,
      g.name,
      g.description,
      g.avatar,
      COUNT(gm2.user_id) AS member_count
    FROM
      group_members gm
    JOIN
      groups g ON gm.group_id = g.id
    LEFT JOIN
      group_members gm2 ON g.id = gm2.group_id
    WHERE
      gm.user_id = $1
    GROUP BY
      g.id, g.name, g.description, g.avatar;
  `;
  const result = await db.query(query, [userId]);
  return result.rows;
}


  async getGroupRequests(userId, lastUpdated) {
    try {
      const lastUpdatedSeconds = Math.floor(lastUpdated / 1000);
      const query = `
        SELECT *
        FROM group_requests
        WHERE user_id = $1 AND created_at > to_timestamp($2);
      `;
      const result = await db.query(query, [userId, lastUpdatedSeconds]);
      return result.rows;
    } catch (err) {
      console.error('Model: Error fetching group requests:', err);
      throw err;
    }
  }

  // New search method
  async searchGroups(searchTerm) {
    const query = `
      SELECT *
      FROM groups
      WHERE name ILIKE $1 OR EXISTS (
        SELECT 1
        FROM unnest(tags) AS tag
        WHERE tag ILIKE $1
      )
    `;
    const term = `%${searchTerm}%`;
    console.log('[GroupModel] Executing search query with term:', term);
    const result = await db.query(query, [term]);
    return result.rows;
  }
  async isMember(groupId, userId) {
    const query = `
      SELECT 1
      FROM group_members
      WHERE group_id = $1 AND user_id = $2;
    `;
    const result = await db.query(query, [groupId, userId]);
    return result.rows.length > 0;
  }
  async getGroupById(groupId) {
    const query = `
      SELECT *
      FROM groups
      WHERE id = $1;
    `;
    const result = await db.query(query, [groupId]);
    return result.rows[0];
  }

  async getGroupByName(name) {
    const query = `
      SELECT *
      FROM groups
      WHERE name = $1;
    `;
    const result = await db.query(query, [name]);
    return result.rows[0]; // Returns the group object or undefined if not found
  }

// groupModel.js
async getTrendingGroups(page = 1, limit = 20) {
  const offset = (page - 1) * limit;
  const query = `
    SELECT
      g.id,
      g.name,
      u.username AS creator_name,
      g.created_at,
      g.description,
      g.avatar,
      COUNT(gm.user_id) AS member_count
    FROM groups g
    JOIN users u ON g.creator_id = u.id
    LEFT JOIN group_members gm ON g.id = gm.group_id
    GROUP BY g.id, u.username, g.created_at, g.description, g.avatar
    ORDER BY member_count DESC
    LIMIT $1 OFFSET $2;
  `;
  const countQuery = `
    SELECT COUNT(DISTINCT g.id) AS total
    FROM groups g
    JOIN users u ON g.creator_id = u.id
    LEFT JOIN group_members gm ON g.id = gm.group_id;
  `;
  const [result, countResult] = await Promise.all([
    db.query(query, [limit, offset]),
    db.query(countQuery)
  ]);
  return {
    groups: result.rows,
    total: parseInt(countResult.rows[0].total)
  };
}
async searchGroupsByTags(tags, limit, offset) {
  // Use LOWER() function to make the comparison case-insensitive
  const query = `
    SELECT
      g.*,
      array(
        SELECT LOWER(unnest(g.tags)) INTERSECT SELECT unnest($1::text[])
      ) AS matching_tags
    FROM groups g
    WHERE EXISTS (
      SELECT 1 FROM unnest(g.tags) AS tag WHERE LOWER(tag) = ANY($1::text[])
    )
    LIMIT $2 OFFSET $3
  `;
  console.log('[GroupModel] Searching for tags (lowercase):', tags);
  const result = await db.query(query, [tags, limit, offset]);
  return result.rows;
}

async getTagSuggestions(query) {
  try {
    const searchTerm = `%${query.toLowerCase()}%`;
    const queryText = `
      SELECT DISTINCT tag
      FROM (
        SELECT unnest(tags) AS tag
        FROM groups
      ) AS unnested_tags
      WHERE tag ILIKE $1
      ORDER BY tag
      LIMIT 10
    `;
    const result = await db.query(queryText, [searchTerm]);
    return result.rows.map(row => row.tag);
  } catch (err) {
    console.error('Error in getTagSuggestions:', err);
    throw new Error('Failed to fetch tag suggestions');
  }
}
// Add this method inside the GroupModel class
async updateAvatar(groupId, newAvatar) {
  const query = `
    UPDATE groups
    SET avatar = $1
    WHERE id = $2
    RETURNING *;
  `;
  const result = await db.query(query, [newAvatar, groupId]);
  return result.rows[0]; // Returns the updated group or undefined if not found
}

async updateName(groupId, newName) {
  const query = `
    UPDATE groups
    SET name = $1
    WHERE id = $2
    RETURNING *;
  `;
  const result = await db.query(query, [newName, groupId]);
  return result.rows[0];
}

async updateDescription(groupId, newDescription) {
  const query = `
    UPDATE groups
    SET description = $1
    WHERE id = $2
    RETURNING *;
  `;
  const result = await db.query(query, [newDescription, groupId]);
  return result.rows[0];
}

async updateTags(groupId, newTags) {
  const query = `
    UPDATE groups
    SET tags = $1
    WHERE id = $2
    RETURNING *;
  `;
  const result = await db.query(query, [newTags, groupId]);
  return result.rows[0];
}

}

module.exports = new GroupModel();

<template>
  <div class="customer-support">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <h1>Oyaa Customer Support</h1>
        <p>We're here to help. How can we assist you today?</p>
      </div>
      <div class="hero-background"></div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
      <div class="support-content">
        <!-- Contact Form Section -->
        <section id="contact-form" class="section">
          <div class="card">
            <div class="card-header">
              <div class="card-title-wrapper">
                <MessageSquare class="icon accent" />
                <h2 class="card-title">Contact Us</h2>
              </div>
              <p class="card-description">Send us a message and we'll get back to you soon</p>
            </div>
            <div class="card-content">
              <form @submit.prevent="submitForm" class="support-form">
                <div class="form-group">
                  <label for="name">Name</label>
                  <div class="input-wrapper">
                    <User class="input-icon" size="16" />
                    <input type="text" id="name" v-model="form.name" required placeholder="Your name">
                  </div>
                </div>
                <div class="form-group">
                  <label for="email">Email</label>
                  <div class="input-wrapper">
                    <Mail class="input-icon" size="16" />
                    <input type="email" id="email" v-model="form.email" required placeholder="Your email address">
                  </div>
                </div>
                <div class="form-group">
                  <label for="subject">Subject</label>
                  <div class="select-wrapper">
                    <select id="subject" v-model="form.subject" required>
                      <option value="">Select a subject</option>
                      <option value="general">General Inquiry</option>
                      <option value="technical">Technical Support</option>
                      <option value="billing">Billing Issue</option>
                      <option value="feedback">Feedback</option>
                    </select>
                    <ChevronDown class="select-icon" size="16" />
                  </div>
                </div>
                <div class="form-group">
                  <label for="message">Message</label>
                  <textarea id="message" v-model="form.message" required placeholder="How can we help you?"></textarea>
                </div>
                <button type="submit" class="submit-button">
                  <Send size="16" />
                  <span>Send Message</span>
                </button>
              </form>
            </div>
          </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="section">
          <div class="card">
            <div class="card-header">
              <div class="card-title-wrapper">
                <HelpCircle class="icon accent" />
                <h2 class="card-title">Frequently Asked Questions</h2>
              </div>
              <p class="card-description">Quick answers to common questions</p>
            </div>
            <div class="card-content">
              <div class="accordion">
                <div v-for="(faq, index) in faqs" :key="index" class="accordion-item">
                  <div 
                    class="accordion-header" 
                    @click="toggleAccordion(index)"
                    :class="{ 'active': openAccordion === index }"
                  >
                    <span>{{ faq.question }}</span>
                    <ChevronDown :class="['accordion-icon', { 'rotated': openAccordion === index }]" />
                  </div>
                  <transition name="accordion">
                    <div class="accordion-content" v-show="openAccordion === index">
                      <p>{{ faq.answer }}</p>
                    </div>
                  </transition>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Support Options Section -->
        <section id="support-options" class="section">
          <div class="card">
            <div class="card-header">
              <div class="card-title-wrapper">
                <LifeBuoy class="icon accent" />
                <h2 class="card-title">Support Options</h2>
              </div>
              <p class="card-description">Choose the best way to get help</p>
            </div>
            <div class="card-content">
              <div class="support-options-grid">
                <div class="support-option">
                  <div class="support-option-icon">
                    <Mail />
                  </div>
                  <h3>Email Support</h3>
                  <p>Send us an email for non-urgent inquiries</p>
                  <a href="mailto:<EMAIL>" class="support-link">
                    <EMAIL>
                    <ExternalLink size="14" />
                  </a>
                </div>
                <div class="support-option">
                  <div class="support-option-icon">
                    <MessageCircle />
                  </div>
                  <h3>Live Chat</h3>
                  <p>Chat with our support team in real-time</p>
                  <button @click="startLiveChat" class="support-button">
                    Start Chat
                    <ArrowRight size="14" />
                  </button>
                </div>
                <div class="support-option">
                  <div class="support-option-icon">
                    <Phone />
                  </div>
                  <h3>Phone Support</h3>
                  <p>Call us for urgent matters</p>
                  <a href="tel:+1234567890" class="support-link">
                    +1 (234) 567-890
                    <ExternalLink size="14" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
  </div>
</template>
  
<script setup>
import { ref } from 'vue';
import { 
  MessageSquare,
  HelpCircle,
  LifeBuoy,
  Mail,
  MessageCircle,
  Phone,
  ChevronDown,
  User,
  Send,
  ExternalLink,
  ArrowRight
} from 'lucide-vue-next';

const form = ref({
  name: '',
  email: '',
  subject: '',
  message: ''
});

const submitForm = () => {
  // Handle form submission logic here
  console.log('Form submitted:', form.value);
  // Reset form after submission
  form.value = { name: '', email: '', subject: '', message: '' };
  // Show a success message or handle errors
  alert('Thank you for your message. We will get back to you soon!');
};

const faqs = [
  {
    question: "How do I reset my password?",
    answer: "To reset your password, go to the login page and click on 'Forgot Password'. Follow the instructions sent to your email to create a new password."
  },
  {
    question: "How can I update my profile information?",
    answer: "Log in to your account, go to 'Settings', and select 'Edit Profile'. Make your changes and click 'Save' to update your information."
  },
  {
    question: "What should I do if I encounter a bug?",
    answer: "If you encounter a bug, please report it through our contact form or email support with details about the issue, including steps to reproduce it and any error messages you received."
  },
  {
    question: "How do I delete my account?",
    answer: "To delete your account, go to 'Settings', select 'Account', and click on 'Delete Account'. Please note that this action is irreversible and will permanently remove all your data."
  },
  {
    question: "Is my data secure on Oyaa?",
    answer: "Yes, we take data security very seriously. All data is encrypted both in transit and at rest. We implement industry-standard security measures and regularly audit our systems to ensure your information remains protected."
  }
];

const openAccordion = ref(null);

const toggleAccordion = (index) => {
  if (openAccordion.value === index) {
    openAccordion.value = null;
  } else {
    openAccordion.value = index;
  }
};

const startLiveChat = () => {
  // Implement live chat functionality here
  console.log('Starting live chat...');
  alert('Live chat is not available at the moment. Please try again later or use another support option.');
};
</script>
  
<style scoped>
.customer-support {
  --bg-primary: #0a0a0f;
  --bg-secondary: #13131a;
  --bg-tertiary: #1c1c26;
  --text-primary: #f2f2f2;
  --text-secondary: #a0a0b0;
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: rgba(99, 102, 241, 0.15);
  --border-color: rgba(40, 40, 60, 0.8);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', sans-serif;
}

/* Hero Section */
.hero {
  position: relative;
  padding: 4rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(to bottom right, var(--bg-primary), var(--bg-secondary), rgba(99, 102, 241, 0.05));
  overflow: hidden;
}

.hero-content {
  position: relative;
  z-index: 10;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
  letter-spacing: -0.025em;
  background: linear-gradient(90deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero p {
  max-width: 36rem;
  margin: 0 auto;
  color: var(--text-secondary);
  font-size: 1.125rem;
}

.hero-background {
  position: absolute;
  inset: 0;
  z-index: 0;
  opacity: 0.2;
  background: radial-gradient(circle at center, var(--accent-primary), transparent 70%);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 3rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.support-content {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

/* Section */
.section {
  scroll-margin-top: 5rem;
}

/* Card */
.card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.card-title-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.card-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.card-content {
  padding: 1.5rem;
}

/* Icons */
.icon {
  width: 20px;
  height: 20px;
}

.icon.accent {
  color: var(--accent-primary);
}

/* Form Styles */
.support-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

input,
select,
textarea {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 0.9375rem;
  transition: all var(--transition-fast);
}

select {
  appearance: none;
}

.select-wrapper {
  position: relative;
  width: 100%;
}

.select-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

textarea {
  min-height: 120px;
  resize: vertical;
  padding: 0.75rem;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px var(--accent-tertiary);
}

input::placeholder,
textarea::placeholder {
  color: var(--text-secondary);
}

.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: var(--accent-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.submit-button:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.submit-button:active {
  transform: translateY(0);
}

/* Accordion Styles */
.accordion-item {
  border-bottom: 1px solid var(--border-color);
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-fast);
  border-radius: var(--radius-sm);
}

.accordion-header:hover {
  background-color: var(--bg-tertiary);
}

.accordion-header.active {
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
}

.accordion-icon {
  width: 16px;
  height: 16px;
  transition: transform var(--transition-fast);
  color: var(--text-secondary);
}

.accordion-header.active .accordion-icon {
  color: var(--accent-primary);
}

.accordion-icon.rotated {
  transform: rotate(180deg);
}

.accordion-content {
  padding: 1rem 1.5rem;
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  line-height: 1.6;
}

.accordion-enter-active,
.accordion-leave-active {
  transition: max-height var(--transition-normal), opacity var(--transition-normal);
  max-height: 300px;
  overflow: hidden;
}

.accordion-enter-from,
.accordion-leave-to {
  max-height: 0;
  opacity: 0;
}

/* Support Options Grid */
.support-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.support-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.support-option:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-tertiary);
}

.support-option-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-tertiary);
  color: var(--accent-primary);
  border-radius: 50%;
}

.support-option h3 {
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.support-option p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.support-link,
.support-button {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
}

.support-link:hover,
.support-button:hover {
  color: var(--accent-secondary);
  transform: translateX(2px);
}

.support-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

/* Responsive Adjustments */
@media (min-width: 768px) {
  .hero {
    padding: 5rem 2rem;
  }
  
  .hero h1 {
    font-size: 3rem;
  }
  
  .main-content {
    padding: 3rem 2rem;
  }
}

@media (min-width: 1024px) {
  .hero h1 {
    font-size: 3.5rem;
  }
}

@media (max-width: 640px) {
  .support-options-grid {
    grid-template-columns: 1fr;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .hero p {
    font-size: 1rem;
  }
  
  .card-title {
    font-size: 1.125rem;
  }
  
  .submit-button {
    width: 100%;
  }
}
</style>
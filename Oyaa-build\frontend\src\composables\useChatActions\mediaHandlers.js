// frontend/src/composables/useChatActions/mediaHandlers.js
import { uploadMedia } from '@/services/mediaService';

export function useMediaHandlers(state, handleSendMessage) {
  const handleFileUpload = async (file) => {
    try {
      const userId = state.currentUser.value.id;
      const chatId = state.friend.value.id;
      const response = await uploadMedia(file, userId, chatId);
      if (response.media) {
        const messagePayload = {
          text: '',
          media: response.media,
        };
        await handleSendMessage(messagePayload);
      }
    } catch (error) {
      console.error('Media upload failed:', error);
    }
  };

  return { handleFileUpload };
}

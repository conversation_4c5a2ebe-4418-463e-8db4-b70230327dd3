export interface User {
  id: string;
  name: string;
  avatar?: string;
  status?: 'online' | 'offline' | 'away';
}

export interface MediaFile {
  url: string;
  type: 'image' | 'audio' | 'video' | 'file';
  name: string;
  size?: number;
  duration?: number;
  width?: number;
  height?: number;
  thumbnail?: string;
}

export interface Message {
  id: string;
  text: string;
  timestamp: Date;
  sender: User;
  type: 'text' | 'image' | 'audio' | 'video' | 'file';
  media?: MediaFile;
  replyTo?: string;
  edited?: boolean;
  reactions?: Record<string, string[]>;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
}

export interface PerformanceMetrics {
  fps: number;
  renderTime: number;
  memoryUsage: { used: number; total: number };
  scrollJank: number;
  domNodes: number;
  virtualization: {
    totalMessages: number;
    renderedMessages: number;
    virtualizationRatio: number;
    visibleRange: { start: number; end: number };
  };
}

// frontend/src/composables/useGroup/useGroupChatActions.js
import useGroupInitialization from './useGroupChatActions/GroupInitialization';
import useGroupLoadMessages from './useGroupChatActions/GroupLoadMessages';
import useGroupMessageSending from './useGroupChatActions/GroupMessageSending';
import useGroupUiHandlers from './useGroupChatActions/GroupUiHandlers';
import useGroupMediaHandlers from './useGroupChatActions/GroupMediaHandlers';
import useGroupReactions from './useGroupChatActions/GroupReactions';
import useGroupMessageHandlers from './useGroupChatActions/GroupMessageHandlers';

export default function useGroupChatActions(state, api, socket, pagination) {
  const init = useGroupInitialization(state, api, socket, pagination);
  const { loadOlderMessages } = useGroupLoadMessages(state, api, pagination);
  const messageSending = useGroupMessageSending(state, api, socket);
  const uiHandlers = useGroupUiHandlers(state);
  const mediaHandlers = useGroupMediaHandlers(state, messageSending.sendMessage);
  const reactions = useGroupReactions(state, socket);
  const messageHandlers = useGroupMessageHandlers(state);

  // New action handlers
  const handleStar = async ({ messageId, action }) => {
    try {
      await api.starMessage(state.group.id, messageId, action);
      const message = state.messages.value.find((msg) => msg.id === messageId);
      if (message) {
        message.isStarred = action === 'star';
      }
    } catch (err) {
      console.error('Failed to star/unstar message:', err.message);
    }
  };

  const handleDelete = async (messageId) => {
    try {
      await api.deleteMessage(state.group.id, messageId);
      state.messages.value = state.messages.value.filter((msg) => msg.id !== messageId);
    } catch (err) {
      console.error('Failed to delete message:', err.message);
    }
  };

  const handleMute = async (userId) => {
    try {
      await api.muteUser(state.group.id, userId);
      if (userId === state.currentUser.id) {
        state.isMuted.value = true;
      }
    } catch (err) {
      console.error('Failed to mute user:', err.message);
    }
  };

  return {
    ...init,
    loadOlderMessages,
    ...messageSending,
    ...uiHandlers,
    ...mediaHandlers,
    ...reactions,
    ...messageHandlers,
    handleStar,
    handleDelete,
    handleMute,
  };
}
// Telegram's viewport slicing approach - much better than virtualization!

export interface ViewportSliceResult {
  invisibleTop: HTMLElement[];
  visible: HTMLElement[];
  invisibleBottom: HTMLElement[];
}

export interface ViewportSliceOptions {
  container: HTMLElement;
  selector: string;
  extraSize?: number;
  extraMinLength?: number;
}

function getVisibleRect(element: HTMLElement, container: HTMLElement): DOMRect | null {
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();

  // Check if element intersects with container
  const isVisible = !(
    elementRect.bottom < containerRect.top ||
    elementRect.top > containerRect.bottom ||
    elementRect.right < containerRect.left ||
    elementRect.left > containerRect.right
  );

  return isVisible ? elementRect : null;
}

export function getViewportSlice(options: ViewportSliceOptions): ViewportSliceResult {
  const { container, selector, extraSize = 0, extraMinLength = 5 } = options;

  const elements = Array.from(container.querySelectorAll<HTMLElement>(selector));
  const containerRect = container.getBoundingClientRect();

  // Expand container rect with extra size for buffer
  const expandedRect = {
    top: containerRect.top - extraSize,
    bottom: containerRect.bottom + extraSize,
    left: containerRect.left - extraSize,
    right: containerRect.right + extraSize
  };

  const invisibleTop: HTMLElement[] = [];
  const visible: HTMLElement[] = [];
  const invisibleBottom: HTMLElement[] = [];

  let foundVisible = false;

  for (const element of elements) {
    const elementRect = element.getBoundingClientRect();

    // Check if element is visible within expanded container
    const isVisible = !(
      elementRect.bottom < expandedRect.top ||
      elementRect.top > expandedRect.bottom
    );

    if (isVisible) {
      foundVisible = true;
      visible.push(element);
    } else if (foundVisible) {
      invisibleBottom.push(element);
    } else {
      invisibleTop.push(element);
    }
  }

  // Add buffer elements for smooth scrolling
  if (extraMinLength) {
    // Move some invisible top elements to visible for buffer
    const topBuffer = invisibleTop.splice(
      Math.max(0, invisibleTop.length - extraMinLength),
      extraMinLength
    );
    visible.unshift(...topBuffer);

    // Move some invisible bottom elements to visible for buffer
    const bottomBuffer = invisibleBottom.splice(0, extraMinLength);
    visible.push(...bottomBuffer);
  }

  return { invisibleTop, visible, invisibleBottom };
}

export class ViewportSlicer {
  private container: HTMLElement;
  private selector: string;
  private extraSize: number;
  private extraMinLength: number;
  private sliceTimeout: number | null = null;
  private removedElements: Map<HTMLElement, { parent: HTMLElement; nextSibling: Node | null }> = new Map();
  private isSlicing = false;

  constructor(options: ViewportSliceOptions) {
    this.container = options.container;
    this.selector = options.selector;
    this.extraSize = options.extraSize || window.innerHeight; // 1x viewport height buffer (more aggressive)
    this.extraMinLength = options.extraMinLength || 3; // Smaller buffer for more aggressive slicing
  }

  public scheduleSlice(delay = 3000) {
    if (this.sliceTimeout) {
      clearTimeout(this.sliceTimeout);
    }

    this.sliceTimeout = setTimeout(() => {
      this.performSlice();
      this.sliceTimeout = null;
    }, delay);
  }

  public cancelSlice() {
    if (this.sliceTimeout) {
      clearTimeout(this.sliceTimeout);
      this.sliceTimeout = null;
    }
  }

  private performSlice() {
    if (this.isSlicing) return;
    this.isSlicing = true;

    try {
      const slice = getViewportSlice({
        container: this.container,
        selector: this.selector,
        extraSize: this.extraSize,
        extraMinLength: this.extraMinLength
      });

      // Remove invisible elements from DOM but keep references
      [...slice.invisibleTop, ...slice.invisibleBottom].forEach(element => {
        if (element.parentElement) {
          this.removedElements.set(element, {
            parent: element.parentElement,
            nextSibling: element.nextSibling
          });
          element.remove();
        }
      });

      const removedCount = slice.invisibleTop.length + slice.invisibleBottom.length;
      const totalCount = removedCount + slice.visible.length;
      console.log(`🔪 Viewport slice: removed ${removedCount}/${totalCount} elements (${((removedCount/totalCount)*100).toFixed(1)}%), kept ${slice.visible.length} visible`);
    } catch (error) {
      console.error('Error during viewport slicing:', error);
    } finally {
      this.isSlicing = false;
    }
  }

  public restoreElements() {
    // Restore all removed elements to their original positions
    for (const [element, position] of this.removedElements) {
      try {
        if (position.nextSibling && position.nextSibling.parentElement === position.parent) {
          position.parent.insertBefore(element, position.nextSibling);
        } else {
          position.parent.appendChild(element);
        }
      } catch (error) {
        console.warn('Failed to restore element:', error);
      }
    }
    this.removedElements.clear();
  }

  public destroy() {
    this.cancelSlice();
    this.restoreElements();
  }

  public getStats() {
    const currentDOMElements = this.container.querySelectorAll(this.selector).length;
    const removedCount = this.removedElements.size;
    const totalOriginal = currentDOMElements + removedCount;

    console.log('🔍 ViewportSlicer stats:', {
      currentDOMElements,
      removedCount,
      totalOriginal,
      selector: this.selector
    });

    return {
      total: totalOriginal,
      visible: currentDOMElements,
      removed: removedCount,
      efficiency: removedCount > 0 ? (removedCount / totalOriginal) * 100 : 0
    };
  }
}
